<?php
namespace Silksoftwarecorp\UnifiedArPayment\Model\Config\Source;

use Magento\Framework\Option\ArrayInterface;

class MarketCode implements ArrayInterface
{
    public function toOptionArray()
    {
        return [
            ['value' => '0', 'label' => __('Default')],
            ['value' => '1', 'label' => __('AutoRental')],
            ['value' => '2', 'label' => __('DirectMarketing')],
            ['value' => '3', 'label' => __('ECommerce')],
            ['value' => '4', 'label' => __('FoodRestaurant')],
            ['value' => '5', 'label' => __('HotelLodging')],
            ['value' => '6', 'label' => __('Petroluem')],
            ['value' => '7', 'label' => __('Retail')],
            ['value' => '8', 'label' => __('QSR')],
            ['value' => '9', 'label' => __('Grocery')],
        ];
    }
} 