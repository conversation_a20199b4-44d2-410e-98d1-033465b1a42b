import { memo, useMemo } from 'react'
import { useSelector } from 'react-redux'

import { installRequestGroup } from '@/utils/constant'

import { StyledFastFaxButton } from './styled'

const FastFaxButton = () => {
  const isLogin = useSelector((state: Store) => state.user.isLogin)
  const locationId = useSelector((state: Store) => state.user.shipTo?.locationId)

  const group = useMemo(() => {
    let targetGroup: any = {}
    installRequestGroup.some((item) => {
      const cur = item.group.find((it) => it.id === locationId)
      if (cur) {
        targetGroup = cur
        return true
      }
      return false
    })
    return targetGroup
  }, [locationId])

  const { btnText, linkHref } = useMemo(() => {
    const fastFaxGroup = [
      'Nashville',
      'Mobile',
      'Lexington',
      'New Carlisle',
      'Harrisburg',
      'Jacksonville',
      'Greenville',
      'Liverpool'
    ]
    if (fastFaxGroup.includes(group?.label)) {
      return { btnText: 'FAST FAX', linkHref: '/install-request' }
    }
    // TODO: Install App (new tab: https://apps.apple.com/us/app/blevins/id1480961249 )
    // Alexandria
    // Rocky Mount
    // Bristol
    // Fort Worth
    // Albuquerque
    // Moline
    return {}
  }, [group])

  return isLogin && btnText ? (
    <StyledFastFaxButton className="fast-fax-button-component" href={linkHref} title={btnText}>
      {btnText}
    </StyledFastFaxButton>
  ) : null
}

export default memo(FastFaxButton)
