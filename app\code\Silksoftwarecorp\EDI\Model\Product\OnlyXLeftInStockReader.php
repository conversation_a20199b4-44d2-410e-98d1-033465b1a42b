<?php

namespace Silksoftwarecorp\EDI\Model\Product;

use Magento\Framework\Exception\InputException;
use Magento\Framework\Exception\LocalizedException;
use Magento\InventoryCatalog\Model\GetStockIdForCurrentWebsite;
use Magento\InventoryConfigurationApi\Api\GetStockItemConfigurationInterface;
use Magento\InventorySalesApi\Api\GetProductSalableQtyInterface;
use Psr\Log\LoggerInterface;

class OnlyXLeftInStockReader
{
    /**
     * @var GetProductSalableQtyInterface
     */
    private $getProductSalableQty;

    /**
     * @var GetStockIdForCurrentWebsite
     */
    private $getStockIdForCurrentWebsite;

    /**
     * @var GetStockItemConfigurationInterface
     */
    private $getStockItemConfiguration;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @param GetProductSalableQtyInterface $getProductSalableQty
     * @param GetStockIdForCurrentWebsite $getStockIdForCurrentWebsite
     * @param GetStockItemConfigurationInterface $getStockItemConfiguration
     * @param LoggerInterface $logger
     */
    public function __construct(
        GetProductSalableQtyInterface $getProductSalableQty,
        GetStockIdForCurrentWebsite $getStockIdForCurrentWebsite,
        GetStockItemConfigurationInterface $getStockItemConfiguration,
        LoggerInterface $logger
    ) {
        $this->getProductSalableQty = $getProductSalableQty;
        $this->getStockIdForCurrentWebsite = $getStockIdForCurrentWebsite;
        $this->getStockItemConfiguration = $getStockItemConfiguration;
        $this->logger = $logger;
    }

    public function execute($sku): ?float
    {
        $stockId = $this->getStockIdForCurrentWebsite->execute();
        try {
            $stockItemConfiguration = $this->getStockItemConfiguration->execute($sku, $stockId);
        } catch (LocalizedException $e) {
            $this->logger->critical($e->getMessage());
            return null;
        }

        $thresholdQty = $stockItemConfiguration->getStockThresholdQty();
        if ($thresholdQty === 0) {
            return null;
        }

        try {
            $productSalableQty = $this->getProductSalableQty->execute($sku, $stockId);
            $stockLeft = $productSalableQty - $stockItemConfiguration->getMinQty();

            if ($productSalableQty > 0 && $stockLeft <= $thresholdQty) {
                return (float)$stockLeft;
            }
        } catch (InputException | LocalizedException $e) {
            // this is expected behavior because ex. Group product doesn't have own quantity
            $this->logger->critical($e->getMessage());
            return null;
        }

        return null;
    }
}
