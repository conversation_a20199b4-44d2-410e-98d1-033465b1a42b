import { memo } from 'react'
import { Spin } from 'antd'
import { clsx } from 'clsx'

import { StyledCommonSpin, StyledCommonLoading } from './styled'

const CommonLoading = ({ children, spinning, style = {} }: any) => {
  return children ? (
    <StyledCommonSpin className={clsx({ 'is-loading': spinning })}>
      <Spin spinning={spinning}>{children}</Spin>
    </StyledCommonSpin>
  ) : (
    <StyledCommonLoading style={style}>
      <Spin spinning={spinning} />
    </StyledCommonLoading>
  )
}

export default memo(CommonLoading)
