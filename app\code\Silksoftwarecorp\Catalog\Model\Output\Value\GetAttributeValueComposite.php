<?php

namespace Silksoftwarecorp\Catalog\Model\Output\Value;

/**
 * Custom implementation to handle null values in GetAttributeValueComposite
 */
class GetAttributeValueComposite extends \Magento\EavGraphQl\Model\Output\Value\GetAttributeValueComposite
{
    /**
     * Override execute method to handle null values
     *
     * @param string $entity
     * @param string $code
     * @param string|null $value
     * @return array|null
     */
    public function execute(string $entity, string $code, ?string $value): ?array
    {
        // Handle null values by converting to empty string
        if ($value === null) {
            $value = '';
        }

        // Call the parent method with the non-null value
        return parent::execute($entity, $code, $value);
    }
}