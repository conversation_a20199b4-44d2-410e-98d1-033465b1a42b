import styled from '@emotion/styled'

export const StyledNewAddressRequest = styled.div`
  padding-bottom: 185px;

  .request-container {
    padding: 24px 24px 40px;
    margin: 96px auto 0;
    max-width: 778px;
    border: 1px solid #d5d9e2;
    border-radius: 5px;
  }

  .request-banner {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
    width: 730px;
    height: 147px;
    border-radius: 5px;
    background-image: url('/images/request-bg.png');
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    font-weight: 700;
    font-size: 32px;
    line-height: 38px;
    letter-spacing: 0;
    color: var(--color-white);
  }

  .request-desc {
    margin: 24px auto 0;
    max-width: 592px;
    font-weight: 400;
    font-size: 15px;
    line-height: 21px;
    letter-spacing: 0.01em;
    text-align: center;
    color: var(--color-text);
  }

  .request-form {
    margin: 32px auto 0;
    width: 730px;

    form {
      .${({ theme }) => theme.namespace} {
        &-form-item {
          margin-bottom: 16px;
        }
        &-select-selection-placeholder {
          font-weight: 400;
          font-size: 16px;
          line-height: 24px;
          letter-spacing: 0.02em;
          color: #777;
        }
      }
      label {
        font-weight: 400;
        font-size: 15px;
        line-height: 21px;
        letter-spacing: 0.01em;
        color: var(--color-font);

        &::before {
          display: none !important;
        }
      }

      .type-item-group {
        display: flex;
        align-items: center;
        margin-top: -5px;

        & > div {
          display: flex;
          align-items: center;
          font-weight: 400;
          font-size: 15px;
          line-height: 21px;
          letter-spacing: 0.01em;
          color: var(--color-text);

          div {
            margin-right: 8px;
          }

          &:not(:first-of-type) {
            margin-left: 24px;
          }
        }
      }

      .submit-btn {
        display: flex;
        align-items: center;
        margin: 32px auto 0;
        width: 128px;
        height: 49px;
        border-radius: 3px;
        background: var(--color-primary) !important;
        border: none !important;

        span {
          font-weight: 700;
          font-size: 16px;
          line-height: 21px;
          letter-spacing: 0.03em;
          text-transform: uppercase;
          color: #fff !important;
        }

        &[disabled] {
          background: #939392 !important;
        }
      }
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    padding: 0 16px 40px;

    .request-container {
      margin-top: 24px;
    }

    .request-banner {
      width: 100%;
      background-image: url('/images/request-bg-mb.png');
      font-size: 25px;
      line-height: 33px;
      text-align: center;
    }

    .request-form {
      width: 100%;

      form {
        .submit-btn {
          width: 100%;
        }
      }
    }
  }
`

export const StyledFormItem = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-column-gap: 16px;
  justify-content: space-between;

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    grid-template-columns: 1fr;
  }
`
