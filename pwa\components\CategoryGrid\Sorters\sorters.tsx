import { clsx } from 'clsx'
import { memo } from 'react'
import { nanoid } from 'nanoid'
import { Button, Radio, Select, Space } from 'antd'

import { MediaLayout } from '@/ui'
import { useSorters } from '@/hooks/CategoryGrid'

import { StyledSorters, StyledMobileSorters, StyledMobileSortersList } from './styled'

const Sorters = ({ search, categoryId, mobileDrawerOpen = false, handleCloseSortDrawer }) => {
  const {
    sortCode,
    isDesc,
    sortFields,
    handleOnChange,
    toggleDirection,
    sortDirectionCode,
    mobileSortChange,
    mobileDirectionChange,
    applySortOrder,
    closeSortDrawer
  } = useSorters({
    search,
    categoryId,
    mobileDrawerOpen,
    handleCloseSortDrawer
  })

  return sortCode ? (
    <>
      <MediaLayout>
        <StyledSorters>
          <span className="sort-label">Sort by</span>
          <Select
            className="sort-select"
            value={sortCode}
            onChange={handleOnChange}
            suffixIcon={
              <svg
                width="1em"
                height="1em"
                fill="currentColor"
                aria-hidden="true"
                focusable="false">
                <use xlinkHref="#icon-select-suffix" />
              </svg>
            }>
            {sortFields.map((sort: any) => {
              return (
                <Select.Option key={nanoid()} value={sort.value}>
                  <span dangerouslySetInnerHTML={{ __html: sort.label }} />
                </Select.Option>
              )
            })}
          </Select>
          <div
            aria-hidden="true"
            className={clsx('sort-icon', { 'is-desc': isDesc })}
            onClick={toggleDirection}>
            <svg width="9px" height="13px" fill="currentColor" aria-hidden="true" focusable="false">
              <use xlinkHref="#icon-sort" />
            </svg>
          </div>
        </StyledSorters>
      </MediaLayout>
      <MediaLayout type="mobile">
        <StyledMobileSorters>
          <div className="mb-sort-header">
            <h3>Sorters</h3>
            <div className="close-btn" aria-hidden="true" onClick={closeSortDrawer}>
              <svg width="16px" height="16px" fill="currentColor" focusable="false">
                <use xlinkHref="#icon-close-white" />
              </svg>
            </div>
          </div>
          <StyledMobileSortersList id="mobile-sorters-list">
            <Radio.Group value={sortCode} onChange={mobileSortChange}>
              <Space direction="vertical">
                {sortFields.map((item) => (
                  <Radio key={item.value} value={item.value}>
                    <div>
                      <h3>{item.label}</h3>
                    </div>
                  </Radio>
                ))}
              </Space>
            </Radio.Group>
            <Radio.Group
              className="mb-sort-direction"
              value={sortDirectionCode}
              onChange={mobileDirectionChange}>
              <Space direction="vertical">
                <Radio value="ASC">
                  <svg
                    className="mb-direction-icon"
                    width="9px"
                    height="13px"
                    fill="currentColor"
                    aria-hidden="true"
                    focusable="false">
                    <use xlinkHref="#icon-sort" />
                  </svg>
                </Radio>
                <Radio value="DESC">
                  <svg
                    className="mb-direction-icon is-desc"
                    width="9px"
                    height="13px"
                    fill="currentColor"
                    aria-hidden="true"
                    focusable="false">
                    <use xlinkHref="#icon-sort" />
                  </svg>
                </Radio>
              </Space>
            </Radio.Group>
          </StyledMobileSortersList>

          <Button type="primary" className="apply-sort" onClick={applySortOrder}>
            APPLY SORT
          </Button>
        </StyledMobileSorters>
      </MediaLayout>
    </>
  ) : null
}

export default memo(Sorters)
