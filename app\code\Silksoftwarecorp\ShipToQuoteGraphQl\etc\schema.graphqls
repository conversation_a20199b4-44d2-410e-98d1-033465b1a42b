type Cart {
    ship_to_id: String @doc(description: "Ship To ID") @resolver(class: "Silksoftwarecorp\\ShipToQuoteGraphQl\\Model\\Resolver\\CartShipToId")
    customer_contact_id: String @doc(description: "Customer Contact ID") @resolver(class: "Silksoftwarecorp\\ShipToQuoteGraphQl\\Model\\Resolver\\CartCustomerContactId")
}

input SetShipToDataInput {
    cart_id: String! @doc(description: "The unique ID that identifies the customer's cart")
    ship_to_id: String! @doc(description: "Ship To ID to be set on the quote")
}

type SetShipToDataOutput {
    cart: Cart! @doc(description: "Updated cart")
}

type CustomerOrder {
    ship_to_id: String @doc(description: "Ship To ID") @resolver(class: "Silksoftwarecorp\\ShipToQuoteGraphQl\\Model\\Resolver\\OrderShipToId")
    customer_contact_id: String @doc(description: "Customer Contact ID") @resolver(class: "Silksoftwarecorp\\ShipToQuoteGraphQl\\Model\\Resolver\\OrderCustomerContactId")
}

type Mutation {
    setShipToDataOnCart(
        input: SetShipToDataInput!
    ): SetShipToDataOutput @resolver(class: "Silksoftwarecorp\\ShipToQuoteGraphQl\\Model\\Resolver\\SetShipToDataOnCart")
}
