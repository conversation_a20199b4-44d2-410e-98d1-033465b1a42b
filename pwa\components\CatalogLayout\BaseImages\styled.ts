import styled from '@emotion/styled'

export const StyledBase = styled.div`
  .carousel {
    .${({ theme }) => theme.namespace} {
      &-carousel {
        .slick-arrow {
          top: 46px;
          z-index: 99;
          transform: unset;
          width: 40px;
          height: 40px;
        }

        .slick-prev {
          left: -45px !important;
        }

        .slick-disabled {
          opacity: 0.7 !important;
        }
      }
    }
  }
`

export const StyledMainImg = styled.div`
  height: 578px;

  .main-item {
    display: flex !important;
    justify-content: center;
    align-items: center;
    height: 578px;

    .main-item-img {
      width: auto;
      height: auto;
      display: block;
      margin: auto;
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    height: 344px;

    .main-item {
      height: 344px;

      .main-item-img {
        width: 100%;
        height: 344px;
        object-fit: contain;
      }
    }
  }
`

export const StyledImage = styled.img`
  display: block;
  max-width: 132px;
  margin: 0 auto;
  width: 132px;
  height: 132px;
  cursor: pointer;
  border: 2px solid transparent;
  object-fit: contain;

  &.active {
    border-color: var(--color-primary);
  }
`
