<?php

namespace Silksoftwarecorp\UnifiedArPaymentGraphQl\Model\Plugin;

use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Silksoftwarecorp\UnifiedArPayment\Model\ResourceModel\BlevinsOrderPayment as BlevinsOrderPaymentResource;

class SetPaymentAndPlaceOrderPlugin
{
    /**
     * @var OrderRepositoryInterface
     */
    protected $orderRepository;

    /**
     * @var SearchCriteriaBuilder
     */
    protected $searchCriteriaBuilder;

    private $blevinsOrderPaymentResource;

    public function __construct(
        OrderRepositoryInterface $orderRepository,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        BlevinsOrderPaymentResource $BlevinsOrderPayment,
    ) {
        $this->orderRepository = $orderRepository;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->blevinsOrderPaymentResource = $BlevinsOrderPayment;
    }

    /**
     * After plugin for setPaymentMethodAndPlaceOrder resolver
     *
     * @param $subject
     * @param $result
     * @param $field
     * @param $context
     * @param $info
     * @param $value
     * @param $args
     * @return mixed
     */
    public function afterResolve($subject, $result, $field, $context, $info, $value, $args)
    {
        $input = $args['input'] ?? [];
        if (isset($result['order']['order_number'])) {
            $orderNumber = $result['order']['order_number'];
            $searchCriteria = $this->searchCriteriaBuilder
                ->addFilter('increment_id', $orderNumber)
                ->create();
            $orders = $this->orderRepository->getList($searchCriteria)->getItems();
            if ($orders) {
                $order = reset($orders);
                $hasChanges = false;

                // Save unified_ar_return_data if provided
                if (!empty($input['unified_ar_return_data']) && !$order->getData('unified_ar_return_data')) {
                    $order->setData('unified_ar_return_data', $input['unified_ar_return_data']);
                    $hasChanges = true;
                }

                // Handle surcharge amount if provided
                if (!empty($input['surcharge_amount']) && $input['surcharge_amount'] > 0) {
                    $surchargeAmount = (float)$input['surcharge_amount'];

                    // Save surcharge amount to order
                    $order->setData('unified_ar_surcharge_amount', $surchargeAmount);

                    // Add surcharge to order total
                    $currentTotal = $order->getGrandTotal();
                    $newTotal = $currentTotal + $surchargeAmount;
                    $order->setGrandTotal($newTotal);

                    // Add surcharge to base total if it exists
                    if ($order->getBaseGrandTotal()) {
                        $currentBaseTotal = $order->getBaseGrandTotal();
                        $newBaseTotal = $currentBaseTotal + $surchargeAmount;
                        $order->setBaseGrandTotal($newBaseTotal);
                    }

                    $hasChanges = true;
                }

                // Save order if there were changes
                if ($hasChanges) {
                    $this->orderRepository->save($order);
                }
                $this->saveReturnData($order, $input['unified_ar_return_data']);
            }
        }
        return $result;
    }

    private function saveReturnData($order, $returnData)
    {
        $quoteId = $order->getQuoteId();
        $orderId = $order->getId();
        $this->blevinsOrderPaymentResource->updateBlePaymentReturnData($quoteId, $returnData);
        $this->blevinsOrderPaymentResource->updateBlePaymentDataByQuoteId($quoteId, $orderId);
    }
}
