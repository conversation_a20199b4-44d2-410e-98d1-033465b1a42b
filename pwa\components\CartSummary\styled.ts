import styled from '@emotion/styled'

export const StyledSummary = styled.div`
  padding: 10px 0 24px;
  border-top: 1px solid #d9d9d9;
  border-bottom: 1px solid #d9d9d9;

  .item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 15px;

    > span {
      font-weight: 400;
      font-size: 15px;
      line-height: 21px;
      letter-spacing: 0.01em;
    }
  }

  .available-in-checkout {
    font-style: italic;
  }

  .total {
    margin-top: 24px;

    span {
      font-weight: 700;
      font-size: 18px;
      line-height: 21px;
      letter-spacing: 0.01em;
    }
  }
`
