import styled from '@emotion/styled'

export const StyledBlogList = styled.div`
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-column-gap: 24px;
  justify-content: space-between;
  align-items: flex-start;

  .image {
    position: relative;
    height: 164px;
    background-color: #d9d9d9;
  }

  h6 {
    line-height: 24px;
    margin-top: 16px;
    margin-bottom: 8px;
    font-family: var(--font-poppins-bold);
    font-size: 16px;
    font-weight: 600;
  }

  .label {
    position: absolute;
    top: 8px;
    left: 8px;
    width: 78px;
    height: 26px;
    display: flex;
    padding: 4px 8px;
    color: #667071;
    font-family: var(--font-montserrat-bold);
    font-size: 14px;
    font-weight: 700;
    z-index: 20;
    background-color: #fff;
    justify-content: center;
    align-items: center;
  }

  .description {
    line-height: 24px;
    font-family: var(--font-montserrat);
    font-size: 15px;
    font-weight: 400;
  }
`
