<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */

namespace Silksoftwarecorp\Inventory\Ui\DataProvider;

use Silksoftwarecorp\Inventory\Api\BranchManagerProfileRepositoryInterface;

/**
 * Class ManagerProfileDataProvider
 * Service class for loading manager profile data
 */
class ManagerProfileDataProvider
{
    /**
     * @var BranchManagerProfileRepositoryInterface
     */
    private $managerProfileRepository;

    /**
     * ManagerProfileDataProvider constructor.
     *
     * @param BranchManagerProfileRepositoryInterface $managerProfileRepository
     */
    public function __construct(
        BranchManagerProfileRepositoryInterface $managerProfileRepository
    ) {
        $this->managerProfileRepository = $managerProfileRepository;
    }

    /**
     * Load manager profile data for a source
     *
     * @param string $sourceCode
     * @return array
     */
    public function loadManagerProfileData(string $sourceCode): array
    {
        // Load manager profile data
        $profiles = $this->managerProfileRepository->getBySourceCode($sourceCode);

        // Initialize manager_profile data structure
        $managerProfileData = [
            'general' => [],
            'territory_manager1' => [],
            'territory_manager2' => [],
            'territory_manager3' => []
        ];

        $territoryIndex = 1;
        foreach ($profiles as $profile) {
            $isGeneral = $profile->getIsGeneral();
            $profileData = [
                'id' => $profile->getId(),
                'image' => $profile->getImage() ? [
                    [
                        'name' => $profile->getImage(),
                        'type' => 'image',
                        'url' => $profile->getImageUrl()
                    ]
                ] : [],
                'name' => $profile->getName(),
                'phone' => $profile->getPhone(),
                'region' => $profile->getRegion(),
            ];

            if ($isGeneral) {
                $managerProfileData['general'] = $profileData;
            } else {
                // Assign territory manager to corresponding position
                if ($territoryIndex <= 3) {
                    $managerProfileData['territory_manager' . $territoryIndex] = $profileData;
                    $territoryIndex++;
                }
            }
        }

        return $managerProfileData;
    }

    /**
     * Add manager profile data to source data array
     *
     * @param array $sourceData
     * @param string $sourceCode
     * @return array
     */
    public function addManagerProfileToSourceData(array $sourceData, string $sourceCode): array
    {
        $sourceData['manager_profile'] = $this->loadManagerProfileData($sourceCode);
        return $sourceData;
    }
}
