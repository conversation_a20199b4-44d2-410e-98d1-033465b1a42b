import { Button } from 'antd'
import { useMemo, memo } from 'react'
import { FormattedMessage } from 'react-intl'

import CurrentFilter from './currentFilter'
import { StyledCurrentFilters } from './styled'

const CurrentFilters = ({
  filtersData,
  filterState,
  filterApi,
  setIsApplying,
  handleClearAll
}: any) => {
  const { removeItem } = filterApi

  const filterElements = useMemo(() => {
    const elements = []
    for (const [group, items] of filterState) {
      const filterItem = filtersData.find((filter) => {
        return filter.attribute_code === group
      })

      for (const item of items) {
        const { label, value } = item || {}
        const key = `${group}-${label}-${value}`

        elements.push(
          <CurrentFilter
            key={key}
            group={group}
            label={filterItem.label}
            item={item}
            removeItem={removeItem}
            setIsApplying={setIsApplying}
          />
        )
      }
    }

    return elements
  }, [filterState, filtersData, removeItem, setIsApplying])

  const hasElements = useMemo(() => {
    return filterElements.length > 0
  }, [filterElements])

  return hasElements ? (
    <StyledCurrentFilters>
      <h3 className="current-filters-title">Now Shopping by</h3>
      <div className="current-filters-content">
        <div>{filterElements}</div>
        <Button className="clear-all" type="link" onClick={handleClearAll}>
          <FormattedMessage id="plp.clearAll" />
        </Button>
      </div>
    </StyledCurrentFilters>
  ) : null
}

export default memo(CurrentFilters)
