<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */
declare(strict_types=1);

namespace Silksoftwarecorp\Inventory\Api;

use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;
use Silksoftwarecorp\Inventory\Api\Data\BranchManagerProfileInterface;

/**
 * Branch Manager Profile Repository Interface
 */
interface BranchManagerProfileRepositoryInterface
{
    /**
     * Save Branch Manager Profile
     *
     * @param BranchManagerProfileInterface $branchManagerProfile
     * @return BranchManagerProfileInterface
     * @throws CouldNotSaveException
     */
    public function save(BranchManagerProfileInterface $branchManagerProfile): BranchManagerProfileInterface;

    /**
     * Get Branch Manager Profile by ID
     *
     * @param int $id
     * @return BranchManagerProfileInterface
     * @throws NoSuchEntityException
     */
    public function getById(int $id): BranchManagerProfileInterface;

    /**
     * Get Branch Manager Profile by Source Code
     *
     * @param string $sourceCode
     * @return BranchManagerProfileInterface[]
     */
    public function getBySourceCode(string $sourceCode): array;

    /**
     * Get General Manager by Source Code
     *
     * @param string $sourceCode
     * @return BranchManagerProfileInterface|null
     */
    public function getGeneralManagerBySourceCode(string $sourceCode): ?BranchManagerProfileInterface;

    /**
     * Get Territory Managers by Source Code
     *
     * @param string $sourceCode
     * @return BranchManagerProfileInterface[]
     */
    public function getTerritoryManagersBySourceCode(string $sourceCode): array;

    /**
     * Delete Branch Manager Profile
     *
     * @param BranchManagerProfileInterface $branchManagerProfile
     * @return bool
     * @throws CouldNotDeleteException
     */
    public function delete(BranchManagerProfileInterface $branchManagerProfile): bool;

    /**
     * Delete Branch Manager Profile by ID
     *
     * @param int $id
     * @return bool
     * @throws CouldNotDeleteException
     * @throws NoSuchEntityException
     */
    public function deleteById(int $id): bool;

    /**
     * Get list
     *
     * @param SearchCriteriaInterface $searchCriteria
     * @return \Magento\Framework\Api\SearchResultsInterface
     */
    public function getList(SearchCriteriaInterface $searchCriteria);
} 