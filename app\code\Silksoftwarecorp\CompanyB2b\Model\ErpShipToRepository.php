<?php

namespace Silksoftwarecorp\CompanyB2b\Model;
use Magento\Framework\Exception\AlreadyExistsException;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\NoSuchEntityException;
use Silksoftwarecorp\CompanyB2b\Api\Data;
use Silksoftwarecorp\CompanyB2b\Api\Data\ErpCompanyShipToInterface;
use Silksoftwarecorp\CompanyB2b\Api\ErpShipToRepositoryInterface;
use Silksoftwarecorp\CompanyB2b\Model\ResourceModel\ErpCompanyShipTo;
use Silksoftwarecorp\CompanyB2b\Api\Data\ErpCompanyShipToInterfaceFactory;
use Silksoftwarecorp\CompanyB2b\Model\ShipTo\GetList;
class ErpShipToRepository implements ErpShipToRepositoryInterface
{

    private array $instances = [];
    public function __construct(
        private ErpCompanyShipToInterfaceFactory $shipToFactory,
        private  ErpCompanyShipTo                $erpCompanyShopToResource,
        private  GetList                         $shopToListGetter
    ) {

    }

    /**
     * @throws AlreadyExistsException
     */
    public function save(ErpCompanyShipToInterface $shipTo): ErpCompanyShipToInterface
    {
        // TODO: Implement save() method.
        unset($this->instances[$shipTo->getId()]);
        $this->erpCompanyShopToResource->save($shipTo);
        return $shipTo;
    }

    /**
     * @param int $entityId
     * @return ErpCompanyShipToInterface
     * @throws NoSuchEntityException
     */
    public function get(int $entityId): ErpCompanyShipToInterface
    {
        // TODO: Implement getById() method.
        if (!isset($this->instances[$entityId])) {
            /** @var ErpCompanyShipTo $shopToObj */
            $shopToObj = $this->shipToFactory->create();
            $shopToObj->load($entityId);
            if (!$shopToObj->getEntityId()) {
                throw NoSuchEntityException::singleField('id', $entityId);
            }
            $this->instances[$entityId] = $shopToObj;
        }
        return $this->instances[$entityId];
    }

    /**
     * @throws CouldNotDeleteException
     */
    public function delete(ErpCompanyShipToInterface $shipTo): bool
    {
        // TODO: Implement delete() method.
        $shopToId = $shipTo->getEntityId();
        try {
            unset($this->instances[$shopToId]);
            $this->erpCompanyShopToResource->delete($shipTo);
        } catch (\Exception $e) {
            throw new CouldNotDeleteException(
                __(
                    'Cannot delete company with id %1',
                    $shopToId
                ),
                $e
            );
        }
        return true;
    }

    /**
     * @param int $entityId
     * @return bool
     * @throws CouldNotDeleteException
     * @throws NoSuchEntityException
     */
    public function deleteById(int $entityId): bool
    {
        // TODO: Implement deleteById() method.
        $ship = $this->get($entityId);
        return $this->delete($ship);
    }

    /**
     * @param \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
     * @return Data\ShipToSearchResultsInterface
     */
    public function getList(\Magento\Framework\Api\SearchCriteriaInterface $searchCriteria)
    {
        // TODO: Implement getList() method.
        return $this->shopToListGetter->getList($searchCriteria);
    }
}
