import { gql } from '@apollo/client'
import type { DocumentNode } from '@apollo/client'

import { cartItems } from '../fragment/cartItems'

export const POST_ADD_PRODUCTS_CART: DocumentNode = gql`
  mutation addProductsToCart($cartId: String!, $cartItems: [CartItemInput!]!) {
    addToCart: addProductsToCart(cartId: $cartId, cartItems: $cartItems) {
      cart {
        id
        quantity: total_quantity
        ...cartItems
        __typename
      }
      user_errors {
        code
        message
      }
    }
  }
  ${cartItems}
`
