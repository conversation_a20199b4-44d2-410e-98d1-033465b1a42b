<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <tab id="silksoftwarecorp" translate="label" sortOrder="300">
            <label>Silk Commerce</label>
        </tab>
        <section id="realtime_b2b_pricing" translate="label" type="text" sortOrder="999" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>Real-time B2B Pricing</label>
            <tab>silksoftwarecorp</tab>
            <resource>Silksoftwarecorp_RealTimeB2BPricing::config</resource>
            <group id="general" translate="label" type="text" sortOrder="1" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>General</label>
                <field id="active" translate="label" type="select" sortOrder="1" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Enable Real-time B2B Pricing</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="debug" translate="label" type="select" sortOrder="55" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Debug</label>
                    <source_model>Magento\Config\Model\Config\Source\Enabledisable</source_model>
                </field>
            </group>
            <group id="api" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>API Settings</label>
                <depends>
                    <field id="realtime_b2b_pricing/general/active">1</field>
                </depends>
                <field id="environment" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Environment</label>
                    <source_model>Silksoftwarecorp\RealTimeB2BPricing\Model\Config\Source\Environment</source_model>
                </field>
                <field id="url" translate="label" type="text" sortOrder="15" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>URL</label>
                    <validate>required-entry</validate>
                    <depends>
                        <field id="environment">production</field>
                    </depends>
                </field>
                <field id="username" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Username</label>
                    <validate>required-entry</validate>
                    <depends>
                        <field id="environment">production</field>
                    </depends>
                </field>
                <field id="password" translate="label" type="obscure" sortOrder="25" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Password</label>
                    <validate>required-entry</validate>
                    <backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
                    <depends>
                        <field id="environment">production</field>
                    </depends>
                </field>
                <field id="request_type" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>RequestType</label>
                    <validate>required-entry</validate>
                    <depends>
                        <field id="environment">production</field>
                    </depends>
                </field>
                <field id="sandbox_url" translate="label" type="text" sortOrder="35" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Sandbox URL</label>
                    <validate>required-entry</validate>
                    <depends>
                        <field id="environment">sandbox</field>
                    </depends>
                </field>
                <field id="sandbox_username" translate="label" type="text" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Sandbox Username</label>
                    <validate>required-entry</validate>
                    <depends>
                        <field id="environment">sandbox</field>
                    </depends>
                </field>
                <field id="sandbox_password" translate="label" type="obscure" sortOrder="45" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Sandbox Password</label>
                    <validate>required-entry</validate>
                    <backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
                    <depends>
                        <field id="environment">sandbox</field>
                    </depends>
                </field>
                <field id="sandbox_request_type" translate="label" type="text" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Sandbox RequestType</label>
                    <validate>required-entry</validate>
                    <depends>
                        <field id="environment">sandbox</field>
                    </depends>
                </field>
                <!--<field id="enable_ssl_certificate_verify" translate="label" type="select" sortOrder="55" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Enable SSL Certificate Verification</label>
                    <validate>required-entry</validate>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Enable SSL Certificate Verification when requesting the Real-Time B2B Pricing api.</comment>
                </field>-->
                <field id="timeout" translate="label" type="text" sortOrder="55" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Timeout</label>
                    <validate>required-entry</validate>
                    <comment>The timeout for request Real-Time B2B Pricing API. The unit is seconds. Default: 10s.</comment>
                </field>
                <field id="cache_lifetime" translate="label" type="text" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Pricing Cache Lifetime (seconds)</label>
                    <validate>required-entry validate-number validate-greater-than-zero</validate>
                    <comment>Real-time B2B pricing cache lifetime. The unit is seconds. Default: 600s.</comment>
                </field>
            </group>
            <group id="cart" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Cart Settings</label>
                <depends>
                    <field id="realtime_b2b_pricing/general/active">1</field>
                </depends>
                <field id="price_validity_time" translate="label" type="text" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Cart Price Validity Duration</label>
                    <validate>required-entry validate-number validate-greater-than-zero</validate>
                    <comment>Real-time B2B pricing validity time on cart. The unit is seconds. Default: 600s.</comment>
                </field>
            </group>
        </section>
    </system>
</config>
