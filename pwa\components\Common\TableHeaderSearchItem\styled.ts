import styled from '@emotion/styled'

export const StyledTableHeaderSearchItem = styled.div`
  padding-top: 14px;

  p {
    display: inline-flex;
    align-items: center;
    padding: 0 16px;
    margin-bottom: 14px;
    width: 100%;
    font-weight: 700;
    font-size: 15px;
    line-height: 21px;
    letter-spacing: 0;
    color: var(--color-black);
    cursor: pointer;
  }

  .item-search {
    padding: 15px;
    height: 128px;
    border-top: 1px solid #d9d9d9;
    background: #f5f5f5;

    input {
      width: 100%;
      max-width: 152px;
      height: 44px !important;
      border-radius: 3px;
      border-color: #d9d9d9;
    }

    .${({ theme }) => theme.namespace} {
      &-input-affix-wrapper {
        padding-top: 0;
        padding-bottom: 0;
        border-radius: 3px;
        max-width: 152px;

        &:hover {
          .${({ theme }) => theme.namespace}-input-clear-icon {
            display: inline-flex;
          }
        }

        input {
          height: 42px !important;
        }

        .${({ theme }) => theme.namespace}-input-clear-icon {
          display: none;
        }
      }
      &-picker {
        padding-top: 0;
        display: block;
        height: 44px;
        width: 152px;
        border-radius: 3px;
        border-color: #d9d9d9;

        &.date-from {
          margin-bottom: 8px;
        }

        table tbody tr td {
          padding-top: 6px;
          padding-bottom: 6px;
        }

        input::placeholder {
          font-weight: 400;
          font-size: 15px;
          line-height: 25px;
          letter-spacing: 0.02em;
          color: #777;
        }

        &-clear svg {
          transform: scale(0.85);
        }
      }

      &-select {
        width: 152px !important;
        height: 44px !important;

        .${({ theme }) => theme.namespace} {
          &-select-selector {
            padding-left: 12px !important;
            border-radius: 3px !important;
            border-color: #d9d9d9;
          }
          &-select-selection-placeholder {
            font-weight: 400;
            font-size: 15px;
            line-height: 25px;
            letter-spacing: 0.02em;
            color: #777;
          }
        }

        &-arrow {
          margin-right: -6px;
        }
      }
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    padding-top: 0;

    p {
      padding: 0;
    }

    .item-search {
      display: none;
    }
  }
`
