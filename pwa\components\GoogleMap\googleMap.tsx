import { isEmpty } from 'lodash-es'
import { useEffect, useState, useMemo, useCallback } from 'react'
import { Map, GoogleApiWrapper, Marker } from 'google-maps-react'
import { useSelector } from 'react-redux'

import { StyledMapPage } from './styled'

const MapPage = ({
  google,
  address = '',
  width = '366px',
  height = '470px',
  defaultLocation = null,
  icon = null
}) => {
  const [location, setLocation] = useState(null)

  const locationValue = useMemo(() => {
    return location || defaultLocation
  }, [defaultLocation, location])

  const iconProps = useMemo(() => {
    return icon ? { icon } : {}
  }, [icon])

  const handleClickMarker = useCallback(() => {
    window.open(
      `https://www.google.com/maps?q=${locationValue.lat},${locationValue.lng}&z=15`,
      '_blank',
      'noopener,noreferrer'
    )
  }, [locationValue])

  useEffect(() => {
    if (!defaultLocation && window?.google?.maps && address) {
      const geocoder = new window.google.maps.Geocoder()
      geocoder?.geocode({ address }, (results, status) => {
        console.log('address: ', address)
        console.log('results: ', results)
        console.log('status: ', status)
        if (status === 'OK' && !isEmpty(results)) {
          try {
            const latLng = results[0]?.geometry?.location ?? null
            if (latLng) {
              setLocation({ lat: latLng.lat(), lng: latLng.lng() })
              console.log('location: ', {
                lat: latLng.lat(),
                lng: latLng.lng()
              })
            }
          } catch (e) {
            console.log('map error: ', e)
          }
        }
      })
    }
  }, [address, defaultLocation])

  return locationValue ? (
    <StyledMapPage width={width} height={height}>
      <Map
        google={google}
        zoom={13}
        style={{
          width,
          height,
          maxWidth: '100%',
          borderRadius: '4px'
        }}
        initialCenter={locationValue}>
        <Marker position={locationValue} onClick={handleClickMarker} {...iconProps} />
      </Map>
    </StyledMapPage>
  ) : null
}

const GoogleMap = (props) => {
  const [WrappedMap, setWrappedMap] = useState(null)
  const apiKey = useSelector(
    (state: Store) => state.app.storeConfig?.amasty_store_locator_google_api_key
  )

  useEffect(() => {
    if (apiKey) {
      try {
        const wrapped = GoogleApiWrapper({
          apiKey
        })(MapPage)
        setWrappedMap(() => wrapped) // Save the dynamically created components
      } catch (e) {
        console.error('Create GoogleApiWrapper component error')
        console.error(e)
      }
    }
  }, [apiKey])

  if (!apiKey || !WrappedMap) return null

  return <WrappedMap {...props} />
}

export default GoogleMap
