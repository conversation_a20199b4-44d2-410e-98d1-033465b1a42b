import styled from '@emotion/styled'
import { Row } from 'antd'

export const StyledCustomFormRow = styled(Row)`
  .${({ theme }) => theme.namespace}-form-item {
    margin-bottom: 16px;

    label {
      font-weight: 400;
      font-size: 15px;
      line-height: 21px;
      letter-spacing: 0.01em;
      color: var(--color-font);

      &::before {
        display: none !important;
      }
    }

    .item-date-picker {
      position: relative;
      padding-left: 48px;
      width: 100%;
      height: 44px;
      border: 1px solid #d9d9d9;
      border-radius: 3px;

      &.${({ theme }) => theme.namespace}-picker-status-error {
        border-color: #ff4d4f;
      }

      &::after {
        content: '';
        display: block;
        position: absolute;
        top: 8px;
        left: 12px;
        z-index: 0;
        width: 24px;
        height: 24px;
        background-image: url('/images/date-picker-icon.png');
        background-position: center;
        background-repeat: no-repeat;
        background-size: cover;
      }
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
  }
`

export const StyledCustomFormCheckoutGrid = styled.div`
  label {
    &:not(:last-of-type) {
      margin-right: 16px;
    }
  }

  &.is-column {
    label {
      display: flex;

      &:not(:last-of-type) {
        margin-bottom: 16px;
      }
    }
  }
`

export const StyledCustomFormRadioGrid = styled.div`
  display: inline-flex;
  padding-top: 8px;

  label {
    position: relative;
    padding-left: 27px;
    min-height: 28px;

    &:not(:last-of-type) {
      margin-right: 16px;
    }

    & > span:last-of-type {
      position: relative;
      bottom: -2px;
    }

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      display: block;
      width: 26px;
      height: 26px;
      border-radius: 3px;
      border: 1px solid #d9d9d9;
    }
    &.${({ theme }) => theme.namespace}-radio-wrapper-checked {
      &::after {
        border-color: var(--color-primary);
        background: var(--color-primary);
        background-image: url('/images/check.png');
        background-position: center;
        background-repeat: no-repeat;
      }
    }

    .${({ theme }) => theme.namespace}-radio {
      display: none !important;
    }
  }

  &.is-column {
    flex-direction: column;

    label {
      margin-right: 0;

      &:not(:last-of-type) {
        margin-bottom: 12px;
      }
    }
  }
`
