import styled from '@emotion/styled'

export const StyledRelatedDocuments = styled.div`
  & > div {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px 0 16px;
    min-height: 42px;
    border: 1px solid #d9d9d9;
    font-weight: 700;
    font-size: 16px;
    line-height: 26px;
    letter-spacing: 0;

    a {
      font-weight: 700;
      font-size: 15px;
      line-height: 24px;
      letter-spacing: 0;
      text-decoration: underline !important;
      color: var(--color-primary) !important;
    }

    &:nth-of-type(2n) {
      border-top: none;
      background: #f4f3f3;
    }

    &:not(:first-of-type) {
      border-top: none;
    }
  }
`
