import { gql } from '@apollo/client'
import type { DocumentNode } from '@apollo/client'

import { cartItems } from '../fragment/cartItems'
import { cart_prices } from '../fragment/cartPrices'

export const POST_REMOVE_CART: DocumentNode = gql`
  mutation removeItemFromCart($cartId: String!, $cartItemId: Int!) {
    removeCart: removeItemFromCart(input: { cart_id: $cartId, cart_item_id: $cartItemId }) {
      cart {
        quantity: total_quantity
        prices {
          ...cart_prices
          __typename
        }
        applied_coupons {
          code
        }
        ...cartItems
        __typename
      }
    }
  }
  ${cartItems}
  ${cart_prices}
`
