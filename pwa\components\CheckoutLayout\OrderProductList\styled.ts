import styled from '@emotion/styled'

export const StyledOrderProductList = styled.div`
  margin-top: 18px;

  .quantity {
    font-weight: 400;
    font-size: 16px;
    line-height: 26px;
    letter-spacing: 0.02em;
  }

  svg.is-collapse {
    transform: rotateX(180deg);
  }

  .${({ theme }) => theme.namespace} {
    &-collapse {
      border-width: 0;
      background-color: transparent;

      .${({ theme }) => theme.namespace} {
        &-collapse-header {
          padding: 16px 0;
        }

        &-collapse-content {
          border-top-width: 0;
          background-color: transparent;
        }

        &-collapse-content-box {
          padding: 0;
          background-color: transparent;
        }
      }
    }
  }
`

export const StyledOrderProductItem = styled.div`
  display: grid;
  grid-template-columns: 120px 1fr;
  grid-column-gap: 16px;
  justify-content: space-between;
  align-items: flex-start;

  &:not(:first-of-type) {
    padding-top: 24px;
    margin-top: 24px;
    border-top: 1px solid #d9d9d9;
  }

  .name {
    display: inline-block;

    span {
      display: -webkit-box;
      overflow: hidden;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      font-weight: 700;
      font-size: 16px;
      line-height: 24px;
      letter-spacing: 0;
      color: var(--color-font) !important;
    }
  }

  .sku {
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: 0;
  }

  div span,
  .price {
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: 0.02em;
  }

  .product-price {
    margin-top: 15px;
  }

  .text-label {
    padding-right: 5px;
  }

  .total span {
    font-weight: 700;
  }
`
