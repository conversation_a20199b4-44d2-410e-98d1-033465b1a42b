<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */

namespace Silksoftwarecorp\Inventory\Model;

use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Exception\FileSystemException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Filesystem\Directory\WriteInterface;

class ImageProcessor
{
    /**
     * Manager profile area inside media folder
     */
    public const MANAGER_PROFILE_MEDIA_PATH = 'manager_profile';

    /**
     * Manager profile temporary area inside media folder
     */
    public const MANAGER_PROFILE_MEDIA_TMP_PATH = 'manager_profile/tmp';

    /**
     * @var \Magento\Catalog\Model\ImageUploader
     */
    private $imageUploader;

    /**
     * @var \Magento\Framework\ImageFactory
     */
    private $imageFactory;

    /**
     * @var \Magento\Store\Model\StoreManagerInterface
     */
    private $storeManager;

    /**
     * @var \Magento\Framework\Filesystem\Directory\WriteInterface
     */
    private $mediaDirectory;

    /**
     * @var \Magento\Framework\Filesystem
     */
    private $filesystem;

    /**
     * @var \Magento\Framework\Message\ManagerInterface
     */
    private $messageManager;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    private $logger;

    /**
     * @var array
     */
    protected $allowedExtensions = ['jpg', 'jpeg', 'gif', 'png'];

    public function __construct(
        \Magento\Framework\Filesystem $filesystem,
        \Magento\Catalog\Model\ImageUploader $imageUploader,
        \Magento\Framework\ImageFactory $imageFactory,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Magento\Framework\Message\ManagerInterface $messageManager,
        \Psr\Log\LoggerInterface $logger
    ) {
        $this->filesystem = $filesystem;
        $this->imageUploader = $imageUploader;
        $this->imageFactory = $imageFactory;
        $this->storeManager = $storeManager;
        $this->messageManager = $messageManager;
        $this->logger = $logger;
    }

    /**
     * Get media directory
     *
     * @return WriteInterface
     * @throws FileSystemException
     */
    private function getMediaDirectory()
    {
        if (!$this->mediaDirectory) {
            $this->mediaDirectory = $this->filesystem->getDirectoryWrite(DirectoryList::MEDIA);
        }
        return $this->mediaDirectory;
    }

    /**
     * Get image relative path
     *
     * @param string $imageName
     * @return string
     */
    public function getImageRelativePath($imageName)
    {
        return self::MANAGER_PROFILE_MEDIA_PATH . DIRECTORY_SEPARATOR . $imageName;
    }

    /**
     * Get manager image absolute path
     *
     * @param int $managerId
     * @param string $imageName
     * @return string
     * @throws FileSystemException
     */
    public function getManagerImageAbsolutePath($managerId, $imageName): string
    {
        return $this->getMediaDirectory()->getAbsolutePath(
            self::MANAGER_PROFILE_MEDIA_PATH . DIRECTORY_SEPARATOR . $managerId . DIRECTORY_SEPARATOR . $imageName
        );
    }

    /**
     * Get file media path
     *
     * @param array $params
     * @return string
     * @throws FileSystemException
     */
    private function getFileMediaPath($params)
    {
        return $this->getMediaDirectory()->getAbsolutePath(implode(DIRECTORY_SEPARATOR, $params));
    }

    /**
     * Get image size
     *
     * @param array $params
     * @return array
     * @throws FileSystemException
     */
    public function getImageSize($params): array
    {
        $filePath = $this->getFileMediaPath($params);
        if (file_exists($filePath)) {
            return getimagesize($filePath);
        }

        return [0, 0];
    }

    /**
     * Get media URL
     *
     * @return string
     * @throws NoSuchEntityException
     */
    public function getMediaUrl()
    {
        return $this->storeManager->getStore()->getBaseUrl(\Magento\Framework\UrlInterface::URL_TYPE_MEDIA);
    }

    /**
     * Get image URL
     *
     * @param array $params
     * @return string
     * @throws NoSuchEntityException
     */
    public function getImageUrl($params = [])
    {
        return $this->getMediaUrl() . implode(DIRECTORY_SEPARATOR, $params);
    }

    /**
     * Move file from temporary directory
     *
     * @param string $imageName
     * @param int $managerId
     * @param bool $managerIsNew
     * @throws FileSystemException
     * @throws LocalizedException
     */
    public function processImage($imageName, $managerId, $managerIsNew)
    {
        $this->setBasePaths($managerId, $managerIsNew);

        try {
            // Move file from temporary directory to target directory
            $this->imageUploader->moveFileFromTmp($imageName, true);

            // Only process image after successfully moving file
            if ($managerId > 0) {
                $filename = $this->getManagerImageAbsolutePath($managerId, $imageName);
                
                // Check if file exists
                if (file_exists($filename)) {
                    $this->prepareImage($filename, true);
                } else {
                    $this->logger->warning("Image file not found after move: {$filename}");
                }
            }
        } catch (\Exception $e) {
            $this->logger->critical($e);
            throw new LocalizedException(__('Error processing image: %1', $e->getMessage()));
        }
    }

    /**
     * Prepare image
     *
     * @param string $filename
     * @param bool $needResize
     */
    public function prepareImage($filename, $needResize = false): void
    {
        /** @var \Magento\Framework\Image $imageProcessor */
        $imageProcessor = $this->imageFactory->create(['fileName' => $filename]);
        $imageProcessor->keepAspectRatio(true);
        $imageProcessor->keepFrame(true);
        $imageProcessor->keepTransparency(true);
        if ($needResize) {
            $imageProcessor->resize(150, 150);
        }
        $imageProcessor->save();
    }

    /**
     * Delete image
     *
     * @param string $imageName
     * @throws FileSystemException
     */
    public function deleteImage($imageName): void
    {
        if ($imageName && str_contains($imageName, '.')) {
            // Try to delete image from different possible paths
            $possiblePaths = [
                $this->getImageRelativePath($imageName),
                self::MANAGER_PROFILE_MEDIA_PATH . DIRECTORY_SEPARATOR . $imageName
            ];

            foreach ($possiblePaths as $path) {
                if ($this->getMediaDirectory()->isExist($path)) {
                    $this->getMediaDirectory()->delete($path);
                    break;
                }
            }
        }
    }

    /**
     * Set base paths
     *
     * @param int $managerId
     * @param bool $managerIsNew
     * @throws FileSystemException
     */
    public function setBasePaths($managerId, $managerIsNew): void
    {
        // Temporary path doesn't use managerId, use unified tmp directory
        $tmpPath = self::MANAGER_PROFILE_MEDIA_TMP_PATH;
        $this->imageUploader->setBaseTmpPath($tmpPath);

        // Set target path - always use managerId
        $targetPath = self::MANAGER_PROFILE_MEDIA_PATH . DIRECTORY_SEPARATOR . $managerId;
        $this->imageUploader->setBasePath($targetPath);

        // Ensure directories exist
        $this->ensureDirectoriesExist($tmpPath, $targetPath);
    }

    /**
     * Ensure directories exist
     *
     * @param string $tmpPath
     * @param string $targetPath
     * @throws FileSystemException
     */
    private function ensureDirectoriesExist($tmpPath, $targetPath): void
    {
        $mediaDirectory = $this->getMediaDirectory();

        // Ensure temporary directory exists
        if (!$mediaDirectory->isExist($tmpPath)) {
            $mediaDirectory->create($tmpPath);
        }

        // Ensure target directory exists
        if (!$mediaDirectory->isExist($targetPath)) {
            $mediaDirectory->create($targetPath);
        }
    }

    /**
     * Reset state
     */
    public function _resetState(): void
    {
        $this->mediaDirectory = null;
    }
}
