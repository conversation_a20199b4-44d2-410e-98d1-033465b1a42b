<?php

namespace Silksoftwarecorp\CompanyB2b\Api\Data;
interface ErpCompanyShipToInterface  extends \Magento\Framework\Api\ExtensibleDataInterface
{

    const ENTITY_ID = 'entity_id';
    const ERP_CUST_NUM = 'erp_cust_num';
    const SHIP_TO_NUM = 'ship_to_num';
    const SHIP_TO_NAME = 'ship_to_name';
    const EMAIL = 'email';
    const REGION_CODE = 'region_code';
    const STREET = 'street';
    const STREET_LINE_2 = 'street_line2';
    const STREET_LINE_3 = 'street_line3';

    const COMPANY_ID = 'company_id';
    const TELEPHONE = 'telephone';
    const POSTCODE = 'postcode';
    const CITY = 'city';
    const REGION_ID = 'region_id';
    const COUNTRY_ID = 'country_id';
    const LOCATION_ID = 'location_id';
    const IS_DEFAULT = 'is_default';


    /**
     * Retrieve entity id
     *
     * @return mixed
     */
    public function getEntityId();

    /**
     * Set entity id
     *
     * @param int $entityId
     * @return $this
     */
    public function setEntityId(int $entityId);
    /**
     * @param string $erpCustomerNum
     * @return $this
     */
    public function setErpCustNum(string $erpCustomerNum): static;

    /**
     *
     * @return string|null
     */
    public function getErpCustNum():string|null;

    /**
     * @param string $shipToNum
     * @return $this
     */
    public function setShipToNum(string $shipToNum):static;

    /**
     * @return string|null
     */
    public function getShipToNum(): ?string;

    /**
     * @param string $shipToName
     * @return $this
     */
    public function setShipToName(string $shipToName):static;

    /**
     * @return string|null
     */
    public function getShipToName(): ?string;

    /**
     * @param string $email
     * @return $this
     */
    public function setEmail(string $email): static;

    /**
     * @return string|null
     */
    public function getEmail(): ?string;

    /**
     * @param string $regionCode
     * @return $this
     */
    public function setRegionCode(string $regionCode): static;

    /**
     * @return string|null
     */
    public function getRegionCode(): ?string;

    /**
     * @param string $street
     * @return $this
     */
    public function setStreet(string $street): static;

    /**
     * @return string|null
     */
    public function getStreet(): ?string;

    /**
     * @param string $street
     * @return $this
     */
    public function setStreetLine2(string $street): static;

    /**
     * @return string|null
     */
    public function getStreetLine2(): ?string;

    /**
     * @param string $street
     * @return $this
     */
    public function setStreetLine3(string $street): static;

    /**
     * @return string|null
     */
    public function getStreetLine3(): ?string;

    /**
     * @param int $company_id
     * @return $this
     */
    public function setCompanyId(int $company_id): static;

    /**
     * @return int|null
     */
    public function getCompanyId(): ?int;

    /**
     * @param string $telephone
     * @return $this
     */
    public function setTelephone(string $telephone): static;

    /**
     * @return string|null
     */
    public function getTelephone(): ?string;

    /**
     * @param string $postcode
     * @return $this
     */
    public function setPostcode(string $postcode): static;

    /**
     * @return string|null
     */
    public function getPostcode(): ?string;

    /**
     * @param string $city
     * @return $this
     */
    public function setCity(string $city): static;

    /**
     * @return string|null
     */
    public function getCity(): ?string;

    /**
     * @param ?int $region_id
     * @return $this
     */
    public function setRegionId(?int $region_id): static;

    /**
     * @return int|null
     */
    public function getRegionId(): ?int;

    /**
     * @param string $countryId
     * @return $this
     */
    public function setCountryId(string $countryId): static;

    /**
     * @return string|null
     */
    public function getCountryId(): ?string;

    /**
     * @param string $locationId
     * @return $this
     */
    public function setLocationId(string $locationId): static;

    /**
     * @return string|null
     */
    public function getLocationId(): ?string;

    /**
     * @param bool $isDefault
     * @return $this
     */
    public function setIsDefault(bool $isDefault): static;

    /**
     * @return bool
     */
    public function getIsDefault(): bool;

    /**
     * Retrieve existing extension attributes object or create a new one.
     *
     * @return \Silksoftwarecorp\CompanyB2b\Api\Data\ErpCompanyShipToExtensionInterface|null
     */
    public function getExtensionAttributes();

    /**
     * @inheritdoc
     * @return  ErpCompanyShipToInterface
     */
    public function setExtensionAttributes(\Silksoftwarecorp\CompanyB2b\Api\Data\ErpCompanyShipToExtensionInterface $extensionAttributes);

}
