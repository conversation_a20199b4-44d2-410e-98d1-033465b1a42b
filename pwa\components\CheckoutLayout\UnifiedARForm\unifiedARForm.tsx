import { useCallback, useRef, useState, useEffect, useMemo, memo } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useMutation } from '@apollo/client'
import { isEmpty } from 'lodash-es'
import { notification } from 'antd'

import { Modal } from '@/ui'
import BasePrice from '@/components/BasePrice/basePrice'
import CommonButton from '@/components/Common/CommonButton/commonButton'
import { CREATE_TRANSACTION_SETUP } from '@/apis/mutations/createTransactionSetup'
import { PAYMENT_ACCOUNT_QUERY } from '@/apis/mutations/paymentAccountQuery'
import { SURCHARGE_QUERY } from '@/apis/mutations/surchargeQuery'
import { actions as appActions } from '@/store/app'
import { actions as checkoutActions } from '@/store/checkout'

import { StyledUnifiedARForm, StyledSurchargeModal, StyledCreditCardInformation } from './styed'

const UnifiedARForm = ({ address, handleUpdateUnifiedARData }: any) => {
  const dispatch = useDispatch()
  const surchargeModalRef = useRef<any>(null)
  const [createTransactionSetup] = useMutation(CREATE_TRANSACTION_SETUP)
  const [paymentAccountQuery] = useMutation(PAYMENT_ACCOUNT_QUERY)
  const [surchargeQuery] = useMutation(SURCHARGE_QUERY)

  const cartDetail = useSelector((state: Store) => state.cart.cartDetail)
  const storeConfig = useSelector((state: Store) => state.app.storeConfig)

  const [transactionData, setTransactionData] = useState<any>({})
  const [unifiedARReturnData, setUnifiedARReturnData] = useState<any>('')
  const [surchargeAmount, setSurchargeAmount] = useState<any>(0)
  const [formVisible, setFormVisible] = useState<boolean>(false)

  const unifiedCreditCardLastFourDigits = useMemo(() => {
    if (unifiedARReturnData) {
      return new URLSearchParams(unifiedARReturnData)?.get('LastFour') ?? ''
    }
    return ''
  }, [unifiedARReturnData])

  const handleContinue = useCallback(
    (returnData, amount) => {
      surchargeModalRef?.current?.close()
      setFormVisible(false)
      handleUpdateUnifiedARData(returnData, amount)
    },
    [handleUpdateUnifiedARData]
  )

  const handleModalContinue = useCallback(() => {
    handleContinue(unifiedARReturnData, surchargeAmount)
  }, [handleContinue, unifiedARReturnData, surchargeAmount])

  const handlePaymentAccountQuery = useCallback(
    async (returnData) => {
      try {
        dispatch(appActions.setLoading(true))
        if (transactionData?.transaction_setup_url) {
          const { data } = await paymentAccountQuery({
            variables: {
              transaction_setup_id: transactionData.transaction_setup_url
            }
          })
          if (data?.paymentAccountQuery?.items?.[0]?.payment_account_id) {
            const res = await surchargeQuery({
              variables: {
                payment_account_id: data?.paymentAccountQuery?.items?.[0]?.payment_account_id,
                billing_state: address?.billing_state,
                amount: cartDetail?.prices?.grand_total?.value,
                country: address?.country
              }
            })
            if (res?.data?.surchargeQuery?.success) {
              const amount = res?.data?.surchargeQuery?.surcharge_amount ?? -1
              if (amount > 0) {
                // Show modal: Credit Card Surcharge: $##.##
                setSurchargeAmount(amount)
                surchargeModalRef?.current?.open()
              } else if (amount === 0) {
                handleContinue(returnData, amount)
              } else {
                notification.info({
                  message: 'Surcharge percent not found.'
                })
              }
            }
          } else {
            notification.info({
              message: 'paymentAccountQuery not found.'
            })
          }
        } else {
          notification.info({
            message: 'transaction_setup_url not found.'
          })
        }
      } catch (e) {
        console.error(e)
      } finally {
        dispatch(appActions.setLoading(false))
      }
    },
    [
      cartDetail,
      paymentAccountQuery,
      surchargeQuery,
      transactionData,
      address,
      dispatch,
      handleContinue
    ]
  )

  const handleCreateTransactionSetup = useCallback(
    async (addressQuery: any) => {
      try {
        dispatch(checkoutActions.setPageLoading(true))
        setFormVisible(false)
        const { data } = await createTransactionSetup({
          variables: {
            address: {
              billing_address1: addressQuery.billing_address1,
              billing_address2: addressQuery.billing_address2,
              billing_city: addressQuery.billing_city,
              billing_state: addressQuery.billing_state,
              billing_zipcode: addressQuery.billing_zipcode
            }
          }
        })
        setTransactionData(data?.createTransactionSetup ?? {})
        setFormVisible(true)
      } catch (e) {
        console.error(e)
        dispatch(checkoutActions.setPageLoading(false))
      }
    },
    [createTransactionSetup, dispatch]
  )

  const resetIframe = useCallback(() => {
    setTransactionData({})
    setFormVisible(false)
    setUnifiedARReturnData('')
    setTimeout(() => {
      handleCreateTransactionSetup(address)
    }, 180)
  }, [address, handleCreateTransactionSetup])

  const handleReturn = useCallback(() => {
    surchargeModalRef?.current?.close()
    resetIframe()
  }, [resetIframe])

  const updateUnifiedArReturnData = useCallback(
    (value) => {
      setUnifiedARReturnData(value)
      handlePaymentAccountQuery(value)
    },
    [handlePaymentAccountQuery]
  )

  // Init data
  useEffect(() => {
    if (!isEmpty(address)) {
      handleCreateTransactionSetup(address)
    }
  }, [address, handleCreateTransactionSetup])

  useEffect(() => {
    const handleUnifiedARReturn = (event: MessageEvent) => {
      const baseUrl = (storeConfig?.base_url ?? '').replace(/\/+$/, '')
      if (event?.origin !== baseUrl) {
        return
      }

      if (event?.data?.type === 'PAGE_RETURN' && event?.data?.query) {
        updateUnifiedArReturnData(event?.data?.query)
      }
    }

    window.addEventListener('message', handleUnifiedARReturn)
    return () => window.removeEventListener('message', handleUnifiedARReturn)
  }, [storeConfig, updateUnifiedArReturnData])

  return (
    <div>
      {formVisible && (
        <StyledUnifiedARForm>
          {transactionData?.iframe_url && (
            <iframe
              title="Transaction"
              allowFullScreen
              src={transactionData.iframe_url}
              width="100%"
              height="500"
              scrolling="auto"
              onLoad={() => {
                dispatch(checkoutActions.setPageLoading(false))
              }}
              allow="geolocation"
            />
          )}
        </StyledUnifiedARForm>
      )}
      {unifiedCreditCardLastFourDigits && !formVisible && (
        <StyledCreditCardInformation>
          <div className="check-icon">
            <svg width="10px" height="8px" fill="currentColor" aria-hidden="true" focusable="false">
              <use xlinkHref="#icon-check" />
            </svg>
          </div>
          <p>
            Credit Card Payment Ending in {unifiedCreditCardLastFourDigits} has been Authorized!
          </p>
        </StyledCreditCardInformation>
      )}
      <Modal
        ref={surchargeModalRef}
        width={778}
        maskClosable={false}
        // onCancel={resetIframe}
        closable={false}>
        <StyledSurchargeModal>
          <h2>
            Credit Card Surcharge:
            <BasePrice value={surchargeAmount} />
          </h2>
          <div>
            Credit cards incur a surcharge. Please Continue with the sale or Return to Checkout and
            provide a debit card or another form of payment.
          </div>
          <div className="surcharge-modal-action">
            <CommonButton height={49} onClick={handleModalContinue}>
              Continue
            </CommonButton>
            <CommonButton height={49} type="default" onClick={handleReturn}>
              Return
            </CommonButton>
          </div>
        </StyledSurchargeModal>
      </Modal>
    </div>
  )
}

export default memo(UnifiedARForm)
