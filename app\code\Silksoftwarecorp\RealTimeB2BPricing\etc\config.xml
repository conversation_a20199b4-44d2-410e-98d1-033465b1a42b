<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Store:etc/config.xsd">
    <default>
        <realtime_b2b_pricing>
            <general>
                <active>1</active>
                <debug>1</debug>
            </general>
            <api>
                <environment>sandbox</environment>
                <enable_ssl_certificate_verify>0</enable_ssl_certificate_verify>
                <timeout>10</timeout>
                <cache_lifetime>600</cache_lifetime>
                <password backend_model="Magento\Config\Model\Config\Backend\Encrypted"/>
                <sandbox_password backend_model="Magento\Config\Model\Config\Backend\Encrypted"/>
            </api>
            <cart>
                <price_validity_time>600</price_validity_time>
            </cart>
        </realtime_b2b_pricing>
    </default>
</config>
