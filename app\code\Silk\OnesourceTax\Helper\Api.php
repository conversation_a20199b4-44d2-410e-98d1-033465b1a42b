<?php
namespace Silk\OnesourceTax\Helper;

use Magento\Framework\App\Helper\AbstractHelper;
use Silk\OnesourceTax\Model\Api\Auth;
use Silk\OnesourceTax\Model\Config;

class Api extends AbstractHelper
{
    protected $auth;
    protected $config;

    public function __construct(
        Auth $auth,
        Config $config
    ) {
        $this->auth = $auth;
        $this->config = $config;
    }

    public function testConnection()
    {
        try {
            $token = $this->auth->getAccessToken();
            return !empty($token);
        } catch (\Exception $e) {
            return false;
        }
    }
}