<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */

declare(strict_types=1);

namespace Silksoftwarecorp\InventoryGraphQl\Model\Formatter;

use Silksoftwarecorp\Inventory\Api\Data\BranchManagerProfileInterface;
use Silksoftwarecorp\Inventory\Model\ManagerProfileImageUrlService;

/**
 * Formatter for Manager Profile data
 */
class ManagerProfile
{
    /**
     * @var ManagerProfileImageUrlService
     */
    private $imageUrlService;

    /**
     * @param ManagerProfileImageUrlService $imageUrlService
     */
    public function __construct(
        ManagerProfileImageUrlService $imageUrlService
    ) {
        $this->imageUrlService = $imageUrlService;
    }

    /**
     * Format single manager profile
     *
     * @param BranchManagerProfileInterface $manager
     * @return array
     */
    public function format(BranchManagerProfileInterface $manager): array
    {
        return [
            'name' => $manager->getName(),
            'image' => $this->formatImage($manager),
            'phone' => $manager->getPhone(),
            'region' => $manager->getRegion(),
        ];
    }

    /**
     * Format image URL for GraphQL
     *
     * @param BranchManagerProfileInterface $manager
     * @return string
     */
    private function formatImage(BranchManagerProfileInterface $manager): string
    {
        if (empty($manager->getImage())) {
            return '';
        }

        try {
            return $this->imageUrlService->getGraphQlImageUrl(
                (int)$manager->getId(),
                $manager->getImage()
            );
        } catch (\Exception $e) {
            return '';
        }
    }
}
