import { Button, Form } from 'antd'
import { FormattedMessage } from 'react-intl'

import { useProductForm } from '@/hooks/CatalogLayout'
import QuantityStep from '@/components/QuantityStep'
import ContactYourBranchButton from '@/components/Common/ContactYourBranchButton'

import ProductTools from '../ProductTools'
import { StyledProductForm } from './styled'

const ProductForm = ({ isActionVisible, isAllowToBackorder }) => {
  const { form, cartLoading, handleOnFinish, actionDisabled, sku, measureValue } =
    useProductForm(isAllowToBackorder)

  const handleContact = () => {
    // TODO
  }

  return (
    <StyledProductForm>
      {isActionVisible ? (
        <Form form={form} initialValues={{ quantity: 1 }} onFinish={handleOnFinish}>
          <div className="grid">
            <QuantityStep
              name="quantity"
              required
              disabled={actionDisabled}
              buttonProps={{ disabled: actionDisabled }}
            />
            <div className="actions">
              <Button
                type="primary"
                htmlType="submit"
                loading={cartLoading}
                disabled={actionDisabled}>
                <FormattedMessage id="global.addToCart" />
              </Button>
            </div>
          </div>
        </Form>
      ) : (
        <ContactYourBranchButton />
      )}
      <div className="list-action">
        <ProductTools sku={sku} form={form} measureValue={measureValue} />
      </div>
    </StyledProductForm>
  )
}

export default ProductForm
