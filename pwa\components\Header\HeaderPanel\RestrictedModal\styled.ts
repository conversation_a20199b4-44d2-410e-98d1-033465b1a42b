import styled from '@emotion/styled'

export const StyledRestrictedModal = styled.div`
  margin: 0 auto;
  padding: 35px 12px;
  text-align: center;

  h2 {
    margin-bottom: 10px;
    font-weight: 700;
    font-size: 32px;
    line-height: 38px;
    letter-spacing: 0;
    text-align: center;
  }

  p {
    margin: 0 auto;
    max-width: 577px;
    font-weight: 400;
    font-size: 15px;
    line-height: 21px;
    letter-spacing: 0.01em;
    text-align: center;
  }

  button {
    margin-top: 25px;
    width: 191px;
    height: 44px;
    border-radius: 3px;

    span {
      font-weight: 700;
      font-size: 14px;
      line-height: 20px;
      letter-spacing: 0.01em;
      border-radius: 3px;
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    padding: 20px 0;
    max-width: 100%;

    h2 {
      font-size: 24px;
      line-height: 30px;
    }

    button {
      width: 100%;
    }
  }
`
