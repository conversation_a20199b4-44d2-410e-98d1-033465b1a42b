<?php

declare(strict_types=1);

namespace Silksoftwarecorp\ShipToGraphQl\Model\ResourceModel;


class CompanyShipTo extends \Silksoftwarecorp\CompanyB2b\Model\ResourceModel\ErpCompanyShipTo
{
    public function getListByCompanyId($companyId)
    {
        $select = $this->getConnection()->select()
            ->from($this->getMainTable() . ' AS main_table')
            ->where('main_table.company_id=?', $companyId);

        return $this->getConnection()->fetchAll($select);
    }

    /**
     * Set all ship-to records as non-default for a specific erp_cust_num except the specified entity_id
     *
     * @param string $erpCustNum
     * @param int $excludeEntityId
     * @return void
     */
    public function setAllNonDefaultByErpCustNum($erpCustNum, $excludeEntityId)
    {
        $connection = $this->getConnection();
        $connection->update(
            $this->getMainTable(),
            ['is_default' => 0],
            [
                'erp_cust_num = ?' => $erpCustNum,
                'entity_id != ?' => $excludeEntityId
            ]
        );
    }
}
