import { useMemo } from 'react'
import { AutoComplete, Form, Input, Spin } from 'antd'
import { clsx } from 'clsx'

import { useMiniSearch } from '@/hooks/MiniSearch'
import { StyledSearch, StyledLoading } from './styled'

const MiniSearch = () => {
  const [form] = Form.useForm()
  const {
    loading,
    products,
    searchRef,
    handleOnSearch,
    handleOnClick,
    handleOnKeyDown,
    handleOnChange,
    searchVisible,
    onDropdownVisibleChange,
    notFoundContentVisible,
    removeNotFoundContent,
    searchInputRef
  } = useMiniSearch(form)

  const autoOptions = useMemo(() => {
    return products.map((item) => {
      const { name, url_key } = item
      return {
        label: <span>{name}</span>,
        value: url_key
      }
    })
  }, [products])

  const autoComplete = useMemo(() => {
    return [
      {
        label: (
          <StyledLoading>
            <Spin />
          </StyledLoading>
        ),
        value: 'loading'
      }
    ]
  }, [])

  return (
    <StyledSearch ref={searchRef} className={clsx({ 'search-visible': searchVisible })}>
      <Form form={form}>
        <Form.Item name="search">
          <AutoComplete
            options={loading ? autoComplete : autoOptions}
            listHeight={335}
            getPopupContainer={() => searchRef.current}
            onSelect={handleOnSearch}
            notFoundContent={notFoundContentVisible ? <div>No Products Found.</div> : null}
            onDropdownVisibleChange={onDropdownVisibleChange}>
            <Input.Search
              size="large"
              placeholder="Search"
              ref={searchInputRef}
              suffix={
                <span aria-hidden onClick={handleOnClick}>
                  <svg
                    className="search"
                    width="1em"
                    height="1em"
                    fill="currentColor"
                    aria-hidden="true"
                    focusable="false">
                    <use xlinkHref="#icon-search" />
                  </svg>
                </span>
              }
              onBlur={removeNotFoundContent}
              onChange={handleOnChange}
              onKeyDown={handleOnKeyDown}
            />
          </AutoComplete>
        </Form.Item>
      </Form>
    </StyledSearch>
  )
}

export default MiniSearch
