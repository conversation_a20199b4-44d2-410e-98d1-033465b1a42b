<?php

namespace Silksoftwarecorp\UnifiedAR\Model\FinancialInformation;

class Mapper
{
    public function execute(array $financialInfo): array
    {
        return [
            'merchantCustomerNbr' => (string)($financialInfo['merchantCustomerNbr'] ?? null),
            'customerId' => (string)($financialInfo['customerId'] ?? null),
            'customerName' => (string)($financialInfo['customerName'] ?? null),
            'paymentTermsCode' => (string)($financialInfo['paymentTermsCode'] ?? null),
            'paymentTermsDescription' => $financialInfo['paymentTermsDescription'] ?? null,
            'creditLimit' => (float)($financialInfo['creditLimit'] ?? 0),
            'availableCredit' => (float)($financialInfo['availableCredit'] ?? 0),
            'balance' => (float)($financialInfo['balance'] ?? 0),
            'pastDueBalance' => (float)($financialInfo['pastDueBalance'] ?? 0),
            'pastDueInvoiceCount' => (float)($financialInfo['pastDueInvoiceCount'] ?? 0),
            'totalOpenInvoiceCount' => (float)($financialInfo['totalOpenInvoiceCount'] ?? 0),
            'lastPaymentAmount' => (float)($financialInfo['lastPaymentAmount'] ?? 0),
            'lastPaymentDate' => $financialInfo['lastPaymentDate'] ?? null,
            'lastPaymentDetail' => $financialInfo['lastPaymentDetail'] ?? null,
            'isOnCreditHold' => (bool)($financialInfo['isOnCreditHold'] ?? false),
            'salespersonCode' => $financialInfo['salespersonCode'] ?? null,
            'salespersonName' => $financialInfo['salespersonName'] ?? null,
            'isTaxExempt' => (bool)($financialInfo['isTaxExempt'] ?? false),
            'sicCode' => $financialInfo['sicCode'] ?? null,
            'isSurchargeExempt' => $financialInfo['isSurchargeExempt'] ?? null,
            'creditLimitPerOrder' => (float)($financialInfo['creditLimitPerOrder'] ?? 0),
            'creditLimitUsed' => (float)($financialInfo['creditLimitUsed'] ?? 0),
            'creditStatusCode' => $financialInfo['creditStatusCode'] ?? null,
            'creditStatusDescription' => $financialInfo['creditStatusDescription'] ?? null,
            'averageInvoiceAgeInDays' => (int)($financialInfo['averageInvoiceAgeInDays'] ?? 0),
        ];
    }
}
