import { useEffect, useState } from 'react'

import { useAwaitQuery } from '@ranger-theme/apollo'
import HowToMeasure from '@/components/HowToMeasure'
import { GET_CATEGORY_HOW_TO_MEASURE } from '@/apis/queries/getCategoryHowToMeasure'

import { StyledCategoryHowToMeasure } from './styled'

const CategoryHowToMeasure = ({ id }) => {
  const [measureValue, setMeasureValue] = useState<string>('')
  const categoryQuery = useAwaitQuery(GET_CATEGORY_HOW_TO_MEASURE)

  useEffect(() => {
    const getCategoryHowToMeasure = async () => {
      try {
        const { data } = await categoryQuery({
          // TODO: remove no-cache
          fetchPolicy: 'no-cache',
          variables: {
            id
          }
        })
        setMeasureValue(data?.category?.how_to_measure || '')
      } catch (error) {
        console.info(error)
      }
    }

    if (id) {
      getCategoryHowToMeasure()
    }
  }, [id, categoryQuery])

  return measureValue ? (
    <StyledCategoryHowToMeasure>
      <HowToMeasure defaultMeasureModalTabKey={measureValue} />
    </StyledCategoryHowToMeasure>
  ) : null
}

export default CategoryHowToMeasure
