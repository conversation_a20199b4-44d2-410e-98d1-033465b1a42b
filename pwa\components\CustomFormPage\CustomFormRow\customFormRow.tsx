import { Form, Col, DatePicker, Checkbox, Radio } from 'antd'
import { clsx } from 'clsx'
import { memo, useState, useMemo } from 'react'

import CommonInput from '@/components/Common/CommonInput'
import CommonSelect from '@/components/Common/CommonSelect'
import CommonTextarea from '@/components/Common/CommonTextarea'
import CommonUpload from '@/components/Common/CommonUpload'
import {
  normalizeAlphaOnly,
  normalizeNumber,
  numberValidator,
  specialNotesValidator,
  normalizePhoneLabel,
  phoneValidator,
  normalizeIntegerNumber
} from '@/utils/format'
import { stateOptions } from '@/utils/constant'

import {
  StyledCustomFormCheckoutGrid,
  StyledCustomFormRadioGrid,
  StyledCustomFormRow
} from './styled'
import MixedItemPropertyLocator from './MixedItemPropertyLocator'
import MixedItemLinesetLength from './MixedItemLinesetLength'
import MixedItemInputQty from './MixedItemInputQty'

const CustomFormRow = ({ formList, handleSetFieldsValue, skirtingColorOptions = [] }: any) => {
  const [isDisplayToEnd, setIsDisplayToEnd] = useState(false)

  const items = useMemo(() => {
    return formList.map((item: any) => {
      return {
        ...item,
        isDisplayed: item?.name?.indexOf('display-by-controller') === -1 || isDisplayToEnd
      }
    })
  }, [formList, isDisplayToEnd])

  return (
    <StyledCustomFormRow gutter={15}>
      {items.map((item, index) => {
        const itemName = item?.name ?? ''
        const type = item?.type ?? ''
        const itemValues = item?.values || null
        const itemLabel = item?.label || ''
        const key = `${type}_${index}`
        const isRequired = item?.required === '1' || itemName?.includes('form-required')
        const layout = item?.layout ?? '' // one / two / three
        const isDisplayed = item?.isDisplayed

        const isAlphaOnly = itemName?.toLowerCase()?.startsWith('alpha-only')
        const isUsStates = itemName?.toLowerCase()?.startsWith('us-states')
        const isPhoneNumber = itemName?.toLowerCase()?.startsWith('phone-number')
        const isIntegerNumber = itemName?.toLowerCase()?.startsWith('integer-number')
        const isEmail = itemName?.toLowerCase()?.startsWith('email')
        const isDisplayController = itemName?.toLowerCase()?.indexOf('display-controller') !== -1
        const isRadio = ['radio', 'radiotwo'].includes(type) && itemValues
        const isEmptyGridPlaceholder = itemLabel === 'Empty Grid Placeholder'
        const isMixedItem = itemName?.includes('-mixed-')

        let rowSpan = 24
        if (layout === 'two') {
          rowSpan = 12
        }
        if (layout === 'three') {
          rowSpan = 8
        }

        if (type === 'text') {
          const isSmallText = itemName?.indexOf('small-text') !== -1
          return isDisplayed ? (
            <Col key={key} span={24} md={{ span: rowSpan }}>
              <h5 className={clsx({ 'is-small': isSmallText })}>{itemLabel}</h5>
            </Col>
          ) : null
        }

        // Empty Grid Placeholder
        if (isEmptyGridPlaceholder) {
          return <Col key={key} span={24} md={{ span: rowSpan }} />
        }

        let rules: any = [{ required: isRequired }, specialNotesValidator]
        const props: any = {}
        if (type === 'number') {
          if (isPhoneNumber) {
            // (xxx)xxx-xxxx
            props.normalize = normalizePhoneLabel
            rules.push(phoneValidator)
          } else if (isIntegerNumber) {
            props.normalize = normalizeIntegerNumber
            rules.push(numberValidator)
          } else {
            props.normalize = normalizeNumber
            rules.push(numberValidator)
          }
        }
        if (isEmail) {
          rules.push({
            type: 'email',
            message: 'Please enter a valid email address'
          })
        }

        // Dependent required
        if (itemName?.includes('required-from-')) {
          const dependentKey = itemName?.split('required-from-')?.[1] ?? ''
          const dependentItem = formList.find((list) => {
            return list?.name?.includes(`required-with-${dependentKey}`)
          })
          if (dependentItem) {
            rules.push(({ getFieldValue }) => ({
              validator(_, value) {
                const dependentValue = getFieldValue(dependentItem?.name)
                if (dependentValue) {
                  return value
                    ? Promise.resolve()
                    : Promise.reject(new Error('The field is required.'))
                }
                return Promise.resolve()
              }
            }))
          }
        }

        // Confirm validate
        if (itemName?.includes('confirm-from-')) {
          const confirmKey = itemName?.split('confirm-from-')?.[1] ?? ''
          const confirmItem = formList.find((list) => {
            return list?.name?.includes(`confirm-with-${confirmKey}`)
          })
          if (confirmItem) {
            rules = [
              { required: isRequired },
              specialNotesValidator,
              ({ getFieldValue }) => ({
                validator(_, value) {
                  const dependentValue = getFieldValue(confirmItem?.name)
                  if (value) {
                    return value === dependentValue
                      ? Promise.resolve()
                      : Promise.reject(new Error(`Please enter same ${confirmKey}.`))
                  }
                  if (!isRequired && dependentValue && !value) {
                    return Promise.reject(new Error(`Please enter same ${confirmKey}.`))
                  }
                  return Promise.resolve()
                }
              })
            ]
          }
        }

        if (isAlphaOnly) {
          props.normalize = normalizeAlphaOnly
        }

        if (type === 'file') {
          props.valuePropName = itemName
          props.getValueFromEvent = (e) => {
            if (Array.isArray(e)) {
              return e
            }
            return e?.fileList
          }
        }

        const isVisible =
          [
            'textinput',
            'number',
            'date',
            'dropdown',
            'textarea',
            'checkbox',
            'checkboxtwo',
            'radio',
            'radiotwo',
            'file'
          ].includes(type) &&
          itemLabel.indexOf('non-displayed') === -1 &&
          isDisplayed

        const handleSaveVal = (val) => {
          handleSetFieldsValue({
            [itemName]: val
          })
        }
        const itemLabelText = itemName?.includes('[title-invisible]') ? '' : itemLabel

        return isVisible ? (
          <Col key={key} span={24} md={{ span: rowSpan }}>
            <Form.Item
              name={itemName}
              label={itemLabelText}
              rules={rules}
              className={clsx(`item-layout__${layout} ${item?.className ?? ''}`)}
              {...props}>
              {['textinput', 'number'].includes(type) && <CommonInput />}
              {type === 'date' && (
                <DatePicker
                  className="item-date-picker"
                  placeholder=""
                  format="MM/DD/YYYY"
                  showNow={false}
                  suffixIcon={null}
                />
              )}
              {type === 'textarea' && <CommonTextarea rows={3} />}
              {type === 'dropdown' && (
                <>
                  {isUsStates ? (
                    <CommonSelect
                      allowClear
                      showSearch
                      placeholder="Select"
                      options={stateOptions}
                    />
                  ) : (
                    <>
                      {itemValues ? (
                        <CommonSelect
                          showSearch
                          allowClear
                          placeholder="Select"
                          options={
                            itemLabel === 'Skirting Color' ? skirtingColorOptions : itemValues
                          }
                        />
                      ) : (
                        <CommonInput />
                      )}
                    </>
                  )}
                </>
              )}
              {['checkbox', 'checkboxtwo'].includes(type) && itemValues && (
                <Checkbox.Group>
                  <StyledCustomFormCheckoutGrid
                    className={clsx({ 'is-column': type === 'checkbox' })}>
                    {itemValues.map((it) => {
                      return (
                        <Checkbox
                          key={it?.value}
                          value={it?.value}
                          onChange={(e) => {
                            if (isDisplayController) {
                              setIsDisplayToEnd(e?.target?.checked)
                            }
                          }}>
                          {it?.label ?? ''}
                        </Checkbox>
                      )
                    })}
                  </StyledCustomFormCheckoutGrid>
                </Checkbox.Group>
              )}
              {/* isLinesetLength */}
              {isRadio && (
                <>
                  {isMixedItem ? (
                    <>
                      {itemName?.includes('mixed-property-locator') && (
                        <MixedItemPropertyLocator handleSaveVal={handleSaveVal} />
                      )}
                      {itemName?.includes('mixed-lineset-length') && (
                        <MixedItemLinesetLength handleSaveVal={handleSaveVal} />
                      )}
                      {itemName?.includes('mixed-input-qty') && (
                        <MixedItemInputQty handleSaveVal={handleSaveVal} />
                      )}
                    </>
                  ) : (
                    <Radio.Group
                      onChange={(e) => {
                        if (isDisplayController) {
                          setIsDisplayToEnd(e?.target?.value === 'Yes')
                        }
                      }}>
                      <StyledCustomFormRadioGrid
                        className={clsx({ 'is-column': type === 'radio' })}>
                        {itemValues.map((it) => {
                          return (
                            <Radio key={it?.value} value={it?.value}>
                              {it?.label ?? ''}
                            </Radio>
                          )
                        })}
                      </StyledCustomFormRadioGrid>
                    </Radio.Group>
                  )}
                </>
              )}
              {type === 'file' && (
                <CommonUpload
                  onUploadDone={(file) => {
                    // Save file to form data
                    if (handleSetFieldsValue) {
                      handleSetFieldsValue({
                        [itemName]: file
                      })
                    }
                  }}
                />
              )}
            </Form.Item>
          </Col>
        ) : null
      })}
    </StyledCustomFormRow>
  )
}

export default memo(CustomFormRow)
