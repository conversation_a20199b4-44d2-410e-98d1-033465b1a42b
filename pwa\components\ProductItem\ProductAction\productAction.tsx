import { Button } from 'antd'
import { memo, useCallback, useState, useMemo } from 'react'

import { useAddCart } from '@ranger-theme/custom-hooks'
import QtyInput from '@/components/QtyInput'
import { actions as cartActions } from '@/store/cart'
import { POST_CREATE_CART } from '@/apis/mutations/createEmptyCart'
import { POST_ADD_PRODUCTS_CART } from '@/apis/mutations/addProductsToCart'
import ContactYourBranchButton from '@/components/Common/ContactYourBranchButton'

import { StyledProductAction } from './styled'

const ProductAction = ({ product, isActionVisible, disabled }) => {
  const { loading: cartLoading, handleAddToCart } = useAddCart({
    actions: cartActions,
    createCartDocumentNode: POST_CREATE_CART,
    addProductsDocumentNode: POST_ADD_PRODUCTS_CART
  })

  const [quantity, setQuantity] = useState(1)

  const isDisabled = useMemo(() => {
    return cartLoading || disabled
  }, [cartLoading, disabled])

  const handleQtyChange = useCallback((e) => {
    setQuantity(e?.target?.value ?? 1)
  }, [])

  const handleAddCart = useCallback(async () => {
    const cartItems: any[] = []

    switch (product?.__typename) {
      case 'SimpleProduct':
        cartItems.push({
          quantity,
          sku: product?.sku
        })
        break
      default:
    }
    try {
      await handleAddToCart(cartItems, product)
    } catch (e) {
      console.log(e)
    }
  }, [quantity, product, handleAddToCart])

  return (
    <StyledProductAction className="produc-action">
      {isActionVisible ? (
        <div className="produc-action-simple">
          <QtyInput value={quantity} onChange={handleQtyChange} disabled={isDisabled} />
          <Button
            className="product-cart-btn"
            type="primary"
            loading={cartLoading}
            disabled={isDisabled}
            onClick={handleAddCart}>
            ADD TO CART
          </Button>
        </div>
      ) : (
        <ContactYourBranchButton />
      )}
    </StyledProductAction>
  )
}

export default memo(ProductAction)
