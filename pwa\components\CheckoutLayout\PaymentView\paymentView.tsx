import { Fragment } from 'react'
import { Button, Form, Radio, Spin, Space, Input } from 'antd'

import CouponCode from '@/components/CouponCode'
import { usePaymentView } from '@/hooks/CheckoutLayout'
import { specialNotesValidator } from '@/utils/format'

import BillingAddress from '../BillingAddress'
import CreditForm from '../CreditForm'
import { StyledPaymentView, StyledPaymentItemDesc } from './styed'

const PaymentView = () => {
  const {
    form,
    method,
    loading,
    paymentMethods,
    handleOnChange,
    handleSubmit,
    isUnifiedARPayment,
    placeOrderaAvailable,
    handleUpdateUnifiedARData
  } = usePaymentView()

  return (
    <StyledPaymentView>
      <Spin spinning={loading}>
        <CouponCode />
        <Form layout="vertical" form={form} initialValues={{ sameas: true }}>
          <h2 className="title">Payment Method</h2>
          <div id="payment-form-items">
            <Form.Item
              name="paymentMethod"
              rules={[{ required: true, message: 'This is a required field.' }]}>
              <Radio.Group onChange={handleOnChange}>
                <Space direction="vertical">
                  {paymentMethods.map((paymentMethod: any) => {
                    return (
                      <Fragment key={paymentMethod.code}>
                        <Radio className="method-code-radio" value={paymentMethod.code}>
                          {paymentMethod.title}
                        </Radio>
                        {paymentMethod.code === method && (
                          <StyledPaymentItemDesc>
                            <BillingAddress
                              form={form}
                              isUnifiedAR={isUnifiedARPayment}
                              handleUpdateUnifiedARData={handleUpdateUnifiedARData}
                            />
                            {method === 'credit_card' && <CreditForm />}
                          </StyledPaymentItemDesc>
                        )}
                      </Fragment>
                    )
                  })}
                </Space>
              </Radio.Group>
            </Form.Item>
            <div className="text-form-item">
              <h3>
                PO Number <span>(optional)</span>
              </h3>
              <Form.Item name="purchaseCode">
                <Input placeholder="Enter PO Number" />
              </Form.Item>
            </div>
            <div className="text-form-item">
              <h3>
                Special Notes <span>(optional)</span>
              </h3>
              <Form.Item
                name="specialNotes"
                rules={[
                  {
                    max: 255,
                    message: '255 Maximum Characters'
                  },
                  specialNotesValidator
                ]}>
                <Input placeholder="Enter order notes" />
              </Form.Item>
            </div>
          </div>
        </Form>
        <div className="actions">
          <Button type="primary" onClick={handleSubmit} disabled={!placeOrderaAvailable}>
            <span>PLACE ORDER</span>
          </Button>
        </div>
      </Spin>
    </StyledPaymentView>
  )
}

export default PaymentView
