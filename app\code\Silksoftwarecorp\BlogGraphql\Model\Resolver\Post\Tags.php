<?php

namespace Silksoftwarecorp\BlogGraphql\Model\Resolver\Post;

use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;

class Tags implements ResolverInterface
{
    /**
     * @var \Amasty\Blog\Api\TagRepositoryInterface
     */
    private $tagRepository;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    private $logger;

    public function __construct(
        \Amasty\Blog\Api\TagRepositoryInterface $tagRepository,
        \Psr\Log\LoggerInterface $logger
    ) {
        $this->tagRepository = $tagRepository;
        $this->logger = $logger;
    }

    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        $postId = $value['post_id'] ?? null;
        if ($postId) {
            try {
                $storeId = (int)$context->getExtensionAttributes()->getStore()->getId();
                $tags = $this->tagRepository->getTagsByPost($postId, $storeId);

                $data = [];
                foreach ($tags as $tag) {
                    $data[] = $tag->getData();
                }

                return $data;
            } catch (\Exception $e) {
                $this->logger->error($e->getMessage());
            }
        }

        return [];
    }
}
