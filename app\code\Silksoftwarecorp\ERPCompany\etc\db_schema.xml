<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="company_erp" resource="default" engine="innodb" comment="company_payment">
        <column xsi:type="int" name="company_id" unsigned="true" nullable="false" identity="false"
                comment="Company ID"/>
        <column xsi:type="varchar" name="erp_customer_id" nullable="false" length="255" comment="Erp Customer Id used to associate with Magento company"/>
        <column xsi:type="varchar" name="erp_sales_rep_id" nullable="false" length="255" comment="Sales Rep ID"/>
        <column xsi:type="varchar" name="erp_sales_rep_name" nullable="false" length="255" comment="Sales Rep Name"/>
        <column xsi:type="varchar" name="erp_credit_status" nullable="false" length="255" comment="ERP Credit Status used to track company credit information"/>
        <constraint xsi:type="foreign" referenceId="COMPANY_ERP_COMPANY_ID_COMPANY_ENTITY_ID" table="company_erp"
                    column="company_id" referenceTable="company" referenceColumn="entity_id" onDelete="CASCADE"/>
        <index referenceId="COMPANY_ERP_COMPANY_ID" indexType="btree">
            <column name="company_id"/>
        </index>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="company_id"/>
        </constraint>
    </table>
</schema>
