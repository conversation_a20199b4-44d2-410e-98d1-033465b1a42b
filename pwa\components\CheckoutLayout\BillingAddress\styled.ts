import styled from '@emotion/styled'

export const StyledBillingAddress = styled.div`
  .blevins-form-item {
    margin-bottom: 10px;
  }

  .billing-address-list {
    font-weight: 400;
    font-size: 15px;
    line-height: 23px;
    letter-spacing: 0.01em;

    .address-company {
      font-weight: 700;
    }

    .address-telephone {
      color: var(--color-font) !important;
    }
  }

  .same-as-item {
    position: relative;
    padding-left: 1px;

    &.is-disabled::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
      width: 100%;
      height: 100%;
    }
  }
`
