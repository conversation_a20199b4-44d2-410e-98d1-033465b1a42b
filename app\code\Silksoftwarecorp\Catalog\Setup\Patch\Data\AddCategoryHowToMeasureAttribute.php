<?php

namespace Silksoftwarecorp\Catalog\Setup\Patch\Data;

use Magento\Eav\Setup\EavSetupFactory;
use Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Catalog\Model\Category;
use Magento\Eav\Model\Config;
use Magento\Eav\Setup\EavSetup;

class AddCategoryHowToMeasureAttribute implements DataPatchInterface
{
    /**
     * @var ModuleDataSetupInterface
     */
    private $moduleDataSetup;

    /**
     * @var EavSetupFactory
     */
    private $eavSetupFactory;

    /**
     * @var Config
     */
    private $eavConfig;

    public function __construct(
        ModuleDataSetupInterface $moduleDataSetup,
        EavSetupFactory $eavSetupFactory,
        Config $eavConfig
    ) {
        $this->moduleDataSetup = $moduleDataSetup;
        $this->eavSetupFactory = $eavSetupFactory;
        $this->eavConfig = $eavConfig;
    }

    public function apply()
    {
        $this->moduleDataSetup->getConnection()->startSetup();

        /** @var EavSetup $eavSetup */
        $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);

        $eavSetup->addAttribute(
            Category::ENTITY,
            'how_to_measure',
            [
                'type' => 'varchar',
                'label' => 'How to Measure',
                'input' => 'select',
                'required' => false,
                'sort_order' => 201,
                'global' => ScopedAttributeInterface::SCOPE_STORE,
                'group' => 'General',
                'visible' => true,
                'user_defined' => true,
                'default' => '',
                'source' => \Silksoftwarecorp\Catalog\Model\Category\Attribute\Source\HowToMeasure::class,
            ]
        );

        $this->eavConfig->clear();

        $this->moduleDataSetup->getConnection()->endSetup();
    }

    public static function getDependencies()
    {
        return [AddCategoryAttributesV1::class];
    }

    public function getAliases()
    {
        return [];
    }
}
