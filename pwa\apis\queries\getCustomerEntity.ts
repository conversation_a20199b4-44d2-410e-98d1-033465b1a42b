import { gql } from '@apollo/client'
import type { DocumentNode } from '@apollo/client'

import { cartItems } from '../fragment/cartItems'
import { cart_prices } from '../fragment/cartPrices'

export const GET_CUSTOMER_ENTITY: DocumentNode = gql`
  query getCustomerEntity {
    cart: customerCart {
      id
      quantity: total_quantity
      prices {
        ...cart_prices
        __typename
      }
      coupons: applied_coupons {
        code
      }
      ...cartItems
      __typename
    }
  }
  ${cartItems}
  ${cart_prices}
`
