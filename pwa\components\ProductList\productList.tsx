import { FormattedMessage } from 'react-intl'
import { Pagination } from 'antd'
import { memo, useMemo, useEffect, useState, useCallback } from 'react'
import type { FC } from 'react'
import { useSelector } from 'react-redux'
import dynamic from 'next/dynamic'

import { MediaLayout } from '@/ui'
import { useProductList } from '@/hooks/CategoryGrid'
import { isB2bUserSelector } from '@/store/user/slice'
import CommonLoading from '@/components/Common/CommonLoading'
import CommonPagination from '@/components/Common/CommonPagination'
import { useB2bProductData } from '@/hooks/B2bProductData'
import { getProductsStockInfo } from '@/utils'

import {
  StyledMatch,
  StyledProductList,
  StyledPagination,
  StyledPlpPanel,
  StyledProductCounter
} from './styled'

const paginationItemRender = (current: number, type: string, originalElement: React.ReactNode) => {
  if (type === 'prev') {
    return (
      <svg
        className="prev-icon"
        width="1em"
        height="1em"
        fill="currentColor"
        aria-hidden="true"
        focusable="false">
        <use xlinkHref="#icon-arrow" />
      </svg>
    )
  }
  if (type === 'next') {
    return (
      <svg
        className="next-icon"
        width="1em"
        height="1em"
        fill="currentColor"
        aria-hidden="true"
        focusable="false">
        <use xlinkHref="#icon-arrow" />
      </svg>
    )
  }

  return originalElement
}

interface ProductListProps {
  id: string
  q: string
  search: string
}

const Sorters = dynamic(() => import('@/components/CategoryGrid/Sorters'))
const MobileCategoryPanel = dynamic(() => import('@/components/CategoryGrid/MobileCategoryPanel'), {
  ssr: false
})
const ProductItem = dynamic(() => import('@/components/ProductItem'), { ssr: false })

const ProductList: FC<ProductListProps> = ({ id, q, search }) => {
  const { productList, totalCount, pageStore, toggleHorizontal, isHorizontal, hasProductList } =
    useProductList({
      id,
      q,
      search
    })
  const [pageState, pageApi] = pageStore
  const { currentPage, pageSize } = pageState
  const isLogin = useSelector((state: Store) => state.user.isLogin)
  const isB2bUser = useSelector(isB2bUserSelector)
  const { fetchProductPrices, fetchProductInventory } = useB2bProductData()

  const [b2bProductData, setB2bProductData] = useState([]) // Real time price and purchased date
  const [inventoryList, setInventoryList] = useState([])
  const [loading, setLoading] = useState(false)

  const infoVisible = useMemo(() => {
    return !isLogin || (inventoryList.length > 0 && !loading)
  }, [inventoryList, isLogin, loading])

  const productCounter = useMemo(() => {
    const start = (currentPage - 1) * pageSize + 1
    const end = currentPage * pageSize > totalCount ? totalCount : currentPage * pageSize
    return `${start} - ${end} of ${totalCount} products`
  }, [currentPage, pageSize, totalCount])

  const productsSkus = useMemo(() => {
    return productList.map((item) => {
      return item.sku
    })
  }, [productList])

  const productsData = useMemo(() => {
    const items = getProductsStockInfo(productList, inventoryList)
    return items.map((product) => {
      const dataItem = b2bProductData?.find((item) => item.sku === product.sku)
      return {
        ...product,
        realTimePrice: dataItem?.price ?? null,
        purchasedDate: dataItem?.last_invoice_date || ''
      }
    })
  }, [inventoryList, productList, b2bProductData])

  const handleOnChange = (value: number) => {
    pageApi.setCurrentPage(value)
  }

  const getB2bProductData = useCallback(
    async (skus) => {
      try {
        setLoading(true)
        const [priceRes, inventoryRes] = await Promise.all([
          fetchProductPrices(skus),
          fetchProductInventory(skus)
        ])
        setB2bProductData(priceRes)
        setInventoryList(inventoryRes)
      } catch (e) {
        console.error(e)
      } finally {
        setLoading(false)
      }
    },
    [fetchProductInventory, fetchProductPrices]
  )

  useEffect(() => {
    if (isLogin && isB2bUser) {
      getB2bProductData(productsSkus)
    }
  }, [getB2bProductData, isB2bUser, isLogin, productsSkus])

  return (
    <CommonLoading spinning={loading}>
      {/* Search Page */}
      {/* <StyledMatch>
        <FormattedMessage
          id="plp.mactchResult"
          values={{ count: totalCount, search: `'${q}'` }}
        />
      </StyledMatch> */}

      {hasProductList && (
        <MediaLayout type="mobile">
          <MobileCategoryPanel id={id} search={q} />
        </MediaLayout>
      )}
      <MediaLayout>
        <>
          {hasProductList && (
            <StyledPlpPanel>
              <div className="product-layout-controller">
                <svg
                  onClick={() => {
                    toggleHorizontal(false)
                  }}
                  width="24px"
                  height="24px"
                  fill="currentColor"
                  aria-hidden="true"
                  focusable="false">
                  <use xlinkHref={isHorizontal ? '#icon-grid-menu' : '#icon-grid-menu-active'} />
                </svg>
                <svg
                  onClick={() => {
                    toggleHorizontal(true)
                  }}
                  width="24px"
                  height="24px"
                  fill="currentColor"
                  aria-hidden="true"
                  focusable="false">
                  <use xlinkHref={isHorizontal ? '#icon-list-menu-active' : '#icon-list-menu'} />
                </svg>
              </div>
              <StyledProductCounter>{productCounter}</StyledProductCounter>
              <Sorters search={q} categoryId={id} />
            </StyledPlpPanel>
          )}
        </>
      </MediaLayout>
      <MediaLayout type="mobile">
        <StyledProductCounter>{productCounter}</StyledProductCounter>
      </MediaLayout>
      <StyledProductList isHorizontal={isHorizontal}>
        {hasProductList ? (
          <>
            {productsData.map((product: any) => {
              return (
                <ProductItem
                  key={product.sku}
                  product={product}
                  isHorizontal={isHorizontal}
                  infoVisible={infoVisible}
                />
              )
            })}
          </>
        ) : (
          <p className="empty">
            <FormattedMessage id="plp.emptyResult" />
          </p>
        )}
      </StyledProductList>
      {hasProductList && (
        <>
          <MediaLayout>
            <StyledPagination>
              <Pagination
                itemRender={paginationItemRender}
                current={currentPage}
                pageSize={pageSize}
                showSizeChanger={false}
                hideOnSinglePage
                total={totalCount}
                onChange={handleOnChange}
              />
            </StyledPagination>
          </MediaLayout>
          <MediaLayout type="mobile">
            <CommonPagination
              current={currentPage}
              pageSize={pageSize}
              total={totalCount}
              onChange={handleOnChange}
            />
          </MediaLayout>
        </>
      )}
    </CommonLoading>
  )
}

export default memo(ProductList)
