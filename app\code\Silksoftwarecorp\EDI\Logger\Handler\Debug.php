<?php

namespace Silksoftwarecorp\EDI\Logger\Handler;

use Magento\Framework\Filesystem\DriverInterface;
use Magento\Framework\Logger\Handler\Base as BaseHandler;
use Monolog\Logger as MonologLogger;
use Silksoftwarecorp\EDI\Helper\Data as Helper;

class Debug extends BaseHandler
{
    /**
     * Logging level
     *
     * @var int
     */
    protected $loggerType = MonologLogger::DEBUG;

    /**
     * File name
     *
     * @var string
     */
    protected $fileName = '/var/log/EDI/debug.log';

    /**
     * @var Helper
     */
    protected $helper;

    public function __construct(
        DriverInterface $filesystem,
        Helper $helper,
        ?string $filePath = null,
        ?string $fileName = null
    ) {
        parent::__construct($filesystem, $filePath, $fileName);

        $this->helper = $helper;
    }

    public function isHandling(array $record): bool
    {
        return $record['level'] == $this->level && $this->helper->isDebugged();
    }
}
