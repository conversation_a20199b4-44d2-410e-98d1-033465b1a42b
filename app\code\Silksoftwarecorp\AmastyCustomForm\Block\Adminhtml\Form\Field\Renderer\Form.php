<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */

namespace Silksoftwarecorp\AmastyCustomForm\Block\Adminhtml\Form\Field\Renderer;

use Magento\Framework\View\Element\Html\Select;
use Magento\Framework\View\Element\Context;
use Amasty\Customform\Api\FormRepositoryInterface;
use Psr\Log\LoggerInterface;

class Form extends Select
{
    /**
     * @var FormRepositoryInterface
     */
    private $formRepository;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @param Context $context
     * @param FormRepositoryInterface $formRepository
     * @param LoggerInterface $logger
     * @param array $data
     */
    public function __construct(
        Context $context,
        FormRepositoryInterface $formRepository,
        LoggerInterface $logger,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->formRepository = $formRepository;
        $this->logger = $logger;
    }

    /**
     * Set "name" for <select> element
     *
     * @param string $value
     * @return $this
     */
    public function setInputName($value)
    {
        return $this->setName($value);
    }

    /**
     * Set "id" for <select> element
     *
     * @param string $value
     * @return $this
     */
    public function setInputId($value)
    {
        return $this->setId($value);
    }

    /**
     * Render form options
     *
     * @return string
     */
    public function _toHtml(): string
    {
        if (!$this->getOptions()) {
            $this->setOptions($this->getFormOptions());
        }
        return parent::_toHtml();
    }

    /**
     * Get form options from Amasty Custom Forms
     *
     * @return array
     */
    private function getFormOptions(): array
    {
        $options = [['label' => __('-- Please Select --'), 'value' => '']];

        try {
            $forms = $this->formRepository->getList();

            foreach ($forms as $form) {
                $options[] = [
                    'label' => ($form->getTitle() ?: $form->getCode()) . "(ID: {$form->getId()})",
                    'value' => $form->getFormId()
                ];
            }
        } catch (\Exception $e) {
            $this->logger->error('[AmastyCustomForm]: Failed to load forms for dropdown', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }

        return $options;
    }
}
