import styled from '@emotion/styled'

export const StyledHeaderPanel = styled.div`
  height: 44px;
  background: #003865;

  .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 44px;
    animation: expand 0.3s ease-in-out;
    overflow: hidden;
  }

  &.is-restricted {
    background: #f8e4cf;

    .restricted-content {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 44px;

      .icon {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 10px;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #8a4700;
      }

      div,
      a {
        font-weight: 400;
        font-size: 16px;
        line-height: 21px;
        letter-spacing: 0;
        color: #8a4700;
      }

      a {
        margin-left: 10px;
        font-weight: 700;
        text-decoration: underline;
      }
    }
  }

  @keyframes expand {
    from {
      height: 0;
    }
    to {
      height: 44px;
    }
  }

  .panel__links .pencil_banner_links {
    button {
      padding: 0;
      margin: 0;
      background: none;

      span {
        margin-left: 24px;
        font-weight: 700;
        font-size: 14px;
        line-height: 20px;
        letter-spacing: 0;
        color: #fff;
        text-transform: initial;
      }
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    .container {
      display: flex;
      justify-content: space-between;
      padding: 0 16px;
      height: 40px;
    }
  }
`
