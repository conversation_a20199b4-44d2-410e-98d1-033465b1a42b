<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */
declare(strict_types=1);

namespace Silksoftwarecorp\ShipToQuoteGraphQl\Plugin;

use Magento\Quote\Api\Data\CartInterface;
use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Quote\Api\Data\CartExtensionFactory;

/**
 * Plugin to handle quote extension attributes
 */
class QuoteRepositoryPlugin
{
    /**
     * @var CartExtensionFactory
     */
    private $cartExtensionFactory;

    /**
     * @param CartExtensionFactory $cartExtensionFactory
     */
    public function __construct(CartExtensionFactory $cartExtensionFactory)
    {
        $this->cartExtensionFactory = $cartExtensionFactory;
    }

    /**
     * Add extension attributes to quote after get
     *
     * @param CartRepositoryInterface $subject
     * @param CartInterface $quote
     * @return CartInterface
     */
    public function afterGet(CartRepositoryInterface $subject, CartInterface $quote): CartInterface
    {
        $this->setExtensionAttributes($quote);
        return $quote;
    }

    /**
     * Add extension attributes to quote after getList
     *
     * @param CartRepositoryInterface $subject
     * @param \Magento\Quote\Api\Data\CartSearchResultsInterface $searchResults
     * @return \Magento\Quote\Api\Data\CartSearchResultsInterface
     */
    public function afterGetList(CartRepositoryInterface $subject, $searchResults)
    {
        foreach ($searchResults->getItems() as $quote) {
            $this->setExtensionAttributes($quote);
        }
        return $searchResults;
    }

    /**
     * Save extension attributes before save
     *
     * @param CartRepositoryInterface $subject
     * @param CartInterface $quote
     * @return array
     */
    public function beforeSave(CartRepositoryInterface $subject, CartInterface $quote): array
    {
        $extensionAttributes = $quote->getExtensionAttributes();
        
        if ($extensionAttributes !== null) {
            if ($extensionAttributes->getShipToId() !== null) {
                $quote->setData('ship_to_id', $extensionAttributes->getShipToId());
            }
            if ($extensionAttributes->getCustomerContactId() !== null) {
                $quote->setData('customer_contact_id', $extensionAttributes->getCustomerContactId());
            }
        }

        return [$quote];
    }

    /**
     * Set extension attributes on quote
     *
     * @param CartInterface $quote
     * @return void
     */
    private function setExtensionAttributes(CartInterface $quote): void
    {
        $extensionAttributes = $quote->getExtensionAttributes();
        if ($extensionAttributes === null) {
            $extensionAttributes = $this->cartExtensionFactory->create();
        }

        if ($quote->getData('ship_to_id') !== null) {
            $extensionAttributes->setShipToId($quote->getData('ship_to_id'));
        }

        if ($quote->getData('customer_contact_id') !== null) {
            $extensionAttributes->setCustomerContactId($quote->getData('customer_contact_id'));
        }

        $quote->setExtensionAttributes($extensionAttributes);
    }
}



