<?php
namespace Silksoftwarecorp\OrderCommentGraphQl\Model\Resolver;

use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;

class OrderSpecialNotes implements ResolverInterface
{
    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        if (!isset($value['model'])) {
            return null;
        }

        $order = $value['model'];
        $specialNotes = $order->getData('special_notes');
        // $value contains the order data
        return $specialNotes;
    }
} 