import styled from '@emotion/styled'

export const StyledMobileTable = styled.div`
  .mb-table-empty {
    padding-top: 30px;
    font-size: 15px;
    line-height: 21px;
    letter-spacing: 0;
    color: #00000073;
    text-align: center;
  }
`

export const StyledMobileTableItem = styled.div`
  padding: 16px;

  &:nth-child(2n -1) {
    background: #f7f7f7;
  }

  .data-item {
    display: grid;
    grid-template-columns: 116px 1fr;
    grid-column-gap: 6px;
    margin-bottom: 10px;
    font-weight: 400;
    font-size: 15px;
    line-height: 21px;
    letter-spacing: 0;
    color: var(--color-black);

    &.mobile-content-full {
      grid-template-columns: 1fr;

      .data-item-title {
        display: none;
      }
    }

    .data-item-title {
      font-weight: 700;
      display: inline-block;

      p {
        margin-bottom: 0;
      }
    }

    .price {
      font-weight: 400;
      font-size: 15px;
      line-height: 21px;
      letter-spacing: 0;
      color: var(--color-black);
    }

    .primary-text {
      font-weight: 700;
      font-size: 15px;
      line-height: 21px;
      letter-spacing: 0;
      text-decoration: underline;
      text-decoration-style: solid;
      text-decoration-thickness: 0;
      color: var(--color-primary);
    }
  }
`
