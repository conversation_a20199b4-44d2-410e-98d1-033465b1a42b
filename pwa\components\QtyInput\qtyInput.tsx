import { FC } from 'react'
import { Input } from 'antd'

import { StyledQtyInput } from './styled'

interface QuantityStepProps {
  min?: number
  max?: number
  value?: number
  disabled?: boolean
  onChange?: (e: any) => void
}

const QtyInput: FC<QuantityStepProps> = ({
  min = 1,
  max = 9999,
  value = 1,
  disabled = false,
  onChange = () => {},
  ...props
}) => {
  return (
    <StyledQtyInput className="qty-input">
      <span className="qty-input-title">QTY</span>
      <Input type="number" value={value} onChange={onChange} disabled={disabled} {...props} />
    </StyledQtyInput>
  )
}

export default QtyInput
