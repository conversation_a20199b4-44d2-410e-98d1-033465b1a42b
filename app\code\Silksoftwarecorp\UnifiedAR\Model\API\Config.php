<?php

namespace Silksoftwarecorp\UnifiedAR\Model\API;

use Magento\Framework\Exception\LocalizedException;
use Silksoftwarecorp\UnifiedAR\Helper\Data as Helper;
use Silksoftwarecorp\UnifiedAR\Model\Config\Source\Environment;

class Config
{
    /**
     * @var Helper
     */
    protected $helper;

    /**
     * @param Helper $helper
     */
    public function __construct(Helper $helper)
    {
        $this->helper = $helper;
    }

    public function getApiUrl($endpoint = null, $storeId = null): string
    {
        $url = $this->helper->getSandboxApiUrl($storeId);
        if ($this->isProduction($storeId)) {
            $url = $this->helper->getApiUrl($storeId);
        }

        $url = rtrim($url, '/') . '/';

        if ($endpoint) {
            $url = $url . ltrim($endpoint, '/');
        }

        return $url;
    }

    public function getApiKey($storeId = null): string
    {
        $apiKey = $this->helper->getSandboxApiKey($storeId);
        if ($this->isProduction($storeId)) {
            $apiKey = $this->helper->getApiKey($storeId);
        }

        if (empty($apiKey)) {
            throw new LocalizedException(__(
                'Please provide a valid %1 API key.',
                $this->getEnvironment($storeId)
            ));
        }

        return $apiKey;
    }

    public function getMerchantKey($storeId = null): string
    {
        $merchantKey = $this->helper->getSandboxMerchantKey($storeId);
        if ($this->isProduction($storeId)) {
            $merchantKey = $this->helper->getMerchantKey($storeId);
        }

        if (empty($merchantKey)) {
            throw new LocalizedException(__(
                'Please provide a valid %1 Merchant Key.',
                $this->getEnvironment($storeId)
            ));
        }

        return $merchantKey;
    }

    public function getLimit(): int
    {
        return 1000;
    }

    public function getTimeout(): int
    {
        return $this->helper->getApiTimeout();
    }

    private function isProduction($storeId = null): bool
    {
        return $this->helper->getApiEnvironment($storeId) == Environment::ENVIRONMENT_PRODUCTION;
    }

    private function getEnvironment($storeId = null): string
    {
        return $this->isProduction($storeId) ? 'Production' : 'Sandbox';
    }
}
