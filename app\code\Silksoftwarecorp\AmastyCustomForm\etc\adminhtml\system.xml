<?xml version="1.0"?>
<!--
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="custom_forms" translate="label" type="text" sortOrder="1000" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>Custom Forms</label>
            <tab>silksoftwarecorp</tab>
            <resource>Silksoftwarecorp_AmastyCustomForm::config</resource>
            <group id="email" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Email</label>
                <field id="send_salesrep_forms" translate="label comment" type="text" sortOrder="1" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Send Sales Rep Forms</label>
                    <comment>Enter the form id separated by commas. When submitting these forms, the email content will include Sales Rep information.</comment>
                </field>
                <field id="enable_email_recipient" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Custom Email Recipient</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="email_recipient" translate="label comment" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Custom Email Recipients</label>
                    <comment>Configure branch-specific email recipients for custom forms. Format: Branch Code, Form ID, Email Recipients (comma-separated)</comment>
                    <frontend_model>Silksoftwarecorp\AmastyCustomForm\Block\Adminhtml\Form\Field\EmailRecipients</frontend_model>
                    <backend_model>Silksoftwarecorp\AmastyCustomForm\Model\System\Config\Backend\EmailRecipients</backend_model>
                    <validate>required-entry validate-multiple-emails</validate>
                    <depends>
                        <field id="enable_email_recipient">1</field>
                    </depends>
                </field>
            </group>
        </section>
    </system>
</config>
