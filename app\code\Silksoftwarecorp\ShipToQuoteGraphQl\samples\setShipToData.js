// Sample GraphQL mutation for setting ship to data on cart
// This should be called after setShipping<PERSON>ddressesOnCart in the checkout workflow

import { gql } from '@apollo/client'

export const SET_SHIP_TO_DATA_ON_CART = gql`
  mutation setShipToDataOnCart($input: SetShipToDataInput!) {
    setShipToDataOnCart(input: $input) {
      cart {
        id
        ship_to_id
        customer_contact_id
        shipping_addresses {
          firstname
          lastname
          street
          city
          postcode
          telephone
          country {
            code
            label
          }
          region {
            region_id
            label
          }
        }
      }
    }
  }
`

// Example usage in React component:
/*
import { useMutation } from '@apollo/client'

const CheckoutComponent = () => {
  const [setShipToData] = useMutation(SET_SHIP_TO_DATA_ON_CART)

  const handleSetShipToData = async (cartId, shipToId) => {
    try {
      const { data } = await setShipToData({
        variables: {
          input: {
            cart_id: cartId,
            ship_to_id: shipToId
          }
        }
      })
      console.log('Ship to data set:', data.setShipToDataOnCart.cart)
    } catch (error) {
      console.error('Error setting ship to data:', error)
    }
  }

  return (
    // Your component JSX here
  )
}
*/

// Query to get cart with ship to data
export const GET_CART_WITH_SHIP_TO_DATA = gql`
  query getCart($cartId: String!) {
    cart(cart_id: $cartId) {
      id
      ship_to_id
      customer_contact_id
      # Add other cart fields as needed
    }
  }
`

// Query to get customer orders with ship to data
export const GET_CUSTOMER_ORDERS_WITH_SHIP_TO_DATA = gql`
  query getCustomerOrders($filter: CustomerOrdersFilterInput, $currentPage: Int, $pageSize: Int) {
    customer {
      orders(filter: $filter, currentPage: $currentPage, pageSize: $pageSize) {
        items {
          id
          number
          order_date
          status
          ship_to_id
          customer_contact_id
          total {
            grand_total {
              value
              currency
            }
          }
        }
        page_info {
          current_page
          page_size
          total_pages
        }
        total_count
      }
    }
  }
`



