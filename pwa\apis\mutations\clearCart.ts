import { gql } from '@apollo/client'
import type { DocumentNode } from '@apollo/client'

import { cartItems } from '../fragment/cartItems'
import { cart_prices } from '../fragment/cartPrices'

export const CLEAR_CART: DocumentNode = gql`
  mutation clearCart($id: ID!) {
    clearCart(input: { uid: $id }) {
      cart {
        id
        quantity: total_quantity
        quantity: total_quantity
        prices {
          ...cart_prices
          __typename
        }
        coupons: applied_coupons {
          code
        }
        ...cartItems
        __typename
      }
      errors {
        message
        type
      }
    }
  }
  ${cartItems}
  ${cart_prices}
`
