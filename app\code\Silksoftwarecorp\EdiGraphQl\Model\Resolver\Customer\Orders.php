<?php

namespace Silksoftwarecorp\EdiGraphQl\Model\Resolver\Customer;

use Magento\Company\Api\CompanyRepositoryInterface;
use Magento\Company\Model\CompanyUser;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlAuthorizationException;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Query\Uid;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\GraphQl\Model\Query\ContextInterface;
use Silksoftwarecorp\EDI\Service\OrderService;

class Orders implements ResolverInterface
{
    /**
     * @var Uid
     */
    private $idEncoder;

    /**
     * @var CompanyRepositoryInterface
     */
    protected $companyRepository;

    /**
     * @var CompanyUser
     */
    protected $companyUser;

    /**
     * @var OrderService
     */
    protected $orderService;

    /**
     * @param Uid $idEncoder
     * @param CompanyRepositoryInterface $companyRepository
     * @param CompanyUser $companyUser
     * @param OrderService $orderService
     */
    public function __construct(
        Uid $idEncoder,
        CompanyRepositoryInterface $companyRepository,
        CompanyUser $companyUser,
        OrderService $orderService
    ) {
        $this->idEncoder = $idEncoder;
        $this->companyRepository = $companyRepository;
        $this->companyUser = $companyUser;
        $this->orderService = $orderService;
    }

    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        /**
         * @var ContextInterface $context
         */
        if (false === $context->getExtensionAttributes()->getIsCustomer()) {
            throw new GraphQlAuthorizationException(__('The request is allowed for logged in customer.'));
        }

        if (empty($args['company_id'])) {
            throw new GraphQlInputException(__('Please specify the company_id.'));
        }

        try {
            $companyId = $this->idEncoder->decode($args['company_id']);
            $currentCompanyId = $this->companyUser->getCurrentCompanyId();
            $company = $this->companyRepository->get($companyId);

            $erpCustomerId = $company?->getExtensionAttributes()?->getErpCustomerId();
            if ($erpCustomerId) {
                return [
                    'items' => $this->orderService->getorders(
                        $erpCustomerId,
                        $args['start_date'] ?? null,
                    )
                ];
            }
        } catch (\Exception $e) {
            throw new GraphQlInputException(__($e->getMessage()), $e);
        }

        return [];
    }
}
