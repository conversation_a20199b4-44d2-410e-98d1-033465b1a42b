<?php

namespace Silksoftwarecorp\EDI\Http\Middleware;

use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\ResponseInterface;
use GuzzleHttp\Psr7;
use Psr\Log\LoggerInterface;
use Silksoftwarecorp\EDI\Http\Middleware\Encoding\Converter;

class EncodingMiddleware
{
    /**
     * @var Converter
     */
    protected $converter;

    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * @param Converter|null $converter
     */
    public function __construct(
        LoggerInterface $logger,
        Converter $converter = null
    ) {
        $this->logger = $logger;
        $this->converter = $converter === null ? new Converter() : $converter;
    }

    public function __invoke(callable $handler)
    {
        return function (RequestInterface $request, array $options) use ($handler) {
            return $handler($request, $options)->then(
                $this->handleEncodeConvert()
            );
        };
    }

    private function handleEncodeConvert(): \Closure
    {
        return function (ResponseInterface $response) {
            $this->logger->critical('EncodingMiddleware');
            if ($this->needConvertEncode($response)) {
                return $this->convertEncode($response);
            }

            return $response;
        };
    }

    /**
     * @param ResponseInterface $response
     * @return bool
     */
    private function needConvertEncode(ResponseInterface $response): bool
    {
        if ($this->converter->getCharset($response) === 'utf-8') {
            return false;
        }

        return true;
    }

    /**
     * @param ResponseInterface $response
     * @return \Psr\Http\Message\MessageInterface|ResponseInterface
     */
    private function convertEncode(ResponseInterface $response)
    {
        $newBody = $this->converter->execute(
            $response->getBody()->getContents(),
            $this->converter->getCharset($response)
        );

        return $response->withBody(Psr7\Utils :: streamFor($newBody));
    }
}
