<?php

namespace Silksoftwarecorp\EDI\Http;

interface ClientInterface
{
    function send($method, $url, $options): Response;

    public function get(string $url, array $queryParams = []): Response;

    public function post(string $url, array $params = []): Response;

    function patch($url, array $params = []): Response;

    function put($url, array $params = []): Response;

    function delete($url, array $params = []): Response;
}
