<?php
namespace Silk\OnesourceTax\Setup;

use Magento\Framework\Setup\InstallDataInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;

class InstallData implements InstallDataInterface
{
    public function install(ModuleDataSetupInterface $setup, ModuleContextInterface $context)
    {
        $setup->startSetup();
        
        // Set default processing options
        $setup->getConnection()->insertOnDuplicate(
            $setup->getTable('core_config_data'),
            [
                'path' => 'tax/onesource/processing_options/charge_included_in_amounts',
                'value' => 1
            ]
        );
        
        $setup->getConnection()->insertOnDuplicate(
            $setup->getTable('core_config_data'),
            [
                'path' => 'tax/onesource/processing_options/charge_response',
                'value' => 'SeparateAuthority'
            ]
        );
        
        $setup->getConnection()->insertOnDuplicate(
            $setup->getTable('core_config_data'),
            [
                'path' => 'tax/onesource/processing_options/response_summary',
                'value' => 'SummaryByErpCode'
            ]
        );
        
        $setup->getConnection()->insertOnDuplicate(
            $setup->getTable('core_config_data'),
            [
                'path' => 'tax/onesource/processing_options/document_amount_type',
                'value' => 'GrossAmount'
            ]
        );
        
        $setup->endSetup();
    }
}