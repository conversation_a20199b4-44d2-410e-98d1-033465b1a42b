import { gql } from '@apollo/client'

import { amBlogCategoriesItem } from '../fragment/amBlogCategoriesItem'

export const AM_BLOG_POSTS_SEARCH = gql`
  query amBlogPostsSearch($page: Int = 1, $query: String) {
    blogPosts: amBlogPostsSearch(page: $page, query: $query) {
      all_post_size
      items {
        ...amBlogCategoriesItem
        __typename
      }
    }
  }
  ${amBlogCategoriesItem}
`
