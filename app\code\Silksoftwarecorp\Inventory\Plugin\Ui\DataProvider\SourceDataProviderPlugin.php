<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */

namespace Silksoftwarecorp\Inventory\Plugin\Ui\DataProvider;

use Magento\InventoryAdminUi\Ui\DataProvider\SourceDataProvider;
use Silksoftwarecorp\Inventory\Ui\DataProvider\ManagerProfileDataProvider;


/**
 * Class SourceDataProviderPlugin
 */
class SourceDataProviderPlugin
{
    /**
     * @var ManagerProfileDataProvider
     */
    private $managerProfileDataProvider;

    /**
     * SourceDataProviderPlugin constructor.
     *
     * @param ManagerProfileDataProvider $managerProfileDataProvider
     */
    public function __construct(
        ManagerProfileDataProvider $managerProfileDataProvider
    ) {
        $this->managerProfileDataProvider = $managerProfileDataProvider;
    }

    /**
     * After get data plugin to add manager profile data for form
     *
     * @param SourceDataProvider $subject
     * @param array $result
     * @return array
     */
    public function afterGetData(SourceDataProvider $subject, $result)
    {
        // Only process in form mode (single record)
        if (is_array($result) && count($result) === 1) {
            foreach ($result as $sourceCode => $item) {
                if (!is_array($item)) {
                    continue;
                }

                $result[$sourceCode] = $this->managerProfileDataProvider->addManagerProfileToSourceData($item, $sourceCode);

                $item = $result[$sourceCode];
                $general = $item['general'] ?? null;
                $extensionAttributes = $general['extension_attributes'] ?? null;
                if ($extensionAttributes && isset($extensionAttributes['schedule'])) {
                    if (is_string($extensionAttributes['schedule'])) {
                        $extensionAttributes['schedule'] = json_decode($extensionAttributes['schedule'], true);
                        $general['extension_attributes'] = $extensionAttributes;
                    }
                }

                $item['general'] = $general;
                $result[$sourceCode] = $item;
            }
        }

        return $result;
    }
}
