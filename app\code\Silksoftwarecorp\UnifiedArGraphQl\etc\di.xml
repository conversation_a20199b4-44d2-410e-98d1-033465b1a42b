<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <!-- PayNowService Configuration -->
    <type name="Silksoftwarecorp\UnifiedAR\Service\PayNowService">
        <arguments>
            <argument name="httpClientFactory" xsi:type="object">Silksoftwarecorp\UnifiedAR\Http\ClientInterfaceFactory</argument>
            <argument name="helper" xsi:type="object">Silksoftwarecorp\UnifiedAR\Helper\Data</argument>
            <argument name="apiConfig" xsi:type="object">Silksoftwarecorp\UnifiedAR\Model\API\Config</argument>
            <argument name="logger" xsi:type="object">UnifiedARLogger</argument>
        </arguments>
    </type>
</config>
