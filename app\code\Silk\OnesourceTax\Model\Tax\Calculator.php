<?php
namespace Silk\OnesourceTax\Model\Tax;

use Magento\Quote\Model\Quote\Address;
use Magento\Quote\Api\Data\ShippingAssignmentInterface;
use Magento\Quote\Model\Quote;
use Silk\OnesourceTax\Api\TaxCalculatorInterface;
use Silk\OnesourceTax\Model\Api\Calculator as ApiCalculator;
use Magento\Framework\Stdlib\DateTime\DateTime;

class Calculator implements TaxCalculatorInterface
{
    protected $apiCalculator;
    protected $dateTime;

    public function __construct(
        ApiCalculator $apiCalculator,
        DateTime $dateTime
    ) {
        $this->apiCalculator = $apiCalculator;
        $this->dateTime = $dateTime;
    }

    public function calculate(Quote $quote, ?ShippingAssignmentInterface $shippingAssignment = null)
    {
        // If shipping assignment is null, create a basic one
        if ($shippingAssignment === null) {
            /** @var \Magento\Quote\Model\ShippingAssignment $shippingAssignment */
            $shippingAssignment = \Magento\Framework\App\ObjectManager::getInstance()
                ->create(\Magento\Quote\Model\ShippingAssignment::class);
            
            /** @var \Magento\Quote\Model\Shipping $shipping */
            $shipping = \Magento\Framework\App\ObjectManager::getInstance()
                ->create(\Magento\Quote\Model\Shipping::class);
            
            $shipping->setAddress($quote->getShippingAddress());
            $shippingAssignment->setShipping($shipping);
            $shippingAssignment->setItems($quote->getAllItems());
        }


        $address = $shippingAssignment->getShipping()->getAddress();
        $items = $shippingAssignment->getItems();

        $requestData = [
            'hostRequestLogId' => '',
            'documents' => [
                $this->prepareDocumentData($quote, $address, $items)
            ]
        ];

        return $this->apiCalculator->calculateTax($requestData);
    }

    protected function prepareDocumentData(Quote $quote, Address $address, array $items)
    {
        $documentDate = $this->dateTime->gmtDate('Y-m-d');

        $document = [
            'basisPercent' => 1,
            'addresses' => $this->prepareAddresses($quote, $address),
            'companyRole' => 'S',
            'countryOfOrigin' => 'US',
            'currencyCode' => $quote->getQuoteCurrencyCode(),
            'customerName' => $quote->getCustomerFirstname() . ' ' . $quote->getCustomerLastname(),
            'customerNumber' => $quote->getCustomerId(),
            'deliveryTerm' => [
                'type' => 'Incoterms',
                'term' => ''
            ],
            'deptOfConsign' => '',
            'documentType' => '',
            'documentDate' => $documentDate,
            'lines' => $this->prepareLines($items)
        ];

        return $document;
    }

    protected function prepareAddresses(Quote $quote, Address $address)
    {
        $shipFromAddress = $this->getShipFromAddress($quote);
        $shipToAddress = $this->getShipToAddress($address);

        return [$shipFromAddress, $shipToAddress];
    }

    protected function getShipFromAddress(Quote $quote)
    {

        /** @var \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig */
            $scopeConfig = \Magento\Framework\App\ObjectManager::getInstance()
            ->get(\Magento\Framework\App\Config\ScopeConfigInterface::class);

        // Get shipping origin from store configuration
        $countryId = $scopeConfig->getValue(
            \Magento\Shipping\Model\Config::XML_PATH_ORIGIN_COUNTRY_ID,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
            $quote->getStoreId()
        );

        $regionId = $scopeConfig->getValue(
            \Magento\Shipping\Model\Config::XML_PATH_ORIGIN_REGION_ID,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
            $quote->getStoreId()
        );

        /** @var \Magento\Directory\Model\Region $region */
        $region = \Magento\Framework\App\ObjectManager::getInstance()
            ->get(\Magento\Directory\Model\Region::class)
            ->load($regionId);

            return [
                'type' => 'shipFrom',
                'country' => $countryId,
                'region' => $region->getCode(),
                'county' => '',
                'city' => $scopeConfig->getValue(
                    \Magento\Shipping\Model\Config::XML_PATH_ORIGIN_CITY,
                    \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
                    $quote->getStoreId()
                ),
                'district' => '',
                'postcode' => $scopeConfig->getValue(
                    \Magento\Shipping\Model\Config::XML_PATH_ORIGIN_POSTCODE,
                    \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
                    $quote->getStoreId()
                ),
                'geocode' => '',
                'isBonded' => '',
                'locationTaxCategory' => ''
            ];
    }

    protected function getShipToAddress(Address $address)
    {
        return [
            'type' => 'shipTo',
            'country' => $address->getCountryId(),
            'region' => $address->getRegionCode(),
            'county' => '',
            'city' => $address->getCity(),
            'district' => '',
            'postcode' => $address->getPostcode(),
            'geocode' => '',
            'isBonded' => '',
            'locationTaxCategory' => ''
        ];
    }

protected function prepareLines(array $items)
{
    $lines = [];
    $lineNumber = 1;

    foreach ($items as $item) {
        if ($item->getParentItem()) {
            continue;
        }

        $lines[] = [
            'accountingCode' => '',
            'basisPercent' => 1,
            'addresses' => [
                [
                    'type' => 'billTo',
                    'country' => '',
                    'region' => '',
                    'county' => '',
                    'city' => '',
                    'district' => '',
                    'postcode' => '',
                    'geocode' => '',
                    'isBonded' => '',
                    'locationTaxCategory' => ''
                ]
            ],
            'commodityCode' => $item->getSku(),
            'countryOfOrigin' => '',
            'customerName' => '',
            'customerNumber' => '',
            'deliveryTerm' => [
                'type' => 'Incoterms',
                'term' => ''
            ],
            'deptOfConsign' => '',
            'description' => $item->getName(),
            'amount' => $item->getRowTotal(),
            'lineNumber' => $item->getId() // Using item ID instead of sequential number
        ];
    }

    return $lines;
}



}