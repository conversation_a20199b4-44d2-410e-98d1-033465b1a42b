<?php
namespace Silksoftwarecorp\Inventory\Ui\DataProvider\Source\Form\Modifier;

use Magento\Ui\DataProvider\Modifier\ModifierInterface;
use Silksoftwarecorp\Inventory\Ui\DataProvider\ManagerProfileDataProvider;

class ManagerProfile implements ModifierInterface
{
    /** @var ManagerProfileDataProvider */
    protected $managerProfileDataProvider;

    public function __construct(
        ManagerProfileDataProvider $managerProfileDataProvider
    ) {
        $this->managerProfileDataProvider = $managerProfileDataProvider;
    }

    public function modifyMeta(array $meta): array
    {
        return $meta;
    }

    public function modifyData(array $data): array
    {
        if (!isset($data['items'])) {
            return $data;
        }

        foreach ($data['items'] as $sourceCode => &$item) {
            $sourceCode = $item['source_code'] ?? $sourceCode;
            $item = $this->managerProfileDataProvider->addManagerProfileToSourceData($item, $sourceCode);
        }

        return $data;
    }
}
