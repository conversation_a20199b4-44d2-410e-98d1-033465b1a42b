import Head from 'next/head'
import { Form, Button } from 'antd'

import Breadcrumb from '@/components/Breadcrumb'
import CommonLoading from '@/components/Common/CommonLoading'
import { useCustomFormPage } from '@/hooks/CustomFormPage'

import CustomFormRow from './CustomFormRow'
import { StyledCustomFormPage } from './styled'

const CustomFormPage = ({
  formId,
  isPage = false,
  handleSubmitForm,
  curFormList,
  curBillRes,
  handleBack,
  isBackVisible,
  stepKey = '',
  group = {}
}: any) => {
  const {
    loading,
    form,
    formList,
    title,
    handleFormSubmit,
    submitBtnText,
    initialValues,
    isInstallRequestPage,
    onValuesChange,
    skirtingColorOptions
  } = useCustomFormPage({ formId, handleSubmitForm, curFormList, curBillRes, stepKey, group })

  return (
    <div>
      {isPage && !formId && (
        <>
          <Head>
            <title>{title}</title>
          </Head>
          {title && <Breadcrumb items={[{ name: title }]} />}
        </>
      )}
      <CommonLoading spinning={loading}>
        {formList.length > 0 && (
          <StyledCustomFormPage className="custom-form-page-component">
            <Form
              form={form}
              layout="vertical"
              onFinish={handleFormSubmit}
              initialValues={initialValues}
              onValuesChange={onValuesChange}>
              <CustomFormRow
                formList={formList}
                skirtingColorOptions={skirtingColorOptions}
                handleSetFieldsValue={(value) => {
                  form.setFieldsValue(value)
                }}
              />
              {submitBtnText && (
                <div className="submit-btn">
                  {isInstallRequestPage && isBackVisible && (
                    <Button loading={loading} className="back-btn" onClick={handleBack}>
                      Back
                    </Button>
                  )}
                  <Button
                    htmlType="submit"
                    loading={loading}
                    // disabled={!allValuesFilled || !addressType}
                  >
                    {submitBtnText}
                  </Button>
                </div>
              )}
            </Form>
          </StyledCustomFormPage>
        )}
      </CommonLoading>
    </div>
  )
}

export default CustomFormPage
