<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <columns name="company_columns">
        <column name="erp_customer_id" sortOrder="999">
            <settings>
                <filter>text</filter>
                <label translate="true">ERP Customer ID</label>
            </settings>
        </column>
        <column name="erp_sales_rep_id" sortOrder="999">
            <settings>
                <filter>text</filter>
                <label translate="true">ERP Sales Rep ID</label>
            </settings>
        </column>
        <column name="erp_sales_rep_name" sortOrder="999">
            <settings>
                <filter>text</filter>
                <label translate="true">ERP Sales Rep Name</label>
            </settings>
        </column>
        <column name="erp_credit_status" sortOrder="999">
            <settings>
                <filter>text</filter>
                <label translate="true">ERP Credit Status</label>
            </settings>
        </column>
    </columns>
</listing>
