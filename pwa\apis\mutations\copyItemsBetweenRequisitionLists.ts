import { gql } from '@apollo/client'

export const COPY_ITEMS_BETWEEN_REQUISITION_LISTS = gql`
  mutation copyItemsBetweenRequisitionLists(
    $sourceId: ID!
    $destinationId: ID
    $items: CopyItemsBetweenRequisitionListsInput
  ) {
    copyItemsBetweenRequisitionLists(
      sourceRequisitionListUid: $sourceId
      destinationRequisitionListUid: $destinationId
      requisitionListItem: $items
    ) {
      requisition_list {
        name
        uid
      }
    }
  }
`
