import styled from '@emotion/styled'

export const StyledCollapseBox = styled.div`
  margin-top: 24px;

  .${({ theme }) => theme.namespace}-collapse {
    &-header {
      align-items: center !important;
      padding: 0 24px !important;
      height: 62px;
      background: #003865;
      border-radius: 4px !important;

      &-text {
        font-weight: 700;
        font-size: 22px;
        line-height: 28px;
        letter-spacing: 0;
        color: var(--color-white);
      }
    }

    &-content-box {
      p {
        margin-top: 12px;
        font-weight: 400;
        font-size: 16px;
        line-height: 26px;
        letter-spacing: 0.02em;
        color: var(--color-text);
      }

      ul,
      ol {
        padding-left: 22px;
        margin-top: 12px;

        li {
          font-weight: 400;
          font-size: 16px;
          line-height: 26px;
          letter-spacing: 0.02em;
          color: var(--color-text);
        }
      }
      ul li {
        list-style-type: disc;
      }

      ol li {
        list-style-type: decimal;
      }
    }
  }

  &.pdp-specifications {
    table {
      width: 100%;
      max-width: 530px;
      margin-top: 8px;
    }

    tbody {
      tr {
        &:nth-of-type(2n) {
          background: #f4f3f3;
        }

        td {
          padding-left: 24px;
          height: 42px;
          font-size: 16px;
          line-height: 26px;
          letter-spacing: 0;
          border: 1px solid #d9d9d9;
          color: var(--color-text);

          &:first-of-type {
            padding-left: 15px;
            width: 182px;
            font-weight: 700;
            color: var(--color-font);
          }
        }
      }
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    &.pdp-specifications {
      table {
        width: 100% !important;
      }
      tbody {
        tr {
          td {
            padding-left: 12px;
            min-width: unset;
          }
        }
      }
    }
  }
`
