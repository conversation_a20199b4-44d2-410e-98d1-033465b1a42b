import { gql } from '@apollo/client'

import { companyUsersItems } from '../fragment/companyUsersItems'

export const GET_B2B_COMPANY_USERS = gql`
  query getCompanyUsers($filter: CompanyUsersFilterInput, $pageSize: Int, $currentPage: Int) {
    company {
      users(filter: $filter, pageSize: $pageSize, currentPage: $currentPage) {
        pages: page_info {
          current_page
          page_size
          total_pages
        }
        total_count
        ...companyUsersItems
        __typename
      }
    }
  }
  ${companyUsersItems}
`
