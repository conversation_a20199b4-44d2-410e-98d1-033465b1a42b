import { memo } from 'react'
import { Select } from 'antd'

import { StyledCommonSelect } from './styled'

const CommonSelect = ({ options, ...commonSelectProps }) => {
  return (
    <StyledCommonSelect
      suffixIcon={
        <svg width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false">
          <use xlinkHref="#icon-select-suffix" />
        </svg>
      }
      {...commonSelectProps}>
      {options.map(({ value, label }) => {
        return (
          <Select.Option key={value} value={value}>
            <span dangerouslySetInnerHTML={{ __html: label }} />
          </Select.Option>
        )
      })}
    </StyledCommonSelect>
  )
}

export default memo(CommonSelect)
