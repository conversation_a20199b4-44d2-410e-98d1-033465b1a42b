<?xml version="1.0" encoding="UTF-8"?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <fieldset name="general">
        <field name="how_to_measure" sortOrder="100" formElement="select">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">category</item>
                </item>
            </argument>
            <settings>
                <dataType>text</dataType>
                <label translate="true">How to Measure</label>
                <dataScope>how_to_measure</dataScope>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">false</rule>
                </validation>
            </settings>
            <formElements>
                <select>
                    <settings>
                        <options class="Silksoftwarecorp\Catalog\Model\Category\Attribute\Source\HowToMeasure"/>
                        <caption translate="true">-- Please Select --</caption>
                    </settings>
                </select>
            </formElements>
        </field>
    </fieldset>
    <fieldset name="display_settings">
        <container name="label_group" component="Magento_Ui/js/form/components/group" sortOrder="10">
            <argument name="data" xsi:type="array">
                <item name="type" xsi:type="string">group</item>
                <item name="config" xsi:type="array">
                    <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                    <item name="required" xsi:type="boolean">false</item>
                    <item name="breakLine" xsi:type="boolean">false</item>
                </item>
            </argument>

            <colorPicker name="label_text_color" class="Magento\Ui\Component\Form\Element\ColorPicker" component="Magento_Ui/js/form/element/color-picker" sortOrder="20">
                <settings>
                    <label translate="true">Label Text Color (hex)</label>
                    <elementTmpl>ui/form/element/color-picker</elementTmpl>
                    <colorFormat>hex</colorFormat>
                    <colorPickerMode>full</colorPickerMode>
                    <dataScope>label_text_color</dataScope>
                    <additionalClasses>
                        <class name="admin__field-group-show-label">true</class>
                    </additionalClasses>
                </settings>
            </colorPicker>
        </container>
    </fieldset>
</form>
