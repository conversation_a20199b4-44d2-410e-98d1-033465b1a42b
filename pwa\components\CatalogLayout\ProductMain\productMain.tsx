// import { scroller } from '@ranger-theme/ui'
import dynamic from 'next/dynamic'
// import { useProductMain } from '@/hooks/CatalogLayout'
import { useSelector } from 'react-redux'
import { isEmpty, subtract } from 'lodash-es'
import { useMemo, memo, useEffect, useCallback, useState } from 'react'

import OrderHelp from '@/components/OrderHelp'
import ProductStock from '@/components/Common/ProductStock'
import CommonLoading from '@/components/Common/CommonLoading'
import ProductStockText from '@/components/Common/ProductStockText'
import { MediaLayout } from '@/ui'
import ProductRealTimePrice from '@/components/Common/ProductRealTimePrice'
import { isB2bUserSelector } from '@/store/user/slice'
import { useB2bProductData } from '@/hooks/B2bProductData'
import { getProductsStockInfo } from '@/utils'

import BaseImages from '../BaseImages'
import ProductForm from '../ProductForm'
// import ScrollTab from '../ScrollTab'
import ShortDescription from '../ShortDescription'
import ProductTitle from './ProductTitle'
import { StyledPdpMain, StyledPdpDetail } from './styled'

const ProductContent = dynamic(() => import('../ProductContent'), {
  ssr: false
})

const ProductMain = (props: any) => {
  const {
    name,
    price_range,
    ble_manufacturer_name,
    sku,
    short_description,
    stock_status,
    only_x_left_in_stock
  } = props
  const { fetchProductPrices, fetchProductInventory } = useB2bProductData()

  const isShowroomView = useSelector((state: Store) => state.user.showroomView)
  const isLogin = useSelector((state: Store) => state.user.isLogin)
  const cartDetail = useSelector((state: Store) => state.cart.cartDetail)
  const isB2bUser = useSelector(isB2bUserSelector)
  const locationId = useSelector((state: Store) => state.user.shipTo?.locationId)

  const [loading, setLoading] = useState(false)
  const [realTimePrice, setRealTimePrice] = useState(null)
  const [purchasedDate, setPurchasedDate] = useState('')
  const [productInfo, setProductInfo] = useState<any>({})

  const infoVisible = useMemo(() => {
    return !isLogin || !isEmpty(productInfo)
  }, [productInfo, isLogin])

  const isActionVisible = useMemo(() => {
    return isLogin ? productInfo?.isAllowToBackorder : stock_status === 'IN_STOCK'
  }, [productInfo, isLogin, stock_status])

  // const maxAvailableQty = useMemo(() => {
  //   const cartItemQty = cartDetail?.items?.find((item) => item?.product?.sku === sku)?.quantity ?? 0
  //   const curStockQty = productInfo?.stockQty ?? 0
  //   const maxQty = subtract(curStockQty, cartItemQty)
  //   return maxQty < 0 ? 0 : maxQty
  // }, [cartDetail, sku, productInfo])

  const getB2bProductData = useCallback(async () => {
    try {
      setLoading(true)
      const [priceRes, inventoryRes] = await Promise.all([
        fetchProductPrices([sku]),
        fetchProductInventory([sku])
      ])
      setRealTimePrice(priceRes?.[0]?.price ?? null)
      setPurchasedDate(priceRes?.[0]?.last_invoice_date ?? null)
      setProductInfo(getProductsStockInfo([{ sku }], inventoryRes)?.[0] ?? {})
    } catch (e) {
      console.error(e)
    } finally {
      setLoading(false)
    }
  }, [fetchProductInventory, fetchProductPrices, sku])

  useEffect(() => {
    if (isLogin && isB2bUser && cartDetail && locationId) {
      getB2bProductData()
    }
  }, [getB2bProductData, isB2bUser, isLogin, cartDetail, locationId])

  // const { t } = useProductMain(sku)

  // const handleOnClick = () => {
  //   const padding: number = 20
  //   const target = document.querySelector('header')
  //
  //   scroller.scrollTo('description', {
  //     offset: -(target.clientHeight + padding),
  //     duration: 500,
  //     smooth: true
  //   })
  // }

  return (
    <>
      <CommonLoading spinning={loading}>
        <StyledPdpMain>
          <MediaLayout type="mobile">
            <ProductTitle
              title={name}
              sku={sku}
              manufacturerName={ble_manufacturer_name}
              purchasedDate={purchasedDate}
            />
          </MediaLayout>
          <BaseImages />
          <StyledPdpDetail>
            <MediaLayout>
              <ProductTitle
                title={name}
                sku={sku}
                manufacturerName={ble_manufacturer_name}
                purchasedDate={purchasedDate}
              />
            </MediaLayout>
            {isLogin && (
              <>
                <p className="stock">
                  <ProductStockText text={productInfo?.stockText ?? ''} />
                  {/*<ProductStock
                    product={{
                      stock_status,
                      only_x_left_in_stock: isB2bUser ? inventoryQty : only_x_left_in_stock
                    }}
                  />*/}
                </p>
                {!isShowroomView && (
                  <div className="prices">
                    <ProductRealTimePrice priceRange={price_range} realTimePrice={realTimePrice} />
                  </div>
                )}
              </>
            )}
            {infoVisible && (
              <ProductForm
                isActionVisible={isActionVisible}
                isAllowToBackorder={productInfo?.isAllowToBackorder}
              />
            )}
            {short_description?.html && (
              <ShortDescription descriptionHtml={short_description.html} />
            )}
            <OrderHelp />
          </StyledPdpDetail>
        </StyledPdpMain>
      </CommonLoading>
      <ProductContent sku={sku} />
      {/* <ScrollTab /> */}
    </>
  )
}

export default memo(ProductMain)
