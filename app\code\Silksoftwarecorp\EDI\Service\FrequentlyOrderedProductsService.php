<?php

namespace Silksoftwarecorp\EDI\Service;

use Silksoftwarecorp\EDI\Helper\FrequentlyOrderedProducts as Helper;
use Silksoftwarecorp\EDI\Model\EdiApiManagement;
use Silksoftwarecorp\EDI\Model\FrequentlyOrderedProducts\ProductsProcessor;

class FrequentlyOrderedProductsService
{
    /**
     * @var Helper
     */
    protected $helper;

    /**
     * @var EdiApiManagement
     */
    protected $ediApiManagement;

    /**
     * @var ProductsProcessor
     */
    protected $productsProcessor;

    /**
     * @param Helper $helper
     * @param EdiApiManagement $EdiApiManagement
     * @param ProductsProcessor $productsProcessor
     */
    public function __construct(
        Helper $helper,
        EdiApiManagement $EdiApiManagement,
        ProductsProcessor $productsProcessor
    ) {
        $this->helper = $helper;
        $this->ediApiManagement = $EdiApiManagement;
        $this->productsProcessor = $productsProcessor;
    }

    public function getList($customerId): array
    {
        if (!$this->helper->isEnabled()) {
            return [];
        }

        $orders = $this->ediApiManagement->getFrequentlyOrders(
            $customerId,
            $this->helper->getStartDate(),
            $this->helper->getRequestType()
        );

        return $this->productsProcessor->execute($orders);
    }
}
