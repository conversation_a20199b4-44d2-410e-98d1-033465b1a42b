<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">ship_to_form.ship_to_form_data_source</item>
        </item>
        <item name="label" xsi:type="string" translate="true">General Information</item>
        <item name="template" xsi:type="string">templates/form/collapsible</item>
    </argument>
    <settings>
        <buttons>
            <button name="save_and_continue" class="Magento\Banner\Block\Adminhtml\Banner\Edit\SaveAndContinueButton"/>
            <button name="save" class="Magento\Banner\Block\Adminhtml\Banner\Edit\SaveButton"/>
            <button name="back">
                <url path="*/*/"/>
                <class>back</class>
                <label translate="true">Back</label>
            </button>
        </buttons>
        <namespace>ship_to_form</namespace>
        <dataScope>data</dataScope>
        <deps>
            <dep>ship_to_form.ship_to_form_data_source</dep>
        </deps>
    </settings>
    <dataSource name="ship_to_form_data_source" component="Magento_Ui/js/form/provider">
        <settings>
            <submitUrl path="erp/shipto/save"/>
        </settings>
        <dataProvider class="Silksoftwarecorp\CompanyB2b\Model\ShipTo\DataProvider" name="ship_to_form_data_source">
            <settings>
                <requestFieldName>id</requestFieldName>
                <primaryFieldName>entity_id</primaryFieldName>
            </settings>
        </dataProvider>
    </dataSource>
    <fieldset name="properties" sortOrder="10">
        <settings>
            <label/>
        </settings>
        <field name="entity_id" formElement="hidden">
            <settings>
                <dataType>text</dataType>
            </settings>
        </field>
        <field name="erp_cust_num" formElement="input">
            <settings>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
                <dataType>text</dataType>
                <label translate="true">Cust Num</label>
                <visible>true</visible>
                <dataScope>erp_cust_num</dataScope>

            </settings>
        </field>
        <field name="ship_to_num" sortOrder="10" formElement="input">
            <settings>
                <dataType>text</dataType>
                <visible>true</visible>
                <label translate="true">Ship To Num</label>
            </settings>
        </field>
        <field name="ship_to_name" sortOrder="10" formElement="input">
            <settings>
                <dataType>text</dataType>
                <visible>true</visible>
                <label translate="true">Ship To Name</label>
            </settings>
        </field>
        <field name="email" sortOrder="10" formElement="input">
            <settings>
                <dataType>text</dataType>
                <visible>true</visible>
                <label translate="true">Email Address</label>
            </settings>
        </field>
        <field name="region_code" sortOrder="10" formElement="input">
            <settings>
                <dataType>text</dataType>
                <visible>true</visible>
                <label translate="true">Region Code</label>
            </settings>
        </field>
        <field name="telephone" sortOrder="10" formElement="input">
            <settings>
                <dataType>text</dataType>
                <visible>true</visible>
                <label translate="true">Telephone</label>
            </settings>
        </field>
        <field name="region_id" component="Magento_Ui/js/form/element/region" formElement="select">
            <settings>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
                <dataType>text</dataType>
                <label translate="true">State/Province</label>
            </settings>
            <formElements>
                <select>
                    <settings>
                        <filterBy>
                            <field>country_id</field>
                            <target>${ $.provider }:${ $.parentScope }.country_id</target>
                        </filterBy>
                        <customEntry>region</customEntry>
                        <options class="Magento\Directory\Model\ResourceModel\Region\Collection"/>
                    </settings>
                </select>
            </formElements>
        </field>
        <field name="postcode" sortOrder="10" formElement="input">
            <settings>
                <dataType>text</dataType>
                <visible>true</visible>
                <label translate="true">ZIP</label>
            </settings>
        </field>
        <field name="country_id" formElement="select" sortOrder="60">
            <settings>
                <dataType>text</dataType>
                <label translate="true">Country</label>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
            </settings>
            <formElements>
                <select>
                    <settings>
                        <options class="Magento\Directory\Model\Config\Source\Country"/>
                    </settings>
                </select>
            </formElements>
        </field>
        <field name="country_id" formElement="select">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">country_id</item>
                </item>
            </argument>
            <settings>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
                <dataType>text</dataType>
                <label translate="true">Country</label>
            </settings>
            <formElements>
                <select>
                    <settings>
                        <options class="Magento\Directory\Model\ResourceModel\Country\Collection"/>
                    </settings>
                </select>
            </formElements>
        </field>
        <field name="city" sortOrder="10" formElement="input">
            <settings>
                <dataType>text</dataType>
                <visible>true</visible>
                <label translate="true">City</label>
            </settings>
        </field>
        <field name="street" sortOrder="17" formElement="input">
            <settings>
                <dataType>text</dataType>
                <visible>true</visible>
                <label translate="true">Street Address</label>
            </settings>
        </field>
        <field name="street_line2" sortOrder="18" formElement="input">
            <settings>
                <dataType>text</dataType>
                <visible>true</visible>
                <label translate="true">Street Address Line 2</label>
            </settings>
        </field>
        <field name="street_line3" sortOrder="19" formElement="input">
            <settings>
                <dataType>text</dataType>
                <visible>true</visible>
                <label translate="true">Street Address Line 3</label>
            </settings>
        </field>
        <field name="location_id" sortOrder="10" formElement="input">
            <settings>
                <dataType>text</dataType>
                <visible>true</visible>
                <label translate="true">Location ID</label>
            </settings>
        </field>
    </fieldset>
</form>
