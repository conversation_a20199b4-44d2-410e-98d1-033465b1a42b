import { gql } from '@apollo/client'
import type { DocumentNode } from '@apollo/client'

import { cartItems } from '../fragment/cartItems'
import { cart_prices } from '../fragment/cartPrices'

export const POST_CANCEL_COUPON: DocumentNode = gql`
  mutation cancelCouponToCart($cartId: String!) {
    cancelCoupon: removeCouponFromCart(input: { cart_id: $cartId }) {
      cart {
        prices {
          ...cart_prices
          __typename
        }
        quantity: total_quantity
        applied_coupon {
          code
        }
        ...cartItems
        __typename
      }
    }
  }
  ${cartItems}
  ${cart_prices}
`
