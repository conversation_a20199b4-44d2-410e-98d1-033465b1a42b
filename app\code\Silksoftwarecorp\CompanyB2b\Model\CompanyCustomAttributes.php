<?php

namespace Silksoftwarecorp\CompanyB2b\Model;
use Silksoftwarecorp\CompanyB2b\Api\Data\CompanyCustomAttributesInterface;
class CompanyCustomAttributes extends \Magento\Framework\Api\AbstractSimpleObject implements CompanyCustomAttributesInterface
{

    /**
     * @return ?string
     */
    public function getErpCustNum(): ?string
    {
        // TODO: Implement getErpCustNum() method.
        return  $this->_get('erp_cust_num');
    }

    /**
     * @return ?string
     */
    public function getErpCustCode(): ?string
    {
        // TODO: Implement getErpCustCode() method.
        return  $this->_get('erp_cust_code');
    }

    /**
     * @param ?string $erpCustomerNum
     * @return CompanyCustomAttributesInterface
     */
    public function setErpCustNum(?string $erpCustomerNum): CompanyCustomAttributesInterface
    {
        // TODO: Implement setErpCustNum() method.
        $this->setData('erp_cust_num', $erpCustomerNum);
        return $this;
    }

    /**
     * @param ?string $erpCustomerCode
     * @return CompanyCustomAttributesInterface
     */
    public function setErpCustCode(?string $erpCustomerCode): CompanyCustomAttributesInterface
    {
        // TODO: Implement setErpCustCode() method.
        $this->setData('erp_cust_code', $erpCustomerCode);
        return $this;
    }

    public function getErpConNum(): ?string
    {
        // TODO: Implement getErpConNum() method.
        return  $this->_get('erp_con_num');
    }

    public function setErpConNum(?string $conID): self
    {
        // TODO: Implement setErpConNum() method.
          $this->setData('erp_con_num', $conID);

          return $this;
    }

    /**
     * @return ?string
     */
    public function getWebsiteId(): ?string
    {
        // TODO: Implement getWebsiteId() method.
        return  $this->_get('website_id');
    }

    /**
     * @param ?string|null $websiteId
     * @return CompanyCustomAttributesInterface
     */
    public function setWebsiteId(?string $websiteId): CompanyCustomAttributesInterface
    {
        // TODO: Implement setWebsiteId() method.
        return $this->setData('website_id', $websiteId);
    }
}
