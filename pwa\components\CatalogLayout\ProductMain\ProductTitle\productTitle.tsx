import CommonPurchased from '@/components/Common/CommonPurchased'

import { StyledProductTitle } from './styled'

const ProductTitle = ({ title, sku, manufacturerName, purchasedDate = '' }) => {
  return (
    <div>
      {purchasedDate && <CommonPurchased date={purchasedDate} />}
      <StyledProductTitle>
        <h1 className="title">
          <span dangerouslySetInnerHTML={{ __html: title }} />
        </h1>
        <div className="info">
          <p className="sku">{sku}</p>
          <p className="manufacture-name">{manufacturerName || ''}</p>
        </div>
      </StyledProductTitle>
    </div>
  )
}

export default ProductTitle
