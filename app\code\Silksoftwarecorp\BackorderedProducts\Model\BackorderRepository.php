<?php
namespace Silksoftwarecorp\BackorderedProducts\Model;

use Silksoftwarecorp\BackorderedProducts\Api\BackorderRepositoryInterface;
use Silksoftwarecorp\BackorderedProducts\Helper\Data as ConfigHelper;
use Magento\Framework\HTTP\Client\Curl;
use Magento\Framework\HTTP\Client\CurlFactory;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;

class BackorderRepository implements BackorderRepositoryInterface
{
    protected $configHelper;
    protected $curlFactory;
    protected $timezone;

    public function __construct(
        ConfigHelper $configHelper,
        CurlFactory $curlFactory,
        TimezoneInterface $timezone
    ) {
        $this->configHelper = $configHelper;
        $this->curlFactory = $curlFactory;
        $this->timezone = $timezone;
    }

    public function getBackorderedProducts($erpContactId)
    {
        $url = $this->configHelper->getApiUrl();
        $username = $this->configHelper->getUsername();
        $password = $this->configHelper->getPassword();
        $daysBack = $this->configHelper->getDaysBack();
        $startDate = $this->timezone->date()->modify("-{$daysBack} days")->format('Y-m-d');

        /** @var Curl $client */
        $client = $this->curlFactory->create();
        $headers = [
            'CustomerID' => $erpContactId,
            'Password' => $password,
            'RequestType' => 'OrderDetails',
            'StartDate' => $startDate,
            'UserName' => $username
        ];
        $client->setHeaders($headers);
        try {
            $client->get($url);
            if ($client->getStatus() == 200) {
                $data = json_decode($client->getBody(), true);
                $result = [];
                $orderMap = []; // To deduplicate orders by OrderNo
                
                if (!empty($data['orders'])) {
                    foreach ($data['orders'] as $order) {
                        if (empty($order['OrderLines'])) continue;
                        
                        $orderNo = $order['OrderNo'];
                        $backorderedLines = [];
                        
                        foreach ($order['OrderLines'] as $line) {
                            if ($line['LineStatus'] === 'Backordered') {
                                $backorderedLines[] = [
                                    'sku' => $line['ItemID'],
                                    'product_name' => $line['ExtendedDesc'],
                                    'quantity' => $line['QtyOrdered']
                                ];
                            }
                        }
                        
                        if (!empty($backorderedLines)) {
                            if (isset($orderMap[$orderNo])) {
                                // Merge backordered lines for existing order
                                $orderMap[$orderNo]['lines'] = array_merge(
                                    $orderMap[$orderNo]['lines'], 
                                    $backorderedLines
                                );
                            } else {
                                // Create new order entry
                                $orderMap[$orderNo] = [
                                    'order_number' => $orderNo,
                                    'date_ordered' => date('m/d/Y', strtotime($order['OrderDate'])),
                                    'lines' => $backorderedLines
                                ];
                            }
                        }
                    }
                    
                    // Convert map to indexed array
                    $result = array_values($orderMap);
                }
                return $result;
            }
        } catch (\Exception $e) {
            // Log error if needed
        }
        return [];
    }
} 