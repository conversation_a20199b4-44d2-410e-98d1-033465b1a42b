<?php

namespace Silksoftwarecorp\UnifiedAR\Http;

use <PERSON>uz<PERSON><PERSON>ttp\ClientFactory;
use GuzzleHttp\Middleware;
use Magento\Framework\Exception\LocalizedException;
use Psr\Log\LoggerInterface;
use Silksoftwarecorp\UnifiedAR\Http\Exception\ConnectionException;
use Silksoftwarecorp\UnifiedAR\Http\Middleware\LogMiddleware;
use Silksoftwarecorp\UnifiedAR\Http\Middleware\RetryMiddleware;
use Silksoftwarecorp\UnifiedAR\Http\ResponseFactory;

class Client implements ClientInterface
{
    protected string $baseUri = '';

    protected string $bodyFormat = 'json';

    protected array $options = [
        'http_errors' => false,
        'verify' => false,
    ];

    protected array $cookies = [];

    protected $transferStats;

    protected array $middlewares = [];

    /**
     * @var ClientFactory
     */
    protected $clientFactory;

    /**
     * @var ResponseFactory
     */
    protected $responseFactory;

    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * @param ClientFactory $clientFactory
     * @param ResponseFactory $responseFactory
     * @param LoggerInterface $logger
     * @param string $baseUri
     */
    public function __construct(
        ClientFactory $clientFactory,
        ResponseFactory $responseFactory,
        LoggerInterface $logger,
        string $baseUri = '',
    ) {
        $this->clientFactory = $clientFactory;
        $this->responseFactory = $responseFactory;
        $this->logger = $logger;
        $this->baseUri = $baseUri;
    }

    /**
     * @param $method
     * @param $url
     * @param $options
     * @return Response
     * @throws ConnectionException|LocalizedException
     */
    function send($method, $url, $options): Response
    {
        try {
            $options = $this->mergeOptions([
                'query' => $this->parseQueryParams($url),
                'on_stats' => function ($transferStats) {
                    $this->transferStats = $transferStats;
                }
            ], $options);

            $httpResponse = $this->buildClient()->request($method, $url, $options);
            $response = $this->responseFactory->create([
                'response' => $httpResponse,
            ]);

            return $this->tap($response, function($response) {
                /**@var $response Response*/
                $response->setCookies($this->cookies)
                    ->setTransferStats($this->transferStats);
            });
        } catch (\GuzzleHttp\Exception\ConnectException $e) {
            $this->logger->error(__($e->getMessage()));

            throw new ConnectionException(__($e->getMessage()), $e);
        } catch (\GuzzleHttp\Exception\GuzzleException $e) {
            $this->logger->error(__($e->getMessage()));

            throw new LocalizedException(__($e->getMessage()), $e);
        }
    }

    public function get(string $url, array $queryParams = []): Response
    {
        return $this->send('GET', $url, [
            'query' => $queryParams,
        ]);
    }

    public function post(string $url, array $params = []): Response
    {
        return $this->send('POST', $url, [
            $this->bodyFormat => $params,
        ]);
    }

    function patch($url, $params = []): Response
    {
        return $this->send('PATCH', $url, [
            $this->bodyFormat => $params,
        ]);
    }

    function put($url, $params = []): Response
    {
        return $this->send('PUT', $url, [
            $this->bodyFormat => $params,
        ]);
    }

    function delete($url, $params = []): Response
    {
        return $this->send('DELETE', $url, [
            $this->bodyFormat => $params,
        ]);
    }

    public function withBaseUri(string $baseUri): static
    {
        return $this->tap($this, function () use ($baseUri) {
            $this->baseUri = $baseUri;
        });
    }

    public function withOptions($options): static
    {
        return $this->tap($this, function () use ($options) {
            return $this->options = array_merge_recursive($this->options, $options);
        });
    }

    public function withoutRedirecting(): static
    {
        return $this->tap($this, function () {
            return $this->options = array_merge_recursive($this->options, [
                'allow_redirects' => false,
            ]);
        });
    }

    public function withVerifying(): static
    {
        return $this->tap($this, function () {
            return $this->options = array_merge_recursive($this->options, [
                'verify' => true,
            ]);
        });
    }

    public function bodyFormat($format): static
    {
        return $this->tap($this, function () use ($format) {
            $this->bodyFormat = $format;
        });
    }

    public function asJson(): static
    {
        return $this->bodyFormat('json')->contentType('application/json');
    }

    public function asFormParams(): static
    {
        return $this->bodyFormat('form_params')->contentType('application/x-www-form-urlencoded');
    }

    public function asMultipart(): static
    {
        return $this->bodyFormat('multipart');
    }

    public function contentType($contentType): static
    {
        return $this->withHeaders(['Content-Type' => $contentType]);
    }

    public function accept($header): static
    {
        return $this->withHeaders(['Accept' => $header]);
    }

    public function withHeaders($headers): static
    {
        return $this->tap($this, function () use ($headers) {
            return $this->options = array_merge_recursive($this->options, [
                'headers' => $headers,
            ]);
        });
    }

    public function withBasicAuth($username, $password): static
    {
        return $this->tap($this, function () use ($username, $password) {
            return $this->options = array_merge_recursive($this->options, [
                'auth' => [$username, $password],
            ]);
        });
    }

    public function withDigestAuth($username, $password)
    {
        return $this->tap($this, function () use ($username, $password) {
            return $this->options = array_merge_recursive($this->options, [
                'auth' => [$username, $password, 'digest'],
            ]);
        });
    }

    public function withCookies($cookies)
    {
        return $this->tap($this, function() use ($cookies) {
            return $this->options = array_merge_recursive($this->options, [
                'cookies' => $cookies,
            ]);
        });
    }

    public function withRetry(): static
    {
        return $this->addMiddleware(Middleware::retry(
            RetryMiddleware::decider($this->logger),
            RetryMiddleware::delay()
        ));
    }

    public function withLog(): static
    {
        return $this->tap($this, function () {
            return $this->options = array_merge_recursive($this->options, [
                'log' => [
                    'on_exception_only' => false,
                    'statistics' => true,
                ]
            ]);
        });
    }

    public function timeout($seconds = 10): static
    {
        return $this->tap($this, function () use ($seconds) {
            $this->options['timeout'] = $seconds;
        });
    }

    protected function buildHandlerStack()
    {
        $this->initMiddleware();

        return $this->tap(\GuzzleHttp\HandlerStack::create(), function ($stack) {
            foreach ($this->middlewares as $middleware) {
                $stack->push($middleware);
            }
        });
    }

    protected function initMiddleware(): void
    {
        $this->addMiddleware(new LogMiddleware($this->logger));
    }

    public function addMiddleware(callable $middleware): static
    {
        return $this->tap($this, function () use ($middleware) {
            $this->middlewares[] = $middleware;
        });
    }

    protected function mergeOptions(...$options): array
    {
        return array_merge_recursive($this->options, ...$options);
    }

    protected function parseQueryParams($url)
    {
        return $this->tap([], function (&$query) use ($url) {
            $urlQuery = parse_url($url, PHP_URL_QUERY);
            if (is_string($urlQuery)) {
                parse_str(parse_url($url, PHP_URL_QUERY), $query);
            }
        });
    }

    protected function buildClient(): \GuzzleHttp\Client
    {
        return $this->clientFactory->create([
            'config' => $this->getDefaultConfig()
        ]);
    }

    protected function getDefaultConfig(): array
    {
        $config = [
            'handler' => $this->buildHandlerStack(),
            'cookies' => true,
        ];

        if ($this->baseUri) {
            $config['base_uri'] = $this->baseUri;
        }

        return $config;
    }

    protected function tap($value, $callback)
    {
        $callback($value);

        return $value;
    }
}
