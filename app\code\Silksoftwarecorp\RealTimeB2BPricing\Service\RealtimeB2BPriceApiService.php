<?php

namespace Silksoftwarecorp\RealTimeB2BPricing\Service;

use Silksoftwarecorp\RealTimeB2BPricing\Api\B2BPriceItemResultInterface;
use Silksoftwarecorp\RealTimeB2BPricing\Helper\Data as DataHelper;
use Silksoftwarecorp\RealTimeB2BPricing\Logger\LoggerInterface;
use Silksoftwarecorp\RealTimeB2BPricing\Model\API\Exception\ApiResponseException;
use Silksoftwarecorp\RealTimeB2BPricing\Model\B2BPriceItem;
use Silksoftwarecorp\RealTimeB2BPricing\Model\B2BPriceItemConverter;
use Silksoftwarecorp\RealTimeB2BPricing\Api\PriceStorageInterface;
use Silksoftwarecorp\RealTimeB2BPricing\Model\API\Http\ClientInterface;
use Silksoftwarecorp\RealTimeB2BPricing\Model\API\RequestFactory;
use Silksoftwarecorp\RealTimeB2BPricing\Model\B2BPriceItemResultFactory;

class RealtimeB2BPriceApiService
{
    /**
     * @var ClientInterface
     */
    protected ClientInterface $client;

    /**
     * @var RequestFactory
     */
    protected $requestFactory;

    /**
     * @var B2BPriceItemConverter
     */
    protected $b2bPriceItemConverter;

    /**
     * @var B2BPriceItemResultInterface
     */
    protected $priceItemResult;

    /**
     * @var PriceStorageInterface
     */
    protected $storage;

    /**
     * @var DataHelper
     */
    protected $helper;

    /**
     * @var LoggerInterface
     */
    protected $logger;

    public function __construct(
        ClientInterface $client,
        RequestFactory $requestFactory,
        B2BPriceItemConverter $b2bPriceItemConverter,
        B2BPriceItemResultFactory $b2bPriceItemResultFactory,
        PriceStorageInterface $storage,
        DataHelper $helper,
        LoggerInterface $logger,
    ) {
        $this->client = $client;
        $this->requestFactory = $requestFactory;
        $this->b2bPriceItemConverter = $b2bPriceItemConverter;
        $this->priceItemResult = $b2bPriceItemResultFactory->create();
        $this->storage = $storage;
        $this->helper = $helper;
        $this->logger = $logger;
    }

    public function getPriceItems(
        $customerId,
        $locationId,
        array $skus
    ): B2BPriceItemResultInterface {
        if (!$this->helper->isActive()) {
            $this->logger->info(__('Real-time pricing service is disabled.'));

            return $this->priceItemResult;
        }

        $this->fetchB2BPricesFromApi($customerId, $locationId, $skus);
        $this->storageB2BPrices($customerId, $locationId);

        $missingPriceSkus = array_diff($skus, $this->priceItemResult->getSkus());
        foreach ($missingPriceSkus as $sku) {
            $emptyPriceItem = $this->b2bPriceItemConverter->getEmptyB2BPriceItem($sku);
            $this->priceItemResult->addItem($emptyPriceItem);
        }

        return $this->priceItemResult;
    }

    protected function fetchB2BPricesFromApi($customerId, $locationId, array $skus): void
    {
        foreach ($this->splitSkusToChunks($skus) as $chunk) {
            $this->logger->debug(__(
                'Start fetching prices from API. Info: %1',
                json_encode([
                    'customer_id' => $customerId,
                    'location_id' => $locationId,
                    'skus' => $chunk
                ])
            ));

            $request = $this->requestFactory->create([
                'customerId' => $customerId,
                'locationId' => $locationId,
                'skus' => $chunk,
            ]);

            try {
                $response = $this->client->doRequest($request);
                $this->parseResponseException($response);

                $items = $this->parseResponse($response);
                $this->priceItemResult->addItems(...$items);

                if (count($items) > 0) {
                    $this->logger->debug(__(
                        'Successfully fetching prices from API. Info: %1',
                        json_encode([
                            'customerId' => $customerId,
                            'locationId' => $locationId,
                            'skus' => $this->priceItemResult->getSkus(),
                        ])
                    ));
                }
            } catch (\Exception $e) {
                $this->logger->critical(__('Response Error: %1', $e->getMessage()));
            }
        }
    }

    /**
     * @param array $response
     * @return array
     */
    protected function parseResponse(array $response): array
    {
        $result = [];

        $items = $response['item_data'] ?? [];
        foreach ($items as $item) {
            if ($this->isValidItem($item)) {
                $priceItem = $this->b2bPriceItemConverter->convert($item);
                $result[$priceItem->getSku()] = $priceItem;
            }
        }

        return $result;
    }

    private function isValidItem(array $item): bool
    {
        $sku = $item['ItemID'] ?? null;

        return !!$sku;
    }

    /**
     * @throws ApiResponseException
     */
    protected function parseResponseException(array $response): void
    {
        if (!isset($response['item_data']) && isset($response['Status'])) {
            throw new ApiResponseException($response['Status']);
        }
    }

    /**
     * @param $customerId
     * @param $locationId
     * @return void
     */
    protected function storageB2BPrices($customerId, $locationId): void
    {
        foreach ($this->priceItemResult->getItems() as $priceItem) {
            /**@var $priceItem B2BPriceItem */
            if ($priceItem->getSku() && $priceItem->getPrice() !== null) {
                $this->storage->save(
                    $customerId,
                    $locationId,
                    $priceItem->getSku(),
                    $priceItem
                );
            }
        }
    }

    private function splitSkusToChunks(array $skus): array
    {
        return array_chunk($skus, 25);
    }
}
