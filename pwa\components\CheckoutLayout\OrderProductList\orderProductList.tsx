import { clsx } from 'clsx'
import Link from 'next/link'
import Image from 'next/image'
import { Collapse } from 'antd'
import type { CollapseProps } from 'antd'
import { useSelector } from 'react-redux'
import { useMemo, useCallback } from 'react'

import BasePrice from '@/components/BasePrice'
import ProductPrice from '@/components/ProductPrice'

import { StyledOrderProductList, StyledOrderProductItem } from './styled'

const OrderProductList = () => {
  const cartQty = useSelector((state: Store) => state.cart.cartQty)
  const cartDetail = useSelector((state: Store) => state.cart.cartDetail)
  const storeConfig = useSelector((state: any) => state.app.storeConfig)
  const suffix: string = storeConfig?.product_url ?? ''

  const cartItems: any[] = useMemo(() => {
    return cartDetail?.items ?? []
  }, [cartDetail])

  const items: CollapseProps['items'] = [
    {
      key: 'product_list',
      label: (
        <div className="quantity">
          {cartQty} {cartQty > 1 ? 'Items' : 'Item'} in Cart
        </div>
      ),
      children: (
        <div>
          {cartItems.map((item: any) => {
            const { id, product, quantity, prices } = item
            const { thumbnail, url_key, name, price_range, sku } = product
            const href = `/${url_key}${suffix}`
            return (
              <StyledOrderProductItem key={id}>
                <div className="img">
                  <Link href={href} title={thumbnail?.label ?? ''}>
                    <Image
                      className="thumbnail"
                      src={thumbnail?.url ?? ''}
                      alt={thumbnail?.label ?? ''}
                      width={120}
                      height={120}
                    />
                  </Link>
                </div>
                <div>
                  <Link className="name" href={href} title={name}>
                    <span dangerouslySetInnerHTML={{ __html: name }} />
                  </Link>
                  <p className="sku">{sku}</p>
                  <div className="product-price">
                    <span className="text-label">Price:</span>
                    <ProductPrice className="prices" {...price_range} />
                  </div>
                  <div>
                    <span className="text-label">Quantity:</span>
                    <span>{quantity}</span>
                  </div>
                  <div className="total">
                    <span className="text-label">Total:</span>
                    <BasePrice value={prices?.row_total?.value ?? 0} />
                  </div>
                </div>
              </StyledOrderProductItem>
            )
          })}
        </div>
      )
    }
  ]

  const expandIconRender = useCallback(
    (panelProps) => (
      <svg
        className={clsx({ 'is-collapse': !panelProps.isActive })}
        width="12px"
        height="7px"
        fill="currentColor"
        aria-hidden="true"
        focusable="false">
        <use xlinkHref="#icon-top" />
      </svg>
    ),
    []
  )

  return (
    <StyledOrderProductList>
      <Collapse
        expandIconPosition="end"
        defaultActiveKey={['']}
        expandIcon={expandIconRender}
        items={items}
      />
    </StyledOrderProductList>
  )
}

export default OrderProductList
