<?php

namespace Silksoftwarecorp\UnifiedArPayment\Model;

use Magento\Framework\HTTP\Client\Curl;
use Silksoftwarecorp\UnifiedArPayment\Helper\Config;
use Silksoftwarecorp\UnifiedArPayment\Helper\Logger;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Silksoftwarecorp\ERPCompany\Model\Company\ERPRepository;
use Silksoftwarecorp\UnifiedArPayment\Model\ResourceModel\BlevinsOrderPayment as BlevinsOrderPaymentResource;

class ProcessAuthorization
{
    /**
     * @var Curl
     */
    private $curl;

    /**
     * @var Config
     */
    private $configHelper;

    /**
     * @var Logger
     */
    private $logger;

    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * @var CustomerRepositoryInterface
     */
    private $customerRepository;

    /**
     * @var ERPRepository
     */
    private $erpRepository;

    private $blevinsOrderPaymentResource;

    public function __construct(
        Curl $curl,
        Config $configHelper,
        Logger $logger,
        ScopeConfigInterface $scopeConfig,
        CustomerRepositoryInterface $customerRepository,
        ERPRepository $erpRepository,
        BlevinsOrderPaymentResource $BlevinsOrderPayment,
    ) {
        $this->curl = $curl;
        $this->configHelper = $configHelper;
        $this->logger = $logger;
        $this->scopeConfig = $scopeConfig;
        $this->customerRepository = $customerRepository;
        $this->erpRepository = $erpRepository;
        $this->blevinsOrderPaymentResource = $BlevinsOrderPayment;
    }

    /**
     * Authorize a payment via UnifiedAR
     *
     * @param \Magento\Sales\Model\Order $order
     * @return array
     */
    public function doProcessAuthorization($order)
    {
        try {
            $envConfig = $this->configHelper->getEnvironmentConfig();
            $publicConfig = $this->configHelper->getPublicConfig();

            $surchargeAmount = 0;
            $allowed = false;
            $quoteId = $order->getQuoteId();
            $paymentAccountId = "";

            $rowData = $this->blevinsOrderPaymentResource->getBlePaymentDataByQuoteId($quoteId);
            if ($rowData) {
                $surchargeAmount = $rowData['unified_ar_surcharge_amount'] ?? 0;
                $allowed = isset($rowData['unified_ar_surcharge_allowed']) && $rowData['unified_ar_surcharge_allowed'] ? 'true' : 'false';
                $paymentAccountId = $rowData['payment_account_id'] ?? '';
            }

            $payment = $order->getPayment();
            $amount = $order->getGrandTotal();
            $amount = (float)$amount + (float)$surchargeAmount;
            //$paymentAccountId = $payment->getAdditionalInformation('payment_account_id');
            //$surchargeAmount = $order->getData('unified_ar_surcharge_amount');
            //$referenceNumber = $order->getIncrementId();
            $ticketNumber = $order->getIncrementId();
            $createdAt = $order->getCreatedAt();
            if ($createdAt) {
                $customerCode = date('Y-m-d', strtotime($createdAt));
            } else {
                $customerCode = date('Y-m-d');
            }

            $currentCustomerId = $order->getCustomerId();
            $customer = $this->customerRepository->getById($currentCustomerId);
            $companyAttributes = $customer->getExtensionAttributes()?->getCompanyAttributes();

            // Get the company's ERP customer ID
            $companyId = $companyAttributes->getCompanyId();
            $erpCompany = $this->erpRepository->get($companyId);
            $referenceNumber = $erpCompany->getErpCustomerId();


            // Use provided marketCode, then publicConfig, then admin config, then default
            $marketCode = null;
            if (!empty($publicConfig['market_code'])) {
                $marketCode = $publicConfig['market_code'];
            } else {
                $marketCode = $this->scopeConfig->getValue('payment/unifiedarpayment/market_code');
                if ($marketCode === null || $marketCode === '') {
                    $marketCode = '2'; // fallback default
                }
            }

            $xml = $this->buildAuthorizationXml(
                $envConfig,
                $publicConfig,
                $amount,
                $paymentAccountId,
                $surchargeAmount,
                $allowed,
                $referenceNumber,
                $ticketNumber,
                $customerCode,
                $marketCode
            );

            $response = $this->makeApiCall($envConfig['payment_account_query_endpoint'], $xml, 'CreditCardAuthorization');
            $this->saveResponseData($quoteId, $response['response']);
            return $response;
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'error_code' => 'EXCEPTION'
            ];
        }
    }

    private function buildAuthorizationXml(
        $envConfig,
        $publicConfig,
        $amount,
        $paymentAccountId,
        $surchargeAmount,
        $allowed,
        $referenceNumber,
        $ticketNumber,
        $customerCode,
        $marketCode
    ) {
        $xml = '<CreditCardAuthorization xmlns="https://transaction.elementexpress.com">';
        $xml .= '<Credentials>';
        $xml .= '<AccountID>' . htmlspecialchars($envConfig['account_id']) . '</AccountID>';
        $xml .= '<AccountToken>' . htmlspecialchars($envConfig['account_token']) . '</AccountToken>';
        $xml .= '<AcceptorID>' . htmlspecialchars($envConfig['acceptor_id']) . '</AcceptorID>';
        $xml .= '</Credentials>';
        $xml .= '<Application>';
        $xml .= '<ApplicationID>' . htmlspecialchars($envConfig['application_id']) . '</ApplicationID>';
        $xml .= '<ApplicationName>' . htmlspecialchars($envConfig['application_name']) . '</ApplicationName>';
        $xml .= '<ApplicationVersion>' . htmlspecialchars($envConfig['application_version']) . '</ApplicationVersion>';
        $xml .= '</Application>';
        $xml .= '<Terminal>';
        $xml .= '<TerminalID>N/A</TerminalID>';
        $xml .= '<CardPresentCode>3</CardPresentCode>';
        $xml .= '<CardholderPresentCode>5</CardholderPresentCode>';
        $xml .= '<CVVPresenceCode>0</CVVPresenceCode>';
        $xml .= '<CardInputCode>4</CardInputCode>';
        $xml .= '<TerminalCapabilityCode>5</TerminalCapabilityCode>';
        $xml .= '<TerminalEnvironmentCode>2</TerminalEnvironmentCode>';
        $xml .= '<MotoECICode>2</MotoECICode>';
        $xml .= '<TerminalType>3</TerminalType>';
        $xml .= '<TerminalEncryptionFormat>0</TerminalEncryptionFormat>';
        $xml .= '</Terminal>';
        $xml .= '<Transaction>';
        $xml .= '<TransactionAmount>' . $amount . '</TransactionAmount>';
        $xml .= '<SurchargeIncluded>' . $allowed . '</SurchargeIncluded>';
        $xml .= '<SurchargeAmount>' . $surchargeAmount . '</SurchargeAmount>';
        $xml .= '<ReferenceNumber>' . $referenceNumber . '</ReferenceNumber>';
        $xml .= '<TicketNumber>' . $ticketNumber . '</TicketNumber>';
        $xml .= '<CommercialCardCustomerCode>' . $customerCode . '</CommercialCardCustomerCode>';
        $xml .= '<MarketCode>' . $marketCode . '</MarketCode>';
        $xml .= '</Transaction>';
        $xml .= '<PaymentAccount>';
        $xml .= '<PaymentAccountID>' . $paymentAccountId . '</PaymentAccountID>';
        $xml .= '</PaymentAccount>';
        $xml .= '</CreditCardAuthorization>';
        return $xml;
    }

    private function makeApiCall($endpoint, $xml, $operation = 'API Call')
    {
        $this->logger->logRequest($endpoint, $xml, $operation);

        $this->curl->setOption(CURLOPT_URL, $endpoint);
        $this->curl->setOption(CURLOPT_POST, true);
        $this->curl->setOption(CURLOPT_POSTFIELDS, $xml);
        $this->curl->setOption(CURLOPT_HTTPHEADER, [
            'Content-Type: text/xml'
        ]);
        $this->curl->setOption(CURLOPT_RETURNTRANSFER, true);
        $this->curl->setOption(CURLOPT_TIMEOUT, 30);

        $this->curl->post($endpoint, $xml);
        $response = $this->curl->getBody();
        $httpCode = $this->curl->getStatus();

        $this->logger->logResponse($response, $httpCode, $operation);

        $xmlResponse = simplexml_load_string($response);
        if ($xmlResponse === false) {
            $this->logger->logError('Invalid XML response from API', ['response' => $response], $operation);
            return [
                'success' => false,
                'message' => 'Invalid XML response from API',
                'error_code' => 'INVALID_RESPONSE'
            ];
        }

        // Convert the entire response to an array
        $responseArray = json_decode(json_encode($xmlResponse), true);
        return [
            'success' => true,
            'response' => $responseArray
        ];
    }

    private function saveResponseData($quoteId, $responseArray)
    {
        $authorizationData = json_encode($responseArray);
        $this->blevinsOrderPaymentResource->updateBlePaymentAuthorizationData($quoteId, $authorizationData);
    }
}
