<?php

namespace Silksoftwarecorp\EDI\Model\Product;

use Magento\Catalog\Model\ResourceModel\Product as ProductResourceModel;

class ProductIdsReader
{
    private $productIdsBySku = [];

    /**
     * @var ProductResourceModel
     */
    private $productResource;

    /**
     * @param ProductResourceModel $productResource
     */
    public function __construct(
        ProductResourceModel $productResource
    ) {
        $this->productResource = $productResource;
    }

    public function loadProductIds(array $skus): void
    {
        $idsBySkus = $this->productResource->getProductsIdsBySkus($skus);
        foreach ($idsBySkus as $sku => $id) {
            $sku = strtolower($sku);
            $this->productIdsBySku[$sku] = $id;
        }
    }

    public function getIdBySku(string $sku): ?int
    {
        $sku = strtolower($sku);

        return $this->productIdsBySku[$sku] ?? null;
    }
}
