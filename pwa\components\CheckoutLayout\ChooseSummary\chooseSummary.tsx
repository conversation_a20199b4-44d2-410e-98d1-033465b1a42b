import Link from 'next/link'
import { Button } from 'antd'
import { isEmpty } from 'lodash-es'
import { useCallback, useMemo } from 'react'
import { useDispatch, useSelector } from 'react-redux'

import { actions as checkoutActions } from '@/store/checkout'
import { shippingMethodsDescriptionOptions } from '@/utils/constant'

import { StyledSummary } from './styled'

const ChooseSummary = () => {
  const dispatch = useDispatch()
  const shippingAddress = useSelector((state: Store) => state.checkout.shippingAddress)
  // const shippingAccount = useSelector((state: Store) => state.checkout.shippingAccount)
  const shippingMethod = useSelector((state: Store) => state.checkout.shippingMethod)

  const isInStorePickup = useMemo(() => {
    return shippingMethod?.method_code === 'in_store_pickup'
  }, [shippingMethod])

  const shippingMethodDesc = useMemo(() => {
    const methodItem = shippingMethodsDescriptionOptions.find(
      (option) => option.method_code === shippingMethod?.method_code
    )
    return methodItem?.description ?? ''
  }, [shippingMethod])

  const handleBack = useCallback(() => {
    window.scrollTo({
      behavior: 'smooth',
      left: 0,
      top: 0
    })
    dispatch(checkoutActions.setStepIndex(0))
  }, [dispatch])

  return (
    <StyledSummary>
      <h4 className="summary-title">
        <span>Fulfillment Information</span>
      </h4>
      <div className="summary-item-title">
        Fulfillment Method
        <Button type="link" className="edit-btn" onClick={handleBack}>
          <svg width="13px" height="13px" fill="currentColor" focusable="false">
            <use xlinkHref="#icon-edit" />
          </svg>
          Edit
        </Button>
      </div>
      <p>{shippingMethod?.carrier_title ?? ''}</p>
      <p>{shippingMethodDesc}</p>
      {!isEmpty(shippingAddress) && (
        <>
          <div className="summary-item-title">
            {isInStorePickup ? 'Store' : 'Company'}
            <Button type="link" className="edit-btn" onClick={handleBack}>
              <svg width="13px" height="13px" fill="currentColor" focusable="false">
                <use xlinkHref="#icon-edit" />
              </svg>
              Edit
            </Button>
          </div>
          <div className="address">
            <div>
              {shippingAddress.street.map((item) => (
                <span key={item}>{`${item} `}</span>
              ))}
            </div>
            <div>
              <span>{`${shippingAddress.city}, `}</span>
              <span>{`${shippingAddress.region.label}, `}</span>
              <span>{shippingAddress.postcode}</span>
            </div>
            <div>
              <Link className="address-telephone" href={`tel:${shippingAddress.telephone}`}>
                {shippingAddress.telephone}
              </Link>
            </div>
          </div>
        </>
      )}
    </StyledSummary>
  )
}

export default ChooseSummary
