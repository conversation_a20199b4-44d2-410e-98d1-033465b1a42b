import { memo, useState, useCallback } from 'react'
import { Checkbox } from 'antd'

import CommonInput from '@/components/Common/CommonInput'
import { normalizeIntegerNumber } from '@/utils/format'

import { StyledMixedItemInputQty } from './styled'

const MixedItemInputQty = ({ handleSaveVal }: any) => {
  const [checkedVal, setCheckedVal] = useState<any>('')
  const [value, setValue] = useState<any>(null)

  const onCheckChange = useCallback(
    (val) => {
      if (checkedVal !== val) {
        setCheckedVal(val)
        handleSaveVal(val === 'yes' ? '' : val)
        setValue('')
      }
    },
    [checkedVal, handleSaveVal]
  )

  return (
    <StyledMixedItemInputQty>
      <div className="mixed-item-box">
        <div className="mixed-item-flex">
          <Checkbox
            checked={checkedVal === 'yes'}
            onChange={() => {
              onCheckChange('yes')
            }}>
            Yes
          </Checkbox>
          <CommonInput
            value={value}
            placeholder="Qty"
            onChange={(v) => {
              const val = normalizeIntegerNumber(v?.target?.value || '')
              setValue(val)
              handleSaveVal(val)
              if (checkedVal !== 'yes') {
                setCheckedVal('yes')
              }
            }}
          />
        </div>
        <div>
          <Checkbox
            checked={checkedVal === 'no'}
            onChange={() => {
              onCheckChange('no')
            }}>
            No
          </Checkbox>
        </div>
      </div>
    </StyledMixedItemInputQty>
  )
}

export default memo(MixedItemInputQty)
