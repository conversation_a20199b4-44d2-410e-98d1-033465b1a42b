// const MAP_KEY = process.env.NEXT_PUBLIC_GOOGLE_KEY

const PageScripts = () => {
  // const dispatch = useDispatch()
  //
  // const googleMapOnLoad = useCallback(() => {
  //   dispatch(appActions.setGoogleMapLoaded(true))
  // }, [dispatch])

  return (
    <>
      {/* TODO: 移除了Script, 在地图组件中使用 GoogleApiWrapper 加载 google api */}
      {/* <Script
        async
        src={`https://maps.googleapis.com/maps/api/js?key=${MAP_KEY}&loading=async&libraries=places`}
        strategy="afterInteractive"
        onLoad={googleMapOnLoad}
      /> */}
    </>
  )
}

export default PageScripts
