import { AutoComplete } from 'antd'
import { memo, useState, useCallback, useRef, useMemo } from 'react'

import CommonButton from '@/components/Common/CommonButton'

import { StyledStoreInputSearch } from './styled'

const StoreInputSearch = ({
  options,
  handleGoogleSearch,
  handleSearch,
  handleClearOptions,
  handleSaveLocation,
  handleMoveToCenter,
  handleSelectPlace
}: any) => {
  const inputRef = useRef<any>(null)

  const [inputValue, setInputValue] = useState('')
  const [isInputActive, setIsInputActive] = useState(false)

  const isClearIconVisible = useMemo(() => {
    return inputValue && isInputActive
  }, [inputValue, isInputActive])

  const onSelect = useCallback(
    (val, option) => {
      if (option?.placeId) {
        handleSelectPlace(option?.placeId)
      } else {
        console.error('option placeId not found: ', option)
      }
    },
    [handleSelectPlace]
  )

  const onInputKeyDown = useCallback(
    (event) => {
      if (event?.target?.value && event?.key === 'Enter') {
        handleSearch(event.target.value)
      }
    },
    [handleSearch]
  )

  const handleClearInput = useCallback(() => {
    setInputValue('')
    handleClearOptions()
    handleSaveLocation()
    handleMoveToCenter()
  }, [handleClearOptions, handleSaveLocation, handleMoveToCenter])

  const handleSearchValue = useCallback(() => {
    handleSearch(inputValue)
  }, [handleSearch, inputValue])

  const handleSearchInputFocus = useCallback(() => {
    setIsInputActive(true)
  }, [])

  const handleSearchInputBlur = useCallback(() => {
    setTimeout(() => {
      setIsInputActive(false)
    }, 200)
  }, [])

  return (
    <StyledStoreInputSearch>
      <div className="input-wrapper">
        <AutoComplete
          value={inputValue}
          ref={inputRef}
          options={options}
          onSelect={onSelect}
          onChange={(val) => {
            setInputValue(val)
            handleGoogleSearch(val)
          }}
          onFocus={handleSearchInputFocus}
          onBlur={handleSearchInputBlur}
          onInputKeyDown={onInputKeyDown}
          placeholder="Search by city or zip code"
        />
        {isClearIconVisible && (
          <div className="clear-icon">
            <svg
              width="12px"
              height="12px"
              fill="currentColor"
              aria-hidden="true"
              onClick={handleClearInput}
              focusable="false">
              <use xlinkHref="#icon-drawer-close" />
            </svg>
          </div>
        )}

        <div className="reset-center-icon" aria-hidden onClick={handleMoveToCenter}>
          <svg width="20px" height="20px" fill="currentColor" aria-hidden="true" focusable="false">
            <use xlinkHref="#icon-part-search" />
          </svg>
        </div>
      </div>

      <CommonButton height={45} onClick={handleSearchValue}>
        SEARCH
      </CommonButton>
    </StyledStoreInputSearch>
  )
}

export default memo(StoreInputSearch)
