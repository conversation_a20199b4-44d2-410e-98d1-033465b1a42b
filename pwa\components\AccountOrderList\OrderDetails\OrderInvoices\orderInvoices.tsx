import { Button } from 'antd'
import { FormattedMessage } from 'react-intl'

import { useOrderInvoices } from '@/hooks/AccountOrderList'
import BasePrice from '@/components/BasePrice'
import CommonTable from '@/components/Common/CommonTable'

import {
  DetailsProductName,
  FooterWrap,
  DetailsFooterWrap,
  DetailsFooterItem,
  OrderQty,
  OrderDescription
} from '../styled'

const OrderInvoices = ({ invoices = [] }) => {
  const { total, shipping, discount, invoicesId, invoicesItems, handlePrintOrder } =
    useOrderInvoices(invoices)

  const columns = [
    {
      title: <FormattedMessage id="global.productName" />,
      dataIndex: 'product_name',
      key: 'product_name',
      render: (param, record) => {
        return (
          <DetailsProductName>
            <div>{param}</div>
            {record.order_item.selected_options.map((option) => {
              return (
                <div key={`${option.label}_${param}`}>
                  <span className="orderDetails__label">{`${option.label}: `}</span>
                  <span>{option.value}</span>
                </div>
              )
            })}
          </DetailsProductName>
        )
      }
    },
    {
      title: <FormattedMessage id="global.sku" />,
      dataIndex: 'product_sku',
      key: 'product_sku'
    },
    {
      title: <FormattedMessage id="global.price" />,
      dataIndex: 'product_sale_price',
      key: 'product_sale_price',
      render: (param) => <BasePrice value={param.value} unit />
    },
    {
      title: <FormattedMessage id="order.qtyInvoiced" />,
      dataIndex: 'quantity_invoiced',
      key: 'quantity_invoiced',
      render: (param) => <OrderQty>{param}</OrderQty>
    },
    {
      title: <FormattedMessage id="global.subtotal" />,
      dataIndex: 'product_sale_price',
      key: 'subtotal',
      render: (param, record) => <BasePrice value={param.value * record.quantity_invoiced} />
    }
  ]

  return (
    <div>
      <OrderDescription>
        <Button type="link" className="details__printAll" onClick={handlePrintOrder}>
          <FormattedMessage id="order.printAll" />
        </Button>
        <h2 className="detailsDescription__title">
          <FormattedMessage id="order.invoiceId" />
          {` ${invoicesId}`}
        </h2>
        <Button type="link" className="details__print" onClick={handlePrintOrder}>
          <FormattedMessage id="order.printInvoice" />
        </Button>
      </OrderDescription>
      <CommonTable
        lineStyle
        rowKey={(record) => record.product_sku}
        columns={columns}
        dataSource={invoicesItems}
        pagination={false}
      />
      <FooterWrap>
        <div>
          <DetailsFooterWrap>
            <DetailsFooterItem>
              <FormattedMessage id="global.subtotal" />
            </DetailsFooterItem>
            <DetailsFooterItem>
              <BasePrice value={total?.subtotal.value} />
            </DetailsFooterItem>
          </DetailsFooterWrap>
          {!!discount && (
            <DetailsFooterWrap>
              <DetailsFooterItem>
                <FormattedMessage id="order.discount" />
              </DetailsFooterItem>
              <DetailsFooterItem>
                <BasePrice value={-discount} />
              </DetailsFooterItem>
            </DetailsFooterWrap>
          )}
          {!!shipping && (
            <DetailsFooterWrap>
              <DetailsFooterItem>
                <FormattedMessage id="order.shippingHandling" />
              </DetailsFooterItem>
              <DetailsFooterItem>
                <BasePrice value={shipping} />
              </DetailsFooterItem>
            </DetailsFooterWrap>
          )}
          <DetailsFooterWrap>
            <DetailsFooterItem>
              <FormattedMessage id="order.grandTotal" />
            </DetailsFooterItem>
            <DetailsFooterItem>
              <BasePrice value={total?.grand_total.value} />
            </DetailsFooterItem>
          </DetailsFooterWrap>
        </div>
      </FooterWrap>
    </div>
  )
}

export default OrderInvoices
