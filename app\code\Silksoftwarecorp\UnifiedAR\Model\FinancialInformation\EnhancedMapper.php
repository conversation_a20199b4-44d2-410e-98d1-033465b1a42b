<?php

namespace Silksoftwarecorp\UnifiedAR\Model\FinancialInformation;

use Silksoftwarecorp\UnifiedAR\Model\Data\AbstractMapper;

/**
 * Enhanced Financial Information Data Mapper
 * 
 * Provides flexible and configurable financial information data mapping
 */
class EnhancedMapper extends AbstractMapper
{
    /**
     * Get default mapping configuration
     * 
     * @return array
     */
    protected function getDefaultMappingConfig(): array
    {
        return [
            'total_credit_limit' => [
                'source' => 'totalCreditLimit',
                'transformer' => 'float',
                'default' => 0.0
            ],
            'total_outstanding' => [
                'source' => 'totalOutstanding',
                'transformer' => 'float',
                'default' => 0.0
            ],
            'total_past_due' => [
                'source' => 'totalPastDue',
                'transformer' => 'float',
                'default' => 0.0
            ],
            'last_payment_amount' => [
                'source' => 'lastPaymentAmount',
                'transformer' => 'float',
                'default' => 0.0
            ],
            'last_payment_date' => [
                'source' => 'lastPaymentDate',
                'transformer' => 'date',
                'default' => null
            ],
            'last_invoice_amount' => [
                'source' => 'lastInvoiceAmount',
                'transformer' => 'float',
                'default' => 0.0
            ],
            'last_invoice_date' => [
                'source' => 'lastInvoiceDate',
                'transformer' => 'date',
                'default' => null
            ],
            'available_credit' => [
                'source' => 'availableCredit',
                'transformer' => 'float',
                'default' => 0.0
            ],
            'currency_code' => [
                'source' => 'currencyCode',
                'transformer' => 'string',
                'default' => 'USD'
            ],
            'credit_rating' => [
                'source' => 'creditRating',
                'transformer' => 'string',
                'default' => null
            ],
            'payment_terms' => [
                'source' => 'paymentTerms',
                'transformer' => 'string',
                'default' => null
            ],
            'account_status' => [
                'source' => 'accountStatus',
                'transformer' => 'account_status',
                'default' => 'active'
            ]
        ];
    }

    /**
     * Get default validation rules
     * 
     * @return array
     */
    protected function getDefaultValidationRules(): array
    {
        return [
            'totalCreditLimit' => [
                'required' => false,
                'type' => 'float'
            ],
            'totalOutstanding' => [
                'required' => false,
                'type' => 'float'
            ]
        ];
    }

    /**
     * Get default transformers
     * 
     * @return array
     */
    protected function getDefaultTransformers(): array
    {
        $transformers = parent::getDefaultTransformers();
        
        // Add financial-specific transformers
        $transformers['account_status'] = function ($value) {
            $statusMap = [
                'active' => 'active',
                'inactive' => 'inactive',
                'suspended' => 'suspended',
                'hold' => 'on_hold',
                'closed' => 'closed',
                'review' => 'under_review'
            ];
            
            $normalizedValue = strtolower(trim($value ?? ''));
            return $statusMap[$normalizedValue] ?? 'active';
        };

        return $transformers;
    }

    /**
     * Post-process mapped data
     * 
     * @param array $result Mapped data
     * @param array $originalData Original source data
     * @param array $options Mapping options
     * @return array
     */
    protected function postProcess(array $result, array $originalData, array $options = []): array
    {
        // Calculate available credit if not provided
        if (!isset($result['available_credit']) || $result['available_credit'] === 0.0) {
            $result['available_credit'] = max(0, $result['total_credit_limit'] - $result['total_outstanding']);
        }

        // Add computed financial metrics
        if (isset($options['include_computed']) && $options['include_computed']) {
            $result = $this->addComputedMetrics($result, $originalData, $options);
        }

        // Format amounts if requested
        if (isset($options['format_amounts']) && $options['format_amounts']) {
            $result = $this->formatAmounts($result, $options);
        }

        return $result;
    }

    /**
     * Add computed financial metrics
     * 
     * @param array $result Mapped data
     * @param array $originalData Original source data
     * @param array $options Mapping options
     * @return array
     */
    protected function addComputedMetrics(array $result, array $originalData, array $options = []): array
    {
        // Credit utilization percentage
        if ($result['total_credit_limit'] > 0) {
            $result['credit_utilization_percentage'] = round(($result['total_outstanding'] / $result['total_credit_limit']) * 100, 2);
        } else {
            $result['credit_utilization_percentage'] = 0;
        }

        // Payment performance indicators
        if ($result['last_payment_date']) {
            $lastPaymentDays = ceil((time() - strtotime($result['last_payment_date'])) / 86400);
            $result['days_since_last_payment'] = $lastPaymentDays;
        } else {
            $result['days_since_last_payment'] = null;
        }

        // Outstanding ratio
        if ($result['total_credit_limit'] > 0) {
            $result['outstanding_ratio'] = round($result['total_outstanding'] / $result['total_credit_limit'], 4);
        } else {
            $result['outstanding_ratio'] = 0;
        }

        // Past due ratio
        if ($result['total_outstanding'] > 0) {
            $result['past_due_ratio'] = round($result['total_past_due'] / $result['total_outstanding'], 4);
        } else {
            $result['past_due_ratio'] = 0;
        }

        return $result;
    }

    /**
     * Format amounts with currency
     * 
     * @param array $result Mapped data
     * @param array $options Formatting options
     * @return array
     */
    protected function formatAmounts(array $result, array $options = []): array
    {
        $currency = $result['currency_code'] ?? $options['default_currency'] ?? 'USD';

        $amountFields = [
            'total_credit_limit',
            'total_outstanding', 
            'total_past_due',
            'last_payment_amount',
            'last_invoice_amount',
            'available_credit'
        ];
        
        foreach ($amountFields as $field) {
            if (isset($result[$field])) {
                $result[$field . '_formatted'] = $this->formatCurrency($result[$field], $currency);
            }
        }

        return $result;
    }

    /**
     * Format currency amount
     * 
     * @param float $amount
     * @param string $currency
     * @return string
     */
    protected function formatCurrency(float $amount, string $currency): string
    {
        $symbols = [
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'JPY' => '¥'
        ];

        $symbol = $symbols[$currency] ?? $currency . ' ';
        return $symbol . number_format($amount, 2);
    }

    /**
     * Legacy execute method for backward compatibility
     * 
     * @param array $financialInfo
     * @return array
     */
    public function execute(array $financialInfo): array
    {
        return $this->map($financialInfo);
    }
} 