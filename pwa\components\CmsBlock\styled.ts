import styled from '@emotion/styled'

export const StyledCmsBlock = styled.div`
  position: relative;

  ul {
    padding-left: 40px;

    > li {
      margin-bottom: 5px;
      list-style-type: disc;

      &:last-of-type {
        margin-bottom: 0;
      }
    }
  }

  @media (min-width: ${({ theme }) => theme.breakPoint.sm + 1}px) {
    .cms-block__desktop-hide {
      display: none !important;
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    .cms-block__mobile-hide {
      display: none !important;
    }
  }
`
