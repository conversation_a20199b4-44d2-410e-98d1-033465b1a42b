import { memo } from 'react'
import { Table } from 'antd'
import { clsx } from 'clsx'

import { useAppMediaQuery } from '@/packages/hooks'

import MobileTable from './MobileTable'
import CommonTableFooter from '../CommonTableFooter'
import { StyledCommonTable, StyledCommonTableMobile } from './styled'

const CommonTable = ({
  lineStyle = false,
  current = 1,
  pageSize = 10,
  total = 0,
  totalCountVisible = false,
  onPageChange,
  onPageSizeChange,
  totalTextLabel,
  ...tableProps
}) => {
  const { isMobile } = useAppMediaQuery()
  return (
    <>
      {isMobile ? (
        <StyledCommonTableMobile>
          <MobileTable
            rowKey={(record) => record.key}
            columns={tableProps.columns}
            dataSource={tableProps.dataSource}
            rowClassName={tableProps.rowClassName}
          />
        </StyledCommonTableMobile>
      ) : (
        <StyledCommonTable className={clsx('common-table-component', { 'line-style': lineStyle })}>
          <Table className="common-table" pagination={false} {...tableProps} />
        </StyledCommonTable>
      )}
      {total > 0 && (
        <CommonTableFooter
          total={total}
          current={current}
          pageSize={pageSize}
          totalTextLabel={totalTextLabel}
          onPageChange={onPageChange}
          onPageSizeChange={onPageSizeChange}
          totalCountVisible={totalCountVisible}
        />
      )}
    </>
  )
}

export default memo(CommonTable)
