<?php

declare(strict_types=1);

namespace Silksoftwarecorp\RequistionGraphQl\Model\Resolver\List;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\Api\FilterBuilder;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Api\SortOrderBuilder;
use Magento\RequisitionList\Api\Data\RequisitionListInterface;
use Magento\RequisitionList\Model\RequisitionListRepository;
use Magento\Framework\GraphQl\Query\Uid as IdEncoder;

class TotalCount implements ResolverInterface
{

    /**
     * @var RequisitionListRepository
     */
    private $repository;

    /**
     * @var FilterBuilder
     */
    private $filterBuilder;

    /**
     * @var SortOrderBuilder
     */
    private $sortOrderBuilder;

    /**
     * @var SearchCriteriaBuilder
     */
    private $searchCriteriaBuilder;

    /**
     * @var IdEncoder
     */
    private $idEncoder;

    /**
     * @param RequisitionListRepository $repository
     * @param FilterBuilder $filterBuilder
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param SortOrderBuilder $sortOrderBuilder
     * @param IdEncoder $idEncoder
     */
    public function __construct(
        RequisitionListRepository $repository,
        FilterBuilder $filterBuilder,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        SortOrderBuilder $sortOrderBuilder,
        IdEncoder $idEncoder
    ) {
        $this->repository = $repository;
        $this->filterBuilder = $filterBuilder;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->sortOrderBuilder = $sortOrderBuilder;
        $this->idEncoder  = $idEncoder;
    }


           /**
     * @inheritDoc
     */
    public function resolve(
        Field $field,
              $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ):int {

        $customerId = (int)$context->getUserId();

        $builderCount = $this->getFilters($customerId, true, $args);
        $countLists = $this->repository->getList($builderCount->create())->getTotalCount();

         return (int)$countLists;
    }


    /**
     * Get Filters
     *
     * @param int $customerId
     * @param bool $isTotal
     * @param array $args
     * @return SearchCriteriaBuilder
     *
     * @SuppressWarnings(PHPMD.CyclomaticComplexity)
     */
    private function getFilters(int $customerId, bool $isTotal, array $args = [])
    {
        $filtersGiven = isset($args['filter']) ? $args['filter'] : [];

        $filters = [];

        if (!$isTotal) {
            if (isset($filtersGiven['uids'])) {
                if (key($filtersGiven['uids']) == 'eq' || key($filtersGiven['uids']) == 'in') {
                    $reqIds = [];
                    if (is_array($filtersGiven['uids'][key($filtersGiven['uids'])])) {
                        foreach ($filtersGiven['uids'][key($filtersGiven['uids'])] as $id) {
                            $reqIds[] = $this->idEncoder->decode($id);
                        }
                    } else {
                        $reqIds = $this->idEncoder->decode($filtersGiven['uids'][key($filtersGiven['uids'])]);
                    }

                    $filters[] = $this->filterBuilder
                        ->setField(RequisitionListInterface::REQUISITION_LIST_ID)
                        ->setConditionType(key($filtersGiven['uids']))
                        ->setValue($reqIds)
                        ->create();
                }
            }

            if (isset($filtersGiven['name'])) {
                if (key($filtersGiven['name']) == 'match') {
                    $filters[] = $this->filterBuilder
                        ->setField(RequisitionListInterface::NAME)
                        ->setConditionType('like')
                        ->setValue($filtersGiven['name'][key($filtersGiven['name'])])
                        ->create();
                }
            }
            return $this->searchCriteriaBuilder
                ->addFilter('customer_id', $customerId)
                ->addFilters($filters);
        } else {
            return $this->searchCriteriaBuilder
                ->addFilter('customer_id', $customerId);
        }
    }
}
