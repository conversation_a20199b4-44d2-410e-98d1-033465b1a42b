import { gql } from '@apollo/client'
import type { DocumentNode } from '@apollo/client'

export const comparableItem: DocumentNode = gql`
  fragment comparableItem on ComparableItem {
    uid
    attributes {
      code
      value
    }
    product {
      stock_status
      url_key
      name
      sku
      small_image {
        label
        position
        url
      }
      __typename
    }
  }
`
