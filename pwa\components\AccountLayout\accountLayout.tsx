import Link from 'next/link'
import { LineContainer } from '@ranger-theme/ui'
import { clsx } from 'clsx'
import type { FC, ReactNode } from 'react'

import { useAccountLayout } from '@/hooks/AccountLayout'
import Breadcrumb from '@/components/Breadcrumb'
import { StyledAccountLayout, StyledMenus } from './styled'

interface AccountLayoutProps {
  children: ReactNode
  title: string
}

const AccountLayout: FC<AccountLayoutProps> = ({ children, title }) => {
  const { activeKey, isB2b, requisitionListsDisabled } = useAccountLayout()
  const crumbs: any[] = [{ url: '/account', name: 'My Account' }, { name: title }]

  return (
    <>
      <Breadcrumb items={crumbs} />
      <LineContainer>
        <StyledAccountLayout>
          <StyledMenus>
            <h4>My Account</h4>
            <ul>
              <li className={clsx({ active: activeKey === '/account' })}>
                <Link href="/account">
                  <span>Dashboard</span>
                </Link>
              </li>
              <li className={clsx({ active: activeKey === '/account/orders' })}>
                <Link href="/account/orders">
                  <span>My Orders</span>
                </Link>
              </li>
              {!requisitionListsDisabled && (
                <li className={clsx({ active: activeKey === '/account/requisition-lists' })}>
                  <Link href="/account/requisition-lists/requisition-lists">
                    <span>Requisition Lists</span>
                  </Link>
                </li>
              )}
            </ul>
            <ul>
              <li className={clsx({ active: activeKey === '/account/address' })}>
                <Link href="/account/address">
                  <span>Address Book</span>
                </Link>
              </li>
              <li className={clsx({ active: activeKey === '/account/information' })}>
                <Link href="/account/information">
                  <span>Account Information</span>
                </Link>
              </li>
            </ul>
            {isB2b && (
              <ul>
                <li className={clsx({ active: activeKey === '/account/company-structure' })}>
                  <Link href="/account/company-structure">
                    <span>Company Users</span>
                  </Link>
                </li>
              </ul>
            )}
          </StyledMenus>
          <div className="wrapper">{children}</div>
        </StyledAccountLayout>
      </LineContainer>
    </>
  )
}

export default AccountLayout
