import styled from '@emotion/styled'

export const StyledTitle = styled.h1`
  margin-top: 32px;
  font-weight: 700;
  font-size: 40px;
  line-height: 48px;
  letter-spacing: 0;

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    padding: 0 16px;
    margin-top: 7px;
    margin-bottom: 20px;
    font-weight: 700;
    font-size: 30px;
    line-height: 38px;
  }
`

export const StyledCartLayout = styled.div`
  display: grid;
  grid-template-columns: 1fr 377px;
  grid-column-gap: 72px;
  justify-content: space-between;
  align-items: flex-start;
  padding-bottom: 100px;

  .empty-cart {
    margin-top: 24px;
    display: flex;
    justify-content: flex-end;

    button {
      width: 157px;
      height: 44px;
      border-radius: 3px;

      &:not(:disabled) {
        border: 2px solid #003865 !important;
      }

      &:disabled span {
        opacity: 0.3;
      }

      span {
        font-weight: 700;
        font-size: 14px;
        line-height: 20px;
        letter-spacing: 0.01em;
        color: var(--color-font);
      }
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    display: flex;
    flex-direction: column-reverse;
    padding: 0 16px 60px;

    & > div {
      width: 100%;
    }

    .empty-cart {
      display: block;
      margin-top: 17px;

      button {
        width: 100%;
        height: 44px;
      }
    }
  }
`

export const StyledCartSummary = styled.div`
  padding: 24px;
  background-color: #f5f5f5;

  h3 {
    font-weight: 700;
    font-size: 24px;
    line-height: 30px;
    letter-spacing: 0;
  }
`

export const StyledEmpty = styled.div`
  display: flex;
  height: 200px;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 22px;

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    padding: 0 16px;
    text-align: center;
  }
`

export const StyledGetSupport = styled.div`
  margin-top: 24px;
  background: #003865;
  border-radius: 4px;

  .get-support-block {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 122px;
    padding: 0 35px;

    div,
    span {
      font-weight: 700;
      font-size: 17px;
      line-height: 24px;
      letter-spacing: 0.03em;
      text-align: center;
      color: #fff;
    }

    .phone-number {
      text-decoration: underline;
    }

    svg {
      margin-bottom: 12px;
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    margin-top: 38px;
  }
`
