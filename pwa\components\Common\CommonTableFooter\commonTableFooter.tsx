import { memo, useMemo } from 'react'

import CommonPagination from '../CommonPagination'
import CommonPageSize from '../CommonPageSize'
import { StyledCommonTableFooter } from './styled'

const CommonTableFooter = ({
  current = 1,
  pageSize = 10,
  total = 0,
  totalCountVisible = false,
  totalTextLabel = 'Items',
  onPageChange,
  onPageSizeChange
}) => {
  const totalText = useMemo(() => {
    if (totalCountVisible) {
      const start = (current - 1) * pageSize + 1
      const end = current * pageSize > total ? total : current * pageSize
      return `${totalTextLabel} ${start} to ${end} of ${total} total`
    }
    return `${total} Item${total > 1 ? 's' : ''}`
  }, [current, pageSize, total, totalCountVisible, totalTextLabel])
  return (
    <StyledCommonTableFooter>
      <div className="table-footer-total">{total > 0 && <span>{totalText}</span>}</div>
      {total > 0 && (
        <>
          <CommonPagination
            current={current}
            pageSize={pageSize}
            total={total}
            onChange={onPageChange}
          />
          <CommonPageSize pageSize={pageSize} onChange={onPageSizeChange} />
        </>
      )}
    </StyledCommonTableFooter>
  )
}

export default memo(CommonTableFooter)
