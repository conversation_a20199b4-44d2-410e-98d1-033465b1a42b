import { memo } from 'react'

import { MediaLayout } from '@/ui'

import CartItem from '../CartItem'
import { StyledCartList } from './styled'

const CartList = ({ cartList = [] }) => {
  return (
    <div>
      <StyledCartList>
        <div>Product</div>
        <MediaLayout>
          <div className="cart-head__price">Price</div>
          <div className="cart-head__qty">Quantity</div>
          <div className="cart-head__price">Total</div>
        </MediaLayout>
      </StyledCartList>
      {cartList.length > 0 && (
        <>
          {cartList.map((cartItem: any) => {
            return <CartItem key={cartItem.id} cartItem={cartItem} />
          })}
        </>
      )}
    </div>
  )
}

export default memo(CartList)
