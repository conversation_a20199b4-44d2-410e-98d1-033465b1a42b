import styled from '@emotion/styled'

export const StyledCategoryDetail = styled.div`
  .container {
    padding: 40px 0 50px;
  }

  h2 {
    display: block;
    margin-bottom: 0;
    font-weight: 700;
    font-size: 40px;
    line-height: 48px;
    letter-spacing: 0;
  }

  .category-description {
    margin-top: 16px;
    font-weight: 400;
    font-size: 15px;
    line-height: 21px;
    letter-spacing: 0.01em;

    &.is-all-visible {
      span {
        -webkit-line-clamp: initial;
        max-height: initial;
      }
    }

    span {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      overflow: hidden;
      -webkit-line-clamp: 4;
      max-height: calc(1.5em * 4);
    }
  }

  .read-more {
    display: inline-block;
    margin-top: 6px;
    font-weight: 700;
    font-size: 15px;
    line-height: 21px;
    letter-spacing: 0.01em;
    text-decoration: underline;
    text-decoration-style: solid;
    text-decoration-thickness: 0;
    color: var(--color-primary);
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    .container {
      padding: 6px 16px 16px;
    }
  }
`
