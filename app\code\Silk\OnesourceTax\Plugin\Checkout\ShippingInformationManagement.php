<?php
namespace Silk\OnesourceTax\Plugin\Checkout;

use Magento\Checkout\Api\Data\ShippingInformationInterface;
use Magento\Checkout\Model\ShippingInformationManagement as BaseShippingInformationManagement;
use Silk\OnesourceTax\Model\Config;
use Silk\OnesourceTax\Model\Tax\Calculator;
use Magento\Quote\Model\QuoteRepository;

class ShippingInformationManagement
{
    protected $config;
    protected $taxCalculator;
    protected $quoteRepository;

    public function __construct(
        Config $config,
        Calculator $taxCalculator,
        QuoteRepository $quoteRepository
    ) {
        $this->config = $config;
        $this->taxCalculator = $taxCalculator;
        $this->quoteRepository = $quoteRepository;
    }

    public function beforeSaveAddressInformation(
        BaseShippingInformationManagement $subject,
        $cartId,
        ShippingInformationInterface $addressInformation
    ) {
        if (!$this->config->isActive()) {
            return [$cartId, $addressInformation];
        }

        $quote = $this->quoteRepository->getActive($cartId);
        $shippingAssignment = $addressInformation->getShippingAssignment();


        try {
            $response = $this->taxCalculator->calculate($quote, $shippingAssignment);
            
            if (isset($response['documents'][0]['lines'])) {
                $items = $quote->getAllItems();
                
                foreach ($response['documents'][0]['lines'] as $line) {
                    $lineNumber = $line['lineNumber'];
                    $lineTaxAmount = $line['totalTaxAmount'] ?? 0;
                    $taxPercent = $line['effectiveTaxRate']['TaxRate'] * 100 ?? 0;
                    
                    foreach ($items as $item) {
                        if ($item->getId() == $lineNumber) {
                            $item->setTaxAmount($lineTaxAmount);
                            $item->setBaseTaxAmount($lineTaxAmount);
                            $item->setTaxPercent($taxPercent);
                            $item->setPriceInclTax($item->getPrice() + ($lineTaxAmount / $item->getQty()));
                            $item->setRowTotalInclTax($item->getRowTotal() + $lineTaxAmount);
                            break;
                        }
                    }
                }
                
                $this->quoteRepository->save($quote);
            }
        } catch (\Exception $e) {
            //$this->logger->error($e->getMessage());
        }

        return [$cartId, $addressInformation];
    }
}