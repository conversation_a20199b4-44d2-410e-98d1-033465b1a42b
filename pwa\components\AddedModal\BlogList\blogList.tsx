import { nanoid } from 'nanoid'

import { StyledBlogs, StyledBlogList } from './styled'

const BlogList = () => {
  const blogs: any[] = [
    {
      label: 'xx-xx-xx',
      name: 'Article Name',
      description:
        '[Article abstract] Leo adipiscing potenti condimentum aliquam maximus arcu enim vestibulum sodales.'
    },
    {
      label: 'xx-xx-xx',
      name: 'Article Name',
      description:
        '[Article abstract] Leo adipiscing potenti condimentum aliquam maximus arcu enim vestibulum sodales.'
    },
    {
      label: 'xx-xx-xx',
      name: 'Article Name',
      description:
        '[Article abstract] Leo adipiscing potenti condimentum aliquam maximus arcu enim vestibulum sodales.'
    }
  ]

  return (
    <StyledBlogs>
      <h4>Featured Blogs</h4>
      <StyledBlogList>
        {blogs.map((blog: any) => {
          return (
            <div className="layer" key={nanoid()}>
              <div className="image">
                <span className="label">{blog.label}</span>
              </div>
              <h6 dangerouslySetInnerHTML={{ __html: blog.name }} />
              <div className="description" dangerouslySetInnerHTML={{ __html: blog.description }} />
            </div>
          )
        })}
      </StyledBlogList>
    </StyledBlogs>
  )
}

export default BlogList
