<?php
namespace Silk\OnesourceTax\Model;

use Magento\Framework\App\Config\ScopeConfigInterface;

class Config
{
    const XML_PATH_ACTIVE = 'tax/onesource/active';
    const XML_PATH_MODE = 'tax/onesource/mode';
    const XML_PATH_CLIENT_ID = 'tax/onesource/client_id';
    const XML_PATH_CLIENT_SECRET = 'tax/onesource/client_secret';
    const XML_PATH_SCOPES = 'tax/onesource/scopes';
    const XML_PATH_COMPANY_ROLE = 'tax/onesource/company_role';
    const XML_PATH_EXTERNAL_COMPANY_ID = 'tax/onesource/external_company_id';
    const XML_PATH_TOKEN_URL = 'tax/onesource/%s/token_url';
    const XML_PATH_CALCULATE_URL = 'tax/onesource/%s/calculate_url';
    const XML_PATH_PROCESSING_OPTIONS = 'tax/onesource/processing_options';

    protected $scopeConfig;

   /**
     * @var  \Magento\Framework\Encryption\Encryptor
     */
    protected $encryptor;

    public function __construct(
        ScopeConfigInterface $scopeConfig, 
         \Magento\Framework\Encryption\Encryptor $encryptor
         )
    {
        $this->scopeConfig = $scopeConfig;
        $this->encryptor = $encryptor;
    }

    public function isActive($storeId = null)
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_ACTIVE,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getMode($storeId = null)
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_MODE,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getClientId($storeId = null)
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_CLIENT_ID,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getClientSecret($storeId = null)
    {
        $secret =  $this->scopeConfig->getValue(
            self::XML_PATH_CLIENT_SECRET,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
            $storeId
        );

        return $this->encryptor->decrypt($secret);
    }

    public function getScopes($storeId = null)
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_SCOPES,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getCompanyRole($storeId = null)
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_COMPANY_ROLE,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getExternalCompanyId($storeId = null)
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_EXTERNAL_COMPANY_ID,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getTokenUrl($storeId = null)
    {
        $mode = $this->getMode($storeId);
        return $this->scopeConfig->getValue(
            sprintf(self::XML_PATH_TOKEN_URL, $mode),
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getCalculateUrl($storeId = null)
    {
        $mode = $this->getMode($storeId);
        return $this->scopeConfig->getValue(
            sprintf(self::XML_PATH_CALCULATE_URL, $mode),
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getProcessingOptions($storeId = null)
    {
        return [
            'chargeIncludedInAmounts' => (bool)$this->scopeConfig->getValue(
                self::XML_PATH_PROCESSING_OPTIONS . '/charge_included_in_amounts',
                \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
                $storeId
            ),
            'chargeResponse' => $this->scopeConfig->getValue(
                self::XML_PATH_PROCESSING_OPTIONS . '/charge_response',
                \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
                $storeId
            ),
            'responseSummary' => $this->scopeConfig->getValue(
                self::XML_PATH_PROCESSING_OPTIONS . '/response_summary',
                \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
                $storeId
            ),
            'documentAmountType' => $this->scopeConfig->getValue(
                self::XML_PATH_PROCESSING_OPTIONS . '/document_amount_type',
                \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
                $storeId
            )
        ];
    }
}