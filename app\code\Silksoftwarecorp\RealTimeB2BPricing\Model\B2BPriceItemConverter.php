<?php

namespace Silksoftwarecorp\RealTimeB2BPricing\Model;

use Silksoftwarecorp\RealTimeB2BPricing\Api\Data\B2BPriceItemInterface;
use Silksoftwarecorp\RealTimeB2BPricing\Api\Data\B2BPriceItemInterfaceFactory;

class B2BPriceItemConverter
{
    protected array $replaceFields = [
        'ItemID' => 'item_id',
        'Price' => 'price',
        'ItemDescription' => 'item_description',
        'ExtendedDescription' => 'extended_description',
        'CustomerPartNumber' => 'customer_part_number',
        'ManufacturerName' => 'manufacturer_name',
        'ManufacturerPartNumber' => 'manufacturer_part_number',
        'UnitOfMeasure' => 'unit_of_measure',
        'Location' => 'location',
        'LeadTime' => 'lead_time',
        'QuantityOnHand' => 'quantity_on_hand',
        'QuantityBackordered' => 'quantity_backordered',
        'QuantityAvailable' => 'quantity_available',
        'LastInvoiceDate' => 'last_invoice_date',
    ];

    /**
     * @var B2BPriceItemInterfaceFactory
     */
    protected $b2bPriceItemFactory;

    /**
     * @param B2BPriceItemInterfaceFactory $b2bPriceItemFactory
     */
    public function __construct(B2BPriceItemInterfaceFactory $b2bPriceItemFactory)
    {
        $this->b2bPriceItemFactory = $b2bPriceItemFactory;
    }

    public function convert(array $itemData): B2bPriceItemInterface
    {
        return $this->b2bPriceItemFactory->create([
            'data' => $this->parseItemData($itemData),
        ]);
    }

    public function parseItemData(array $data): array
    {
        $result = [];
        foreach ($data as $key => $value) {
            if (isset($this->replaceFields[$key])) {
                $newKey = $this->replaceFields[$key];
                $result[$newKey] = $value;
            } else {
                $result[$key] = $value;
            }
        }

        return $result;
    }

    public function getEmptyB2BPriceItem($sku): B2BPriceItemInterface
    {
        return $this->b2bPriceItemFactory->create([
            'data' => [
                'item_id' => $sku,
                'is_empty_item' => true,
            ]
        ]);
    }
}
