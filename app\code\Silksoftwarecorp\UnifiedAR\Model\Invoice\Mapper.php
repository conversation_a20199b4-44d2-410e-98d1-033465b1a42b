<?php

namespace Silksoftwarecorp\UnifiedAR\Model\Invoice;

class Mapper
{
    public function execute(array $invoice): array
    {
        return [
            'invoice_number' => $invoice['invoiceNumber'] ?? null,
            'order_number' => $invoice['orderNumber'] ?? null,
            'order_date' => $invoice['orderDate'] ?? null,
            'total_amount' => (float)($invoice['totalAmount'] ?? 0),
            'po_number' => $invoice['customerPoNumber'] ?? null,
            'paid_in_full' => (bool)($invoice['paidInFull'] ?? false),
            'pdf_url' => $invoice['pdfUrl'] ?? null,
            'html_url' =>  $invoice['htmlUrl'] ?? null,
        ];
    }
}
