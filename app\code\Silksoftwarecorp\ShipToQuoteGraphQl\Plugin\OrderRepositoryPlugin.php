<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */
declare(strict_types=1);

namespace Silksoftwarecorp\ShipToQuoteGraphQl\Plugin;

use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Sales\Api\Data\OrderExtensionFactory;

/**
 * Plugin to handle order extension attributes
 */
class OrderRepositoryPlugin
{
    /**
     * @var OrderExtensionFactory
     */
    private $orderExtensionFactory;

    /**
     * @param OrderExtensionFactory $orderExtensionFactory
     */
    public function __construct(OrderExtensionFactory $orderExtensionFactory)
    {
        $this->orderExtensionFactory = $orderExtensionFactory;
    }

    /**
     * Add extension attributes to order after get
     *
     * @param OrderRepositoryInterface $subject
     * @param OrderInterface $order
     * @return OrderInterface
     */
    public function afterGet(OrderRepositoryInterface $subject, OrderInterface $order): OrderInterface
    {
        $this->setExtensionAttributes($order);
        return $order;
    }

    /**
     * Add extension attributes to order after getList
     *
     * @param OrderRepositoryInterface $subject
     * @param \Magento\Sales\Api\Data\OrderSearchResultsInterface $searchResults
     * @return \Magento\Sales\Api\Data\OrderSearchResultsInterface
     */
    public function afterGetList(OrderRepositoryInterface $subject, $searchResults)
    {
        foreach ($searchResults->getItems() as $order) {
            $this->setExtensionAttributes($order);
        }
        return $searchResults;
    }

    /**
     * Save extension attributes before save
     *
     * @param OrderRepositoryInterface $subject
     * @param OrderInterface $order
     * @return array
     */
    public function beforeSave(OrderRepositoryInterface $subject, OrderInterface $order): array
    {
        $extensionAttributes = $order->getExtensionAttributes();
        
        if ($extensionAttributes !== null) {
            if ($extensionAttributes->getShipToId() !== null) {
                $order->setData('ship_to_id', $extensionAttributes->getShipToId());
            }
            if ($extensionAttributes->getCustomerContactId() !== null) {
                $order->setData('customer_contact_id', $extensionAttributes->getCustomerContactId());
            }
        }

        return [$order];
    }

    /**
     * Set extension attributes on order
     *
     * @param OrderInterface $order
     * @return void
     */
    private function setExtensionAttributes(OrderInterface $order): void
    {
        $extensionAttributes = $order->getExtensionAttributes();
        if ($extensionAttributes === null) {
            $extensionAttributes = $this->orderExtensionFactory->create();
        }

        if ($order->getData('ship_to_id') !== null) {
            $extensionAttributes->setShipToId($order->getData('ship_to_id'));
        }

        if ($order->getData('customer_contact_id') !== null) {
            $extensionAttributes->setCustomerContactId($order->getData('customer_contact_id'));
        }

        $order->setExtensionAttributes($extensionAttributes);
    }
}



