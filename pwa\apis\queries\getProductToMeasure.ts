import { gql } from '@apollo/client'
import type { DocumentNode } from '@apollo/client'

export const GET_PRODUCT_TO_MEASURE: DocumentNode = gql`
  query getProductHowToMeasure($filter: ProductAttributeFilterInput) {
    products(filter: $filter) {
      items {
        id
        name
        sku
        how_to_measure
        categories {
          name
          how_to_measure
        }
      }
    }
  }
`
