<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */
declare(strict_types=1);

namespace Silksoftwarecorp\Inventory\Model;

use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\Api\SearchResultsInterface;
use Magento\Framework\Api\SearchResultsInterfaceFactory;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;
use Silksoftwarecorp\Inventory\Api\BranchManagerProfileRepositoryInterface;
use Silksoftwarecorp\Inventory\Api\Data\BranchManagerProfileInterface;
use Silksoftwarecorp\Inventory\Model\ResourceModel\BranchManagerProfile as ResourceModel;
use Silksoftwarecorp\Inventory\Model\ResourceModel\BranchManagerProfile\CollectionFactory;

/**
 * Branch Manager Profile Repository
 */
class BranchManagerProfileRepository implements BranchManagerProfileRepositoryInterface
{
    /**
     * @var BranchManagerProfileFactory
     */
    private $branchManagerProfileFactory;

    /**
     * @var CollectionFactory
     */
    private $collectionFactory;

    /**
     * @var SearchResultsInterfaceFactory
     */
    private $searchResultsFactory;

    /**
     * @var CollectionProcessorInterface
     */
    private $collectionProcessor;

    /**
     * @var ResourceModel
     */
    private $resourceModel;

    /**
     * @param BranchManagerProfileFactory $branchManagerProfileFactory
     * @param CollectionFactory $collectionFactory
     * @param SearchResultsInterfaceFactory $searchResultsFactory
     * @param CollectionProcessorInterface $collectionProcessor
     * @param ResourceModel $resourceModel
     */
    public function __construct(
        BranchManagerProfileFactory $branchManagerProfileFactory,
        CollectionFactory $collectionFactory,
        SearchResultsInterfaceFactory $searchResultsFactory,
        CollectionProcessorInterface $collectionProcessor,
        ResourceModel $resourceModel
    ) {
        $this->branchManagerProfileFactory = $branchManagerProfileFactory;
        $this->collectionFactory = $collectionFactory;
        $this->searchResultsFactory = $searchResultsFactory;
        $this->collectionProcessor = $collectionProcessor;
        $this->resourceModel = $resourceModel;
    }

    /**
     * @inheritdoc
     */
    public function save(BranchManagerProfileInterface $branchManagerProfile): BranchManagerProfileInterface
    {
        try {
            $this->resourceModel->save($branchManagerProfile);
        } catch (\Exception $exception) {
            throw new CouldNotSaveException(__('Could not save the branch manager profile: %1', $exception->getMessage()));
        }
        return $branchManagerProfile;
    }

    /**
     * @inheritdoc
     */
    public function getById(int $id): BranchManagerProfileInterface
    {
        $branchManagerProfile = $this->branchManagerProfileFactory->create();
        $this->resourceModel->load($branchManagerProfile, $id);
        if (!$branchManagerProfile->getId()) {
            throw new NoSuchEntityException(__('Branch manager profile with id "%1" does not exist.', $id));
        }
        return $branchManagerProfile;
    }

    /**
     * @inheritdoc
     */
    public function getBySourceCode(string $sourceCode): array
    {
        $collection = $this->collectionFactory->create();
        $collection->addFieldToFilter('source_code', $sourceCode);
        return $collection->getItems();
    }

    /**
     * @inheritdoc
     */
    public function getGeneralManagerBySourceCode(string $sourceCode): ?BranchManagerProfileInterface
    {
        $collection = $this->collectionFactory->create();
        $collection->addFieldToFilter('source_code', $sourceCode)
                   ->addFieldToFilter('is_general', 1)
                   ->setPageSize(1);
        
        $items = $collection->getItems();
        return !empty($items) ? reset($items) : null;
    }

    /**
     * @inheritdoc
     */
    public function getTerritoryManagersBySourceCode(string $sourceCode): array
    {
        $collection = $this->collectionFactory->create();
        $collection->addFieldToFilter('source_code', $sourceCode)
                   ->addFieldToFilter('is_general', 0);
        return $collection->getItems();
    }

    /**
     * @inheritdoc
     */
    public function delete(BranchManagerProfileInterface $branchManagerProfile): bool
    {
        try {
            $this->resourceModel->delete($branchManagerProfile);
        } catch (\Exception $exception) {
            throw new CouldNotDeleteException(__('Could not delete the branch manager profile: %1', $exception->getMessage()));
        }
        return true;
    }

    /**
     * @inheritdoc
     */
    public function deleteById(int $id): bool
    {
        return $this->delete($this->getById($id));
    }

    /**
     * @inheritdoc
     */
    public function getList(SearchCriteriaInterface $searchCriteria)
    {
        $collection = $this->collectionFactory->create();
        $this->collectionProcessor->process($searchCriteria, $collection);
        
        $searchResults = $this->searchResultsFactory->create();
        $searchResults->setSearchCriteria($searchCriteria);
        $searchResults->setItems($collection->getItems());
        $searchResults->setTotalCount($collection->getSize());
        
        return $searchResults;
    }
} 