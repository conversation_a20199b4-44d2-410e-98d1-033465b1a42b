<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="amblog">
            <group id="search_engine">
                <field id="home_page_description" translate="label comment" type="select" sortOrder="18" 
                       showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Blog Home Page Description (CMS Block)</label>
                    <comment>Select a CMS block to display as the blog home page description.</comment>
                    <source_model>Silksoftwarecorp\Blog\Model\Config\Source\Block</source_model>
                </field>
            </group>
        </section>
    </system>
</config> 