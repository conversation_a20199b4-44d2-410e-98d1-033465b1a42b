import { FC, memo } from 'react'
import { clsx } from 'clsx'

import { useBasePrice } from '@/hooks/BasePrice'

import { StyledBasePrice } from './styled'

interface BasePriceProps {
  className?: string
  decimal?: number
  delimiter?: string
  digit?: number
  round?: boolean
  unit?: boolean
  value: number
}

const BasePrice: FC<BasePriceProps> = ({
  className = '',
  decimal = 2,
  delimiter = ',',
  digit = 3,
  round = true,
  unit = false,
  value,
  ...props
}) => {
  const { price } = useBasePrice({
    decimal,
    delimiter,
    digit,
    round,
    value
  })

  return (
    <StyledBasePrice className={clsx('price', [className])} {...props}>
      {price}
      {/* TODO: unit */}
      {unit && <span className="price-unit"> / EA</span>}
    </StyledBasePrice>
  )
}

export default memo(BasePrice)
