<?php

namespace Silksoftwarecorp\EDI\Model\Config\Source;

use Magento\Framework\Data\OptionSourceInterface;

class Environment implements OptionSourceInterface
{
    public const PRODUCTION = 'production';

    public const SANDBOX = 'sandbox';

    public function toOptionArray(): array
    {
        return [
            ['value' => self::SANDBOX, 'label' => __('Sandbox')],
            ['value' => self::PRODUCTION, 'label' => __('Production')]
        ];
    }
}
