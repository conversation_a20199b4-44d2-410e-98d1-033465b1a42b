import Link from 'next/link'
import { Form, Checkbox } from 'antd'
import { memo } from 'react'
import { clsx } from 'clsx'

import { useBillingAddress } from '@/hooks/CheckoutLayout'
import UnifiedARForm from '@/components/CheckoutLayout/UnifiedARForm'

import AddressForm from '../AddressForm'
import AddressList from '../AddressList'
import { StyledBillingAddress } from './styled'

const BillingAddress = ({ form, isUnifiedAR, handleUpdateUnifiedARData }: any) => {
  const {
    addressList,
    checked,
    handleOnChange,
    unifiedAddress,
    activeAddress: address,
    getRegionNameByCode
  } = useBillingAddress(form, isUnifiedAR)

  return (
    <StyledBillingAddress>
      <Form.Item
        name="sameas"
        valuePropName="checked"
        className={clsx('same-as-item', { 'is-disabled': !isUnifiedAR })}>
        <Checkbox onChange={handleOnChange}>My billing and shipping address are the same</Checkbox>
      </Form.Item>
      {checked ? (
        <>
          <div className="billing-address-list">
            {/* TODO: company name */}
            <div className="address-company">{address?.company ?? ''}</div>
            <div>{address?.ship_to_name ?? ''}</div>
            <div>{`${address?.street ?? ''} ${address?.street_line2 ? `, ${address?.street_line2}` : ''}`}</div>

            <div>
              <span>{`${address.city}, `}</span>
              <span>{`${getRegionNameByCode(address?.country_id, address?.region_code)}, `}</span>
              <span>{address.postcode}</span>
            </div>
            <div>
              <Link className="address-telephone" href={`tel:${address?.telephone}`}>
                {address?.telephone}
              </Link>
            </div>
          </div>
          {isUnifiedAR && (
            <UnifiedARForm
              address={unifiedAddress}
              handleUpdateUnifiedARData={handleUpdateUnifiedARData}
            />
          )}
        </>
      ) : (
        <>
          {addressList.length > 0 ? (
            <AddressList
              type="billing"
              isUnifiedAR={isUnifiedAR}
              handleUpdateUnifiedARData={handleUpdateUnifiedARData}
            />
          ) : (
            <AddressForm />
          )}
        </>
      )}
    </StyledBillingAddress>
  )
}

export default memo(BillingAddress)
