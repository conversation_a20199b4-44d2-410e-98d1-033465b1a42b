<?php

namespace Silksoftwarecorp\EDI\Model\API;

use Magento\Framework\Exception\LocalizedException;
use Silksoftwarecorp\EDI\Helper\Data as Helper;
use Silksoftwarecorp\EDI\Model\Config\Source\Environment;

class Config
{
    /**
     * @var Helper
     */
    protected $helper;

    /**
     * @param Helper $helper
     */
    public function __construct(Helper $helper)
    {
        $this->helper = $helper;
    }

    public function getApiUrl($endpoint = null, $storeId = null): string
    {
        $url = $this->helper->getSandboxApiUrl($storeId);
        if ($this->isProduction($storeId)) {
            $url = $this->helper->getApiUrl($storeId);
        }

        // Ensure base URL ends with slash when no endpoint is provided
        if ($endpoint) {
            // Remove trailing slash from base URL and add endpoint
            $url = rtrim($url, '/') . '/' . ltrim($endpoint, '/');
        } else {
            // Ensure base URL ends with slash when no endpoint
            $url = rtrim($url, '/') . '/';
        }

        return $url;
    }

    /**
     * @param $storeId
     * @return string
     *
     * @throws LocalizedException
     */
    public function getUsername($storeId = null): string
    {
        $username = $this->helper->getSandboxApiUsername($storeId);
        if ($this->isProduction($storeId)) {
            $username = $this->helper->getApiUsername($storeId);
        }

        if (empty($username)) {
            throw new LocalizedException(__(
                'Please provide a valid %1 API username.',
                $this->getEnvironment($storeId)
            ));
        }

        return $username;
    }

    /**
     * @param $storeId
     * @return string
     *
     * @throws LocalizedException
     */
    public function getPassword($storeId = null): string
    {
        $password = $this->helper->getSandboxApiPassword($storeId);
        if ($this->isProduction($storeId)) {
            $password = $this->helper->getApiPassword($storeId);
        }

        if (empty($password)) {
            throw new LocalizedException(__(
                'Please provide a valid %1 API password.',
                $this->getEnvironment($storeId)
            ));
        }

        return $password;
    }

    public function getTimeout(): int
    {
        return $this->helper->getApiTimeout();
    }

    private function isProduction($storeId = null): bool
    {
        return $this->helper->getApiEnvironment($storeId) == Environment::PRODUCTION;
    }

    private function getEnvironment($storeId = null): string
    {
        return $this->isProduction($storeId) ? 'Production' : 'Sandbox';
    }
}
