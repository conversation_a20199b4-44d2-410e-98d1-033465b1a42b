<?php

namespace Silksoftwarecorp\BlogGraphql\Model\Resolver;

use Amasty\Blog\Helper\Settings;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;

class UrlPostfix implements ResolverInterface
{
    /**
     * @var Settings
     */
    private $settings;

    public function __construct(
        Settings $settings
    ) {
        $this->settings = $settings;
    }

    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
        return $this->settings->getBlogPostfix();
    }
}
