import { isEmpty } from 'lodash-es'

import { useCompanyUsersPage } from '@/hooks/CompanyUsersPage'
import { MediaLayout } from '@/ui'

import CommonTable from '@/components/Common/CommonTable'
import CommonButton from '@/components/Common/CommonButton'
import CommonLoading from '@/components/Common/CommonLoading'
import ConfirmModal from '@/components/ConfirmModal'
import CompanyUserForm from '@/components/Account/CompanyUserForm'
import CommonPageTitle from '@/components/Common/CommonPageTitle/commonPageTitle'

import { StyledUsersContainer, StyledAddUser, StyledRemoveAction } from './styled'

const CompanyUsersPage = () => {
  const {
    isFetching,
    visible,
    users,
    userData,
    handleEditUser,
    handlePageChange,
    handleModalOpen,
    handleModalCancel,
    handleToggleView,
    handleCreateUser,
    handleRemoveUser,
    page,
    handlePageSizeChange,
    isShowALl
  } = useCompanyUsersPage()
  const userList = users?.items ?? []
  const totalCount = isShowALl ? userList.length : (users?.total_count ?? 0)

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id'
    },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (param, record) => <span>{`${record.firstname} ${record.lastname}`}</span>
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email'
    },
    {
      title: 'Role',
      dataIndex: 'role',
      key: 'role',
      render: (param) => {
        return <p>{param?.name ?? 'Company Administrator'}</p>
      }
    },
    {
      title: 'Team',
      dataIndex: 'team',
      key: 'team',
      align: 'center',
      render: (param) => {
        return <p>{param?.name ?? '-'}</p>
      }
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      render: (param) => {
        return <p className="status">{param}</p>
      }
    },
    {
      title: '',
      dataIndex: 'x',
      key: 'x',
      align: 'center',
      width: 110,
      mobileContentFull: true,
      render: (param, record) => {
        const { id } = record
        return (
          <div className="item-action">
            <CommonButton
              type="text"
              uppercase={false}
              underline
              onClick={() => {
                handleEditUser(record)
              }}>
              Edit
            </CommonButton>
            <span className="item-action-line">|</span>
            <ConfirmModal
              title="Delete this user?"
              onOk={() => {
                handleRemoveUser(id)
              }}
              trigger={
                <CommonButton type="text" uppercase={false} underline>
                  Delete
                </CommonButton>
              }>
              <StyledRemoveAction>Delete the user account and content.</StyledRemoveAction>
            </ConfirmModal>
          </div>
        )
      }
    }
  ]

  return (
    <>
      <StyledUsersContainer>
        <CommonLoading spinning={isFetching}>
          {!isEmpty(users) && (
            <>
              <MediaLayout type="mobile">
                <div>
                  <div className="tools tools-mb">
                    <CommonPageTitle title="Company Users" />
                    <CommonButton
                      type="text"
                      underline
                      uppercase={false}
                      onClick={handleToggleView}>
                      {isShowALl ? 'Show Pages' : 'Show All'}
                    </CommonButton>
                  </div>
                  <StyledAddUser>
                    <CommonButton onClick={handleModalOpen}>+ Add New User</CommonButton>
                  </StyledAddUser>
                </div>
              </MediaLayout>
              <MediaLayout>
                <div className="tools">
                  <CommonButton type="text" underline uppercase={false} onClick={handleToggleView}>
                    {isShowALl ? 'Show Pages' : 'Show All Users'}
                  </CommonButton>
                </div>
              </MediaLayout>
              <CommonTable
                rowKey={() => `random__${Math.random()}`}
                columns={columns}
                dataSource={userList}
                lineStyle
                total={isShowALl ? 0 : totalCount}
                current={page.currentPage}
                pageSize={page.pageSize}
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
              />
              {isShowALl && (
                <div className="all-users-items">
                  {totalCount} Item{totalCount > 1 ? 's' : ''}
                </div>
              )}
              <MediaLayout>
                <StyledAddUser>
                  <CommonButton onClick={handleModalOpen}>+ Add New User</CommonButton>
                </StyledAddUser>
              </MediaLayout>
              {visible && (
                <CompanyUserForm
                  userData={userData}
                  onCancel={handleModalCancel}
                  handleSubmit={handleCreateUser}
                />
              )}
            </>
          )}
        </CommonLoading>
      </StyledUsersContainer>
    </>
  )
}

export default CompanyUsersPage
