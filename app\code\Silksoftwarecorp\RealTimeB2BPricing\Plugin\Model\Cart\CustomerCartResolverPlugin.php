<?php

namespace Silksoftwarecorp\RealTimeB2BPricing\Plugin\Model\Cart;

use Magento\Quote\Model\Cart\CustomerCartResolver as Subject;
use Magento\Quote\Model\Quote;
use Silksoftwarecorp\RealTimeB2BPricing\Model\Quote\UpdateExpiredPriceTrigger;

class CustomerCartResolverPlugin
{
    /**
     * @var UpdateExpiredPriceTrigger
     */
    protected $updateExpiredPriceTrigger;

    /**
     * @param UpdateExpiredPriceTrigger $updateExpiredPriceTrigger
     */
    public function __construct(
        UpdateExpiredPriceTrigger $updateExpiredPriceTrigger
    ) {
        $this->updateExpiredPriceTrigger = $updateExpiredPriceTrigger;
    }

    /**
     * @param Subject $subject
     * @param \Closure $proceed
     * @param int $customerId
     * @param string|null $predefinedMaskedQuoteId
     *
     * @return Quote
     */
    public function aroundResolve(
        Subject $subject,
        \Closure $proceed,
        int $customerId,
        string $predefinedMaskedQuoteId = null
    ): Quote {
        /**@var Quote $quote*/
        $quote =  $proceed($customerId, $predefinedMaskedQuoteId);
        $this->updateExpiredPriceTrigger->execute($quote, $customerId);

        return $quote;
    }
}
