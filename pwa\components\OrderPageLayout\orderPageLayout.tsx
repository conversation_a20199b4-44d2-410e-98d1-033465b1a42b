import { useRouter } from 'next/compat/router'
import { useMemo } from 'react'

// import OrderDetails from '@/components/AccountOrderList/OrderDetails'
import B2bOrderDetails from '@/components/AccountOrderList/B2bOrderDetails'

const OrderPageLayout = () => {
  const router = useRouter()

  const number = useMemo(() => router?.query?.number, [router])

  // return id ? <OrderDetails id={id} /> : null
  return number ? <B2bOrderDetails number={number} /> : null
}

export default OrderPageLayout
