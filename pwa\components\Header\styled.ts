import styled from '@emotion/styled'

export const StyledHeader = styled.header`
  height: unset;
  background-color: ${({ theme }) => theme.colors.white};

  .panel-hide {
    height: 0;
    overflow: hidden;
  }

  .guest-header-panel {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 44px;
    width: 100%;
    background: #003865;

    span {
      font-weight: 700;
      font-size: 15px;
      line-height: 21px;
      letter-spacing: 0.01em;
      color: #fff;

      svg {
        margin-left: 6px;
      }
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    .guest-header-panel {
      height: 40px;
    }
  }
`

export const StyledSearchBar = styled.div`
  .container {
    display: grid;
    grid-template-columns: auto 1fr auto;
    //grid-column-gap: 24px;
    //justify-content: space-between;
    align-items: center;
    height: 100px;

    @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
      position: relative;
      z-index: 12;
      background-color: var(--color-white);
      padding: 0 16px;
      height: 76px;
    }
  }

  &.is-collapse {
    .collapse-wrapper {
      display: flex;
      flex-direction: row-reverse;
      justify-content: space-between;
      max-width: 1180px;
      margin: 0 auto;

      & > .container {
        flex-basis: 300px;
      }
    }

    .container {
      grid-template-columns: auto;
    }
  }
`

export const StyledTools = styled.div`
  display: grid;
  grid-auto-flow: column;
  grid-column-gap: 48px;
  justify-content: flex-end;
  align-items: center;

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    grid-column-gap: 12px;
  }
`
