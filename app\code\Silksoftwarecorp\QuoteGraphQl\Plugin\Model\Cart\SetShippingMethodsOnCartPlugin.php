<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */
declare(strict_types=1);

namespace Silksoftwarecorp\QuoteGraphQl\Plugin\Model\Cart;

use Magento\Framework\GraphQl\Query\Resolver\ContextInterface;
use Magento\Quote\Api\Data\CartInterface;
use Magento\QuoteGraphQl\Model\Cart\SetShippingMethodsOnCart;
use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Psr\Log\LoggerInterface;

/**
 * After plugin for SetShippingMethodsOnCart to force quote refresh
 */
class SetShippingMethodsOnCartPlugin
{
    /**
     * @var CartRepositoryInterface
     */
    private $cartRepository;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @param CartRepositoryInterface $cartRepository
     * @param LoggerInterface $logger
     */
    public function __construct(
        CartRepositoryInterface $cartRepository,
        LoggerInterface $logger
    ) {
        $this->cartRepository = $cartRepository;
        $this->logger = $logger;
    }

    /**
     * After plugin to force quote refresh after setting shipping methods
     *
     * @param SetShippingMethodsOnCart $subject
     * @param \Closure $proceed
     * @param ContextInterface $context
     * @param mixed $cart
     * @param array $shippingMethodsInput
     * @return void
     */
    public function aroundExecute(
        SetShippingMethodsOnCart $subject,
        \Closure $proceed,
        ContextInterface $context,
        CartInterface $cart,
        array $shippingMethodsInput
    ): void {
        $proceed($context, $cart, $shippingMethodsInput);

        try {
            $quote = $this->cartRepository->get($cart->getId());
            $quote->setTotalsCollectedFlag(false);
            $quote->collectTotals();
            $this->cartRepository->save($quote);
        } catch (\Exception $e) {
            $this->logger->critical(__(
                'Failed to refresh quote totals after setting shipping methods. Message: ',
                $e->getMessage()
            ));
        }
//
//        $proceed($context, $cart, $shippingMethodsInput);
    }
}
