import dynamic from 'next/dynamic'
import { Button } from 'antd'
import { FormattedMessage } from 'react-intl'

import CountryTransform from '@/components/CountryTransform'
import CommonTable from '@/components/Common/CommonTable'
import CommonCheckbox from '@/components/Common/CommonCheckbox'

import { StyledButton, StyledDefaultCheckbox } from '../styled'

const AddressModal = dynamic(() => import('@/components/AddressModal'), {
  ssr: false
})
const ConfirmModal = dynamic(() => import('@/components/ConfirmModal'), {
  ssr: false
})

const AdditionalAddress = (props) => {
  const { addressList, editAddressModal, showEditAddressModal, handleAddressDelete, ...rests } =
    props
  const keys: string = addressList.map((item) => item.key)
  const shouldRender: boolean = keys.includes(editAddressModal?.addressInfo?.key)

  const DeleteBtn = (
    <Button className="additionalAddress__button" type="link">
      <FormattedMessage id="global.delete" />
    </Button>
  )

  const columns = [
    {
      title: 'Default',
      dataIndex: 'default_shipping',
      key: 'default_shipping',
      render: (param) => (
        <StyledDefaultCheckbox>
          <CommonCheckbox checked={param} />
        </StyledDefaultCheckbox>
      )
    },
    {
      title: 'Company',
      dataIndex: 'company',
      key: 'company',
      render: (value, record) => (
        <div>
          {record.company}
          {` ${record.firstname}  ${record.lastname}`}
        </div>
      )
    },
    {
      title: 'Street Address',
      dataIndex: 'street',
      key: 'street',
      render: (param) => (
        <p>
          {param.map((item, index) => {
            return (
              <span key={item}>{index > param.length - 1 && item !== '' ? `${item}, ` : item}</span>
            )
          })}
        </p>
      )
    },
    {
      title: <FormattedMessage id="global.city" />,
      dataIndex: 'city',
      key: 'city'
    },
    {
      title: 'State',
      dataIndex: 'region',
      key: 'region',
      render: (param) => <span>{param.region}</span>
    },
    {
      title: 'Zip',
      dataIndex: 'postcode',
      key: 'postcode'
    },
    {
      title: 'Phone',
      dataIndex: 'telephone',
      key: 'telephone'
    },
    {
      title: <FormattedMessage id="global.action" />,
      dataIndex: '',
      key: 'x',
      width: 70,
      render: (param, record) => {
        return (
          <StyledButton>
            <Button
              className="additionalAddress__button"
              type="link"
              onClick={() => {
                showEditAddressModal(record)
              }}>
              <FormattedMessage id="global.edit" />
            </Button>
            {/*<ConfirmModal
              onOk={() => {
                handleAddressDelete(record)
              }}
              trigger={DeleteBtn}>
              <FormattedMessage id="account.deleteMessage" />
            </ConfirmModal>*/}
          </StyledButton>
        )
      }
    }
  ]

  const onChange = (curPagination, filters, sorter, extra) => {
    console.log('params', curPagination, filters, sorter, extra)
  }
  console.log('addressList: ', addressList)

  return (
    <div>
      {addressList.length === 0 ? (
        <FormattedMessage id="account.noAdditional" />
      ) : (
        <div>
          <CommonTable columns={columns} dataSource={addressList} onChange={onChange} />
          {editAddressModal.visible && shouldRender && (
            <AddressModal addressData={editAddressModal} {...rests} />
          )}
        </div>
      )}
    </div>
  )
}

export default AdditionalAddress
