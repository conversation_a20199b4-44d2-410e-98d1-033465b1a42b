<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Payment\Model\Config">
        <arguments>
            <argument name="methods" xsi:type="array">
                <item name="unifiedarpayment" xsi:type="string">Silksoftwarecorp\UnifiedArPayment\Model\Payment\UnifiedAr</item>
            </argument>
        </arguments>
    </type>

    <virtualType name="UnifiedArPaymentLogger" type="Magento\Framework\Logger\Monolog">
        <arguments>
            <argument name="name" xsi:type="string">unifiedarpayment</argument>
            <argument name="handlers" xsi:type="array">
                <item name="system" xsi:type="object">UnifiedArPaymentLoggerHandler</item>
            </argument>
        </arguments>
    </virtualType>

    <virtualType name="UnifiedArPaymentLoggerHandler" type="Silksoftwarecorp\UnifiedArPayment\Logger\Handler\UnifiedArPayment">
        <arguments>
            <argument name="filesystem" xsi:type="object">Magento\Framework\Filesystem\Driver\File</argument>
        </arguments>
    </virtualType>

    <type name="Silksoftwarecorp\UnifiedArPayment\Helper\Logger">
        <arguments>
            <argument name="logger" xsi:type="object">UnifiedArPaymentLogger</argument>
        </arguments>
    </type>

    <preference for="Silksoftwarecorp\UnifiedArPayment\Api\UnifiedArReturnDataInterface" type="Silksoftwarecorp\UnifiedArPayment\Model\UnifiedArReturnData" />
    <preference for="Silksoftwarecorp\UnifiedArPayment\Api\Data\UnifiedArReturnDataInterface" type="Silksoftwarecorp\UnifiedArPayment\Model\Data\UnifiedArReturnData" />

    <!-- Order Total Configuration -->
    <type name="Magento\Sales\Model\Order\Total\Config">
        <arguments>
            <argument name="totalModels" xsi:type="array">
                <item name="unified_ar_surcharge" xsi:type="string">Silksoftwarecorp\UnifiedArPayment\Model\Order\Total\Surcharge</item>
            </argument>
        </arguments>
    </type>

    <preference for="Magento\Sales\Block\Adminhtml\Order\Totals" type="Silksoftwarecorp\UnifiedArPayment\Block\Adminhtml\Totals" />

    <preference for="Magento\Sales\Block\Order\Totals" type="Silksoftwarecorp\UnifiedArPayment\Block\Email\Order\Totals" />

</config>
