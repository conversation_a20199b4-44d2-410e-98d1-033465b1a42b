import styled from '@emotion/styled'

export const StyledScrollTab = styled.div`
  padding-top: 48px;
  padding-bottom: 56px;

  .head {
    position: sticky;
    top: 149px;
    display: grid;
    margin-bottom: 24px;
    padding-top: 12px;
    grid-auto-flow: column;
    grid-column-gap: 24px;
    justify-content: flex-start;
    align-items: center;
    z-index: 50;
    background-color: #fff;
    border-bottom: 1px solid #f2f2f2;

    a {
      line-height: 31px;
      position: relative;
      display: block;
      padding-bottom: 16px;
      color: var(--color-font);
      font-size: 22px;
      font-weight: 400;
      cursor: pointer;

      &:hover {
        text-decoration: none;
      }

      &.active {
        font-family: var(--font-poppins-semi-bold);
        font-weight: 600;

        &::after {
          position: absolute;
          bottom: 0;
          right: 0;
          left: 0;
          height: 8px;
          display: block;
          content: '';
          border-radius: 4px;
          background: #cf102d;
        }
      }
    }
  }

  .scroll-item {
    margin-bottom: 56px;
    padding-bottom: 56px;
    border-bottom: 1px solid #f2f2f2;

    > h2 {
      line-height: 41px;
      margin-bottom: 24px;
      font-family: var(--font-poppins-medium);
      font-size: 34px;
      font-weight: 500;
    }

    &-specifications {
      table {
        border-width: 0;

        tbody {
          > tr {
            &:first-of-type {
              td {
                background-color: #d9d9d9;

                &:last-of-type {
                  font-family: var(--font-poppins-semi-bold);
                  font-size: 16px;
                  font-weight: 600;
                }
              }
            }
          }
        }

        tr {
          display: grid;
          grid-template-columns: 315px 1fr;
          justify-content: flex-start;
          align-items: center;

          td {
            width: unset !important;
            height: 48px;
            padding: 12px 32px;
            font-family: var(--font-montserrat);
            font-size: 15px;
            font-weight: 400;
            background-color: #d9d9d9;
            border-width: 0;
            border-bottom: 1px solid #fff;
            border-right: 1px solid #fff;

            span {
              line-height: 24px;
            }

            &:first-of-type {
              font-family: var(--font-poppins-semi-bold);
              font-size: 16px;
              font-weight: 600;
              background-color: #d9d9d9;
            }

            &:last-of-type {
              background-color: #f2f2f2;
            }
          }
        }
      }
    }

    &-videos {
      .pagebuilder-column-line {
        display: grid !important;
        grid-template-columns: 1fr 187px;
        grid-column-gap: 24px;
        justify-content: space-between;
        align-items: center;
      }

      .pagebuilder-column {
        width: unset !important;

        &:first-of-type {
          .pagebuilder__text {
            margin-top: 24px;

            h4 {
              line-height: 31px;
              margin-bottom: 8px;
              font-size: 22px;
              font-weight: 500;

              span {
                font-size: 22px !important;
              }
            }

            p {
              line-height: 24px;
              font-family: var(--font-montserrat);
              font-size: 15px;
              font-weight: 400;
            }
          }
        }

        &:last-of-type {
          .pagebuilder__text {
            margin-top: 12px;
            margin-bottom: 24px;

            h6 {
              color: #000;
              font-family: var(--font-poppins-medium);
              font-size: 16px;
              font-weight: 600;
              line-height: 24px;
            }
          }
        }
      }
    }

    &-description {
      .pagebuilder__text {
        h4 {
          line-height: 31px;
          margin-bottom: 8px;
          font-family: var(--font-poppins-medium);
          font-size: 22px;
          font-weight: 500;

          span {
            font-size: 22px !important;
          }
        }

        > p {
          line-height: 24px;
          margin-bottom: 24px;
          font-family: var(--font-montserrat);
          font-size: 15px;
          font-weight: 400;

          span {
            font-size: 15px !important;
          }

          &:last-of-type {
            margin-bottom: 0;
          }
        }

        ul {
          margin-bottom: 24px;
          padding-left: 20px;

          > li {
            margin-bottom: 8px;
            list-style-type: disc;
          }
        }

        a {
          line-height: 24px;
          color: var(--color-primary);
          font-family: var(--font-montserrat-bold);
          font-size: 15px;
          font-weight: 700;
        }
      }
    }

    &-resources {
      .pagebuilder__text {
        h4 {
          line-height: 31px;
          margin-bottom: 16px;
          font-family: var(--font-poppins-medium);
          font-size: 22px;
          font-weight: 500;
        }

        a {
          line-height: 24px;
          display: inline-block;
          margin-bottom: 16px;
          color: var(--color-font);
          font-family: var(--font-montserrat-bold);
          font-size: 15px;
          font-weight: 700;
          text-decoration: underline;

          span {
            font-size: 15px !important;
          }
        }
      }

      .pagebuilder-download {
        a {
          position: relative;
          text-decoration: none;

          &::after {
            position: absolute;
            top: 0;
            right: -34px;
            width: 24px;
            height: 24px;
            display: block;
            content: '';
            z-index: 20;
            background-image: url('/images/download.svg');
            background-position: center;
            background-repeat: no-repeat;
            background-size: contain;
          }
        }
      }
    }

    &-blogs {
      margin-bottom: 0;
    }
  }
`
