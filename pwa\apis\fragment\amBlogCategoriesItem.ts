import { gql } from '@apollo/client'
import type { DocumentNode } from '@apollo/client'

import { amBlogPostTag } from './amBlogPostTag'

export const amBlogCategoriesItem: DocumentNode = gql`
  fragment amBlogCategoriesItem on AmBlogPost {
    created_at
    original_list_thumbnail
    short_content
    title
    post_id
    url_key
    tags {
      ...amBlogPostTag
      __typename
    }
  }
  ${amBlogPostTag}
`
