import styled from '@emotion/styled'

export const StyledProductItem = styled.div`
  position: relative;
  padding: 15px;
  border: 1px solid #d9d9d9;
  border-radius: 1px;

  .product-item-purchased {
    position: absolute;
    top: 4px;
    left: 4px;
    z-index: 2;
  }

  .product-image {
    display: block;

    img {
      object-fit: contain;
    }
  }

  .product-name {
    display: -webkit-box;
    overflow: hidden;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    margin-top: 15px;
    text-align: left;

    a {
      display: block;
      max-width: 100%;
      overflow: hidden;
      white-space: nowrap;
      //text-overflow: ellipsis;
      text-overflow: clip;
      font-weight: 700;
      font-size: 18px;
      line-height: 26px;
      letter-spacing: 0;
      color: var(--color-font) !important;
      text-decoration: none !important;

      &:hover {
        color: var(--color-primary);
      }
    }
  }

  .product-sku {
    margin-top: 3px;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: 0;
    text-align: left;
  }

  .product-description {
    //display: -webkit-box;
    //overflow: hidden;
    //-webkit-box-orient: vertical;
    //-webkit-line-clamp: 3;
    margin-top: 6px;
    font-weight: 400;
    font-size: 15px;
    line-height: 21px;
    letter-spacing: 0.01em;
    color: var(--color-text);

    & > p {
      display: none;
    }
  }

  .product-stock {
    margin-top: 8px;
    margin-bottom: 8px;
    font-weight: 700;
    font-size: 16px;
    line-height: 26px;
    letter-spacing: 0.02em;
    color: #a74906;
    text-align: left;

    &.in-stock {
      color: #0f8c22;
    }

    &.is-empty-stock {
      color: var(--color-primary);
    }
  }

  .prices {
    display: block;
    margin-top: 8px;
  }

  .product-content-grid {
    div {
      text-align: left;
    }
  }

  &.is-horizontal {
    display: grid;
    grid-template-columns: 175px 1fr;
    grid-column-gap: 24px;

    .product-content {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }

    .product-name {
      margin-top: 0;
    }

    .product-content-grid {
      display: grid;
      grid-template-columns: 1fr auto;
      grid-column-gap: 24px;
      align-items: center;
      margin-top: 16px;
      min-height: 63px;

      .product-stock,
      .produc-action {
        margin-top: 0;

        .product-cart-btn {
          min-width: 179px;
        }
      }
    }
  }
`
