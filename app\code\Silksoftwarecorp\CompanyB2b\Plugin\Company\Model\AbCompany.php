<?php

namespace Silksoftwarecorp\CompanyB2b\Plugin\Company\Model;

use Magento\Company\Api\Data\CompanyExtensionFactory;
use Magento\Company\Api\Data\CompanyInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Silksoftwarecorp\CompanyB2b\Api\Data\CompanyCustomAttributesInterface;
use Magento\Framework\App\ResourceConnection;
class AbCompany
{


    protected CustomerRepositoryInterface $customerRepository;

    /**
     * @var CompanyExtensionFactory
     */
    protected $companyExtensionFactory;

    /**
     * @param CompanyExtensionFactory $companyExtensionFactory
     */
    public function __construct(
        CompanyExtensionFactory $companyExtensionFactory
    ) {
        $this->companyExtensionFactory = $companyExtensionFactory;
    }

    protected function erpInfo(
        CompanyInterface $company
    ):void {
        $extensionAttributes = $company->getExtensionAttributes();
        if ($extensionAttributes === null) {
            $extensionAttributes = $this->companyExtensionFactory->create();
        }

        $erpExtension = $extensionAttributes->getErpAttributes();
        if($erpExtension && $erpExtension->getErpCustCode() && $erpExtension->getErpCustNum()) return;
        
        /**
         * @var CompanyCustomAttributesInterface $erpExtensionAttributes
         */
        $erpExtensionAttributes = \Magento\Framework\App\ObjectManager::getInstance()->create(CompanyCustomAttributesInterface::class);
        $erpExtensionAttributes->setErpCustCode($company->getData(CompanyCustomAttributesInterface::ERP_CUST_CODE));
        $erpExtensionAttributes->setErpConNum($company->getData(CompanyCustomAttributesInterface::ERP_CON_NUM));
        $erpExtensionAttributes->setErpCustNum($company->getData(CompanyCustomAttributesInterface::ERP_CUST_NUM));
     
          //get website_Id
          $customer_id = $company->getSuperUserId();
          $connection  = \Magento\Framework\App\ObjectManager::getInstance()->get(ResourceConnection::class)->getConnection();
          $userInfo  = $connection->query(sprintf("SELECT website_id FROM customer_entity WHERE entity_id = %d",$customer_id))->fetch();
 
          if($userInfo) $erpExtensionAttributes->setWebsiteId($userInfo['website_id']);

        $extensionAttributes->setErpAttributes($erpExtensionAttributes);
        $company->setExtensionAttributes($extensionAttributes);

    }
}