status: response.status
headers: response.headers
body: response.body

response:
  resolver: conditional
  when:
    - matches: request.url.pathname
      pattern: '^/maintenance/?$'
      use: maintenance
  default: pwa

pwa:
  target:
    inline: 'http://127.0.0.1:3000/'
  headers:
    resolver: inline
    inline:
      content-type:
        inline: text/html; charset=utf-8

maintenance:
  inline:
    status:
      resolver: inline
      inline: 200
    headers:
      resolver: inline
      inline:
        content-type:
          resolver: inline
          inline: 'text/string'
    body:
      resolver: inline
      inline: 'Upgrading maintenance deployment!'
