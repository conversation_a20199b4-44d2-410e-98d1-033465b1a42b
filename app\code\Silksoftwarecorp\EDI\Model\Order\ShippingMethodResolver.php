<?php

namespace Silksoftwarecorp\EDI\Model\Order;

use Magento\Shipping\Model\Config as ShippingConfig;

class ShippingMethodResolver
{
    /**
     * In-memory cache of all shipping methods
     *
     * @var array|null
     */
    private $allShippingMethods = null;

    /**
     * Index: lower(carrier_code) => list of methods
     *
     * @var array
     */
    private $indexByCarrierCode = [];

    /**
     * Index: normalize(carrier_title) => list of methods
     *
     * @var array
     */
    private $indexByCarrierTitle = [];

    /**
     * Index: normalize(carrier_title)|normalize(method_title) => method
     *
     * @var array
     */
    private $indexByPair = [];

    /**
     * @var ShippingConfig
     */
    protected $shippingConfig;

    /**
     * @param ShippingConfig $shippingConfig
     */
    public function __construct(ShippingConfig $shippingConfig)
    {
        $this->shippingConfig = $shippingConfig;
    }

    public function resolve(string $carrier): ?array
    {
        if (empty(trim($carrier))) {
            return null;
        }

        $parts = array_values(array_filter(array_map('trim', explode('-', $carrier))));
        if (empty($parts)) {
            return null;
        }

        $allShippingMethods = $this->getAllShippingMethods();

        // Single-part input, try quick lookups first
        if (count($parts) === 1) {
            $key = strtolower($parts[0]);

            // 1) Exact carrier_code match
            if (isset($this->indexByCarrierCode[$key]) && !empty($this->indexByCarrierCode[$key])) {
                return $this->indexByCarrierCode[$key][0];
            }

            // 2) Exact carrier_title match
            $titleKey = $this->normalizeKey($parts[0]);
            if (isset($this->indexByCarrierTitle[$titleKey]) && !empty($this->indexByCarrierTitle[$titleKey])) {
                return $this->indexByCarrierTitle[$titleKey][0];
            }

            // 3) Fallback: fuzzy match over all
            foreach ($allShippingMethods as $shippingMethod) {
                if ($this->isMatchedShippingMethod($shippingMethod, $parts[0])) {
                    return $shippingMethod;
                }
            }

            return null;
        }

        // Two or more parts: use first as carrier, second+ as method
        $carrierPart = $parts[0];
        $methodPart = implode('-', array_slice($parts, 1));

        // 1) Exact pair match by normalized titles
        $pairKey = $this->normalizeKey($carrierPart) . '|' . $this->normalizeKey($methodPart);
        if (isset($this->indexByPair[$pairKey])) {
            return $this->indexByPair[$pairKey];
        }

        // 2) Narrow down by carrier then fuzzy method
        $candidates = [];
        $codeKey = strtolower($carrierPart);
        if (isset($this->indexByCarrierCode[$codeKey])) {
            $candidates = array_merge($candidates, $this->indexByCarrierCode[$codeKey]);
        }

        $titleKey = $this->normalizeKey($carrierPart);
        if (isset($this->indexByCarrierTitle[$titleKey])) {
            $candidates = array_merge($candidates, $this->indexByCarrierTitle[$titleKey]);
        }

        // If we have candidates, try fuzzy method matching among them first
        if (!empty($candidates)) {
            foreach ($candidates as $shippingMethod) {
                if ($this->isMatchedShippingMethod($shippingMethod, $carrierPart, $methodPart)) {
                    return $shippingMethod;
                }
            }
        }

        // 3) Fallback: scan all
        foreach ($allShippingMethods as $shippingMethod) {
            if ($this->isMatchedShippingMethod($shippingMethod, $carrierPart, $methodPart)) {
                return $shippingMethod;
            }
        }

        return null;
    }

    protected function getAllShippingMethods(): array
    {
        if ($this->allShippingMethods !== null) {
            return $this->allShippingMethods;
        }

        $carriers = $this->shippingConfig->getAllCarriers();

        $result = [];
        $dedupe = [];
        foreach ($carriers as $carrierCode => $carrierModel) {
            $carrierTitle = (string)$carrierModel->getConfigData('title');

            if (method_exists($carrierModel, 'getAllowedMethods')) {
                $allowedMethods = (array)$carrierModel->getAllowedMethods();

                foreach ($allowedMethods as $methodCode => $methodTitle) {
                    $key = strtolower($carrierCode) . '|' . strtolower((string)$methodCode);
                    if (isset($dedupe[$key])) {
                        continue;
                    }
                    $dedupe[$key] = true;

                    $row = [
                        'carrier_code' => (string)$carrierCode,
                        'carrier_title' => __($carrierTitle),
                        'method_code' => (string)$methodCode,
                        'method_title' => __($methodTitle),
                    ];

                    $result[] = $row;
                }
            }
        }

        // Include predefined defaults
        foreach ($this->getDefaultShippingMethods() as $row) {
            $key = strtolower($row['carrier_code']) . '|' . strtolower($row['method_code']);
            if (!isset($dedupe[$key])) {
                $dedupe[$key] = true;
                $result[] = $row;
            }
        }

        // Build indexes for faster lookup
        $this->buildIndexes($result);
        $this->allShippingMethods = $result;

        return $this->allShippingMethods;
    }

    protected function getDefaultShippingMethods(): array
    {
        return [
            [
                "carrier_code"=> "instore",
                "carrier_title"=> __("In Store Pickup"),
                "method_code"=> "pickup",
                "method_title"=> __("In Store Pickup")
            ],
            [
                "carrier_code"=> "flatrate",
                "carrier_title"=> __("Blevins Delivery "),
                "method_code"=> "flatrate",
                "method_title"=> __("Blevins Delivery")
            ],
            [
                "carrier_code"=> "freeshipping",
                "carrier_title"=> __("LTL"),
                "method_code"=> "freeshipping",
                "method_title"=> __("LTL")
            ]
        ];
    }

    private function isMatchedShippingMethod(
        array $shippingMethod,
        string $carrierTitle,
        ?string $methodTitle = null
    ): bool {
        $inputCarrier = $this->normalize((string)$carrierTitle);
        $inputMethod = $this->normalize((string)$methodTitle);

        $shippingMethodCarrierTitle = $this->normalize((string)$shippingMethod['carrier_title']);
        $shippingMethodCarrierCode = $this->normalize((string)$shippingMethod['carrier_code']);
        $shippingMethodTitle = $this->normalize((string)$shippingMethod['method_title']);

        // If both provided: require both to match (by equality/contains or token overlap)
        if ($inputMethod !== '') {
            $carrierOk = $this->equalsOrContains($inputCarrier, $shippingMethodCarrierTitle)
                || $this->equalsOrContains($inputCarrier, $shippingMethodCarrierCode)
                || $this->tokensOverlap($inputCarrier, $shippingMethodCarrierTitle);

            $methodOk = $this->equalsOrContains($inputMethod, $shippingMethodTitle)
                || $this->tokensOverlap($inputMethod, $shippingMethodTitle);

            return $carrierOk && $methodOk;
        }

        // Only carrier provided
        return $this->equalsOrContains($inputCarrier, $shippingMethodCarrierTitle)
            || $this->equalsOrContains($inputCarrier, $shippingMethodCarrierCode)
            || $this->tokensOverlap($inputCarrier, $shippingMethodCarrierTitle)
            || $this->tokensOverlap($inputCarrier, $shippingMethodCarrierCode);
    }

    private function equalsOrContains(string $needle, string $haystack): bool
    {
        if ($needle === '' || $haystack === '') {
            return false;
        }

        if ($needle === $haystack) {
            return true;
        }

        return str_contains($haystack, $needle);
    }

    private function tokensOverlap(string $a, string $b): bool
    {
        if ($a === '' || $b === '') {
            return false;
        }

        $wordsA = preg_split('/\s+/', strtoupper(trim($a)));
        $wordsB = preg_split('/\s+/', strtoupper(trim($b)));
        $common = array_intersect($wordsA ?: [], $wordsB ?: []);

        return !empty($common);
    }

    private function normalize(?string $s): string
    {
        $s = (string)$s;
        $s = strtolower(trim($s));
        $s = preg_replace('/\s+/', ' ', $s);
        return (string)$s;
    }

    private function normalizeKey(?string $s): string
    {
        return str_replace(' ', '_', $this->normalize($s));
    }

    private function buildIndexes(array $methods): void
    {
        $this->indexByCarrierCode = [];
        $this->indexByCarrierTitle = [];
        $this->indexByPair = [];

        foreach ($methods as $row) {
            $codeKey = strtolower((string)$row['carrier_code']);
            $titleKey = $this->normalizeKey((string)$row['carrier_title']);
            $pairKey = $titleKey . '|' . $this->normalizeKey((string)$row['method_title']);

            $this->indexByCarrierCode[$codeKey][] = $row;
            $this->indexByCarrierTitle[$titleKey][] = $row;
            $this->indexByPair[$pairKey] = $row;
        }
    }
}

