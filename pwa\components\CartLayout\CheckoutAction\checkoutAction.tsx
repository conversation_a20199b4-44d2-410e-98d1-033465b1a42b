import { Button } from 'antd'
import { useCallback } from 'react'
import { useRouter } from 'next/compat/router'

import { Modal } from '@/ui'

import { StyledCheckoutAction, StyledBackorderedContent } from './styled'

const CheckoutAction = ({ modalRef, handleToCheckout, actionDisabled }) => {
  const router = useRouter()
  const closeModal = useCallback(() => {
    modalRef.current?.close()
  }, [modalRef])

  const continueToCheckout = useCallback(() => {
    router.push('/checkout?step=shipping')
  }, [router])

  return (
    <StyledCheckoutAction>
      <Button type="primary" disabled={actionDisabled} onClick={handleToCheckout}>
        <svg
          className="lock"
          width="14px"
          height="14px"
          fill="currentColor"
          aria-hidden="true"
          focusable="false">
          <use xlinkHref="#icon-lock" />
        </svg>
        CHECKOUT
      </Button>
      <Modal ref={modalRef} width={778}>
        <StyledBackorderedContent>
          <h2>Backordered Items</h2>
          <p>
            This order contains backordered items and fulfillment may be delayed. Please continue to
            Checkout or edit your cart.
          </p>
          <div className="backordered-action">
            <Button type="primary" onClick={continueToCheckout}>
              CONTINUE
            </Button>
            <Button className="edit-btn" onClick={closeModal}>
              EDIT
            </Button>
          </div>
        </StyledBackorderedContent>
      </Modal>
    </StyledCheckoutAction>
  )
}

export default CheckoutAction
