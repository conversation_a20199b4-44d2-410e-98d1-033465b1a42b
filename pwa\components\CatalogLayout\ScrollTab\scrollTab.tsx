import dynamic from 'next/dynamic'
import { useEffect, useRef, useState } from 'react'
import { FormattedMessage } from 'react-intl'
import { ScrollLink, ScrollElement } from '@ranger-theme/ui'
import { clsx } from 'clsx'
import { nanoid } from 'nanoid'
import { throttle } from 'lodash-es'

import { useProductContext } from '@/route/ProductProvider'
import BlogList from '../BlogList'
import { StyledScrollTab } from './styled'

const PageBuilder = dynamic(() => import('@ranger-theme/pagebuilder'), {
  ssr: false
})

const ScrollTab = () => {
  const { product } = useProductContext()
  const { main_description, videos, resources, specifications } = product
  const isScrollingByClick = useRef(false) // 用于锁定状态
  const [scrollTop, setScrollTop] = useState<number>(0)
  const [ativeKey, setActiveKey] = useState<string>('videos')
  const linkList: string[] = []

  if (videos) linkList.push('videos')
  if (main_description?.html) linkList.push('description')
  if (specifications) linkList.push('specifications')
  if (resources) linkList.push('resources')
  linkList.push('blogs')

  const handleOnKey = (key: string) => {
    isScrollingByClick.current = true // 锁定滚动事件处理
    setActiveKey(key)

    // 解锁，允许 scroll 事件触发
    setTimeout(() => {
      isScrollingByClick.current = false
    }, 500) // 根据 smooth 滚动时间调整
  }

  useEffect(() => {
    const padding: number = 20
    const target = document.querySelector('header')
    if (target) setScrollTop(target.clientHeight + padding)
  }, [])

  useEffect(() => {
    const handleScroll = () => {
      // 如果是点击触发的滚动，直接返回
      if (isScrollingByClick.current) return
      const elements = document.querySelectorAll('.scroll-item')
      elements.forEach((el) => {
        const rect = el.getBoundingClientRect()

        if (rect.top >= 0 && rect.top < window.innerHeight / 2) {
          const name = el.getAttribute('data-name')
          if (name) setActiveKey(name)
        }

        if (rect.bottom > window.innerHeight / 2 && rect.bottom <= window.innerHeight) {
          const name = el.getAttribute('data-name')
          if (name) setActiveKey(name)
        }
      })
    }
    const throttleScroll = throttle(handleScroll, 200)

    window.addEventListener('scroll', throttleScroll)

    return () => {
      window.removeEventListener('scroll', throttleScroll)
    }
  }, [])

  return (
    <StyledScrollTab>
      <div className="head">
        {linkList.map((link: string) => {
          return (
            <ScrollLink
              key={nanoid()}
              className={clsx({ active: ativeKey === link })}
              to={link}
              offset={-scrollTop}
              duration={500}
              smooth
              onClick={() => handleOnKey(link)}
            >
              <FormattedMessage id={`pdp.${link}`} />
            </ScrollLink>
          )
        })}
      </div>
      <div className="wrapper">
        {videos && (
          <ScrollElement
            className="scroll-item scroll-item-videos"
            name="videos"
            data-name="videos"
          >
            <PageBuilder html={videos} />
          </ScrollElement>
        )}
        {main_description?.html && (
          <ScrollElement
            className="scroll-item scroll-item-description"
            name="description"
            data-name="description"
          >
            <h2>
              <FormattedMessage id="pdp.description" />
            </h2>
            <PageBuilder html={main_description?.html} />
          </ScrollElement>
        )}
        {specifications && (
          <ScrollElement
            className="scroll-item scroll-item-specifications"
            name="specifications"
            data-name="specifications"
          >
            <h2>
              <FormattedMessage id="pdp.specifications" />
            </h2>
            <PageBuilder html={specifications} />
          </ScrollElement>
        )}
        {resources && (
          <ScrollElement
            className="scroll-item scroll-item-resources"
            name="resources"
            data-name="resources"
          >
            <h2>
              <FormattedMessage id="pdp.resources" />
            </h2>
            <PageBuilder html={resources} />
          </ScrollElement>
        )}
        <ScrollElement className="scroll-item scroll-item-blogs" name="blogs" data-name="blogs">
          <h2>
            <FormattedMessage id="pdp.blogs" />
          </h2>
          <BlogList />
        </ScrollElement>
      </div>
    </StyledScrollTab>
  )
}

export default ScrollTab
