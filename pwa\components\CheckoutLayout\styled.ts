import styled from '@emotion/styled'

export const StyledSteps = styled.div`
  display: flex;
  margin-top: 40px;
  margin-bottom: 56px;

  .item {
    width: 210px;
    text-align: center;
    cursor: pointer;

    .line {
      position: relative;
      width: 210px;
      height: 9px;
      background-color: #d9d9d9;
    }

    &:first-of-type {
      .line {
        border-top-left-radius: 10px;
        border-bottom-left-radius: 10px;
      }
    }

    &:last-of-type {
      .line {
        border-top-right-radius: 10px;
        border-bottom-right-radius: 10px;
      }
    }

    &.active {
      .line {
        background-color: var(--color-bg-base);
      }

      .circle {
        background-color: var(--color-bg-base);

        > span {
          color: var(--color-white);
          background-color: var(--color-bg-base);
        }
      }

      .text {
        font-weight: 700;
        color: var(--color-font);
      }
    }
  }

  .circle {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 41px;
    height: 41px;
    display: flex;
    margin: 0 auto;
    z-index: 20;
    transform: translate(-50%, -50%);
    background-color: #d9d9d9;
    border-radius: 50%;
    justify-content: center;
    align-items: center;

    > span {
      width: 25px;
      height: 25px;
      display: flex;
      font-weight: 700;
      font-size: 16px;
      line-height: 23px;
      letter-spacing: 0;
      background-color: var(--color-white);
      border-radius: 50%;
      justify-content: center;
      align-items: center;
    }
  }

  .text {
    display: block;
    padding-top: 26px;
    color: #9f9d9d;
    font-weight: 400;
    font-size: 18px;
    line-height: 24px;
    letter-spacing: 0;
    text-align: center;
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    margin-top: 52px;
    padding: 0 16px;

    .item {
      flex: 1;
      width: unset;

      .line {
        position: relative;
        width: 100%;
        height: 9px;
        background-color: #d9d9d9;
      }
    }
  }
`

export const StyledCheckout = styled.div`
  display: grid;
  padding-bottom: 100px;
  grid-template-columns: 1fr 378px;
  grid-column-gap: 75px;
  justify-content: space-between;
  align-items: flex-start;

  .${({ theme }) => theme.namespace} {
    &-select-selector {
      border: 1px solid #d9d9d9 !important;
    }
  }

  .modal-addresss,
  .modal-checkout {
    .${({ theme }) => theme.namespace} {
      &-modal-content {
        padding: 0;
      }

      &-modal-header {
        margin-bottom: 0;
        padding: 22px 0;

        text-align: center;
        background-color: #f2f2f2;

        .${({ theme }) => theme.namespace} {
          &-modal-title {
            line-height: 37px;
            color: var(--color-font);
            font-size: 28px;
            font-weight: 500;
          }
        }
      }
    }
  }

  .modal-addresss {
    .${({ theme }) => theme.namespace} {
      &-modal-body {
        padding: 16px 40px 40px;
      }
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    grid-template-columns: 1fr;
    padding: 0 16px 160px;
  }
`

export const StyledEmpty = styled.div`
  display: flex;
  height: 200px;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 22px;
`
