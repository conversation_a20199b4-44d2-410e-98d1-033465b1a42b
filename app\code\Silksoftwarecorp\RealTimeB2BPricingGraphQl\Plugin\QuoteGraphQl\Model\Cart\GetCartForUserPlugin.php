<?php

namespace Silksoftwarecorp\RealTimeB2BPricingGraphQl\Plugin\QuoteGraphQl\Model\Cart;

use Magento\Quote\Model\Quote;
use Magento\QuoteGraphQl\Model\Cart\GetCartForUser as Subject;
use Silksoftwarecorp\RealTimeB2BPricing\Model\Quote\UpdateExpiredPriceTrigger;

/**
 * Class GetCartForUserPlugin
 */
class GetCartForUserPlugin
{
    /**
     * @var UpdateExpiredPriceTrigger
     */
    protected $updateExpiredPriceTrigger;

    /**
     * @param UpdateExpiredPriceTrigger $updateExpiredPriceTrigger
     */
    public function __construct(
        UpdateExpiredPriceTrigger $updateExpiredPriceTrigger
    ) {
        $this->updateExpiredPriceTrigger = $updateExpiredPriceTrigger;
    }

    /**
     * @param Subject $subject
     * @param \Closure $proceed
     * @param string $cartHash
     * @param int|null $customerId
     * @param int $storeId
     *
     * @return Quote
     */
    public function aroundExecute(
        Subject $subject,
        \Closure $proceed,
        string $cartHash,
        ?int $customerId,
        int $storeId
    ): Quote {
        /**@var Quote $quote*/
        $quote = $proceed($cartHash, $customerId, $storeId);
        $this->updateExpiredPriceTrigger->execute($quote, $customerId, $storeId);

        return $quote;
    }
}
