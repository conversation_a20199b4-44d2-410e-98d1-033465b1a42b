<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */

namespace Silksoftwarecorp\Inventory\Model;

use Magento\Framework\UrlInterface;
use Magento\Store\Model\StoreManagerInterface;

/**
 * Class ManagerProfileImageUrlService
 * Service class for generating manager profile image URLs
 */
class ManagerProfileImageUrlService
{
    /**
     * Manager profile area inside media folder
     */
    public const MANAGER_PROFILE_MEDIA_PATH = 'manager_profile';

    /**
     * @var StoreManagerInterface
     */
    private $storeManager;

    /**
     * ManagerProfileImageUrlService constructor.
     *
     * @param StoreManagerInterface $storeManager
     */
    public function __construct(
        StoreManagerInterface $storeManager
    ) {
        $this->storeManager = $storeManager;
    }

    /**
     * Get full image URL with base URL
     *
     * @param int $managerId
     * @param string $imageName
     * @return string|null
     */
    public function getFullImageUrl(int $managerId, string $imageName): ?string
    {
        if (!$imageName) {
            return null;
        }

        try {
            $mediaUrl = $this->storeManager->getStore()->getBaseUrl(UrlInterface::URL_TYPE_MEDIA);
            return $mediaUrl . $this->getRelativeImagePath($managerId, $imageName);
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Get relative image path (without base URL)
     *
     * @param int $managerId
     * @param string $imageName
     * @return string|null
     */
    public function getRelativeImagePath(int $managerId, string $imageName): ?string
    {
        if (!$imageName) {
            return null;
        }

        return self::MANAGER_PROFILE_MEDIA_PATH . '/' . $managerId . '/' . $imageName;
    }

    /**
     * Get image URL for GraphQL (relative path)
     *
     * @param int $managerId
     * @param string $imageName
     * @return string|null
     */
    public function getGraphQlImageUrl(int $managerId, string $imageName): ?string
    {
        return $this->getRelativeImagePath($managerId, $imageName);
    }

    /**
     * Get image URL for admin form (full URL)
     *
     * @param int $managerId
     * @param string $imageName
     * @return string|null
     */
    public function getAdminImageUrl(int $managerId, string $imageName): ?string
    {
        return $this->getFullImageUrl($managerId, $imageName);
    }
}
