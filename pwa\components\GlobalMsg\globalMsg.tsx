import Link from 'next/link'
import { memo, useEffect, useState, useCallback } from 'react'
import { events } from '@ranger-theme/utils'

import { StyledGlobalMsg } from './styled'

const GlobalMsg = () => {
  const [visible, setVisible] = useState(false)
  const [contentVisible, setContentVisible] = useState(false)
  const [itemText, setItemText] = useState('item')

  const triggerMsg = useCallback((params) => {
    setVisible(true)
    if (params?.itemText) {
      setItemText(params?.itemText)
    }
    setTimeout(() => {
      setContentVisible(true)
    }, 100)
    setTimeout(() => {
      setContentVisible(false)
      setTimeout(() => {
        setVisible(false)
        setItemText('item')
      }, 300)
    }, 3000)
  }, [])

  useEffect(() => {
    events.on('triggerCartSuccessMsg', triggerMsg)

    return () => {
      events.off('triggerCartSuccessMsg', triggerMsg)
    }
  }, [triggerMsg])

  return visible ? (
    <StyledGlobalMsg style={{ opacity: contentVisible ? 1 : 0 }}>
      <div className="global-msg-cart-success">
        <div className="success-icon">
          <svg width="10px" height="8px" fill="currentColor" aria-hidden="true" focusable="false">
            <use xlinkHref="#icon-check" />
          </svg>
        </div>
        {/* TODO: Configurable content */}
        You added {itemText} to your
        <Link href="/checkout/cart">Cart</Link>
      </div>
    </StyledGlobalMsg>
  ) : null
}

export default memo(GlobalMsg)
