type Mutation {
    setCartSpecialNotes(cart_id: String!, special_notes: String!): SetCartSpecialNotesOutput @resolver(class: "Silksoftwarecorp\\OrderCommentGraphQl\\Model\\Resolver\\SetCartSpecialNotes")
}

type SetCartSpecialNotesOutput {
    cart_id: String
    special_notes: String
    success: <PERSON><PERSON>an
}

extend type CustomerOrder {
    special_notes: String @resolver(class: "Silksoftwarecorp\\OrderCommentGraphQl\\Model\\Resolver\\OrderSpecialNotes")
} 