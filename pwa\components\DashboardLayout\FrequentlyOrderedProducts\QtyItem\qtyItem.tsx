import { memo, useMemo } from 'react'
import { Button, Form } from 'antd'
import { useSelector } from 'react-redux'

import QuantityStep from '@/components/QuantityStep'
import ContactYourBranchButton from '@/components/Common/ContactYourBranchButton'
import { useAddCart } from '@/packages/hooks'
import { actions as cartActions } from '@/store/cart'
import { POST_CREATE_CART } from '@/apis/mutations/createEmptyCart'
import { POST_ADD_PRODUCTS_CART } from '@/apis/mutations/addProductsToCart'

import { StyledQtyItem } from './styled'

const QtyItem = ({ product }) => {
  const { __typename, sku, stock_status } = product
  const inStock = stock_status === 'IN_STOCK'
  const [form] = Form.useForm()
  const { loading: cartLoading, handleAddToCart } = useAddCart({
    actions: cartActions,
    createCartDocumentNode: POST_CREATE_CART,
    addProductsDocumentNode: POST_ADD_PRODUCTS_CART
  })
  const isShowroomView = useSelector((state: Store) => state.user.showroomView)

  const actionDisabled = useMemo(() => {
    return !inStock || isShowroomView
  }, [inStock, isShowroomView])

  const qtyDisabled = useMemo(() => {
    return actionDisabled || cartLoading
  }, [actionDisabled, cartLoading])

  const handleSubmit = async (values: any) => {
    const cartItems: any[] = []

    switch (__typename) {
      case 'SimpleProduct':
        cartItems.push({
          quantity: values.quantity,
          sku
        })
        break
      default:
    }
    await handleAddToCart(cartItems, product)
  }

  return (
    <StyledQtyItem>
      {inStock ? (
        <Form form={form} initialValues={{ quantity: 1 }} onFinish={handleSubmit}>
          <QuantityStep
            disabled={qtyDisabled}
            buttonProps={{ disabled: qtyDisabled }}
            name="quantity"
          />
          <Button
            type="primary"
            htmlType="submit"
            loading={cartLoading}
            disabled={actionDisabled}
            className="cart-btn">
            <svg width="20px" height="18px" focusable="false">
              <use xlinkHref="#icon-cart" />
            </svg>
          </Button>
        </Form>
      ) : (
        <div className="contact-btn">
          <ContactYourBranchButton height={50} />
        </div>
      )}
    </StyledQtyItem>
  )
}

export default memo(QtyItem)
