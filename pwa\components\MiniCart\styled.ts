import styled from '@emotion/styled'

export const StyledMiniCart = styled.div`
  .cart {
    position: relative;
    width: 44px;
    height: 44px;
    display: flex;
    background-color: #968c83;
    cursor: pointer;
    border-radius: 50%;
    justify-content: center;
    align-items: center;
  }

  .bag {
    font-size: 15px;
  }

  .count {
    position: absolute;
    top: -9px;
    right: -8px;
    padding: 0 2px;
    min-width: 23px;
    height: 23px;
    display: flex;
    color: ${({ theme }) => theme.colors.white};
    font-size: 12px;
    font-weight: 700;
    z-index: 20;
    background-color: var(--color-primary);
    border: 2px solid ${({ theme }) => theme.colors.white};
    border-radius: 11px;
    justify-content: center;
    align-items: center;
  }
`
