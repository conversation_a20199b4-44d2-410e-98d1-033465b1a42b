import styled from '@emotion/styled'

export const StyledFrequentlyOrderedProducts = styled.div`
  .common-table-component table {
    thead th {
      padding: 0 !important;

      .item-search {
        height: 76px;
      }
    }

    tbody tr td {
      padding-top: 10px;
      padding-bottom: 10px;
    }
  }

  &.action-inactive {
    .common-table-component {
      .common-table-sort {
        display: none;
      }
      table thead th {
        p {
          cursor: initial;
        }
        .item-search {
          display: none;
        }
      }
    }
  }

  .table-description {
    padding-top: 16px;
    margin-bottom: 16px;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: 0;
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    .common-table-sort {
      display: none;
    }
  }
`
