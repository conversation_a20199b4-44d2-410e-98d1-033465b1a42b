import dynamic from 'next/dynamic'
import { Suspense } from 'react'
import { Spin } from 'antd'

import { MediaLayout } from '@/ui'
import ProductList from '@/components/ProductList'

import { StyledCategoryGrid, StyledProductGrid } from './styled'

const Filters = dynamic(() => import('./Filters'))

interface CategoryGridProps {
  children?: React.ReactNode
  id?: string
  search?: string
  q?: string
  fullWidth?: boolean
}

const CategoryGrid: React.FC<CategoryGridProps> = ({ children, id = '', search = '', q = '' }) => {
  return (
    <StyledCategoryGrid>
      <MediaLayout>
        <Filters id={id} search={q} />
      </MediaLayout>
      <StyledProductGrid>
        {/* TODO: Remove code */}
        {/* Sub Categories */}
        {/* {children} */}
        <Suspense
          fallback={
            <div className="product-grid-loading">
              <Spin />
            </div>
          }>
          <ProductList id={id} search={search} q={q} />
        </Suspense>
      </StyledProductGrid>
    </StyledCategoryGrid>
  )
}

export default CategoryGrid
