import { Button } from 'antd'
import styled from '@emotion/styled'

export const StyledCommonButton = styled(Button)`
  text-transform: none;
  text-decoration: none;

  &.is-underline {
    text-decoration: underline;
  }

  &.is-uppercase {
    text-transform: uppercase;
  }

  &.${({ theme }) => theme.namespace}-btn {
    &-default,
    &-primary {
      display: inline-flex;
      justify-content: center;
      align-items: center;
      padding-left: ${(props: any) => `${props.ph}px`};
      padding-right: ${(props: any) => `${props.ph}px`};
      height: ${(props: any) => `${props.height}px`};
      border-radius: 3px;
      font-weight: 700;
      font-size: ${(props: any) => `${props.fontSize}px`};
      line-height: 21px;
      letter-spacing: 0.03em;
      background-color: var(--color-primary) !important;

      &.is-dark {
        background-color: var(--color-bg-base) !important;

        &[disabled] {
          background: rgba(0, 0, 0, 0.04) !important;
          color: #999;
          border-color: #bbb !important;
        }
      }

      &[disabled] {
        color: #fff;
        opacity: 0.6;
      }
    }

    &-default {
      background-color: var(--color-white) !important;
    }

    &-text,
    &-link {
      padding: 0;
      font-weight: 700;
      line-height: 21px;
      letter-spacing: 0;
      font-size: ${(props: any) => `${props.fontSize}px`};
      color: var(--color-primary) !important;
      background: none !important;

      &.is-dark {
        color: var(--color-black) !important;
      }

      &[disabled] {
        opacity: 0.6;
      }
    }
  }
`
