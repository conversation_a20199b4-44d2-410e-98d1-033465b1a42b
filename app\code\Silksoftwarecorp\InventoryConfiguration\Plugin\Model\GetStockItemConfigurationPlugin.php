<?php

namespace Silksoftwarecorp\InventoryConfiguration\Plugin\Model;

use Magento\InventoryConfiguration\Model\StockItemConfigurationFactory;
use Magento\InventoryConfiguration\Model\GetLegacyStockItem;

class GetStockItemConfigurationPlugin
{
    /**
     * @var GetLegacyStockItem
     */
    private $getLegacyStockItem;

    /**
     * @var StockItemConfigurationFactory
     */
    private $stockItemConfigurationFactory;

    public function __construct(
        GetLegacyStockItem $getLegacyStockItem,
        StockItemConfigurationFactory $stockItemConfigurationFactory,
    ) {
        $this->getLegacyStockItem = $getLegacyStockItem;
        $this->stockItemConfigurationFactory = $stockItemConfigurationFactory;
    }

    public function aroundExecute(
        \Magento\InventoryConfiguration\Model\GetStockItemConfiguration $subject,
        \Closure $proceed,
        string $sku,
        int $stockId
    ) {
        return $this->stockItemConfigurationFactory->create(
            [
                'stockItem' => $this->getLegacyStockItem->execute($sku)
            ]
        );
    }
}
