import styled from '@emotion/styled'

export const StyledBreadcrumb = styled.div`
  display: flex;
  justify-content: flex-start;
  align-items: center;
  height: 40px;
  background-color: rgba(150, 140, 131, 0.18);

  & > .container {
    width: 100%;
  }

  .${({ theme }) => theme.namespace} {
    &-breadcrumb {
      ol {
        align-items: center;
      }

      .arrow {
        font-size: 9px;

        use {
          fill: var(--color-font);
        }
      }
    }

    &-breadcrumb-separator {
      margin-top: -1px;
    }

    &-breadcrumb-link {
      > a {
        font-weight: 400;
        font-size: 13px;
        line-height: 20px;
        letter-spacing: 0;
        color: var(--color-font) !important;

        &:hover {
          color: var(--color-primary) !important;
          background-color: transparent !important;
        }
      }

      > span {
        font-weight: 700;
        font-size: 13px;
        line-height: 20px;
        letter-spacing: 0;
        color: var(--color-font);
      }
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    padding: 0 16px;
    background-color: var(--color-white);

    .back-icon {
      margin-top: -2px;
      margin-right: 8px;
    }

    .${({ theme }) => theme.namespace} {
      &-breadcrumb-link {
        > a {
          font-weight: 700;
          font-size: 14px;
          line-height: 20px;
          letter-spacing: 0;
          color: var(--color-font);
        }
      }
    }
  }
`
