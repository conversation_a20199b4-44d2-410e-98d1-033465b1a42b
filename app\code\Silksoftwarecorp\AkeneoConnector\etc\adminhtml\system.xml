<?xml version="1.0"?>
<!--
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="akeneo_connector">
            <group id="product">
                <field id="clear_product_name_when_missing" translate="label comment" type="select" sortOrder="37" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Clear product name when missing</label>
                    <comment>If set to No, empty product names will be filtered out during import. If set to Yes, empty names will be processed normally.</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
            </group>
        </section>
    </system>
</config>
