import styled from '@emotion/styled'

export const StyledFormItem = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-column-gap: 24px;
  justify-content: space-between;
  align-items: center;
`

export const StyledFormLine = styled.div`
  display: grid;
  grid-template-columns: 1fr 186px 186px;
  grid-column-gap: 24px;
  justify-content: space-between;
  align-items: center;
`

export const StyledAddressForm = styled.div`
  .${({ theme }) => theme.namespace}-select-selector {
    padding-left: 10px !important;
  }
`
