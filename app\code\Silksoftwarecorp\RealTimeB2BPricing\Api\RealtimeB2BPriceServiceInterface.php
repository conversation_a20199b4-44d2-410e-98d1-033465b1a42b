<?php

namespace Silksoftwarecorp\RealTimeB2BPricing\Api;

use Magento\Framework\Exception\LocalizedException;
use Silksoftwarecorp\RealTimeB2BPricing\Api\B2BPriceItemResultInterface;
use Silksoftwarecorp\RealTimeB2BPricing\Api\Data\B2BPriceItemInterface;

interface RealtimeB2BPriceServiceInterface
{
    /**
     *
     *
     * @param $customerId
     * @param $locationId
     * @param $sku
     *
     * @return B2BPriceItemInterface|null
     *
     * @throws LocalizedException
     */
    public function getPriceItem($customerId, $locationId, $sku): ?B2BPriceItemInterface;

    /**
     *
     *
     * @param $customerId
     * @param $locationId
     * @param array $skus
     *
     * @return B2BPriceItemResultInterface
     *
     * @throws LocalizedException
     */
    public function getPriceItems($customerId, $locationId, array $skus): B2BPriceItemResultInterface;
}
