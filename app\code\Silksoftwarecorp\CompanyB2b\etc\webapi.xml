<?xml version="1.0"?>
<!--
/**
 *
 * @package     Silksoftwarecorp_CompanyB2b
 * @copyright   Copyright © 2021 Silk Software Corp (https://www.silksoftware.com)
 * <AUTHOR> <EMAIL>
 */
-->
<routes xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Webapi:etc/webapi.xsd">

<!--    <route url="/V1/shipto/" method="GET">-->
<!--        <service class="Silksoftwarecorp\CompanyB2b\Api\ErpShipToRepositoryInterface" method="getList"/>-->
<!--        <resources>-->
<!--            <resource ref="Silksoftwarecorp_CompanyB2b::index"/>-->
<!--        </resources>-->
<!--    </route>-->
<!--    <route url="/V1/shipto/:entityId" method="GET">-->
<!--        <service class="Silksoftwarecorp\CompanyB2b\Api\ErpShipToRepositoryInterface" method="get"/>-->
<!--        <resources>-->
<!--            <resource ref="Silksoftwarecorp_CompanyB2b::index"/>-->
<!--        </resources>-->
<!--    </route>-->
<!--    <route url="/V1/shipto/:entityId" method="DELETE">-->
<!--        <service class="Silksoftwarecorp\CompanyB2b\Api\ErpShipToRepositoryInterface" method="deleteById"/>-->
<!--        <resources>-->
<!--            <resource ref="Silksoftwarecorp_CompanyB2b::delete"/>-->
<!--        </resources>-->
<!--    </route>-->
    <route url="/V1/shipto/:entityId" method="PUT">
        <service class="Silksoftwarecorp\CompanyB2b\Api\ErpShipToRepositoryInterface" method="save"/>
        <resources>
            <resource ref="Silksoftwarecorp_CompanyB2b::add"/>
        </resources>
    </route>
    <route url="/V1/shipto/" method="POST">
        <service class="Silksoftwarecorp\CompanyB2b\Api\ErpShipToRepositoryInterface" method="save"/>
        <resources>
            <resource ref="Silksoftwarecorp_CompanyB2b::add"/>
        </resources>
    </route>
</routes>
