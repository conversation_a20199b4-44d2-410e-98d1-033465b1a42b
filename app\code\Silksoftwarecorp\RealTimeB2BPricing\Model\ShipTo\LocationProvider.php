<?php

namespace Silksoftwarecorp\RealTimeB2BPricing\Model\ShipTo;

use Magento\Framework\App\RequestInterface;

class LocationProvider
{
    const KEY_LOCATION_ID =  'X-Location-Id';

    /**
     * @var RequestInterface
     */
    protected $request;

    /**
     * @param RequestInterface $request
     */
    public function __construct(RequestInterface $request)
    {
        $this->request = $request;
    }

    public function getLocationId($storeId = null): string
    {
        return (string)$this->request->getHeader('X-Location-Id');
    }
}
