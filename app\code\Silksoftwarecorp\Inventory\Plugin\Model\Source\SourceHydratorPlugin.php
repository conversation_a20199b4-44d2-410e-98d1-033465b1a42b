<?php

namespace Silksoftwarecorp\Inventory\Plugin\Model\Source;

use Magento\InventoryAdminUi\Model\Source\SourceHydrator;
use Magento\InventoryApi\Api\Data\SourceInterface;

class SourceHydratorPlugin
{
    public function beforeHydrate(
        SourceHydrator $subject,
        SourceInterface $source,
        array $data
    ): array {
        $general = $data['general'] ?? [];
        if ($general) {
            $extensionAttributes = $general['extension_attributes'] ?? null;
            $schedule = $extensionAttributes['schedule'] ?? null;
            if ($schedule && is_array($schedule)) {
                unset($schedule['office']['monday']['fill_schedule_button']);
                unset($schedule['store']['monday']['fill_schedule_button']);

                $schedule = json_encode($schedule);
                $extensionAttributes['schedule'] = $schedule;
                $general['extension_attributes'] = $extensionAttributes;
            }

            $data['general'] = $general;
        }

        return [$source, $data];
    }
}
