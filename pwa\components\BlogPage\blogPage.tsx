import { Input } from 'antd'
import { LineContainer } from '@ranger-theme/ui'
import { clsx } from 'clsx'
import Image from 'next/image'
import Link from 'next/link'
import dynamic from 'next/dynamic'

import Breadcrumb from '@/components/Breadcrumb'
import CommonButton from '@/components/Common/CommonButton'
import CommonPagination from '@/components/Common/CommonPagination'
import CommonLoading from '@/components/Common/CommonLoading'
import { useBlogPage } from '@/hooks/BlogPage'

import {
  StyledBlogDescription,
  StyledBlogList,
  StyledBlogListEmpty,
  StyledBlogPage,
  StyledBlogPanel
} from './styled'

const PageBuilder = dynamic(() => import('@ranger-theme/pagebuilder'), {
  ssr: false
})

const BlogPage = () => {
  const {
    categories,
    hasData,
    isEmptyList,
    postItems,
    currentPage,
    pageSize,
    total,
    onPageChange,
    handleCategorySelect,
    categoryUrlKey,
    loading,
    searchInputChange,
    handleSearch,
    searchInputValue,
    blogDescription
  } = useBlogPage()

  return (
    <>
      <Breadcrumb items={[{ name: 'Blog' }]} />
      {/* Description */}
      {blogDescription && (
        <StyledBlogDescription>
          <PageBuilder html={blogDescription} />
        </StyledBlogDescription>
      )}
      <LineContainer>
        <StyledBlogPage>
          {/* Title */}
          <div className="blog-page-title">
            <div>EXPERT INSIGHTS</div>
            <h2>Discover More with Us</h2>
          </div>
          <CommonLoading spinning={loading}>
            {hasData && (
              <div>
                {/* Tags and search */}
                <StyledBlogPanel>
                  <div className="tag-list">
                    <div className="tag-list-inner">
                      {categories.map(({ name, url_key }) => {
                        const active =
                          categoryUrlKey === url_key ||
                          (categoryUrlKey === 'search' && name === 'All')
                        return (
                          <CommonButton
                            key={name}
                            className={clsx({ active })}
                            height={53}
                            ph={24}
                            uppercase={false}
                            onClick={() => {
                              handleCategorySelect(url_key)
                            }}>
                            {name}
                          </CommonButton>
                        )
                      })}
                    </div>
                  </div>
                  <div className="blog-search">
                    <Input
                      size="large"
                      placeholder="Search"
                      onChange={searchInputChange}
                      value={searchInputValue}
                      suffix={
                        <span
                          aria-hidden
                          className="blog-search-icon"
                          onClick={(e) => {
                            e.stopPropagation()
                            handleSearch()
                          }}>
                          <svg
                            width="1.1em"
                            height="1.1em"
                            fill="currentColor"
                            aria-hidden="true"
                            focusable="false">
                            <use xlinkHref="#icon-search" />
                          </svg>
                        </span>
                      }
                    />
                  </div>
                </StyledBlogPanel>

                {isEmptyList ? (
                  <StyledBlogListEmpty>There is no any item.</StyledBlogListEmpty>
                ) : (
                  <>
                    <StyledBlogList>
                      {postItems.map((item) => {
                        const postUrl = `/blog/post/${item?.url_key ?? ''}`
                        return (
                          <div key={item?.post_id}>
                            <Link className="item-img" href={postUrl} title="blog-item-img">
                              <Image
                                src={item?.thumbnailSrc ?? ''}
                                alt="blog-item-img"
                                width={384}
                                height={200}
                              />
                            </Link>
                            <div className="item-tags">
                              {item?.tags.map((tag) => (
                                <div key={tag?.tag_id}>{tag?.name ?? ''}</div>
                              ))}
                            </div>
                            {item?.created_at && (
                              <div className="item-date">
                                {new Date(item.created_at)?.toLocaleDateString('en-US', {
                                  year: 'numeric',
                                  month: 'long',
                                  day: 'numeric'
                                })}
                              </div>
                            )}
                            <Link className="item-title" href={postUrl} title="blog-item-title">
                              {item?.title ?? ''}
                            </Link>

                            {item?.short_content && (
                              <p
                                dangerouslySetInnerHTML={{ __html: item.short_content }}
                                className="item-desc"
                              />
                            )}
                            <Link
                              className="item-read-more"
                              href={postUrl}
                              title="blog-item-read-more">
                              Read More
                            </Link>
                          </div>
                        )
                      })}
                    </StyledBlogList>

                    {/* Pagination */}
                    {pageSize > 0 && (
                      <CommonPagination
                        current={currentPage}
                        pageSize={pageSize}
                        total={total}
                        onChange={onPageChange}
                      />
                    )}
                  </>
                )}
              </div>
            )}
          </CommonLoading>
        </StyledBlogPage>
      </LineContainer>
    </>
  )
}

export default BlogPage
