<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Company\Model\SaveHandlerPool">
        <arguments>
            <argument name="handlers" xsi:type="array">
                <item name="erpDataAssociation" xsi:type="object">Silksoftwarecorp\ERPCompany\Model\Company\SaveHandler\ERPDataAssociation</item>
            </argument>
        </arguments>
    </type>
    <type name="Magento\Company\Api\CompanyRepositoryInterface">
        <plugin name="erpDataAssociation" type="Silksoftwarecorp\ERPCompany\Plugin\Company\CompanyRepositoryPlugin"/>
    </type>
    <type name="Magento\Company\Model\Company\DataProvider">
        <plugin name="erpDataAssociationCompanyDataProviderPlugin" type="Silksoftwarecorp\ERPCompany\Plugin\Company\DataProviderPlugin"/>
    </type>
    <preference for="Silksoftwarecorp\ERPCompany\Model\Company\ErpResolverInterface" type="Silksoftwarecorp\ERPCompany\Model\Company\ErpResolver" />
</config>
