import Link from 'next/link'
import { FormattedMessage } from 'react-intl'
import { Drawer } from 'antd'
import dynamic from 'next/dynamic'
import { clsx } from 'clsx'

import { Modal, MediaLayout } from '@/ui'
import { useMiniAccount } from '@/hooks/MiniAccount'
import SignInModal from '@/components/SignInModal'

import { StyledMiniAccount, StyledAccountDrawer } from './styled'

const ShowroomView = dynamic(() => import('@/components/ShowroomView'))

const MiniAccount = () => {
  const {
    modalRef,
    isLogin,
    handleOpen,
    handleLogout,
    drawerOpen,
    showAccountDrawer,
    closeAccountDrawer,
    userName,
    accountManagementList,
    companyManagementtList,
    showroomViewVisible
  } = useMiniAccount()

  return (
    <div>
      {isLogin ? (
        <StyledMiniAccount>
          <div className="account-item" aria-hidden onClick={showAccountDrawer}>
            <span className="icon">
              <svg
                className="user"
                width="1.2em"
                height="1.2em"
                fill="currentColor"
                focusable="false">
                <use xlinkHref="#icon-user" />
              </svg>
            </span>
            <MediaLayout>
              <span className="account-text">
                <FormattedMessage id="global.account" />
              </span>
            </MediaLayout>
          </div>
          <Drawer
            width={307}
            onClose={closeAccountDrawer}
            open={drawerOpen}
            extra={<div>CLOSE</div>}
            styles={{ header: { display: 'none' } }}>
            <StyledAccountDrawer>
              <div className="account-drawer-close">
                <span aria-hidden onClick={closeAccountDrawer}>
                  <svg
                    className="close"
                    width="16px"
                    height="16px"
                    fill="currentColor"
                    aria-hidden="true"
                    focusable="false">
                    <use xlinkHref="#icon-drawer-close" />
                  </svg>
                </span>
              </div>
              {/* TODO: Company */}
              <div className="account-drawer-title">Hello, Company {userName}</div>
              <div className="account-drawer-logout" aria-hidden onClick={handleLogout}>
                Sign Out
              </div>
              {showroomViewVisible && (
                <div className="account-drawer-showroom">
                  <ShowroomView />
                </div>
              )}
              <div className="line" />
              <div className="account-list">
                <h3>Account Management</h3>
                <ul>
                  {accountManagementList.map((item) => (
                    <li
                      key={`${item.label}_${item.url}`}
                      className={clsx({ active: item.isActivePath })}>
                      <Link href={item.url}>
                        <span>{item.label}</span>
                      </Link>
                    </li>
                  ))}
                </ul>
                <h3>Company Management</h3>
                <ul>
                  {companyManagementtList.map((item) => (
                    <li
                      key={`${item.label}_${item.url}`}
                      className={clsx({ active: item.isActivePath })}>
                      <Link href={item.url}>
                        <span>{item.label}</span>
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            </StyledAccountDrawer>
          </Drawer>
        </StyledMiniAccount>
      ) : (
        <StyledMiniAccount aria-hidden onClick={handleOpen}>
          <span className="icon" aria-hidden>
            <svg
              className="user"
              width="1.2em"
              height="1.2em"
              fill="currentColor"
              focusable="false">
              <use xlinkHref="#icon-user" />
            </svg>
          </span>
          <MediaLayout>
            <span className="login-text">
              <FormattedMessage id="global.login" />
            </span>
          </MediaLayout>
        </StyledMiniAccount>
      )}
      <Modal
        ref={modalRef}
        width={980}
        className="sign-in-modal"
        maskClosable={false}
        closable={false}>
        <SignInModal />
      </Modal>
    </div>
  )
}

export default MiniAccount
