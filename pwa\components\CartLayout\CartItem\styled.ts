import styled from '@emotion/styled'

export const StyledCartItem = styled.div`
  display: grid;
  padding: 24px 0;
  grid-template-columns: 120px 1fr;
  justify-content: space-between;
  align-items: flex-start;
  border-bottom: 1px solid #d9d9d9;

  .cart-item__main {
    display: grid;
    grid-template-columns: 1fr 82px 124px 82px;
    grid-column-gap: 20px;
    padding-left: 24px;
  }

  .cart-item__img img {
    object-fit: contain;
  }

  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    appearance: auto;
  }

  .cart-item__qty {
    .${({ theme }) => theme.namespace}-form-item {
      margin-bottom: 12px;
    }
    .quantity {
      display: block;

      input {
        padding: 0 30px;
        width: 100%;
        height: 50px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        font-weight: 400;
        font-size: 16px;
        line-height: 24px;
        letter-spacing: 0.02em;
      }

      .btn-prev,
      .btn-next {
        position: absolute;
        top: 50%;
        left: 8px;
        width: 32px;
        height: 32px;
        border: none;
        background: transparent;
        z-index: 20;
        transform: translateY(-50%);
      }

      .btn-next {
        left: unset;
        right: 8px;
      }
    }

    .update-btn {
      width: 100%;
      height: 36px;
      border-radius: 4px;
    }
  }

  .cart-item__price {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    min-height: 50px;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: 0.02em;
  }

  .cart-item__utils {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-end;
    min-height: 120px;

    .action div {
      font-weight: 700;
      font-size: 13px;
      line-height: 20px;
      letter-spacing: 0.02em;
      color: var(--color-primary);
      cursor: pointer;

      svg {
        margin: -4px 5px 0 0;
      }
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    padding-bottom: 20px;

    .cart-item__main {
      grid-template-columns: 1fr;
      padding-left: 16px;
    }

    .cart-item__price {
      justify-content: flex-start;
      margin-top: 10px;
      min-height: unset;
    }

    .cart-item__qty {
      margin-top: 15px;

      .update-btn {
        margin-bottom: 16px;
      }
    }

    .cart-item__utils {
      align-items: flex-start;
      min-height: 60px;

      .price-wrapper {
        display: flex;
        font-weight: 700;
        font-size: 16px;
        line-height: 24px;
        letter-spacing: 0.02em;

        .price {
          margin-top: 0;
          margin-left: 5px;
          font-weight: 700;
        }
      }

      .action {
        display: flex;
        justify-content: flex-end;
        width: 100%;
      }
    }
  }
`

export const StyledItemMain = styled.div`
  .title {
    display: inline-block;
    margin-top: 6px;
    margin-bottom: 0;
    font-weight: 700;
    font-size: 18px;
    line-height: 26px;
    letter-spacing: 0;
    color: var(--color-font) !important;

    span {
      display: -webkit-box;
      overflow: hidden;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
    }
  }

  .sku {
    margin-top: 6px;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: 0;
  }

  .stock {
    margin-top: 6px;
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    .title {
      margin-top: 8px;
    }

    .sku {
      margin-top: 0;
    }
  }
`
