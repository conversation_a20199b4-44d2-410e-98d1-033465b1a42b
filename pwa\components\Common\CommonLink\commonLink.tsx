import { memo } from 'react'
import { clsx } from 'clsx'
import { useSelector } from 'react-redux'

import { StyledCommonLink } from './styled'

const CommonLink = ({
  children,
  href = '/',
  withoutSuffix = false,
  className = '',
  fontSize = 15,
  lineHeight = 21,
  dark = false,
  underline = false,
  uppercase = false,
  type = '',
  ...commonLinkProps
}) => {
  const storeConfig = useSelector((state: any) => state.app.storeConfig)
  const suffix: string = storeConfig?.product_url ?? ''
  // const blogConfig = useSelector((state: Store) => state.app.blogConfig)
  // const suffix: string = blogConfig?.url_fix ?? ''

  return (
    <StyledCommonLink
      className={clsx(`${className} common-link-component`, {
        'is-dark': dark,
        'is-btn': type === 'button',
        'is-uppercase': uppercase
      })}
      href={`${href}${withoutSuffix ? '' : suffix}`}
      underline={underline}
      fontSize={fontSize}
      lineHeight={lineHeight}
      {...commonLinkProps}>
      {children}
    </StyledCommonLink>
  )
}

export default memo(CommonLink)
