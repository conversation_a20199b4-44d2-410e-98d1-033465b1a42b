<?php

namespace Silksoftwarecorp\CompanyB2b\Plugin\Company\Model;

class ResourcePoolPlugin
{
    /**
     * Around plugin for getDefaultResources
     *
     * @param \Magento\Company\Model\ResourcePool $subject
     * @param callable $proceed
     * @return array
     */
    public function aroundGetDefaultResources(
        \Magento\Company\Model\ResourcePool $subject,
        callable $proceed
    ) {
        $result = [
            "Magento_Sales::all",
            "Magento_Sales::place_order",
            "Magento_Sales::payment_account",
            "Magento_Sales::view_orders",
            "Magento_Sales::view_orders_sub",
            "Magento_NegotiableQuote::all",
            "Magento_NegotiableQuote::view_quotes",
            "Magento_NegotiableQuote::manage",
            "Magento_NegotiableQuote::checkout",
            "Magento_NegotiableQuoteTemplate::all",
            "Magento_NegotiableQuoteTemplate::view_template",
            "Magento_NegotiableQuoteTemplate::manage",
            "Magento_NegotiableQuoteTemplate::generate_quote",
            "Magento_PurchaseOrder::all",
            "Magento_PurchaseOrder::view_purchase_orders",
            "Magento_PurchaseOrder::view_purchase_orders_for_subordinates",
            "Magento_PurchaseOrder::view_purchase_orders_for_company",
            "Magento_PurchaseOrderRule::view_approval_rules",
            "Magento_Company::view",
            "Magento_Company::view_account",
            "Magento_Company::view_address",
            "Magento_Company::contacts",
            "Magento_Company::payment_information",
            "Magento_Company::shipping_information",
            "Magento_Company::user_management",
            "Magento_Company::users_view"
        ];

        return $result;
    }
} 