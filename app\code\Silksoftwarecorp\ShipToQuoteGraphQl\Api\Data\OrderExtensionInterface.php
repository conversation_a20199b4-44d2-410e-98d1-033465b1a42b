<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */
declare(strict_types=1);

namespace Silksoftwarecorp\ShipToQuoteGraphQl\Api\Data;

/**
 * Interface for order extension attributes
 */
interface OrderExtensionInterface
{
    /**
     * Get ship to id
     *
     * @return string|null
     */
    public function getShipToId(): ?string;

    /**
     * Set ship to id
     *
     * @param string $shipToId
     * @return $this
     */
    public function setShipToId(string $shipToId): self;

    /**
     * Get customer contact id
     *
     * @return string|null
     */
    public function getCustomerContactId(): ?string;

    /**
     * Set customer contact id
     *
     * @param string $customerContactId
     * @return $this
     */
    public function setCustomerContactId(string $customerContactId): self;
}



