import { gql } from '@apollo/client'
import type { DocumentNode } from '@apollo/client'

import { price_range } from '../fragment/priceRange'

export const GET_BESTSELLERS: DocumentNode = gql`
  query getBestSellers(
    $search: String
    $filter: ProductAttributeFilterInput
    $pageSize: Int
    $currentPage: Int
    $sort: ProductAttributeSortInput
  ) {
    products(
      search: $search
      pageSize: $pageSize
      currentPage: $currentPage
      filter: $filter
      sort: $sort
    ) {
      items {
        name
        sku
        url_key
        image {
          label
          url
        }
        small_image {
          label
          url
        }
        price_range {
          ...price_range
          __typename
        }
        stock_status
        __typename
      }
    }
  }
  ${price_range}
`
