import styled from '@emotion/styled'

export const StyledProductContent = styled.div`
  padding-bottom: 70px;

  .half-width {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-column-gap: 24px;
  }

  .pdp-video {
    margin-top: 50px;

    .pagebuilder-column-line {
      display: grid !important;
      grid-template-columns: 779px 1fr;
      align-items: center;
      min-height: 474px;

      .pagebuilder-column {
        width: 100% !important;

        &:last-of-type {
          justify-content: center !important;
          align-items: center !important;

          h2,
          div {
            width: 277px;
          }

          h2 {
            margin-bottom: 10px;
            font-weight: 700;
            font-size: 32px;
            line-height: 38px;
            letter-spacing: 0;
          }

          p span {
            font-weight: 400;
            font-size: 15px;
            line-height: 21px;
            letter-spacing: 0.01em;
          }
        }
      }
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    padding: 0 16px 60px;

    .half-width {
      grid-template-columns: 1fr;
    }

    .pdp-video {
      .pagebuilder-column-line {
        display: flex !important;
        flex-direction: column-reverse !important;
        min-height: unset;

        .pagebuilder-column {
          &:first-of-type {
            margin-top: 25px;
            min-height: 209px;
          }

          &:last-of-type {
            h2,
            div {
              width: 100%;
            }

            h2 {
              font-size: 30px;
              line-height: 36px;
            }

            p span {
              font-size: 16px;
              line-height: 24px;
              letter-spacing: 0.02em;
            }
          }
        }
      }
    }
  }
`
