<?xml version="1.0"?>
<!--
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Silksoftwarecorp\Inventory\Api\BranchManagerProfileRepositoryInterface"
                type="Silksoftwarecorp\Inventory\Model\BranchManagerProfileRepository"/>
    <preference for="Silksoftwarecorp\Inventory\Api\Data\BranchManagerProfileInterface"
                type="Silksoftwarecorp\Inventory\Model\BranchManagerProfile"/>

    <!-- Configure inventory source form ModifierPool -->
    <virtualType name="Silksoftwarecorp\Inventory\Ui\DataProvider\Source\Form\ModifierPool" type="Magento\Ui\DataProvider\Modifier\Pool">
        <arguments>
            <argument name="modifiers" xsi:type="array">
                <item name="manager_profile" xsi:type="array">
                    <item name="class" xsi:type="string">Silksoftwarecorp\Inventory\Ui\DataProvider\Source\Form\Modifier\ManagerProfile</item>
                    <item name="sortOrder" xsi:type="number">10</item>
                </item>
            </argument>
        </arguments>
    </virtualType>

    <!-- Register Modifier to inventory_source_form data provider chain -->
    <type name="Magento\InventoryAdminUi\Ui\DataProvider\SourceDataProvider">
        <arguments>
            <argument name="pool" xsi:type="object">Silksoftwarecorp\Inventory\Ui\DataProvider\Source\Form\ModifierPool</argument>
        </arguments>
    </type>

    <!-- Register Plugin to SourceDataProvider to handle form data -->
    <type name="Magento\InventoryAdminUi\Ui\DataProvider\SourceDataProvider">
        <plugin name="managerProfilesDataProvider" type="Silksoftwarecorp\Inventory\Plugin\Ui\DataProvider\SourceDataProviderPlugin" sortOrder="10"/>
    </type>

    <type name="Magento\InventoryAdminUi\Model\Source\SourceHydrator">
        <plugin name="formatScheduleData" type="Silksoftwarecorp\Inventory\Plugin\Model\Source\SourceHydratorPlugin" sortOrder="10"/>
    </type>

    <!-- Configure Manager Profile image uploader -->
    <virtualType name="Silksoftwarecorp\Inventory\Model\ManagerProfileImageUploader" type="Magento\Catalog\Model\ImageUploader">
        <arguments>
            <argument name="baseTmpPath" xsi:type="string">manager_profile/tmp</argument>
            <argument name="basePath" xsi:type="string">manager_profile</argument>
            <argument name="allowedExtensions" xsi:type="array">
                <item name="jpg" xsi:type="string">jpg</item>
                <item name="jpeg" xsi:type="string">jpeg</item>
                <item name="gif" xsi:type="string">gif</item>
                <item name="png" xsi:type="string">png</item>
            </argument>
        </arguments>
    </virtualType>

    <!-- Configure Upload controller to use custom ImageUploader -->
    <type name="Silksoftwarecorp\Inventory\Controller\Adminhtml\Media\Upload">
        <arguments>
            <argument name="imageUploader" xsi:type="object">Silksoftwarecorp\Inventory\Model\ManagerProfileImageUploader</argument>
        </arguments>
    </type>

    <!-- Configure ImageProcessor to use custom ImageUploader -->
    <type name="Silksoftwarecorp\Inventory\Model\ImageProcessor">
        <arguments>
            <argument name="imageUploader" xsi:type="object">Silksoftwarecorp\Inventory\Model\ManagerProfileImageUploader</argument>
        </arguments>
    </type>

    <!-- Register SourceRepository plugins to handle extension attributes -->
    <type name="Magento\InventoryApi\Api\SourceRepositoryInterface">
        <plugin name="load_custom_fields_on_get" type="Silksoftwarecorp\Inventory\Plugin\InventoryApi\SourceRepository\LoadCustomFieldsOnGetPlugin"/>
        <plugin name="load_custom_fields_on_get_list" type="Silksoftwarecorp\Inventory\Plugin\InventoryApi\SourceRepository\LoadCustomFieldsOnGetListPlugin"/>
        <plugin name="save_custom_fields" type="Silksoftwarecorp\Inventory\Plugin\InventoryApi\SourceRepository\SaveCustomFieldsPlugin"/>
    </type>
</config>
