<?php

namespace Silksoftwarecorp\CatalogGraphQl\Model;

use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Framework\Exception\NoSuchEntityException;

class ProductDataProvider extends \Magento\CatalogGraphQl\Model\ProductDataProvider
{
    /**
     * @var ProductRepositoryInterface
     */
    private $productRepository;

    /**
     * @param ProductRepositoryInterface $productRepository
     */
    public function __construct(ProductRepositoryInterface $productRepository)
    {
        parent::__construct($productRepository);

        $this->productRepository = $productRepository;
    }

    /**
     * Get product data by id
     *
     * @param int $productId
     * @return array
     * @throws NoSuchEntityException
     */
    public function getProductDataById(int $productId): array
    {
        $product = $this->productRepository->getById($productId);
        $productData = $product->toArray();
        $productData = array_filter($productData, function ($value) {
            return $value !== null;
        });

        $productData['model'] = $product;
        return $productData;
    }
}

