<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\StoreGraphQl\Model\Resolver\Store\StoreConfigDataProvider">
        <arguments>
            <argument name="extendedConfigData" xsi:type="array">
                <item name="google_website_key_v2_recaptcha" xsi:type="string">recaptcha_frontend/type_recaptcha/public_key</item>
                <item name="google_website_key_v2_invisible" xsi:type="string">recaptcha_frontend/type_invisible/public_key</item>
                <item name="google_website_key_v3_recaptcha" xsi:type="string">recaptcha_frontend/type_recaptcha_v3/public_key</item>
                <item name="recaptcha_frontend_type_for_customer_login" xsi:type="string">recaptcha_frontend/type_for/customer_login</item>
                <item name="recaptcha_frontend_type_for_customer_forgot_password" xsi:type="string">recaptcha_frontend/type_for/customer_forgot_password</item>
                <item name="recaptcha_frontend_type_for_customer_create" xsi:type="string">recaptcha_frontend/type_for/customer_create</item>
                <item name="recaptcha_frontend_type_for_customer_edit" xsi:type="string">recaptcha_frontend/type_for/customer_edit</item>
                <item name="recaptcha_frontend_type_for_company_create" xsi:type="string">recaptcha_frontend/type_for/company_create</item>
                <item name="recaptcha_frontend_type_for_contact" xsi:type="string">recaptcha_frontend/type_for/contact</item>
                <item name="recaptcha_frontend_type_for_product_review" xsi:type="string">recaptcha_frontend/type_for/product_review</item>
                <item name="recaptcha_frontend_type_for_newsletter" xsi:type="string">recaptcha_frontend/type_for/newsletter</item>
                <item name="recaptcha_frontend_type_for_giftcard" xsi:type="string">recaptcha_frontend/type_for/giftcard</item>
                <item name="recaptcha_frontend_type_for_customer_invite_create" xsi:type="string">recaptcha_frontend/type_for/customer_invite_create</item>
                <item name="recaptcha_frontend_type_for_sendfriend" xsi:type="string">recaptcha_frontend/type_for/sendfriend</item>
                <item name="recaptcha_frontend_type_for_place_order" xsi:type="string">recaptcha_frontend/type_for/place_order</item>
                <item name="recaptcha_frontend_type_for_wishlist" xsi:type="string">recaptcha_frontend/type_for/wishlist</item>
                <item name="recaptcha_frontend_type_for_coupon_code" xsi:type="string">recaptcha_frontend/type_for/coupon_code</item>
                <item name="recaptcha_frontend_type_for_paypal_payflowpro" xsi:type="string">recaptcha_frontend/type_for/paypal_payflowpro</item>
                <item name="recaptcha_frontend_type_for_braintree" xsi:type="string">recaptcha_frontend/type_for/braintree</item>
            </argument>
        </arguments>
    </type>
</config>
