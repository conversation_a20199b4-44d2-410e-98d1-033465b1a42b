import styled from '@emotion/styled'

export const StyledShipTo = styled.div`
  display: grid;
  grid-template-columns: auto auto auto;
  grid-column-gap: 8px;
  align-items: center;
  font-weight: 700;
  font-size: 16px;
  line-height: 22px;
  letter-spacing: 0;
  color: #fff;

  .ship-to-text {
    margin-bottom: -2px;
  }

  .ship-edit {
    font-size: 14px;
    text-decoration: underline;
    cursor: pointer;
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    font-size: 15px;

    .ship-edit {
      font-size: 13px;
    }
  }
`

export const StyledShipToAddress = styled.div`
  margin-top: 24px;

  .address-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 16px;
    border-bottom: 1px solid #d9d9d9;

    h2 {
      margin-bottom: 0;
      font-weight: 700;
      font-size: 26px;
      line-height: 34px;
      letter-spacing: 0;
      color: var(--color-black);
    }
  }

  .address-search {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 32px;
    padding-bottom: 20px;

    .blevins-input-affix-wrapper {
      padding-right: 0;
      width: 100%;
      max-width: 438px;
      height: 44px;
      border-radius: 3px;

      input::placeholder {
        font-weight: 400;
        font-size: 16px;
        line-height: 24px;
        letter-spacing: 0.02em;
        color: #777;
      }

      .search-icon {
        padding: 6px;
        cursor: pointer;
      }
    }
  }

  .addresses-available {
    font-weight: 400;
    font-size: 15px;
    line-height: 21px;
    letter-spacing: 0.01em;
    color: #000;
  }

  .${({ theme }) => theme.namespace}-table-thead tr th {
    font-size: 16px !important;
  }

  .${({ theme }) => theme.namespace}-table-body {
    height: 365px;
  }

  &#ship-to-address .common-table-component table tbody tr {
    &.is-selected {
      td {
        background: var(--color-bg-base) !important;

        div {
          color: var(--color-white) !important;
        }

        &:first-of-type {
          border-radius: 5px 0 0 5px;
        }

        &:last-of-type {
          border-radius: 0 5px 5px 0;
        }

        .select-btn {
          color: var(--color-white) !important;
          text-decoration: none;

          svg {
            margin-right: -5px;
          }
        }
      }
    }
  }

  .view-all {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 24px auto 0;
    width: 132px;
    height: 44px;
    font-weight: 700;
    font-size: 14px;
    line-height: 20px;
    letter-spacing: 0.01em;
    color: var(--color-black);
    border: 2px solid #003865;
    border-radius: 3px;
    cursor: pointer;
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    .address-header {
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;
      border-bottom: none;

      h2 {
        margin-bottom: 8px;
      }

      button {
        margin-top: 24px;
        max-width: 100% !important;
      }
    }

    .address-search {
      padding-top: 0;
    }

    .select-btn {
      margin-top: 8px;
      width: 100%;
      height: 41px;
      background: #fff !important;
      border: 1px solid var(--color-primary);
      border-radius: 5px;
      text-decoration: none;
    }

    .mobile-table-item.is-selected {
      background: var(--color-bg-base);
      border-radius: 4px;

      div,
      p,
      span {
        color: var(--color-white);
      }

      .select-btn {
        background: rgba(255, 255, 255, 0.3) !important;
        color: var(--color-white) !important;
        border: none;
      }
    }

    .view-all {
      width: 100%;
    }
  }
  @media (min-width: ${({ theme }) => theme.breakPoint.sm + 1}px) {
    &#ship-to-address .common-table-component table tbody tr {
      td {
        height: 74px;
        padding: 0;
      }
    }
  }
`

export const StyledShipToTableRow = styled.div`
  @media (min-width: ${({ theme }) => theme.breakPoint.sm + 1}px) {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 20px 16px;
    height: 100%;
    cursor: pointer;

    &.row-action {
      justify-content: center;
    }
  }
`
