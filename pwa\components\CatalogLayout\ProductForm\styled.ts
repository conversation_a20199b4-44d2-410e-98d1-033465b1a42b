import styled from '@emotion/styled'

export const StyledProductForm = styled.div`
  margin-top: 24px;

  .list-action {
    margin-top: 24px;
  }

  .grid {
    display: grid;
    grid-template-columns: 179px 179px;
    grid-column-gap: 16px;
    align-items: center;

    .${({ theme }) => theme.namespace} {
      &-form-item {
        margin-bottom: 0;
      }

      &-input {
        padding: 0 46px;
        height: 50px;
        border-radius: 3px;
        font-weight: 400;
        font-size: 16px;
        line-height: 24px;
        letter-spacing: 0.02em;
      }
    }

    .btn-prev,
    .btn-next {
      position: absolute;
      top: 50%;
      left: 12px;
      width: 32px;
      height: 32px;
      border-width: 0;
      background: transparent;
      z-index: 20;
      transform: translateY(-50%);
    }

    .btn-next {
      left: unset;
      right: 12px;
    }

    .quantity {
      display: block;
    }
  }

  .actions {
    button {
      width: 100%;
      height: 49px;
      text-transform: uppercase;
      border-radius: 3px;
      font-weight: 700;
      font-size: 16px;
      line-height: 21px;
      letter-spacing: 0.03em;

      &.${({ theme }) => theme.namespace}-btn-loading {
        background: var(--color-primary);
        color: var(--color-white);
      }
    }
  }

  .contact-btn {
    width: 100%;
    max-width: 245px;
    height: 49px;
    background: var(--color-bg-base) !important;
    border-radius: 3px;
    font-weight: 700;
    font-size: 16px;
    line-height: 21px;
    letter-spacing: 0.03em;
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    margin-top: 20px;

    .grid {
      grid-template-columns: 149px 1fr;
    }

    .contact-btn {
      max-width: 100%;
    }
  }
`
