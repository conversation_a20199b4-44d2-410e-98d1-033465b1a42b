<?php
namespace Silksoftwarecorp\Customer\Plugin;

use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Framework\Exception\InputException;

class CustomerValidatePlugin
{
    public function beforeSave(\Magento\Customer\Api\CustomerRepositoryInterface $subject, CustomerInterface $customer)
    {
        $customAttribute = $customer->getCustomAttribute('erp_contact_id');

        if (!$customAttribute || empty($customAttribute->getValue())) {
            throw new InputException(__('Erp Contact ID is required.'));
        }
    }
}
