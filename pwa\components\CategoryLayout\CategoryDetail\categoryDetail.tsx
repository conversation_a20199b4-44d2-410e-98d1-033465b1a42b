import dynamic from 'next/dynamic'
import { clsx } from 'clsx'
import { memo, useState, useEffect, useRef, useCallback } from 'react'
import { LineContainer } from '@ranger-theme/ui'

import { StyledCategoryDetail } from './styled'

const Pagebuilder = dynamic(() => import('@ranger-theme/pagebuilder'), { ssr: false })

const CategoryDetail = (props: any) => {
  const { categoryDescription, image, name } = props
  const descriptionRef = useRef<any>(null)

  const [isTextOverflowing, setIsTextOverflowing] = useState<boolean>(false)
  const [isAllVisible, setIsAllVisible] = useState<boolean>(false)

  const toggleCollapse = useCallback(() => {
    setIsAllVisible(!isAllVisible)
  }, [isAllVisible])

  useEffect(() => {
    if (!categoryDescription || !descriptionRef?.current) {
      return
    }
    let chechNum = 0
    const checkInterval = setInterval(() => {
      chechNum++
      const descriptionELe =
        descriptionRef.current?.querySelectorAll('.pagebuilder__text span')?.[0]
      if (descriptionELe && descriptionELe?.scrollHeight && descriptionELe?.clientHeight) {
        setIsTextOverflowing(descriptionELe.scrollHeight > descriptionELe.clientHeight)
        clearInterval(checkInterval)
      }
      if (chechNum >= 10) {
        clearInterval(checkInterval)
      }
    }, 200)

    return () => clearInterval(checkInterval)
  }, [categoryDescription])

  return (
    <StyledCategoryDetail style={{ backgroundImage: image ? `url(${image})` : 'unset' }}>
      <LineContainer>
        <h2 dangerouslySetInnerHTML={{ __html: name }} />
        {categoryDescription && (
          <div
            ref={descriptionRef}
            className={clsx('category-description', { 'is-all-visible': isAllVisible })}>
            <Pagebuilder html={categoryDescription} />
          </div>
        )}
        {isTextOverflowing && (
          <span aria-hidden="true" className="read-more" onClick={toggleCollapse}>
            {isAllVisible ? 'Collapse' : 'Read More'}
          </span>
        )}
      </LineContainer>
    </StyledCategoryDetail>
  )
}

export default memo(CategoryDetail)
