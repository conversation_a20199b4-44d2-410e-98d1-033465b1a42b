<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */

declare(strict_types=1);

namespace Silksoftwarecorp\InventoryGraphQl\Model\Formatter;

use Magento\InventoryApi\Api\Data\SourceInterface;
use Silksoftwarecorp\InventoryGraphQl\Model\Region\DataProvider as RegionDataProvider;

/**
 * Formatter for Source data
 */
class Source
{
    /**
     * @var RegionDataProvider
     */
    private $regionDataProvider;

    /**
     * @param RegionDataProvider $regionDataProvider
     */
    public function __construct(
        RegionDataProvider $regionDataProvider
    ) {
        $this->regionDataProvider = $regionDataProvider;
    }

    /**
     * Format source data to match BranchSource type
     *
     * @param SourceInterface $source
     * @return array
     */
    public function format(SourceInterface $source): array
    {
        return [
            'code' => $source->getSourceCode(),
            'name' => $source->getName(),
            'active' => (bool)$source->isEnabled(),
            'description' => $source->getDescription(),
            'address' => $this->formatAddress($source),
            'lat' => $source->getLatitude(),
            'lng' => $source->getLongitude(),
            'contact_name' => $source->getContactName(),
            'email' => $source->getEmail(),
            'phone' => $source->getPhone(),
            'fax' => $source->getFax(),
            'install_request_form' => (bool)$source->getData('install_request_form'),
            'install_app' => (bool)$source->getData('install_app'),
            'text_number' => $source->getData('text_number'),
            'schedule_string' => $source->getData('schedule'),
            'schedules' => $this->formatSchedules($source),
            'holiday_closures' => $source->getData('holiday_closures'),
        ];
    }

    /**
     * Format address data
     *
     * @param SourceInterface $source
     * @return array
     */
    private function formatAddress(SourceInterface $source): array
    {
        return [
            'country_code' => $source->getCountryId(),
            'region' => $this->formatRegion($source),
            'city' => $source->getCity(),
            'street' => $this->formatStreet($source),
            'postcode' => $source->getPostcode(),
        ];
    }

    private function formatStreet(SourceInterface $source): array
    {
        $streets = [$source->getStreet()];
        if ($source->getData('street1')) {
            $streets[] = $source->getData('street1');
        }

        return $streets;
    }

    /**
     * Format region data
     *
     * @param SourceInterface $source
     * @return array
     */
    private function formatRegion(SourceInterface $source): array
    {
        $regionId = $source->getRegionId();
        $countryId = $source->getCountryId();

        $region = $this->regionDataProvider->getRegionById($countryId, (string)$regionId);

        return [
            'region_id' => $regionId,
            'region_code' => $region?->getCode(),
            'region' => $region?->getName() ?? $source->getRegion(),
        ];
    }

    /**
     * Format schedules data
     *
     * @param SourceInterface $source
     * @return array
     */
    private function formatSchedules(SourceInterface $source): array
    {
        // Parse the schedule data
        $scheduleData = $source->getData('schedule');
        if ($scheduleData && is_string($scheduleData)) {
            $scheduleData = json_decode($scheduleData, true);
        }

        $defaultSchedule = [
            'enabled' => false,
            'monday' => $this->getDefaultScheduleInfo(),
            'tuesday' => $this->getDefaultScheduleInfo(),
            'wednesday' => $this->getDefaultScheduleInfo(),
            'thursday' => $this->getDefaultScheduleInfo(),
            'friday' => $this->getDefaultScheduleInfo(),
            'saturday' => $this->getDefaultScheduleInfo(),
            'sunday' => $this->getDefaultScheduleInfo(),
        ];

        $formattedOfficeSchedule = $defaultSchedule;
        $formattedStoreSchedule = $defaultSchedule;

        if ($scheduleData && is_array($scheduleData)) {
            // Handle the office schedule
            if (isset($scheduleData['office']) && is_array($scheduleData['office'])) {
                $isActive = $scheduleData['office']['active'] ?? false;
                if ($isActive) {
                    $formattedOfficeSchedule = $this->formatScheduleByType($scheduleData['office'], $defaultSchedule);
                }
            }

            // Handle the store schedule
            if (isset($scheduleData['store']) && is_array($scheduleData['store'])) {
                $isActive = $scheduleData['store']['active'] ?? false;
                if ($isActive) {
                    $formattedStoreSchedule = $this->formatScheduleByType($scheduleData['store'], $defaultSchedule);
                }
            }
        }

        return [
            'office' => $formattedOfficeSchedule,
            'store' => $formattedStoreSchedule,
        ];
    }

    /**
     * Format schedule by type (office or store)
     *
     * @param array $scheduleTypeData
     * @param array $defaultSchedule
     * @return array
     */
    private function formatScheduleByType(array $scheduleTypeData, array $defaultSchedule): array
    {
        $formattedSchedule = $defaultSchedule;

        $formattedSchedule['enabled'] = (bool)($scheduleTypeData['active'] ?? false);
        unset($scheduleTypeData['active']);

        foreach ($scheduleTypeData as $day => $dayData) {
            if (isset($defaultSchedule[$day]) && is_array($dayData)) {
                $formattedSchedule[$day] = [
                    'status' => (bool)($dayData['status'] ?? false),
                    'from' => $this->formatTime($dayData['form'] ?? []),
                    'break_form' => $this->formatTime($dayData['break_form'] ?? []),
                    'break_to' => $this->formatTime($dayData['break_to'] ?? []),
                    'to' => $this->formatTime($dayData['to'] ?? []),
                ];
            }
        }

        return $formattedSchedule;
    }

    /**
     * Format time from hours:minutes format
     *
     * @param array $timeData
     * @return string|null
     */
    private function formatTime(array $timeData): ?string
    {
        $hours = $timeData['hours'] ?? null;
        $minutes = $timeData['minutes'] ?? '00';

        // If "hours" is null or an empty string, return null directly
        if ($hours === null || $hours === '') {
            return null;
        }

        // Make sure that both hours and minutes are in two-digit format
        $hours = str_pad($hours, 2, '0', STR_PAD_LEFT);
        $minutes = str_pad($minutes, 2, '0', STR_PAD_LEFT);

        return $hours . ':' . $minutes;
    }

    /**
     * Get default schedule info
     *
     * @return array
     */
    private function getDefaultScheduleInfo(): array
    {
        return [
            'status' => false,
            'from' => null,
            'break_form' => null,
            'break_to' => null,
            'to' => null,
        ];
    }
}
