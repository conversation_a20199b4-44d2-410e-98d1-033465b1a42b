<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */

declare(strict_types=1);

namespace Silksoftwarecorp\InventoryGraphQl\Model\Resolver\Source;

use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\InventoryApi\Api\Data\SourceInterface;
use Silksoftwarecorp\Inventory\Api\BranchManagerProfileRepositoryInterface;
use Silksoftwarecorp\InventoryGraphQl\Model\Formatter\ManagerProfile;

/**
 * Resolver for getting general manager profile
 */
class GeneralManager implements ResolverInterface
{
    /**
     * @var BranchManagerProfileRepositoryInterface
     */
    private $managerProfileRepository;

    /**
     * @var ManagerProfile
     */
    private $managerFormatter;

    /**
     * @param BranchManagerProfileRepositoryInterface $managerProfileRepository
     * @param ManagerProfile $managerFormatter
     */
    public function __construct(
        BranchManagerProfileRepositoryInterface $managerProfileRepository,
        ManagerProfile $managerFormatter
    ) {
        $this->managerProfileRepository = $managerProfileRepository;
        $this->managerFormatter = $managerFormatter;
    }

    /**
     * @inheritdoc
     */
    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
        if (!isset($value['code'])) {
            return null;
        }

        try {
            $sourceCode = $value['code'];
            $generalManager = $this->managerProfileRepository->getGeneralManagerBySourceCode($sourceCode);

            if ($generalManager) {
                return $this->managerFormatter->format($generalManager);
            }
        } catch (\Exception $e) {
            // 如果获取失败，返回 null
        }

        return null;
    }
} 