import styled from '@emotion/styled'

export const StyledDashboardNav = styled.div`
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  grid-column-gap: 24px;
  align-items: center;

  a {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 92px;
    border-radius: 4px;
    background-color: var(--color-primary);
    font-weight: 700;
    font-size: 14px;
    line-height: 20px;
    letter-spacing: 0.03em;
    text-transform: capitalize;
    color: var(--color-white) !important;

    svg {
      margin-bottom: 8px;
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 24px 16px;
  }
`
