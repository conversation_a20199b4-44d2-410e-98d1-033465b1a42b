import { gql } from '@apollo/client'

import { price_range } from '../fragment/priceRange'

export const GET_B2B_REQUISTION_LIST = gql`
  query getRequisitionList(
    $pageSize: Int = 20
    $currentPage: Int = 1
    $filter: RequisitionListFilterInput
    $itemPageSize: Int = 100
    $itemCurrentPage: Int = 1
  ) {
    customer {
      requisitionList: requisition_lists(
        pageSize: $pageSize
        currentPage: $currentPage
        filter: $filter
      ) {
        items {
          description
          items(currentPage: $itemCurrentPage, pageSize: $itemPageSize) {
            items {
              product {
                id
                name
                sku
                type_id
                url_key
                only_x_left_in_stock
                stock_status
                thumbnail {
                  label
                  url
                }
                price_range {
                  ...price_range
                  __typename
                }
              }
              quantity
              uid
            }
          }
          items_count
          name
          uid
          updated_at
        }
        pages: page_info {
          current_page
          page_size
          total_pages
        }
        total_count
      }
    }
  }
  ${price_range}
`
