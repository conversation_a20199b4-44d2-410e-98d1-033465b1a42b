import Link from 'next/link'
import styled from '@emotion/styled'

export const StyledCommonLink = styled(Link)`
  font-weight: 700;
  font-size: ${(props: any) => props.fontSize}px !important;
  line-height: ${(props: any) => props.lineHeight}px !important;
  letter-spacing: 0.01em;
  text-decoration: underline;
  text-decoration-style: solid;
  text-decoration-thickness: 0;
  color: var(--color-primary) !important;
  text-decoration: ${(props: any) => (props.underline ? 'underline' : 'normal')} !important;

  &.is-dark {
    color: var(--color-font) !important;
  }

  &.is-uppercase {
    text-transform: uppercase;
  }

  &.is-btn {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    padding: 0 32px;
    height: 44px;
    background: var(--color-primary);
    color: var(--color-white) !important;
    border-radius: 3px;
  }
`
