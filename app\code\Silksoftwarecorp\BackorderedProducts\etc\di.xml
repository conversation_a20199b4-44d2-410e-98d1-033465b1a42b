<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Silksoftwarecorp\BackorderedProducts\Model\BackorderRepository">
        <arguments>
            <argument name="curlFactory" xsi:type="object">Magento\Framework\HTTP\Client\CurlFactory</argument>
        </arguments>
    </type>
    <preference for="Silksoftwarecorp\BackorderedProducts\Api\BackorderRepositoryInterface" type="Silksoftwarecorp\BackorderedProducts\Model\BackorderRepository"/>
</config> 