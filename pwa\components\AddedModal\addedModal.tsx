import Image from 'next/image'
import { useRouter } from 'next/compat/router'
import { useMemo } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { FormattedMessage } from 'react-intl'
import { Button, Modal } from 'antd'

import { getUnitPrice } from '@/utils'
import { actions as cartActions } from '@/store/cart'
import BasePrice from '@/components/BasePrice'
import BlogList from './BlogList'
import { StyledAddedModal, StyledProducts, StyledActions } from './styled'

const AddedModal = () => {
  const dispatch = useDispatch()
  const router = useRouter()
  const cartDetail = useSelector((state: Store) => state.cart.cartDetail)
  const addedModal = useSelector((state: Store) => state.cart.addedModal)
  const inStock: boolean = addedModal?.product?.stock_status === 'IN_STOCK'
  const price: number = getUnitPrice(addedModal?.product?.price_range ?? {})

  const matchItem = useMemo(() => {
    const items: any[] = cartDetail?.items ?? []
    const match = items.find((item: any) => item.product.sku === addedModal?.product?.sku)
    return match
  }, [addedModal, cartDetail])

  const onClose = () => {
    dispatch(
      cartActions.setAddedModal({
        open: false
      })
    )
  }

  const redirectCheckout = () => {
    onClose()
    router.push('/checkout/cart')
  }

  return (
    <Modal
      className="modal-common"
      footer={null}
      open={addedModal.open}
      width={1004}
      title={`${addedModal?.quantity} ${addedModal?.quantity > 1 ? 'items' : 'item'} was added to your cart. What’s next?`}
      centered
      getContainer={false}
      onClose={onClose}
      onCancel={onClose}
    >
      {addedModal?.product && (
        <StyledAddedModal>
          <StyledProducts>
            <div className="main">
              <div className="image">
                {addedModal?.product?.main_image?.url && (
                  <Image
                    src={addedModal?.product?.main_image?.url}
                    alt={addedModal?.product?.main_image?.label}
                    width={200}
                    height={200}
                  />
                )}
              </div>
              <div className="info">
                <h4 className="name">
                  <span dangerouslySetInnerHTML={{ __html: addedModal?.product?.name }} />
                </h4>
                {inStock ? (
                  <p className="instock">
                    <FormattedMessage id="global.inStock" />
                  </p>
                ) : (
                  <p className="ships">Usually ships in X-X weeks</p>
                )}
                <p>
                  <span>Item #:</span>
                  <span>{addedModal?.product?.sku}</span>
                </p>
                <p>
                  <span>Quantity:</span>
                  <span>{addedModal?.quantity ?? 1}</span>
                </p>
                <p>
                  <span>Price:</span>
                  <BasePrice value={price} />
                </p>
                <p>
                  <span>Subtotal:</span>
                  <BasePrice value={price * (addedModal?.quantity ?? 1)} />
                </p>
              </div>
            </div>
            <div className="actions">
              <div className="total">
                ({matchItem?.quantity ?? 1}) {matchItem?.quantity > 1 ? 'Items' : 'Item'} in Cart
                |&nbsp;
                <BasePrice value={matchItem?.prices?.row_total?.value} />
              </div>
              <StyledActions>
                <Button type="primary" onClick={redirectCheckout}>
                  <span>Proceed to Cart</span>
                </Button>
                <Button type="default" onClick={onClose}>
                  <span>Continue Shopping</span>
                </Button>
              </StyledActions>
            </div>
          </StyledProducts>
          <BlogList />
        </StyledAddedModal>
      )}
    </Modal>
  )
}

export default AddedModal
