<?php

namespace Silksoftwarecorp\RealTimeB2BPricing\Model\API;

use Magento\Framework\Exception\LocalizedException;
use Silksoftwarecorp\RealTimeB2BPricing\Model\Config\Source\Environment;

class Config
{
    /**
     * @var \Silksoftwarecorp\RealTimeB2BPricing\Helper\Data
     */
    protected $helper;

    /**
     * @param \Silksoftwarecorp\RealTimeB2BPricing\Helper\Data $helper
     */
    public function __construct(\Silksoftwarecorp\RealTimeB2BPricing\Helper\Data $helper)
    {
        $this->helper = $helper;
    }

    /**
     * @param $storeId
     * @return string
     *
     * @throws LocalizedException
     */
    public function getApiUrl($storeId = null): string
    {
        $apiUrl = $this->helper->getSandboxApiUrl($storeId);
        if ($this->isProduction($storeId)) {
            $apiUrl = $this->helper->getApiUrl($storeId);
        }

        if (empty($apiUrl)) {
            throw new LocalizedException(__('API URL is missing.'));
        }

        return $apiUrl;
    }

    /**
     * @param $storeId
     * @return string
     *
     * @throws LocalizedException
     */
    public function getUsername($storeId = null): string
    {
        $username = $this->helper->getSandboxApiUsername($storeId);
        if ($this->isProduction($storeId)) {
            $username = $this->helper->getApiUsername($storeId);
        }

        if (empty($username)) {
            throw new LocalizedException(__('API Username is missing.'));
        }

        return $username;
    }

    /**
     * @param $storeId
     * @return string
     *
     * @throws LocalizedException
     */
    public function getPassword($storeId = null): string
    {
        $password = $this->helper->getSandboxApiPassword($storeId);
        if ($this->isProduction($storeId)) {
            $password = $this->helper->getApiPassword($storeId);
        }

        if (empty($password)) {
            throw new LocalizedException(__('API Password is missing.'));
        }

        return $password;
    }

    /**
     * @param $storeId
     * @return string
     *
     * @throws LocalizedException
     */
    public function getRequestType($storeId = null): string
    {
        $requestType = $this->helper->getSandboxApiRequestType($storeId);
        if ($this->isProduction($storeId)) {
            $requestType = $this->helper->getApiRequestType($storeId);
        }

        if (empty($requestType)) {
            throw new LocalizedException(__('Request Type is missing.'));
        }

        return $requestType;
    }

    public function verifySSLCertificate($storeId = null): bool
    {
        return $this->helper->enableSSLCertificateVerify($storeId);
    }

    public function getTimeout($storeId = null): string
    {
        return $this->helper->getApiTimeout($storeId);
    }

    private function isProduction($storeId = null): bool
    {
        return $this->helper->getApiEnvironment($storeId) == Environment::ENVIRONMENT_PRODUCTION;
    }

    /**
     * @return array
     *
     * @throws LocalizedException
     */
    public function getConfig(): array
    {
        return [
            'base_uri' => $this->getApiUrl(),
            'timeout' => $this->getTimeout(),
            'allow_redirects' => false,
            'verify' => false,
            'headers' => [
                'UserName' => $this->getUsername(),
                'Password' => $this->getPassword(),
                'RequestType' => $this->getRequestType(),
            ]
        ];
    }
}
