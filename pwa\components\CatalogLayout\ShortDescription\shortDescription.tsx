import { clsx } from 'clsx'
import { Button } from 'antd'
import { useState, useCallback, useEffect } from 'react'

import { StyledShortDescription } from './styled'

const ShortDescription = ({ descriptionHtml }) => {
  const [viewAllBtnVisible, setViewAllBtnVisible] = useState(false)
  const [isViewAll, setIsViewAll] = useState(false)

  const toggleViewAll = useCallback(() => {
    setIsViewAll(!isViewAll)
  }, [isViewAll])

  useEffect(() => {
    try {
      const liTags = document.getElementById('pdp-short-description').getElementsByTagName('li')
      if (liTags.length > 3) {
        setViewAllBtnVisible(true)
      }
    } catch (e) {
      console.info('Short Description error: ', e)
    }
  }, [])

  return (
    <StyledShortDescription
      id="pdp-short-description"
      className={clsx('pdp-short-description', { 'is-view-all': isViewAll })}>
      <div dangerouslySetInnerHTML={{ __html: `${descriptionHtml}` }} />
      {viewAllBtnVisible && (
        <Button type="link" className="view-all" onClick={toggleViewAll}>
          {isViewAll ? 'Hide' : 'View All'}
        </Button>
      )}
    </StyledShortDescription>
  )
}

export default ShortDescription
