import styled from '@emotion/styled'

export const StyledCheckoutAction = styled.div`
  margin-top: 16px;

  .${({ theme }) => theme.namespace}-btn {
    width: 100%;
    height: 49px;
    border-radius: 3px;

    span {
      font-weight: 700;
      font-size: 16px;
      line-height: 21px;
      letter-spacing: 0.03em;
    }

    svg {
      margin-top: -2px;
    }
  }
`

export const StyledBackorderedContent = styled.div`
  margin: 0 auto;
  padding: 35px;
  max-width: 577px;
  text-align: center;

  h2 {
    margin-bottom: 10px;
    font-weight: 700;
    font-size: 32px;
    line-height: 38px;
    letter-spacing: 0;
    text-align: center;
  }

  p {
    font-weight: 400;
    font-size: 15px;
    line-height: 21px;
    letter-spacing: 0.01em;
    text-align: center;
  }

  .backordered-action {
    display: flex;
    justify-content: center;
    margin-top: 24px;

    button {
      width: 151px;
      height: 49px;

      span {
        font-weight: 700;
        font-size: 16px;
        line-height: 21px;
        letter-spacing: 0.03em;
        border-radius: 3px;
      }

      &.edit-btn {
        margin-left: 15px;
        border: 1px solid #666565 !important;

        span {
          color: #666565;
        }
      }
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    padding: 30px 0;
    max-width: 100%;

    h2 {
      font-size: 24px;
      line-height: 30px;
    }

    .backordered-action {
      button {
        width: 100%;
      }
    }
  }
`
