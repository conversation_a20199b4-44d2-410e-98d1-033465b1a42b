<?php

declare(strict_types=1);

namespace Silksoftwarecorp\RequistionGraphQl\Model\Resolver\List;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlAuthorizationException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\GraphQl\Model\Query\ContextInterface;
use Magento\Framework\GraphQl\Query\Uid as IdEncoder;
use Magento\Framework\GraphQl\Exception\GraphQlNoSuchEntityException;
class ListId implements ResolverInterface
{

    private $idEncoder;

    public function __construct(IdEncoder $idEncoder)
    {
        $this->idEncoder = $idEncoder;
    }
 /**
     * @inheritDoc
     */
    public function resolve(
        Field $field,
              $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        /** @var ContextInterface $context */
        if (false === $context->getExtensionAttributes()->getIsCustomer()) {
            throw new GraphQlAuthorizationException(__('The current customer isn\'t authorized.'));
        }

        if(empty($value['uid'])) {
            throw new GraphQlNoSuchEntityException(__('Uid is required.'));
        }

        return  $this->idEncoder->decode($value['uid']);

    }
}
