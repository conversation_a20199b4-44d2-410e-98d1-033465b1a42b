import { Form, Checkbox, Modal, Spin } from 'antd'
import { FormattedMessage } from 'react-intl'
import { nanoid } from 'nanoid'
import type { FC } from 'react'

import StreetFields from '@/components/StreetFields'
import CommonButton from '@/components/Common/CommonButton'
import { useAddressModal } from '@/hooks/AccountAddressPage'
import { TextField, TextSelect } from '@/ui'

import { StyledAddressModal, StyledFormItem } from './styled'

interface AddressDataProps {
  visible?: boolean
  isEdit: boolean
  addressInfo: any
}

interface AddressModalProps {
  addressData: AddressDataProps
  loading?: boolean
}

const AddressModal: FC<AddressModalProps> = ({ addressData, loading, ...params }) => {
  const [form] = Form.useForm()
  const { isEdit, visible } = addressData
  const { countries, regions, handleOnCancel, handleCountryChange, handleFormSubmit } =
    useAddressModal({
      addressData,
      form,
      params
    })

  const title = <FormattedMessage id={isEdit ? 'account.eidtAddress' : 'account.addNewAddress'} />

  return (
    <Modal title={title} footer={null} width={800} open={visible} onCancel={handleOnCancel}>
      <StyledAddressModal>
        <Spin spinning={loading}>
          <Form
            form={form}
            initialValues={{
              street: ['', ''],
              default_billing: false,
              default_shipping: false
            }}
            onFinish={handleFormSubmit}>
            <StyledFormItem>
              <Form.Item name="firstname" rules={[{ required: true }]}>
                <TextField label="First Name" />
              </Form.Item>
              <Form.Item name="lastname" rules={[{ required: true }]}>
                <TextField label="Last Name" />
              </Form.Item>
            </StyledFormItem>
            <Form.Item name="company">
              <TextField label="Company (Optional)" />
            </Form.Item>
            <StreetFields />
            <StyledFormItem>
              <Form.Item name="city" rules={[{ required: true }]}>
                <TextField label="City" />
              </Form.Item>
              {regions.length > 0 ? (
                <Form.Item name="region" rules={[{ required: true }]}>
                  <TextSelect label="State" elementId={1}>
                    {regions.map((region: any) => {
                      return (
                        <TextSelect.Option key={nanoid()} value={region.id} param={region.id}>
                          <span dangerouslySetInnerHTML={{ __html: region.name }} />
                        </TextSelect.Option>
                      )
                    })}
                  </TextSelect>
                </Form.Item>
              ) : (
                <Form.Item name="region" rules={[{ required: true }]}>
                  <TextField label="State" />
                </Form.Item>
              )}
            </StyledFormItem>
            <StyledFormItem>
              <Form.Item name="country" rules={[{ required: true }]}>
                <TextSelect label="Country" onChange={handleCountryChange}>
                  {countries.map((country: any) => {
                    return (
                      <TextSelect.Option key={country.id} value={country.id} param={country.name}>
                        <span dangerouslySetInnerHTML={{ __html: country.name }} />
                      </TextSelect.Option>
                    )
                  })}
                </TextSelect>
              </Form.Item>
              <Form.Item name="postcode" rules={[{ required: true }]}>
                <TextField label="Zip Code" />
              </Form.Item>
            </StyledFormItem>
            <Form.Item name="telephone" rules={[{ required: true }]}>
              <TextField label="Phone Number" />
            </Form.Item>
            <Form.Item
              name="default_billing"
              valuePropName="checked"
              className="default-billing-item">
              <Checkbox>
                <FormattedMessage id="account.checkBilling" />
              </Checkbox>
            </Form.Item>
            <Form.Item name="default_shipping" valuePropName="checked">
              <Checkbox>
                <FormattedMessage id="account.checkShipping" />
              </Checkbox>
            </Form.Item>
            <CommonButton htmlType="submit" className="addressModal__checkButton" height={44}>
              <FormattedMessage id="global.save" />
            </CommonButton>
          </Form>
        </Spin>
      </StyledAddressModal>
    </Modal>
  )
}

export default AddressModal
