<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Silksoftwarecorp\EDI\Http\ClientInterface" type="Silksoftwarecorp\EDI\Http\Client"/>
    <virtualType name="EDILogger" type="Magento\Framework\Logger\Monolog">
        <arguments>
            <argument name="handlers" xsi:type="array">
                <item name="debug" xsi:type="object">Silksoftwarecorp\EDI\Logger\Handler\Debug</item>
                <item name="system" xsi:type="object">Silksoftwarecorp\EDI\Logger\Handler\Info</item>
            </argument>
        </arguments>
    </virtualType>
    <type name="Silksoftwarecorp\EDI\Http\Client">
        <arguments>
            <argument name="logger" xsi:type="object">EDILogger</argument>
        </arguments>
    </type>
    <type name="Silksoftwarecorp\EDI\Http\Middleware\LogMiddleware">
        <arguments>
            <argument name="logger" xsi:type="object">EDILogger</argument>
        </arguments>
    </type>
</config>
