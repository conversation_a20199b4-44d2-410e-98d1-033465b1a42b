import { gql } from '@apollo/client'

export const GET_PRODUCTS_CUSTOM_ATTRIBUTES = gql`
  query getProductsCustomAttributes($filter: ProductAttributeFilterInput) {
    products(filter: $filter) {
      items {
        sku
        custom_attributesV2(filters: { is_visible_on_front: true }) {
          items {
            code
            ... on AttributeValue {
              value
            }
            ... on AttributeSelectedOptions {
              selected_options {
                label
                value
              }
            }
            __typename
          }
        }
      }
    }
  }
`
