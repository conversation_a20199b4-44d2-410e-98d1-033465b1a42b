<?php
namespace Silksoftwarecorp\UnifiedArPayment\Block\Adminhtml;

use Magento\Sales\Block\Adminhtml\Order\Totals as CoreTotals;
use Magento\Framework\DataObject;

class Totals extends CoreTotals
{
    protected function _initTotals()
    {
        parent::_initTotals();
        $order = $this->getSource();
        $surcharge = (float) $order->getData('unified_ar_surcharge_amount');
        if ($surcharge) {
            $total = new DataObject([
                'code'  => 'unified_ar_surcharge',
                'value' => $surcharge,
                'label' => __('Surcharge'),
            ]);
            // Insert before tax if present, otherwise before grand_total
            if (isset($this->_totals['tax'])) {
                $this->addTotalBefore($total, 'tax');
            } else {
                $this->addTotalBefore($total, 'grand_total');
            }
        }
        return $this;
    }
}
