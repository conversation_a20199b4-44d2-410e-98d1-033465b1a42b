<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */
declare(strict_types=1);

namespace Silksoftwarecorp\CompanyGraphQl\Model\Resolver\Company;

use Magento\CompanyGraphQl\Model\Company\ResolverAccess;
use Silksoftwarecorp\CompanyGraphQl\Model\Company\Users as CompanyUsers;
use Magento\CustomerGraphQl\Model\Customer\ExtractCustomerData;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;

/**
 * Provides all company users without pagination
 */
class AllUsers implements ResolverInterface
{
    /**
     * @var CompanyUsers
     */
    private $companyUsers;

    /**
     * @var ExtractCustomerData
     */
    private $customerData;

    /**
     * @var ResolverAccess
     */
    private $resolverAccess;

    /**
     * @var array
     */
    private $allowedResources;

    /**
     * @param CompanyUsers $companyUsers
     * @param ExtractCustomerData $customerData
     * @param ResolverAccess $resolverAccess
     * @param array $allowedResources
     */
    public function __construct(
        CompanyUsers $companyUsers,
        ExtractCustomerData $customerData,
        ResolverAccess $resolverAccess,
        array $allowedResources = []
    ) {
        $this->companyUsers = $companyUsers;
        $this->customerData = $customerData;
        $this->resolverAccess = $resolverAccess;
        $this->allowedResources = $allowedResources;
    }

    /**
     * @inheritdoc
     */
    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        if (isset($value['isNewCompany']) && $value['isNewCompany'] === true) {
            return null;
        }

        $this->resolverAccess->isAllowed($this->allowedResources);
        $company = $value['model'];
        
        $searchResults = $this->companyUsers->getAllCompanyUsers($company, $args);
        $companyUsers = [];

        foreach ($searchResults->getItems() as $companyUser) {
            $companyUsers[] = $this->customerData->execute($companyUser);
        }

        return [
            'items' => $companyUsers,
            'total_count' => count($companyUsers)
        ];
    }
} 