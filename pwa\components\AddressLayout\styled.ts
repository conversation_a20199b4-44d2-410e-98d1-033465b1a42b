import styled from '@emotion/styled'

export const StyledAddressLayout = styled.div`
  .address-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-column-gap: 24px;

    .address-grid-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      min-height: 198px;
      padding: 24px 32px;
      border: 1px solid #d9d9d9;
    }
  }

  .address-table-panel {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 56px;
    margin-bottom: 16px;

    h2 {
      font-weight: 700;
      font-size: 24px;
      line-height: 30px;
      letter-spacing: 0;
      color: #231f20;
    }

    button {
      padding: 12px 32px;
      height: 44px;
      border-radius: 3px;
      font-weight: 700;
      font-size: 14px;
      line-height: 20px;
      letter-spacing: 0.01em;
      text-transform: uppercase;
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    .address-grid {
      grid-template-columns: 1fr;
      grid-gap: 16px;

      .address-grid-item {
        display: block;
      }
    }

    .address-table-panel {
      display: block;

      h2 {
        margin-bottom: 16px;
      }

      button {
        width: 100%;
        height: 53px;
      }
    }

    .mobile-address-list {
      li {
        padding: 24px 16px 16px;

        &:nth-of-type(2n - 1) {
          background: #efefef;
        }

        & > div {
          display: grid;
          grid-template-columns: 105px 1fr;
          grid-column-gap: 6px;
          margin-bottom: 10px;
          font-weight: 400;
          font-size: 15px;
          line-height: 21px;
          letter-spacing: 0;
          color: var(--color-black);

          b {
            font-weight: 700;
          }
        }

        .mobile-address-item-header {
          display: grid;
          grid-template-columns: auto 1fr auto;
          grid-column-gap: 10px;
          align-items: center;

          button {
            font-weight: 700;
            font-size: 15px;
            line-height: 21px;
            letter-spacing: 0;
            color: var(--color-primary) !important;
          }
        }
      }
    }
  }
`

export const StyledButton = styled.div`
  .additionalAddress__button {
    padding: 0.2rem 1.6rem 0.2rem 0;
    font-weight: 700;
    font-size: 15px;
    line-height: 21px;
    letter-spacing: 0;
    color: var(--color-primary) !important;
  }
`

export const StyledDefaultCheckbox = styled.div`
  div {
    cursor: initial;
  }
`
