<?php

namespace Silksoftwarecorp\EdiGraphQl\Model\Resolver\Customer;

use Magento\Company\Api\CompanyRepositoryInterface;
use Magento\Company\Model\CompanyUser;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlAuthorizationException;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Query\Uid;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\GraphQl\Model\Query\ContextInterface;
use Magento\NegotiableQuoteGraphQl\Model\NegotiableQuote\IdEncoder;
use Silksoftwarecorp\EDI\Service\FrequentlyOrderedProductsService;

class FrequentlyOrderedProducts implements ResolverInterface
{
    /**
     * @var Uid
     */
    private $idEncoder;

    /**
     * @var FrequentlyOrderedProductsService
     */
    protected $frequentlyOrderedProductService;

    /**
     * @var CompanyRepositoryInterface
     */
    protected $companyRepository;

    /**
     * @var CompanyUser
     */
    protected $companyUser;

    /**
     * @param IdEncoder $idEncoder
     * @param CompanyRepositoryInterface $companyRepository
     * @param CompanyUser $companyUser
     * @param FrequentlyOrderedProductsService $frequentlyOrderedProductService
     */
    public function __construct(
        IdEncoder $idEncoder,
        CompanyRepositoryInterface $companyRepository,
        CompanyUser $companyUser,
        FrequentlyOrderedProductsService $frequentlyOrderedProductService
    ) {
        $this->idEncoder = $idEncoder;
        $this->companyRepository = $companyRepository;
        $this->companyUser = $companyUser;
        $this->frequentlyOrderedProductService = $frequentlyOrderedProductService;
    }

    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        /**
         * @var ContextInterface $context
         */
        if (false === $context->getExtensionAttributes()->getIsCustomer()) {
            throw new GraphQlAuthorizationException(__('The request is allowed for logged in customer.'));
        }

        if (empty($args['company_id'])) {
            throw new GraphQlInputException(__('The company_id field is required.'));
        }

        try {
            $companyId = $this->idEncoder->decode($args['company_id']);
            $currentCompanyId = $this->companyUser->getCurrentCompanyId();
            if ($currentCompanyId != $companyId) {
                /*throw new GraphQlAuthorizationException(
                    __(
                        'The current user cannot perform operations on company "%company_id"',
                        ['company_id' => $args['company_id']]
                    )
                );*/
            }

            $company = $this->companyRepository->get($companyId);

            $erpCustomerId = $company?->getExtensionAttributes()?->getErpCustomerId();
            if ($erpCustomerId) {
                return $this->frequentlyOrderedProductService->getList($erpCustomerId);
            }
        } catch (\Exception $e) {
            throw new GraphQlInputException(__($e->getMessage()), $e);
        }

        return [];
    }
}
