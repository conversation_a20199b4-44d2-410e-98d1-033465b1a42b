<?php

namespace Silksoftwarecorp\ERPCompany\Model;

class ERPCompany extends \Magento\Framework\Model\AbstractModel
{
    const ERP_CUSTOMER_ID = 'erp_customer_id';
    const ERP_SALES_REP_ID = 'erp_sales_rep_id';
    const ERP_SALES_REP_NAME = 'erp_sales_rep_name';
    const ERP_CREDIT_STATUS = 'erp_credit_status';

    /**
     * {@inheritdoc}
     */
    protected $_idFieldName = 'company_id';

    /**
     * {@inheritdoc}
     */
    protected function _construct()
    {
        $this->_init(\Silksoftwarecorp\ERPCompany\Model\ResourceModel\ERPCompany::class);
    }

    /**
     * Set company ID.
     *
     * @param int $companyId
     * @return $this
     */
    public function setCompanyId($companyId)
    {
        return $this->setData($this->_idFieldName, $companyId);
    }

    /**
     * Get company ID.
     *
     * @return int
     */
    public function getCompanyId()
    {
        return $this->getData($this->_idFieldName);
    }

    /**
     * Set erp customer id.
     *
     * @param $erpCustomerId
     * @return $this
     */
    public function setErpCustomerId($erpCustomerId)
    {
        return $this->setData(self::ERP_CUSTOMER_ID, $erpCustomerId);
    }

    /**
     * Get applicable payment method.
     *
     * @return string
     */
    public function getErpCustomerId()
    {
        return $this->getData(self::ERP_CUSTOMER_ID);
    }

    /**
     * Set erp sales rep id.
     *
     * @param $erpSalesRepId
     * @return $this
     */
    public function setErpSalesRepId($erpSalesRepId)
    {
        return $this->setData(self::ERP_SALES_REP_ID, $erpSalesRepId);
    }

    /**
     * Get erp sales rep id.
     *
     * @return string
     */
    public function getErpSalesRepId()
    {
        return $this->getData(self::ERP_SALES_REP_ID);
    }

    /**
     * Set erp Sales rep name.
     *
     * @param $erpSalesRepName
     * @return $this
     */
    public function setErpSalesRepName($erpSalesRepName)
    {
        return $this->setData(self::ERP_SALES_REP_NAME, $erpSalesRepName);
    }

    /**
     * Get ERP Sales rep name.
     *
     * @return string
     */
    public function getErpSalesRepName()
    {
        return $this->getData(self::ERP_SALES_REP_NAME);
    }

    /**
     * Set erp credit status.
     *
     * @param $erpCreditStatus
     * @return $this
     */
    public function setErpCreditStatus($erpCreditStatus)
    {
        return $this->setData(self::ERP_CREDIT_STATUS, $erpCreditStatus);
    }

    /**
     * Get ERP credit status.
     *
     * @return string
     */
    public function getErpCreditStatus()
    {
        return $this->getData(self::ERP_CREDIT_STATUS);
    }

    /**
     * {@inheritdoc}
     */
    protected function _afterLoad()
    {
        parent::_afterLoad();
        if (!$this->getCompanyId()) {
            $this->isObjectNew(true);
        }

        return $this;
    }
}
