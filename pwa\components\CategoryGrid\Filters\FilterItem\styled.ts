import styled from '@emotion/styled'

export const StyledFilterItem = styled.div`
  border-bottom: 1px solid #d9d9d9;
  padding-top: 22px;
  padding-bottom: 22px;

  &.filter-item {
    .${({ theme }) => theme.namespace}-collapse {
      &-item-active {
        .${({ theme }) => theme.namespace}-collapse-header {
          margin-bottom: 0;
        }
      }
      &-header {
        padding: 0;
        align-items: center;
        height: 30px;
        font-weight: 700;
        font-size: 16px;
        line-height: 24px;
        letter-spacing: 0;
        color: var(--color-font);

        .filter-item-header {
          display: flex;
          align-items: center;
          padding-right: 3px;
        }
      }
      &-content-box {
        padding-block: 2px !important;
        padding: 0;

        .filter-item-options {
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          align-items: flex-start;

          .filter-item-option {
            display: inline-grid;
            grid-template-columns: auto 1fr;
            grid-column-gap: 12px;
            align-items: center;
            padding-right: 8px;
            min-height: 32px;
            cursor: pointer;

            span {
              font-weight: 400;
              font-size: 16px;
              line-height: 32px;
              letter-spacing: 0.02em;
              color: var(--color-text);

              &.item-count {
                position: relative;
                top: -2px;
                margin-left: 5px;
              }
            }

            &.option-active {
              font-weight: 700;
              color: var(--color-primary);
            }

            .option-checkbox {
              display: flex;
              justify-content: center;
              align-items: center;
              width: 20px;
              height: 20px;
              border: 1px solid #d9d9d9;
              border-radius: 2px;

              &-active {
                background: var(--color-primary);
                border-color: var(--color-primary);
              }
            }
          }
        }
      }
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    padding-top: 24px;

    &.filter-item {
      padding: 22px 16px;

      .${({ theme }) => theme.namespace}-collapse {
        &-header {
          height: auto;
        }
        &-content-box {
          margin-top: 10px;

          .filter-item-options {
            .filter-item-option {
              margin-bottom: 10px;

              span {
                line-height: 24px;
              }
            }
          }
        }
      }
    }
  }
`
