import { useCallback, memo } from 'react'
import { Button } from 'antd'

import { StyledCurrentFilter } from './styled'

const CurrentFilter = ({ group, label, item, removeItem, setIsApplying }: any) => {
  const handleClick = useCallback(() => {
    removeItem({ group, item })
    setIsApplying(true)
  }, [removeItem, group, item, setIsApplying])

  return (
    <StyledCurrentFilter>
      <span className="current-filter-label" dangerouslySetInnerHTML={{ __html: `${label}:` }} />
      <span dangerouslySetInnerHTML={{ __html: item.label }} />
      <span aria-hidden className="current-filter-close" onClick={handleClick}>
        <svg
          className="close"
          width="8px"
          height="8px"
          fill="currentColor"
          aria-hidden="true"
          focusable="false">
          <use xlinkHref="#icon-close-bold" />
        </svg>
      </span>
    </StyledCurrentFilter>
  )
}

export default memo(CurrentFilter)
