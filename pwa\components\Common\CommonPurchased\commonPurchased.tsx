import { memo, useMemo } from 'react'

import { StyledCommonPurchased } from './styled'

const CommonPurchased = ({ date = '' }) => {
  const dateText = useMemo(() => {
    try {
      return date
        ? new Date(date)?.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          })
        : ''
    } catch (e) {
      console.error('Purchased date error')
      console.error(e)
      return ''
    }
  }, [date])

  return dateText ? <StyledCommonPurchased>{`Purchased: ${dateText}`}</StyledCommonPurchased> : null
}

export default memo(CommonPurchased)
