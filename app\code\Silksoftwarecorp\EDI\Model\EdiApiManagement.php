<?php

namespace Silksoftwarecorp\EDI\Model;

use Psr\Log\LoggerInterface;
use Silksoftwarecorp\EDI\Http\Client;
use Silksoftwarecorp\EDI\Http\ClientInterface;
use Silksoftwarecorp\EDI\Model\API\ClientFactory;

class EdiApiManagement
{
    /**
     * @var ClientFactory
     */
    protected $apiClientFactory;

    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * @param ClientFactory $apiClientFactory
     * @param LoggerInterface $logger
     */
    public function __construct(
        ClientFactory $apiClientFactory,
        LoggerInterface $logger
    ) {
        $this->apiClientFactory = $apiClientFactory;
        $this->logger = $logger;
    }

    public function getItems(
        $customerId,
        $locationId,
        array $skus,
        $requestType = 'ItemInfo'
    ): array {
        try {
            $response = $this->getApiClient()->withHeaders([
                'CustomerID' =>  $customerId,
                'Location' =>  $locationId,
                'ItemID' =>  implode(',', $skus),
                'RequestType' => $requestType,
            ])->get('');

            if ($response->isOk()) {
                return $response->json('item_data');
            }
        } catch (\Exception $e) {
            $this->logger->critical($e->getMessage());
        }

        return [];
    }

    public function getOrders($customerId, $startDate = null): array
    {
        try {
            $headers = [
                'CustomerID' => $customerId,
                'RequestType' => 'OrderSummary',
            ];

            if ($startDate) {
                $headers['StartDate'] = $startDate;
            }

            $response = $this->getApiClient()->withHeaders($headers)->get('');
            if ($response->isOk()) {
                return $response->json('orders');
            }
        } catch (\Exception $e) {
            $this->logger->critical($e->getMessage());
        }

        return [];
    }

    public function getOrder($orderNumber): array
    {
        $ordersDetails = $this->getOrdersDetails([$orderNumber]);
        foreach ($ordersDetails as $orderDetails) {
            $orderNo = $orderDetails['OrderNo'] ?? null;
            if ($orderNumber == $orderNo) {
                return $orderDetails;
            }
        }

        return [];
    }

    public function getOrdersDetails(array $orderNumbers): array {
        try {
            $response = $this->getApiClient()->withHeaders([
                'RequestType' => 'OrderDetails',
                'OrderNumbers' => implode(',', $orderNumbers),
            ])->get('');

            if ($response->isOk()) {
                return $response->json('orders');
            }
        } catch (\Exception $e) {
            $this->logger->critical($e->getMessage());
        }

        return [];
    }

    public function getFrequentlyOrders(
        $customerId,
        $startDate,
        $requestType = 'OrderDetails'
    ): array {
        try {
            $response = $this->getApiClient()->withHeaders([
                'CustomerID' => $customerId,
                'RequestType' => $requestType,
                'StartDate' => $startDate,
            ])->get('');

            if ($response->isOk()) {
                return $response->json('orders');
            }
        } catch (\Exception $e) {
            $this->logger->critical($e->getMessage());
        }

        return [];
    }

    /**
     * @return ClientInterface|Client
     */
    public function getApiClient()
    {
        return $this->apiClientFactory->create();
    }
}
