import styled from '@emotion/styled'

export const StyledQtyInput = styled.div`
  position: relative;
  width: 58px;
  height: 49px;

  .qty-input-title {
    position: absolute;
    top: -12px;
    left: 11px;
    z-index: 1;
    width: 36px;
    background: #fff;
    font-weight: 700;
    font-size: 12px;
    line-height: 26px;
    letter-spacing: 0.02em;
    text-align: center;
  }
  
  input {
    width: 100%;
    height: 100%;
    border: 1px solid #D9D9D9;
    border-radius: 3px;
    text-align: center;
    font-weight: 400;
    font-size: 16px;
    line-height: 26px;
    letter-spacing: 0.02em;
    color: var(--color-font);
  }
`
