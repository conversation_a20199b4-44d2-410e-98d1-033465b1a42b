<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <fieldset name="erp_settings" sortOrder="999">
        <settings>
            <collapsible>true</collapsible>
            <label translate="true">ERP Settings</label>
            <dataScope>erp_settings</dataScope>
        </settings>
        <field name="extension_attributes.erp_customer_id" formElement="input">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">extension_attributes.erp_customer_id</item>
                </item>
            </argument>
            <settings>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
                <dataType>text</dataType>
                <label translate="true">ERP Customer ID</label>
                <disabled>1</disabled>
            </settings>
        </field>
        <field name="extension_attributes.erp_sales_rep_id" formElement="input">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">extension_attributes.erp_sales_rep_id</item>
                </item>
            </argument>
            <settings>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">false</rule>
                </validation>
                <dataType>text</dataType>
                <label translate="true">ERP Sales Rep ID</label>
                <disabled>1</disabled>
            </settings>
        </field>
        <field name="extension_attributes.erp_sales_rep_name" formElement="input">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">extension_attributes.erp_sales_rep_name</item>
                </item>
            </argument>
            <settings>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">false</rule>
                </validation>
                <dataType>text</dataType>
                <label translate="true">ERP Sales Rep Name</label>
                <disabled>1</disabled>
            </settings>
        </field>
        <field name="extension_attributes.erp_credit_status" formElement="input">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">extension_attributes.erp_credit_status</item>
                </item>
            </argument>
            <settings>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">false</rule>
                </validation>
                <dataType>text</dataType>
                <label translate="true">ERP Credit Status</label>
                <disabled>1</disabled>
            </settings>
        </field>
    </fieldset>
</form>
