import styled from '@emotion/styled'

export const StyledStoreInputSearch = styled.div`
  display: grid;
  grid-template-columns: 1fr auto;
  grid-gap: 8px;

  .input-wrapper {
    display: flex;
    align-items: center;
    padding-right: 8px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;

    .${({ theme }) => theme.namespace}-select {
      width: 100% !important;
      height: 45px !important;

      &-selector {
        padding: 10px 14px !important;
        border-radius: 4px !important;
        border: none;
        box-shadow: none !important;
        height: 45px !important;

        input {
          height: 45px !important;
          font-weight: 400;
          font-size: 16px;
          line-height: 24px;
          letter-spacing: 0.02em;
          color: var(--color-font);
        }
      }

      &-selection-placeholder {
        font-weight: 400;
        font-size: 16px;
        line-height: 24px;
        letter-spacing: 0.02em;
        color: var(--color-font);
      }
    }

    .clear-icon {
      margin: 0 6px;
      padding: 2px 5px;
      cursor: pointer;
    }

    .reset-center-icon {
      padding: 2px 2px;
      cursor: pointer;
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    grid-template-columns: 1fr;
  }
`
