import { gql } from '@apollo/client'
import type { DocumentNode } from '@apollo/client'

export const SET_PAYMENT_METHOD_AND_PLACE_ORDER: DocumentNode = gql`
  mutation setPaymentMethodAndPlaceOrder($input: SetPaymentMethodAndPlaceOrderInput!) {
    setPaymentMethodAndPlaceOrder(input: $input) {
      order {
        order_id
        order_number
      }
      orderV2 {
        id
        increment_id
        order_number
      }
    }
  }
`
