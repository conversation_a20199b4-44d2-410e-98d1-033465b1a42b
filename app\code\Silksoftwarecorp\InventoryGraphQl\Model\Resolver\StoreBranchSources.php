<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */

declare(strict_types=1);

namespace Silksoftwarecorp\InventoryGraphQl\Model\Resolver;

use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\InventoryApi\Api\SourceRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Api\FilterBuilder;
use Magento\Framework\Api\Search\FilterGroupBuilder;
use Silksoftwarecorp\InventoryGraphQl\Model\Formatter\Source;

/**
 * Resolver for store branch sources
 */
class StoreBranchSources implements ResolverInterface
{
    /**
     * @var SourceRepositoryInterface
     */
    private $sourceRepository;

    /**
     * @var SearchCriteriaBuilder
     */
    private $searchCriteriaBuilder;

    /**
     * @var FilterBuilder
     */
    private $filterBuilder;

    /**
     * @var FilterGroupBuilder
     */
    private $filterGroupBuilder;

    /**
     * @var Source
     */
    private $sourceFormatter;

    /**
     * @param SourceRepositoryInterface $sourceRepository
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param FilterBuilder $filterBuilder
     * @param FilterGroupBuilder $filterGroupBuilder
     * @param Source $sourceFormatter
     */
    public function __construct(
        SourceRepositoryInterface $sourceRepository,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        FilterBuilder $filterBuilder,
        FilterGroupBuilder $filterGroupBuilder,
        Source $sourceFormatter
    ) {
        $this->sourceRepository = $sourceRepository;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->filterBuilder = $filterBuilder;
        $this->filterGroupBuilder = $filterGroupBuilder;
        $this->sourceFormatter = $sourceFormatter;
    }

    /**
     * @inheritdoc
     */
    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
        try {
            // 构建搜索条件，只获取启用的源
            $enabledFilter = $this->filterBuilder
                ->setField('enabled')
                ->setConditionType('eq')
                ->setValue(1)
                ->create();

            $filterGroup = $this->filterGroupBuilder
                ->addFilter($enabledFilter)
                ->create();

            $searchCriteria = $this->searchCriteriaBuilder
                ->setFilterGroups([$filterGroup])
                ->create();

            $searchResults = $this->sourceRepository->getList($searchCriteria);
            $sources = $searchResults->getItems();

            $branchSources = [];
            foreach ($sources as $source) {
                $branchSources[] = $this->sourceFormatter->format($source);
            }

            return $branchSources;

        } catch (\Exception $e) {
            return [];
        }
    }
}
