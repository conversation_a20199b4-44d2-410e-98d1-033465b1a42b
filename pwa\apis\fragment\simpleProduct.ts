import { gql } from '@apollo/client'
import type { DocumentNode } from '@apollo/client'

import { price_range } from '../fragment/priceRange'

export const simpleProduct: DocumentNode = gql`
  fragment simpleProduct on SimpleProduct {
    sku
    name
    url_key
    stock_status
    only_x_left_in_stock
    meta_title
    meta_keyword
    meta_description
    main_image: image {
      label
      url
    }
    media_gallery {
      disabled
      label
      position
      url
    }
    price_range {
      ...price_range
      __typename
    }
    description {
      html
    }
    short_description {
      html
    }
    ble_manufacturer_name
    pdp_ble_specifications {
      html
    }
    pdp_ble_warranty_information {
      html
    }
    pdp_ble_product_video {
      html
    }
    categories {
      name
      url_path
    }
  }
  ${price_range}
`
