import { Button, Typography } from 'antd'
import { FormattedMessage } from 'react-intl'
import { isEmpty } from 'lodash-es'
import type { FC } from 'react'
import { memo } from 'react'

import CountryTransform from '@/components/CountryTransform'
import { StyledDefaultAddress } from './styled'

interface AddressProps {
  key?: string
  firstname?: string
  lastname?: string
  company?: string
  street?: any
  region?: any
  city?: string
  postcode?: string
  country_code?: any
  telephone?: string
}
interface DefaultAddressProps {
  title?: string
  titleText?: string
  address: AddressProps
  isClick?: boolean
  showEditAddressModal?: (value: any) => void
}

const DefaultAddress: FC<DefaultAddressProps> = ({
  title = '',
  titleText = '',
  address,
  isClick = false,
  showEditAddressModal = () => {}
}) => {
  return (
    <StyledDefaultAddress>
      <h3>{titleText || <FormattedMessage id={titleText || title} />}</h3>
      {isEmpty(address) ? (
        <p>
          <FormattedMessage id="account.noDefaultAddress" />
        </p>
      ) : (
        <>
          <div className="default-address-item">
            <p>
              {address.company}
              {` ${address.firstname}  ${address.lastname}`}
            </p>
            {address.street.map((item) => (
              <p key={item}>{item}</p>
            ))}
            {isEmpty(address.region.region) ? (
              <p>
                <span>{`${address.city}, `}</span>
                {address.region.length > 0 ? <span>{`${address.region}, `}</span> : ''}
                <span>{`${address.postcode}`}</span>
              </p>
            ) : (
              <p>{`${address.city}, ${address.region.region}, ${address.postcode}`}</p>
            )}
            <p>
              <CountryTransform code={address.country_code} />
            </p>
            <p>
              {/* TODO: Format telephone */}
              <span key={`tel:${address.telephone}`}>{address.telephone}</span>
            </p>
          </div>
          {isClick && (
            <>
              <Button
                type="primary"
                className="eidt__address"
                onClick={() => {
                  showEditAddressModal(address)
                }}>
                <FormattedMessage id="account.eidtAddress" />
              </Button>
            </>
          )}
        </>
      )}
    </StyledDefaultAddress>
  )
}

export default memo(DefaultAddress)
