<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Silksoftwarecorp\UnifiedAR\Http\ClientInterface" type="Silksoftwarecorp\UnifiedAR\Http\Client"/>

    <!-- Logger Configuration -->
    <virtualType name="UnifiedARLogger" type="Magento\Framework\Logger\Monolog">
        <arguments>
            <argument name="handlers" xsi:type="array">
                <item name="debug" xsi:type="object">Silksoftwarecorp\UnifiedAR\Logger\Handler\Debug</item>
                <item name="system" xsi:type="object">Silksoftwarecorp\UnifiedAR\Logger\Handler\Info</item>
            </argument>
        </arguments>
    </virtualType>

    <type name="Silksoftwarecorp\UnifiedAR\Http\Client">
        <arguments>
            <argument name="logger" xsi:type="object">UnifiedARLogger</argument>
        </arguments>
    </type>

    <type name="Silksoftwarecorp\UnifiedAR\Http\Middleware\LogMiddleware">
        <arguments>
            <argument name="logger" xsi:type="object">UnifiedARLogger</argument>
        </arguments>
    </type>

    <!-- UnifiedAR API Service Configuration -->
    <type name="Silksoftwarecorp\UnifiedAR\Model\UnifiedArApiService">
        <arguments>
            <argument name="logger" xsi:type="object">UnifiedARLogger</argument>
        </arguments>
    </type>
</config>
