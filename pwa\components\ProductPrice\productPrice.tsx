import { memo } from 'react'
import type { FC } from 'react'

import { useProductPrice } from '@/hooks/ProductPrice'
import BasePrice from '@/components/BasePrice'
import { StyledPrice } from './styled'

interface ProductPriceProps {
  className?: string
  minimum_price: any
  maximum_price: any
  qty?: number
  decimal?: number
}

const ProductPrice: FC<ProductPriceProps> = ({
  minimum_price = {},
  maximum_price = {},
  qty = 1,
  decimal = 2,
  ...props
}) => {
  const { hasGroup, hasSpecial } = useProductPrice({
    minimum_price,
    maximum_price
  })

  return (
    <StyledPrice {...props}>
      {hasGroup ? (
        <>
          {hasSpecial && (
            <BasePrice
              className="product__price--specail"
              decimal={decimal}
              value={minimum_price.final_price.value * qty}
            />
          )}
          <BasePrice
            unit
            className={hasSpecial ? 'product__price--old' : 'product__price--default'}
            decimal={decimal}
            value={minimum_price.regular_price.value * qty}
          />
        </>
      ) : (
        <BasePrice decimal={decimal} value={minimum_price.final_price.value * qty} />
      )}
    </StyledPrice>
  )
}

export default memo(ProductPrice)
