# Unified A/R Payment Module

A Magento 2 module that integrates with Unified A/R payment system, providing payment processing capabilities with GraphQL and REST API support.

## Overview

This module provides:
- Payment method integration with Unified A/R system
- GraphQL mutations for payment processing
- REST API endpoints for retrieving payment data
- Comprehensive logging and error handling
- Sandbox and production environment support

## Installation

1. Copy the module to `app/code/Silksoftwarecorp/UnifiedArPayment/`
2. Run the following commands:
   ```bash
   php bin/magento setup:upgrade
   php bin/magento setup:di:compile
   php bin/magento cache:clean
   ```

## Configuration

### Admin Configuration

Navigate to **Admin → Stores → Configuration → Sales → Payment Methods → Unified A/R Payment**

#### General Settings
- **Enable Unified A/R Payment**: Enable/disable the payment method
- **Title**: Payment method title displayed to customers
- **Return URL Title**: Title for the return link
- **Welcome Message**: Message displayed on payment page
- **Company Name**: Company name for payment processing
- **Custom CSS**: Custom CSS to inject into payment iframe
- **Payment Account Reference Number**: Reference number for payment accounts
- **Return URL**: URL to redirect after payment completion
- **UI Iframe Page Endpoint**: Base URL for iframe payment interface
- **Enable Logs**: Enable detailed logging

#### Environment Settings
- **Environment**: Choose between Sandbox and Production

#### Sandbox Configuration
- Create Transaction Setup Endpoint
- PaymentAccount Query Endpoint
- Surcharge Query Endpoint
- Account ID, Token, Acceptor ID
- Application ID, Name, and Version

#### Production Configuration
- Same fields as Sandbox but for production environment

## GraphQL Mutations

### 1. CreateTransactionSetup

Creates a new transaction setup for payment processing.

```graphql
mutation CreateTransactionSetup($address: UnifiedArAddressInput!) {
  createTransactionSetup(address: $address) {
    success
    message
    transaction_setup_url
    iframe_url
    error_code
  }
}
```

**Variables:**
```json
{
  "address": {
    "billing_address1": "123 Main Street",
    "billing_address2": "Apt 4B",
    "billing_city": "Nashville",
    "billing_state": "TN",
    "billing_zipcode": "37209"
  }
}
```

### 2. PaymentAccountQuery

Queries available payment accounts for a transaction.

```graphql
mutation PaymentAccountQuery($transaction_setup_id: String!) {
  paymentAccountQuery(transaction_setup_id: $transaction_setup_id) {
    success
    message
    items {
      payment_account_id
      payment_account_type
      truncated_card_number
      expiration_month
      expiration_year
      payment_account_reference_number
      transaction_setup_id
      payment_brand
      pass_updater_batch_status
      pass_updater_status
      token_provider_id
    }
    error_code
  }
}
```

**Variables:**
```json
{
  "transaction_setup_id": "98ca00a9-bc1d-448b-a9fa-cdd257232bff"
}
```

### 3. SurchargeQuery

Queries surcharge information for a payment.

```graphql
mutation SurchargeQuery(
  $payment_account_id: String!
  $origin_state: String!
  $billing_state: String!
  $amount: Float!
  $country: String!
) {
  surchargeQuery(
    payment_account_id: $payment_account_id
    origin_state: $origin_state
    billing_state: $billing_state
    amount: $amount
    country: $country
  ) {
    success
    message
    surcharge_allowed
    surcharge_percent
    error_code
  }
}
```

**Variables:**
```json
{
  "payment_account_id": "NMQXQEB21GFKNHPY10RC3C31FQRZFK",
  "origin_state": "TN",
  "billing_state": "TN",
  "amount": 234.56,
  "country": "US"
}
```

### 4. Place Order with Return Data and Surcharge

Place an order with Unified A/R return data and optional surcharge amount.

```graphql
mutation SetPaymentMethodAndPlaceOrder($input: SetPaymentMethodAndPlaceOrderInput!) {
  setPaymentMethodAndPlaceOrder(input: $input) {
    order {
      order_number
      # ... other order fields
    }
  }
}
```

**Variables:**
```json
{
  "input": {
    "cart_id": "cart_id_here",
    "payment_method": {
      "code": "unifiedarpayment"
    },
    "unified_ar_return_data": "HostedPaymentStatus=Complete&TransactionSetupID=98ca00a9-bc1d-448b-a9fa-cdd257232bff&...",
    "surcharge_amount": 5.99
  }
}
```

**Note:** The `surcharge_amount` will be added to the order total and displayed in order details.

### 5. Query Order with Surcharge Amount

Query order information including the surcharge amount.

```graphql
query GetOrder($order_number: String!) {
  customer {
    orders(filter: { order_number: { eq: $order_number } }) {
      items {
        order_number
        grand_total
        unified_ar_surcharge_amount
        # ... other order fields
      }
    }
  }
}
```

**Variables:**
```json
{
  "order_number": "*********"
}
```

**Response:**
```json
{
  "data": {
    "customer": {
      "orders": {
        "items": [
          {
            "order_number": "*********",
            "grand_total": 105.99,
            "unified_ar_surcharge_amount": 5.99
          }
        ]
      }
    }
  }
}
```

## REST API Endpoints

### Get Unified A/R Return Data

Retrieves and decodes the Unified A/R return data for a specific order.

**Endpoint:** `GET /rest/V1/unified-ar-return-data/{order_increment_id}`

**Headers:**
```
Authorization: Bearer {access_token}
Content-Type: application/json
```

**Example Request:**
```bash
curl -X GET "https://your-domain.com/rest/V1/unified-ar-return-data/*********" \
  -H "Authorization: Bearer {access_token}" \
  -H "Content-Type: application/json"
```

**Response:**
```json
{
  "HostedPaymentStatus": "Complete",
  "TransactionSetupID": "98ca00a9-bc1d-448b-a9fa-cdd257232bff",
  "TransactionID": "",
  "ExpressResponseCode": "0",
  "ExpressResponseMessage": "N",
  "AVSResponseCode": "N",
  "LastFour": "4444",
  "ValidationCode": "RDTTAGJIFJIRPSAKELTR",
  "CardLogo": "mastercard",
  "PaymentAccountID": "NMQXQEB21GFKNHPY10RC3C31FQRZFK",
  "BillingAddress1": "123 Main Street",
  "BillingZipcode": "37209",
  "Bin": "555555",
  "Entry": "Manual",
  "TranDT": "2025-07-02 07:20:42",
  "ExpirationMonth": "12",
  "ExpirationYear": "28"
}
```

**Error Response:**
```json
{
  "message": "Order not found"
}
```

## Database Schema

The module adds new columns to the `sales_order` table:

- `unified_ar_return_data` (TEXT) - Stores the Unified A/R return data
- `unified_ar_surcharge_amount` (DECIMAL(12,4)) - Stores the surcharge amount for the order

## Backend Order Display

The surcharge amount is displayed in the Magento 2 admin order details page:
- **Location**: Order totals section, after "Shipping & Handling" and before "Tax"
- **Label**: "Surcharge"
- **Format**: Properly formatted currency amount
- **Visibility**: Only shown when surcharge amount is greater than 0

## Logging

The module logs all API requests and responses to `var/log/unifiedarpayment.log`. Logs include:
- API request details (endpoint, parameters, headers)
- API response details (status code, response body)
- Error information with stack traces
- Performance metrics (request duration)

## ACL (Access Control List)

The module defines the following ACL resources:
- `Silksoftwarecorp_UnifiedArPayment::unified_ar_payment` - Main module access
- `Silksoftwarecorp_UnifiedArPayment::config` - Configuration access
- `Silksoftwarecorp_UnifiedArPayment::unified_ar_return_data` - Return data access

## File Structure

```
app/code/Silksoftwarecorp/UnifiedArPayment/
├── Api/
│   └── UnifiedArReturnDataInterface.php
├── etc/
│   ├── acl.xml
│   ├── adminhtml/
│   │   ├── menu.xml
│   │   └── system.xml
│   ├── config.xml
│   ├── db_schema.xml
│   ├── db_schema_whitelist.json
│   ├── di.xml
│   ├── payment.xml
│   └── webapi.xml
├── Helper/
│   ├── Config.php
│   └── Logger.php
├── Logger/
│   └── Handler/
│       └── UnifiedArPayment.php
├── Model/
│   ├── Payment/
│   │   └── UnifiedAr.php
│   ├── PaymentAccountQuery.php
│   ├── SurchargeQuery.php
│   ├── TransactionSetup.php
│   └── UnifiedArReturnData.php
├── composer.json
├── registration.php
└── README.md
```

## Troubleshooting

### Common Issues

1. **API 400 Errors**: Check endpoint URLs and credentials in configuration
2. **Permission Denied**: Ensure admin user has proper ACL permissions
3. **Logging Issues**: Verify log file permissions and configuration
4. **GraphQL Errors**: Check mutation syntax and required fields

### Debug Mode

Enable debug logging in the module configuration to get detailed API request/response information.

## Support

For issues and questions, please check the logs at `var/log/unifiedarpayment.log` and ensure all configuration settings are correct.

## Version

Current version: 1.0.0

## License

This module is proprietary software. All rights reserved. 