<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Magento\CatalogGraphQl\Model\ProductDataProvider" type="Silksoftwarecorp\CatalogGraphQl\Model\ProductDataProvider" />
    <type name="Magento\CatalogGraphQl\Model\Resolver\Product\ProductCustomAttributes">
        <arguments>
            <argument name="productDataProvider" xsi:type="object">Silksoftwarecorp\CatalogGraphQl\Model\ProductDataProvider</argument>
        </arguments>
    </type>
</config>
