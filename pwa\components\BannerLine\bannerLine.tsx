import { useMemo } from 'react'

import { StyledBannerLine } from './styled'

const BannerLine = ({ curIndex, totalCount, handlePrev, handleNext }) => {
  const curPage = useMemo(() => {
    return curIndex + 1
  }, [curIndex])

  const lineWidth = useMemo(() => {
    if (totalCount) {
      return `${(curPage / totalCount) * 100}%`
    }
    return 0
  }, [curPage, totalCount])

  return (
    <StyledBannerLine>
      <div className="banner-controller-line">
        <span style={{ width: lineWidth }} />
      </div>
      <div className="banner-controller-arrows">
        <div aria-hidden="true" className="controller-arrow prev" onClick={handlePrev}>
          <svg width="1em" height="1em" fill="currentColor" focusable="false">
            <use xlinkHref="#icon-next" />
          </svg>
        </div>
        <span>
          {curPage}
          <span className="line-separator">/</span>
          {totalCount}
        </span>
        <div aria-hidden="true" className="controller-arrow next" onClick={handleNext}>
          <svg width="1em" height="1em" fill="currentColor" focusable="false">
            <use xlinkHref="#icon-next" />
          </svg>
        </div>
      </div>
    </StyledBannerLine>
  )
}

export default BannerLine
