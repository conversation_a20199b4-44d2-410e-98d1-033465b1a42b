<?php
namespace Silksoftwarecorp\ShipToGraphQl\Model\Resolver;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Mail\Template\TransportBuilder;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\Translate\Inline\StateInterface;

class RequestNewShipTo implements ResolverInterface
{
    const XML_PATH_ENABLE     = 'bel_ship_to/request_shipto/email_enable';
    const XML_PATH_RECIPIENTS = 'bel_ship_to/request_shipto/email_receiver';
    const XML_PATH_SENDER     = 'bel_ship_to/request_shipto/email_identity';
    const XML_PATH_TEMPLATE   = 'bel_ship_to/request_shipto/email_template';

    protected $transportBuilder;
    protected $scopeConfig;
    protected $storeManager;
    protected $inlineTranslation;

    public function __construct(
        TransportBuilder $transportBuilder,
        ScopeConfigInterface $scopeConfig,
        StoreManagerInterface $storeManager,
        StateInterface $inlineTranslation,
    ) {
        $this->transportBuilder = $transportBuilder;
        $this->scopeConfig = $scopeConfig;
        $this->storeManager = $storeManager;
        $this->inlineTranslation = $inlineTranslation;
    }

    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
        $input = $args['input'] ?? [];
        $storeId = (int)$context->getExtensionAttributes()->getStore()->getId();
        // Validate required fields
        if (
            empty($input['business_name']) ||
            empty($input['business_address1']) ||
            empty($input['city']) ||
            empty($input['state']) ||
            empty($input['zip_code']) ||
            empty($input['phone_number']) ||
            empty($input['email_address']) ||
            empty($input['confirm_email_address']) ||
            empty($input['address_types'])
        ) {
            return ['success' => false, 'message' => __('Please fill in all required fields.')];
        }

        if ($input['email_address'] !== $input['confirm_email_address']) {
            return ['success' => false, 'message' => __('Email and Confirm Email do not match.')];
        }

        // Check if email is enabled
        $emailEnabled = $this->scopeConfig->getValue(self::XML_PATH_ENABLE, 'store', $storeId);
        if (!$emailEnabled) {
            return ['success' => false, 'message' => __('Request New Ship To is currently disabled.')];
        }

        // Get config
        $recipients = $this->scopeConfig->getValue(self::XML_PATH_RECIPIENTS, 'store', $storeId);
        $sender = $this->scopeConfig->getValue(self::XML_PATH_SENDER, 'store', $storeId);
        $template = $this->scopeConfig->getValue(self::XML_PATH_TEMPLATE, 'store', $storeId);

        if (!$recipients || !$sender || !$template) {
            return ['success' => false, 'message' => __('Email configuration is incomplete.')];
        }

        $recipientEmails = array_map('trim', explode(',', $recipients));

        $templateVars = [
            'business_name' => $input['business_name'],
            'business_address1' => $input['business_address1'],
            'business_address2' => $input['business_address2'] ?? '',
            'city' => $input['city'],
            'state' => $input['state'],
            'zip_code' => $input['zip_code'],
            'phone_number' => $input['phone_number'],
            'email_address' => $input['email_address'],
            'address_types' => implode(', ', $input['address_types']),
            'delivery_instructions' => $input['delivery_instructions'] ?? '',
        ];

        $this->inlineTranslation->suspend();
        try {
            foreach ($recipientEmails as $emailSingle) {

                $transport = $this->transportBuilder
                    ->setTemplateIdentifier($template)
                    ->setTemplateOptions(
                        [
                            'area' => \Magento\Framework\App\Area::AREA_FRONTEND,
                            'store' => $storeId,//\Magento\Store\Model\Store::DEFAULT_STORE_ID,
                        ]
                    )
                    ->setTemplateVars($templateVars)
                    ->setFrom($sender)
                    ->addTo(trim($emailSingle))
                    ->getTransport();
                $transport->sendMessage();
            }
            $this->inlineTranslation->resume();
         } catch (\Exception $e) {
             return ['success' => false, 'message' => __('Failed to send email: %1', $e->getMessage())];
         }

        return ['success' => true, 'message' => __('Your request has been submitted successfully.')];
    }
}
