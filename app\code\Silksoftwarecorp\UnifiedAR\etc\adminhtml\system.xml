<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="unified_ar" translate="label" type="text" sortOrder="999" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>Unified A/R</label>
            <tab>silksoftwarecorp</tab>
            <resource>Silksoftwarecorp_UnifiedAR::config</resource>
            <group id="general" translate="label" type="text" sortOrder="1" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>General</label>
                <field id="active" translate="label" type="select" sortOrder="1" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Enable Unified A/R</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="debug" translate="label" type="select" sortOrder="55" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Debug</label>
                    <source_model>Magento\Config\Model\Config\Source\Enabledisable</source_model>
                </field>
            </group>
            <group id="api" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>API Settings</label>
                <depends>
                    <field id="unified_ar/general/active">1</field>
                </depends>
                <field id="environment" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Environment</label>
                    <source_model>Silksoftwarecorp\UnifiedAR\Model\Config\Source\Environment</source_model>
                </field>
                <field id="url" translate="label" type="text" sortOrder="15" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>URL</label>
                    <validate>required-entry</validate>
                    <depends>
                        <field id="environment">production</field>
                    </depends>
                </field>
                <field id="api_key" translate="label" type="obscure" sortOrder="25" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Api Key</label>
                    <validate>required-entry</validate>
                    <backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
                    <depends>
                        <field id="environment">production</field>
                    </depends>
                </field>
                <field id="merchant_key" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Merchant Key</label>
                    <validate>required-entry</validate>
                    <depends>
                        <field id="environment">production</field>
                    </depends>
                </field>
                <field id="sandbox_url" translate="label" type="text" sortOrder="35" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Sandbox URL</label>
                    <validate>required-entry</validate>
                    <depends>
                        <field id="environment">sandbox</field>
                    </depends>
                </field>
                <field id="sandbox_api_key" translate="label" type="obscure" sortOrder="45" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Sandbox Api Key</label>
                    <validate>required-entry</validate>
                    <backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
                    <depends>
                        <field id="environment">sandbox</field>
                    </depends>
                </field>
                <field id="sandbox_merchant_key" translate="label" type="text" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Sandbox Merchant Key</label>
                    <validate>required-entry</validate>
                    <depends>
                        <field id="environment">sandbox</field>
                    </depends>
                </field>
                <field id="timeout" translate="label" type="text" sortOrder="55" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Timeout</label>
                    <validate>required-entry</validate>
                    <comment>The timeout for request API. The unit is seconds. Default: 10s.</comment>
                </field>
            </group>
            <group id="invoice" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Pay Now(Invoice) Setting</label>
                <field id="pay_now_key" translate="label" type="text" sortOrder="5" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Pay Now Key</label>
                </field>
                <field id="pay_now_link" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Pay Now Link</label>
                </field>
                <field id="complete_url" translate="label" type="text" sortOrder="15" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Completed Redirect Url</label>
                </field>
            </group>
        </section>
    </system>
</config>
