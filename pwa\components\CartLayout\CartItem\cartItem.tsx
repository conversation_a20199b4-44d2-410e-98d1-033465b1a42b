import Link from 'next/link'
import Image from 'next/image'
import { FormattedMessage } from 'react-intl'
import { Button, Form, Modal, Spin } from 'antd'

import { MediaLayout } from '@/ui'
import { useCartItem } from '@/hooks/CartLayout'
import BasePrice from '@/components/BasePrice'
import QuantityStep from '@/components/QuantityStep'
import ProductStock from '@/components/Common/ProductStock'
import ProductStockText from '@/components/Common/ProductStockText'
import CommonPurchased from '@/components/Common/CommonPurchased'

import { StyledCartItem, StyledItemMain } from './styled'

const CartItem = ({ cartItem }: any) => {
  const [form] = Form.useForm()
  const [modal, contextHolder] = Modal.useModal()
  const { product, prices, quantity } = cartItem
  const {
    loading,
    inputQty,
    handleQtyChange,
    handleOnEdit,
    handleOnUpdate,
    handleDelete,
    productUrl,
    actionDisabled,
    isB2bUser
  } = useCartItem(cartItem)

  const handleOnDelete = () => {
    modal.confirm({
      centered: true,
      icon: null,
      title: 'Are you sure you would like to remove this item from the cart?',
      okButtonProps: { disabled: loading },
      cancelButtonProps: { disabled: loading },
      onOk: () => handleDelete()
    })
  }

  return (
    <Spin spinning={loading}>
      <StyledCartItem>
        <Link href={productUrl} className="cart-item__img">
          <Image
            src={product.thumbnail.url}
            alt={product.thumbnail.label}
            width={120}
            height={120}
          />
        </Link>
        <div className="cart-item__main">
          <StyledItemMain>
            <CommonPurchased date={cartItem?.purchasedDate ?? ''} />
            <Link className="title" href={productUrl}>
              <span dangerouslySetInnerHTML={{ __html: product.name }} />
            </Link>
            <div className="info">
              <p className="sku">{product.sku}</p>
              <div className="stock">
                {/*<ProductStock
                  product={{
                    ...product,
                    only_x_left_in_stock: isB2bUser
                      ? cartItem?.inventoryQty
                      : product?.only_x_left_in_stock
                  }}
                />*/}
                {cartItem?.stockText && <ProductStockText text={cartItem.stockText} />}
              </div>
            </div>
          </StyledItemMain>
          <div>
            <BasePrice className="cart-item__price" value={prices.price.value} />
          </div>
          <div className="cart-item__qty">
            <Form form={form} initialValues={{ quantity }}>
              <QuantityStep
                disabled={actionDisabled}
                buttonProps={{ disabled: actionDisabled }}
                name="quantity"
                initValue={quantity}
                max={cartItem?.stockQty ?? 999}
                handleQtyChange={handleQtyChange}
              />
            </Form>
            {inputQty !== quantity && (
              <Button className="update-btn" type="primary" onClick={handleOnUpdate}>
                <FormattedMessage id="global.update" />
              </Button>
            )}
          </div>
          <div className="cart-item__utils">
            <div className="price-wrapper">
              <MediaLayout type="mobile">Total: </MediaLayout>
              <BasePrice className="cart-item__price" value={prices.row_total.value} />
            </div>
            <div className="action">
              {/* TODO: Edit */}
              {!actionDisabled && (
                <div aria-hidden="true" onClick={handleOnDelete}>
                  <svg
                    className="delete"
                    width="1em"
                    height="1em"
                    fill="currentColor"
                    focusable="false">
                    <use xlinkHref="#icon-delete" />
                  </svg>
                  Delete
                </div>
              )}
            </div>
          </div>
        </div>
      </StyledCartItem>
      {contextHolder}
    </Spin>
  )
}

export default CartItem
