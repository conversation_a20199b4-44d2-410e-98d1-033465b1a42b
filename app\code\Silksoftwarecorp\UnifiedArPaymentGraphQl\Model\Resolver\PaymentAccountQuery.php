<?php

namespace Silksoftwarecorp\UnifiedArPaymentGraphQl\Model\Resolver;

use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Silksoftwarecorp\UnifiedArPayment\Model\PaymentAccountQuery as PaymentAccountQueryModel;

class PaymentAccountQuery implements ResolverInterface
{
    /**
     * @var PaymentAccountQueryModel
     */
    private $paymentAccountQuery;

    /**
     * @param PaymentAccountQueryModel $paymentAccountQuery
     */
    public function __construct(
        PaymentAccountQueryModel $paymentAccountQuery
    ) {
        $this->paymentAccountQuery = $paymentAccountQuery;
    }

    /**
     * @inheritdoc
     */
    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
        try {
            $transactionSetupId = $args['transaction_setup_id'] ?? null;
            
            if (!$transactionSetupId) {
                throw new GraphQlInputException(__('Transaction Setup ID is required'));
            }

            // Call the payment account query model
            $response = $this->paymentAccountQuery->queryPaymentAccount($transactionSetupId);

            return [
                'success' => $response['success'],
                'message' => $response['message'],
                'items' => $response['items'] ?? [],
                'error_code' => $response['error_code'] ?? null
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'items' => [],
                'error_code' => 'EXCEPTION'
            ];
        }
    }
} 