<?php

declare(strict_types=1);

namespace Silksoftwarecorp\UnifiedArPayment\Model\ResourceModel\BlevinsOrderPayment;

use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;
use Silksoftwarecorp\UnifiedArPayment\Model\ResourceModel\BlevinsOrderPayment as BlevinsOrderPaymentResource;
use Silksoftwarecorp\UnifiedArPayment\Model\BlevinsOrderPayment;

/**
 * Resource Collection
 */
class Collection extends AbstractCollection
{
    protected function _construct()
    {
        $this->_init(BlevinsOrderPayment::class, BlevinsOrderPaymentResource::class);
    }

}
