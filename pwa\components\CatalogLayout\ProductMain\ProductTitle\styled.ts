import styled from '@emotion/styled'

export const StyledProductTitle = styled.div`
  margin-top: 10px;

  .title {
    margin-bottom: 10px;
    font-weight: 700;
    font-size: 38px;
    line-height: 44px;
    color: var(--color-font);
    letter-spacing: 0;
  }

  .info {
    .sku {
      font-weight: 400;
      font-size: 16px;
      line-height: 24px;
      letter-spacing: 0;
      color: var(--color-text);
    }

    .manufacture-name {
      margin-top: 6px;
      font-weight: 700;
      font-size: 16px;
      line-height: 28px;
      letter-spacing: 0.02em;
      color: var(--color-font);
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    margin-top: 8px;
    margin-bottom: 14px;

    .title {
      margin-bottom: 15px;
      font-size: 34px;
    }

    .info {
      .manufacture-name {
        margin-top: 10px;
      }
    }
  }
`
