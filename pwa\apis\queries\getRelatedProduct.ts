import { gql } from '@apollo/client'
import type { DocumentNode } from '@apollo/client'

import { price_range } from '../fragment/priceRange'

export const GET_PRODUCT_RELATED: DocumentNode = gql`
  query getRelatedProduct(
    $search: String
    $filter: ProductAttributeFilterInput
    $pageSize: Int
    $currentPage: Int
    $sort: ProductAttributeSortInput
  ) {
    products(
      search: $search
      pageSize: $pageSize
      currentPage: $currentPage
      filter: $filter
      sort: $sort
    ) {
      items {
        products: related_products {
          name
          sku
          url_key
          image {
            label
            url
          }
          small_image {
            label
            url
          }
          price_range {
            ...price_range
            __typename
          }
          stock_status
          __typename
        }
      }
    }
  }
  ${price_range}
`
