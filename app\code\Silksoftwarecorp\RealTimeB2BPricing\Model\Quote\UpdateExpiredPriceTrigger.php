<?php

namespace Silksoftwarecorp\RealTimeB2BPricing\Model\Quote;

use Magento\Quote\Model\Quote;
use Silksoftwarecorp\Customer\Model\B2BUserChecker;
use Silksoftwarecorp\RealTimeB2BPricing\Helper\Data as DataHelper;

class UpdateExpiredPriceTrigger
{
    /**
     * @var DataHelper
     */
    private $helper;

    /**
     * @var B2BUserChecker
     */
    private $b2bUserChecker;

    /**
     * @param DataHelper $helper
     * @param B2BUserChecker $b2bUserChecker
     */
    public function __construct(
        DataHelper $helper,
        B2BUserChecker $b2bUserChecker
    ) {
        $this->helper = $helper;
        $this->b2bUserChecker = $b2bUserChecker;
    }

    /**
     * If enabled Real-Time B2B Pricing
     *
     * B2B customers need to execute the `collectTotals` method to trigger the update expired price logic.
     *
     * @param Quote $quote
     * @param $customerId
     * @param $storeId
     *
     * @return void
     */
    public function execute(Quote $quote, $customerId = null, $storeId = null): void
    {
        if ($quote->getData('rtp_updated_flag')) {
            return;
        }

        $storeId = $storeId ?: $quote->getStoreId();
        if (!$this->helper->isActive($storeId)) {
            return;
        }

        $customerId = $customerId ?: $quote->getCustomerId();
        if ($customerId && $this->b2bUserChecker->isB2BUser($customerId)) {
            $quote->collectTotals();
        }
    }
}
