<?php

namespace Silksoftwarecorp\CompanyB2b\Api\Data;

interface CompanyCustomAttributesInterface
{

    const ERP_CUST_NUM = 'erp_cust_num';
    const ERP_CUST_CODE = 'erp_cust_code';
    const ERP_CON_NUM = 'erp_con_num';

    /**
     *  get Erp Cust Num
     * @return ?string
     */
    public function getErpCustNum():?string;

    /**
     * @return ?string
     */
    public function getErpCustCode():?string;

    /**
     * @param ?string $erpCustomerNum
     * @return self
     */
    public function setErpCustNum(?string $erpCustomerNum):self;

    /**
     * @param ?string $erpCustomerCode
     * @return self
     */

    public function setErpCustCode(?string $erpCustomerCode):self;


    /**
     * @return ?string
     */
    public function getErpConNum():?string;

    /**
     * @param ?string $conID
     * @return self
     */
    public function setErpConNum(?string $conID):self;

    /**
     * @return ?string
     */
    public function getWebsiteId():?string;

    /**
     * @param ?string $websiteId
     * @return  self
     */
    public function setWebsiteId(?string $websiteId):self;
}
