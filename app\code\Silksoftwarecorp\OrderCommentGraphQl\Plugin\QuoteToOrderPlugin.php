<?php
namespace Silksoftwarecorp\OrderCommentGraphQl\Plugin;

use Magento\Quote\Model\QuoteManagement;
use Magento\Quote\Api\Data\CartInterface;

class QuoteToOrderPlugin
{
    public function beforeSubmit(QuoteManagement $subject, CartInterface $quote)
    {
        if ($quote->getExtensionAttributes() && $quote->getExtensionAttributes()->getSpecialNotes() !== null) {
            /** @var \Magento\Quote\Model\Quote $quote */
            $quote->setData('special_notes', $quote->getExtensionAttributes()->getSpecialNotes());
        }
        return [$quote];
    }
} 