<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */
declare(strict_types=1);

namespace Silksoftwarecorp\CompanyGraphQl\Model\Company;

use Magento\Company\Api\Data\CompanyCustomerInterface;
use Magento\Company\Api\Data\CompanyInterface;
use Magento\Company\Model\ResourceModel\Users\Grid\CollectionFactory;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Api\Data\CustomerSearchResultsInterface;
use Magento\Framework\Api\FilterBuilder;
use Magento\Framework\Api\SearchCriteriaBuilder;

/**
 * Company all users data provider
 */
class Users
{
    /**
     * Status of company user - active
     */
    public const STATUS_ACTIVE = 'ACTIVE';

    /**
     * Status of company user - inactive
     */
    public const STATUS_INACTIVE = 'INACTIVE';

    /**
     * @var CollectionFactory
     */
    private $companyUserCollectionFactory;

    /**
     * @var CustomerRepositoryInterface
     */
    private $customerRepository;

    /**
     * @var SearchCriteriaBuilder
     */
    private $searchCriteriaBuilder;

    /**
     * @var FilterBuilder
     */
    private $filterBuilder;

    /**
     * @var array
     */
    private $companyUserStatus;

    /**
     * @param CollectionFactory $companyUserCollectionFactory
     * @param CustomerRepositoryInterface $customerRepository
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param FilterBuilder $filterBuilder
     * @param array $companyUserStatus
     */
    public function __construct(
        CollectionFactory $companyUserCollectionFactory,
        CustomerRepositoryInterface $customerRepository,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        FilterBuilder $filterBuilder,
        array $companyUserStatus = []
    ) {
        $this->companyUserCollectionFactory = $companyUserCollectionFactory;
        $this->customerRepository = $customerRepository;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->filterBuilder = $filterBuilder;
        $this->companyUserStatus = $companyUserStatus;
    }

    /**
     * Get all company users without pagination
     *
     * @param CompanyInterface $company
     * @param array $args
     * @return CustomerSearchResultsInterface
     */
    public function getAllCompanyUsers(CompanyInterface $company, array $args): CustomerSearchResultsInterface
    {
        $usersCollection = $this->companyUserCollectionFactory->create();
        
        // Apply status filter if provided
        if (isset($args['filter']['status'])) {
            $usersCollection->addFieldToFilter(
                'company_customer.' . CompanyCustomerInterface::STATUS,
                array_search(
                    $args['filter']['status'],
                    array_column($this->companyUserStatus, 'label', 'value'),
                    false
                )
            );
        }

        $usersCollection->addFieldToFilter(
            'company.' . CompanyInterface::COMPANY_ID,
            $company->getId()
        );

        $companyUserIds = $usersCollection->getAllIds();
        
        if (empty($companyUserIds)) {
            // Return empty result
            $this->searchCriteriaBuilder->setPageSize(0);
            $searchCriteria = $this->searchCriteriaBuilder->create();
            return $this->customerRepository->getList($searchCriteria);
        }

        $filters = [];
        $filters[] = $this->filterBuilder
            ->setField('entity_id')
            ->setConditionType('in')
            ->setValue($companyUserIds)
            ->create();

        $this->searchCriteriaBuilder->addFilters($filters);
        $searchCriteria = $this->searchCriteriaBuilder->create();

        return $this->customerRepository->getList($searchCriteria);
    }

    /**
     * Get company user's status
     *
     * @return string[]
     */
    public function getCompanyUserStatus(): array
    {
        return $this->companyUserStatus;
    }
} 