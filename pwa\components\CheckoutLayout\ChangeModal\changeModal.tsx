import { memo } from 'react'
import Link from 'next/link'
import { FormattedMessage } from 'react-intl'
import { Button, Form, Spin, Modal as AntdModal } from 'antd'

import { Modal } from '@/ui'
import { useChangeModal } from '@/hooks/CheckoutLayout'

import { StyledAddressTable, StyledAddressItem, StyledRemovalModal } from './styled'

const ChangeModal = memo(({ activeId, addressList = [], handleChooseAddress, ...props }: any) => {
  const { visible, ...rest } = props
  const [form] = Form.useForm()
  const {
    currentList,
    handleFilterSearch,
    handleProceed,
    removalModalRef,
    closeRemovalModal,
    onRemovalModalCancel,
    handleSelectAddress
  } = useChangeModal({
    addressList,
    form,
    handleChooseAddress
  })

  return (
    <>
      <AntdModal
        className="modal-checkout"
        footer={null}
        width={1004}
        open={visible}
        getContainer={() => document.getElementById('checkout')}
        title="Address Book"
        {...rest}>
        <StyledAddressTable>
          {currentList.map((address: any) => {
            return (
              <StyledAddressItem key={address.key}>
                <div className="address">
                  <div className="address-company">{address.company}</div>
                  <div>{`${address.firstname} ${address.lastname}`}</div>
                  {address.street.map((item) => (
                    <div key={item}>{item}</div>
                  ))}
                  <div>
                    <span>{`${address.city}, `}</span>
                    <span>{`${address.region.region}, `}</span>
                    <span>{address.postcode}</span>
                  </div>
                  <div>
                    <Link className="address-telephone" href={`tel:${address.telephone}`}>
                      {address.telephone}
                    </Link>
                  </div>
                </div>
                <div className="actions">
                  {address.key === activeId ? (
                    <span className="selected">
                      <FormattedMessage id="global.selected" />
                    </span>
                  ) : (
                    <Button
                      type="link"
                      onClick={() => {
                        handleSelectAddress(address.key)
                      }}>
                      <FormattedMessage id="global.select" />
                    </Button>
                  )}
                </div>
              </StyledAddressItem>
            )
          })}
        </StyledAddressTable>
      </AntdModal>

      <Modal
        ref={removalModalRef}
        width={626}
        getContainer={() => document.getElementById('checkout')}
        onCancel={onRemovalModalCancel}>
        <StyledRemovalModal>
          <svg width="40px" height="40px" fill="currentColor" aria-hidden="true" focusable="false">
            <use xlinkHref="#icon-modal-warning-icon" />
          </svg>
          <div>
            Changing the address will result in the removal of the following item:
            <b>{/* TODO: shipping address */}</b>
          </div>
          <div className="removal-action">
            <Button type="primary" onClick={handleProceed}>
              PROCEED
            </Button>
            <Button className="cancel-btn" onClick={closeRemovalModal}>
              CANCEL
            </Button>
          </div>
        </StyledRemovalModal>
      </Modal>
    </>
  )
})

export default ChangeModal
