<?php

namespace Silksoftwarecorp\EDI\Model\Product;

use Magento\InventoryCatalog\Model\GetStockIdForCurrentWebsite;
use Magento\InventorySalesApi\Api\AreProductsSalableInterface;
use Magento\InventorySalesApi\Api\Data\IsProductSalableResultInterface;

class StockStatusReader
{
    /**
     * @var GetStockIdForCurrentWebsite
     */
    private $getStockIdForCurrentWebsite;

    /**
     * @var AreProductsSalableInterface|null
     */
    private $areProductsSalable;

    /**
     * @var IsProductSalableResultInterface[]
     */
    private $_stockStatus = [];

    /**
     * @param GetStockIdForCurrentWebsite $getStockIdForCurrentWebsite
     * @param AreProductsSalableInterface $areProductsSalable
     */
    public function __construct(
        GetStockIdForCurrentWebsite $getStockIdForCurrentWebsite,
        AreProductsSalableInterface $areProductsSalable
    ) {
        $this->getStockIdForCurrentWebsite = $getStockIdForCurrentWebsite;
        $this->areProductsSalable = $areProductsSalable;
    }

    public function execute(array $skus): void
    {
        $stockId = $this->getStockIdForCurrentWebsite->execute();
        $result = $this->areProductsSalable->execute($skus, $stockId);

        foreach ($result as $item) {
            $this->_stockStatus[$item->getSku()] = $item;
        }
    }

    /**
     * @param $sku
     * @return IsProductSalableResultInterface|null
     */
    public function get($sku): ?IsProductSalableResultInterface
    {
        return $this->_stockStatus[$sku] ?? null;
    }
}
