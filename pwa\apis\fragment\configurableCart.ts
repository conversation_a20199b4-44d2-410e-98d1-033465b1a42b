import { gql } from '@apollo/client'
import type { DocumentNode } from '@apollo/client'

export const configurableCart: DocumentNode = gql`
  fragment configurableCart on ConfigurableCartItem {
    customizable_options {
      customizable_option_uid
      id
      is_required
      label
      sort_order
      type
      values {
        customizable_option_value_uid
        id
        label
        value
      }
    }
    configurable_options {
      id: configurable_product_option_uid
      option_label
      value_id
      value_label
    }
  }
`
