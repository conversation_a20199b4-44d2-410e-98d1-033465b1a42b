import styled from '@emotion/styled'

export const StyledBranchDetailsPage = styled.div`
  padding: 40px 0 64px;

  h1 {
    margin-bottom: 32px;
    font-weight: 700;
    font-size: 40px;
    line-height: 48px;
    letter-spacing: 0;
    color: #191919;
  }

  .details-grid {
    display: grid;
    grid-template-columns: 1fr 678px;
    grid-gap: 44px;
  }

  .details-map {
    height: 100%;
    border-radius: 5px;
    overflow: hidden;

    & > div {
      height: 100%;
    }
  }

  .details-list {
    & > div {
      position: relative;
      padding: 24px;
      border: 1px solid #d9d9d9;
      border-radius: 5px;

      &:not(:first-of-type) {
        margin-top: 16px;
      }

      &::after {
        content: '';
        position: absolute;
        top: 27px;
        left: 0;
        z-index: 1;
        display: block;
        width: 5px;
        height: 19px;
        background: #003865;
      }

      &.manager-item {
        p {
          font-size: 15px;
        }
      }

      h5 {
        font-weight: 700;
        font-size: 22px;
        line-height: 28px;
        letter-spacing: 0;
      }

      p {
        margin-bottom: 1px;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        letter-spacing: 0.01em;
        color: #191919;
      }

      a {
        font-weight: 700;
        color: #9d2235 !important;
        text-decoration: underline !important;
      }

      .mt-item {
        margin-top: 16px;
      }

      .general-manager {
        display: grid;
        grid-template-columns: 124px 1fr;
        grid-gap: 16px;
        align-items: center;
        margin-top: 16px;

        img {
          border-radius: 50%;
          overflow: hidden;
          border: 1px solid #f5f5f5;
          background: #fff;
        }
      }
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    padding: 22px 0 40px;

    h1 {
      padding: 0 16px;
      font-size: 30px;
      line-height: 38px;
    }

    .details-grid {
      grid-template-columns: 1fr;
      grid-gap: 40px;
    }

    .details-list {
      padding: 0 16px;

      & > div {
        padding: 24px 18px;

        h5 {
          margin-bottom: 6px;
        }
      }
    }
  }
`
