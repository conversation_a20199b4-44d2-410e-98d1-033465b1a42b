import { memo, useMemo } from 'react'

import { LineContainer } from '@ranger-theme/ui'
import CommonPageTitle from '@/components/Common/CommonPageTitle'
import Breadcrumb from '@/components/Breadcrumb'

import { StyledCommonAccountPageLayout } from './styled'

const CommonAccountPageLayout = ({
  children,
  title = '',
  titleVisible = true,
  breadcrumbTitle = '',
  singleBreadcrumb = false,
  breadcrumbItems = null,
  breadcrumbProps = {}
}) => {
  const items = useMemo(() => {
    const name = breadcrumbTitle || title
    return singleBreadcrumb ? [{ name }] : [{ url: '/account', name: 'Account' }, { name }]
  }, [breadcrumbTitle, singleBreadcrumb, title])

  return (
    <>
      <Breadcrumb items={breadcrumbItems || items} {...breadcrumbProps} />
      <StyledCommonAccountPageLayout>
        <LineContainer>
          {titleVisible && <CommonPageTitle title={title} />}
          {children}
        </LineContainer>
      </StyledCommonAccountPageLayout>
    </>
  )
}

export default memo(CommonAccountPageLayout)
