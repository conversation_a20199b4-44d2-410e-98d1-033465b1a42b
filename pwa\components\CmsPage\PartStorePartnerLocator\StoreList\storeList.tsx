import { memo, useState, useEffect, useCallback, useMemo, useRef } from 'react'
import { isEmpty, debounce } from 'lodash-es'

import { useAwaitQuery } from '@ranger-theme/apollo'
import CommonCheckbox from '@/components/Common/CommonCheckbox'
import PartStoreLocationItem from '@/components/Common/PartStoreLocationItem'
import { GET_AM_STORE_LOCATOR_ATTRIBUTES_TO_FILTER } from '@/apis/queries/getAmStoreLocatorAttributesToFilter'
import { SEARCH_AM_STORE_LOCATIONS } from '@/apis/queries/searchAmStoreLocations'

import StoreInputSearch from './StoreInputSearch'
import { StyledStoreList } from './styled'

const StoreList = ({
  center,
  google,
  storeItems,
  handleSaveStoreItems,
  handleSetLoading,
  handleMoveToCenter,
  handleMoveTo
}: any) => {
  const storeWrapperRef = useRef<any>(null)
  const getFilterQuery = useAwaitQuery(GET_AM_STORE_LOCATOR_ATTRIBUTES_TO_FILTER)
  const searchAmStoreLocationsQuery = useAwaitQuery(SEARCH_AM_STORE_LOCATIONS)

  const [filterList, setFilterList] = useState([])
  const [selectedFilters, setSelectedFilters] = useState([])
  const [searchLocation, setSearchLocation] = useState(null)
  const [options, setOptions] = useState<any>([])
  const [isDataReady, setIsDataReady] = useState(false)

  const isEmptyList = useMemo(() => {
    return isDataReady && storeItems.length === 0
  }, [isDataReady, storeItems])

  const handleClearOptions = useCallback(() => {
    setOptions([])
  }, [])

  const handleSaveLocation = useCallback((val) => {
    setSearchLocation(val)
  }, [])

  const handleGoogleSearch = useCallback(
    async (val) => {
      try {
        return new Promise((resolve) => {
          if (!val) {
            resolve([])
          }
          if (!google) {
            console.error('Google not found.')
            resolve([])
          }
          const service = new google.maps.places.AutocompleteService()
          service.getPlacePredictions(
            {
              input: val,
              types: ['locality', 'postal_code'],
              componentRestrictions: {
                country: 'US'
              }
            },
            async (results, status) => {
              if (status === 'OK') {
                resolve(
                  results.map((res) => {
                    return {
                      value: res?.description ?? '',
                      placeId: res?.place_id ?? ''
                    }
                  })
                )
              } else {
                // No result
                resolve([])
              }
            }
          )
        })
      } catch (e) {
        console.error(e)
        return Promise.resolve([])
      }
    },
    [google]
  )

  const onChange = debounce(async (val) => {
    try {
      if (val) {
        const optionsValues = await handleGoogleSearch(val)
        setOptions(optionsValues || [])
      } else {
        handleSaveLocation(null)
      }
    } catch (e) {
      console.error(e)
    }
  }, 500)

  const handleSelectFilter = useCallback(
    (item, checked) => {
      if (checked) {
        setSelectedFilters([...selectedFilters, item])
      } else {
        setSelectedFilters(
          selectedFilters.filter(
            (filterItem) => filterItem?.attribute_code !== item?.attribute_code
          )
        )
      }
    },
    [selectedFilters]
  )

  const getFilter = useCallback(async () => {
    try {
      const { data } = await getFilterQuery({
        fetchPolicy: 'no-cache'
      })
      setFilterList(data?.amStoreLocatorAttributesToFilter ?? [])
    } catch (e) {
      console.error(e)
    }
  }, [getFilterQuery])

  const handleSearchLocationByPlaceId = useCallback(
    (id) => {
      const geocoder = new google.maps.Geocoder()
      geocoder.geocode({ placeId: id }, (curRes, curStatus) => {
        if (curStatus === google.maps.GeocoderStatus.OK) {
          const lat = curRes[0].geometry.location.lat()
          const lng = curRes[0].geometry.location.lng()
          if (lat && lng) {
            handleMoveTo({
              lat,
              lng
            })
            // Save location and search store list
            handleSaveLocation({
              lat,
              lng
            })
          } else {
            console.error('Location not found.')
          }
        } else {
          console.error('Result not found.')
        }
      })
    },
    [google, handleMoveTo, handleSaveLocation]
  )

  const handleSearch = useCallback(
    async (val: string) => {
      try {
        if (!val) {
          // Show default list
          handleSaveLocation(null)
          return
        }
        // Search place by google
        const optionsValues = await handleGoogleSearch(val)

        const targetPlaceId = optionsValues?.[0]?.placeId
        if (targetPlaceId) {
          // Search location by place id
          handleSearchLocationByPlaceId(targetPlaceId)
        } else {
          console.error(`${val} not found`)
          handleSaveStoreItems([])
        }
      } catch (e) {
        console.error(e)
      }
    },
    [handleGoogleSearch, handleSearchLocationByPlaceId, handleSaveStoreItems, handleSaveLocation]
  )

  const handleSearchStore = useCallback(async () => {
    try {
      handleSetLoading(true)
      let filterQuery = {}
      if (selectedFilters.length > 0) {
        filterQuery = {
          attributes: selectedFilters.map((filterItem) => {
            return {
              name: filterItem?.attribute_id,
              value: 1
            }
          })
        }
      }
      const locationValues = searchLocation || center
      const { data } = await searchAmStoreLocationsQuery({
        // fetchPolicy: 'no-cache',
        variables: {
          filter: {
            distance: {
              latitude: locationValues?.lat,
              longitude: locationValues?.lng,
              radius: 0
            },
            ...filterQuery
          },
          currentPage: 1,
          pageSize: 50
        }
      })
      handleSaveStoreItems(data?.searchAmStoreLocations?.items ?? [])
      setIsDataReady(true)
    } catch (e) {
      console.error(e)
    } finally {
      handleSetLoading(false)
    }
  }, [
    selectedFilters,
    searchAmStoreLocationsQuery,
    handleSaveStoreItems,
    handleSetLoading,
    searchLocation,
    center
  ])

  const handleSelectPlace = useCallback(
    (val) => {
      handleSearchLocationByPlaceId(val)
    },
    [handleSearchLocationByPlaceId]
  )

  useEffect(() => {
    handleSearchStore()
  }, [handleSearchStore])

  useEffect(() => {
    getFilter()
  }, [getFilter])

  useEffect(() => {
    if (storeWrapperRef?.current?.scrollTo) {
      storeWrapperRef.current.scrollTop = 0
    }
  }, [storeItems])

  return (
    <StyledStoreList>
      <h2>Find the Nearest Part Store</h2>

      {/* Search */}
      <StoreInputSearch
        options={options}
        handleGoogleSearch={onChange}
        handleSearch={handleSearch}
        handleClearOptions={handleClearOptions}
        handleSaveLocation={handleSaveLocation}
        handleMoveToCenter={handleMoveToCenter}
        handleSelectPlace={handleSelectPlace}
      />

      {/* Filter */}
      {!isEmpty(filterList) && (
        <div className="store-filter">
          <div className="filter-title">Filter by:</div>
          {filterList.map((item) => {
            if (item?.frontend_input === 'boolean') {
              return (
                <div key={item?.attribute_id} className="filter-item">
                  <CommonCheckbox
                    checked={
                      !!selectedFilters?.find(
                        (filter) => filter?.attribute_code === item?.attribute_code
                      )
                    }
                    onChange={(checked) => {
                      handleSelectFilter(item, checked)
                    }}
                  />
                  {item?.label ?? ''}
                </div>
              )
            }
            return null
          })}
        </div>
      )}

      {/* List */}
      {isEmptyList ? (
        <div className="store-empty">No Results Found</div>
      ) : (
        <>
          <div className="store-items" ref={storeWrapperRef}>
            {storeItems.map((item) => {
              return (
                <div key={item?.id} className="item">
                  <PartStoreLocationItem location={item} />
                </div>
              )
            })}
          </div>

          <div className="panel-mask" />
        </>
      )}
    </StyledStoreList>
  )
}

export default memo(StoreList)
