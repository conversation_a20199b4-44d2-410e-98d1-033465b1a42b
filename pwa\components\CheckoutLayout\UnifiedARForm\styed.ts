import styled from '@emotion/styled'

export const StyledUnifiedARForm = styled.div`
  margin-top: 32px;
  height: 235px;
  overflow: hidden;

  iframe {
    border: none !important;
    box-shadow: none !important;
    overflow: hidden !important;
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
  }
`

export const StyledSurchargeModal = styled.div`
  margin: 40px auto 30px;
  max-width: 557px;

  h2 {
    margin-bottom: 10px;
    font-weight: 700;
    font-size: 32px;
    line-height: 38px;
    letter-spacing: 0;
    text-align: center;

    .price {
      padding-left: 10px;
      font-size: 32px;
      line-height: 38px;
      letter-spacing: 0;
    }
  }

  div {
    font-weight: 400;
    font-size: 15px;
    line-height: 21px;
    letter-spacing: 0.01em;
    text-align: center;
    color: var(--color-text);
  }

  .surcharge-modal-action {
    margin-top: 24px;

    button:not(:first-of-type) {
      margin-left: 16px;
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    h2 {
      font-size: 28px;
      line-height: 32px;

      .price {
        font-size: 28px;
        line-height: 32px;
      }
    }

    .surcharge-modal-action {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
`

export const StyledCreditCardInformation = styled.div`
  display: inline-flex;
  justify-content: center;
  align-items: center;
  margin-top: 32px;
  padding: 16px 48px;
  background-color: #ddecdd;
  border-radius: 3px;

  .check-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #21881f;
  }

  p {
    padding-left: 5px;
    font-weight: 700;
    font-size: 14px;
    line-height: 20px;
    letter-spacing: 0;
    color: #21881f;
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    display: flex;
    padding: 16px;
  }
`
