<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */
declare(strict_types=1);

namespace Silksoftwarecorp\ShipToQuoteGraphQl\Model\Resolver;

use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlAuthorizationException;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Exception\GraphQlNoSuchEntityException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Quote\Api\CartRepositoryInterface;
use Magento\QuoteGraphQl\Model\Cart\GetCartForUser;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\Api\AttributeValueFactory;

/**
 * Set ship to data on cart resolver
 */
class SetShipToDataOnCart implements ResolverInterface
{
    /**
     * @var GetCartForUser
     */
    private $getCartForUser;

    /**
     * @var CartRepositoryInterface
     */
    private $cartRepository;

    /**
     * @var CustomerRepositoryInterface
     */
    private $customerRepository;

    /**
     * @var AttributeValueFactory
     */
    private $attributeValueFactory;

    /**
     * @param GetCartForUser $getCartForUser
     * @param CartRepositoryInterface $cartRepository
     * @param CustomerRepositoryInterface $customerRepository
     * @param AttributeValueFactory $attributeValueFactory
     */
    public function __construct(
        GetCartForUser $getCartForUser,
        CartRepositoryInterface $cartRepository,
        CustomerRepositoryInterface $customerRepository,
        AttributeValueFactory $attributeValueFactory
    ) {
        $this->getCartForUser = $getCartForUser;
        $this->cartRepository = $cartRepository;
        $this->customerRepository = $customerRepository;
        $this->attributeValueFactory = $attributeValueFactory;
    }

    /**
     * @inheritdoc
     */
    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
        if (empty($args['input']['cart_id'])) {
            throw new GraphQlInputException(__('Required parameter "cart_id" is missing'));
        }

        if (empty($args['input']['ship_to_id'])) {
            throw new GraphQlInputException(__('Required parameter "ship_to_id" is missing'));
        }

        $cartId = $args['input']['cart_id'];
        $shipToId = $args['input']['ship_to_id'];

        $storeId = (int)$context->getExtensionAttributes()->getStore()->getId();
        $userId = $context->getUserId() ?? null;
        $cart = $this->getCartForUser->execute($cartId, $userId, $storeId);

        // Set ship_to_id on quote
        $cart->setData('ship_to_id', $shipToId);

        // Get customer_contact_id from customer's erp_contact_id attribute
        $customerId = $cart->getCustomerId();
        if ($customerId) {
            try {
                $customer = $this->customerRepository->getById($customerId);
                $erpContactIdAttribute = $customer->getCustomAttribute('erp_contact_id');
                if ($erpContactIdAttribute && $erpContactIdAttribute->getValue()) {
                    $cart->setData('customer_contact_id', $erpContactIdAttribute->getValue());
                }
            } catch (\Exception $e) {
                // Log error but don't fail the operation
                // Customer contact ID is optional
            }
        }

        $this->cartRepository->save($cart);

        return [
            'cart' => [
                'model' => $cart,
            ],
        ];
    }
}
