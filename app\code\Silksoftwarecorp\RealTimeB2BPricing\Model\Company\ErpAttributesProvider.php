<?php

namespace Silksoftwarecorp\RealTimeB2BPricing\Model\Company;

use Magento\Company\Api\CompanyRepositoryInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Framework\Exception\NoSuchEntityException;

class ErpAttributesProvider
{
    /**
     * @var CompanyRepositoryInterface
     */
    protected $companyRepository;

    /**
     * @var CustomerRepositoryInterface
     */
    protected $customerRepository;

    /**
     * @param CompanyRepositoryInterface $companyRepository
     * @param CustomerRepositoryInterface $customerRepository
     */
    public function __construct(
        CompanyRepositoryInterface $companyRepository,
        CustomerRepositoryInterface $customerRepository
    ) {
        $this->companyRepository = $companyRepository;
        $this->customerRepository = $customerRepository;
    }

    public function getErpCustomerIdByCustomerId($customerId): ?string
    {
        try {
            $customer = $this->customerRepository->getById($customerId);

            return $this->getErpCustomerId($customer);
        }  catch (NoSuchEntityException $e) {}

        return null;
    }

    public function getErpCustomerId(CustomerInterface $customer): ?string
    {
        try {
            $companyAttributes = $customer->getExtensionAttributes()?->getCompanyAttributes();
            $companyId = $companyAttributes?->getCompanyId();
            if ($companyId && $companyId > 0) {
                $company = $this->companyRepository->get($companyId);

                return $company?->getExtensionAttributes()?->getErpCustomerId();
            }
        } catch (NoSuchEntityException $e) {}

        return null;
    }
}
