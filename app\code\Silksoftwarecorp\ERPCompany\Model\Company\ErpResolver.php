<?php

namespace Silksoftwarecorp\ERPCompany\Model\Company;

use Magento\Company\Api\CompanyRepositoryInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Psr\Log\LoggerInterface;

/**
 * Company ERP Resolver
 *
 * Resolves ERP customer identifiers from Magento company data
 */
class ErpResolver implements ErpResolverInterface
{
    /**
     * @var CompanyRepositoryInterface
     */
    private $companyRepository;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * Constructor
     *
     * @param CompanyRepositoryInterface $companyRepository
     * @param LoggerInterface $logger
     */
    public function __construct(
        CompanyRepositoryInterface $companyRepository,
        LoggerInterface $logger
    ) {
        $this->companyRepository = $companyRepository;
        $this->logger = $logger;
    }

    /**
     * Get ERP Customer ID by Company ID
     *
     * @param int $companyId
     * @return string
     * @throws LocalizedException
     */
    public function getErpCustomerId(int $companyId): string
    {
        try {
            $company = $this->companyRepository->get($companyId);
            $erpCustomerId = $company->getExtensionAttributes()->getErpCustomerId();

            if (empty($erpCustomerId)) {
                $this->logger->error('No ERP Customer ID found for company', [
                    'company_id' => $companyId,
                    'company_name' => $company->getCompanyName()
                ]);

                throw new LocalizedException(__('No ERP Customer ID found for company ID: %1', $companyId));
            }

            return $erpCustomerId;

        } catch (NoSuchEntityException $e) {
            $this->logger->error('Company not found', [
                'company_id' => $companyId,
                'error' => $e->getMessage()
            ]);
            throw new LocalizedException(__('Company not found with ID: %1', $companyId), $e);
        } catch (\Exception $e) {
            $this->logger->error('Error resolving ERP Customer ID', [
                'company_id' => $companyId,
                'error' => $e->getMessage()
            ]);

            throw new LocalizedException(__('Error retrieving ERP Customer ID: %1', $e->getMessage()), $e);
        }
    }

    /**
     * Check if company has ERP Customer ID
     *
     * @param int $companyId
     * @return bool
     */
    public function hasErpCustomerId(int $companyId): bool
    {
        try {
            $this->getErpCustomerId($companyId);
            return true;
        } catch (LocalizedException $e) { }

        return false;
    }

    /**
     * Get ERP Sales Rep ID by Company ID
     *
     * @param int $companyId
     * @return string
     * @throws LocalizedException
     */
    public function getErpSalesRepId(int $companyId): string
    {
        try {
            $company = $this->companyRepository->get($companyId);
            $erpSalesRepId = $company->getExtensionAttributes()->getErpSalesRepId();

            return $erpSalesRepId;

        } catch (NoSuchEntityException $e) {
            $this->logger->error('Company not found', [
                'company_id' => $companyId,
                'error' => $e->getMessage()
            ]);
            throw new LocalizedException(__('Company not found with ID: %1', $companyId), $e);
        } catch (\Exception $e) {
            $this->logger->error('Error resolving ERP Sales Rep ID', [
                'company_id' => $companyId,
                'error' => $e->getMessage()
            ]);

            throw new LocalizedException(__('Error retrieving ERP Sales Rep ID: %1', $e->getMessage()), $e);
        }
    }

    /**
     * Check if company has  ERP Sales Rep ID
     *
     * @param int $companyId
     * @return bool
     */
    public function hasErpSalesRepId(int $companyId): bool
    {
        try {
            $this->getErpSalesRepId($companyId);
            return true;
        } catch (LocalizedException $e) { }

        return false;
    }

    /**
     * Get ERP Sales Rep Name by Company ID
     *
     * @param int $companyId
     * @return string
     * @throws LocalizedException
     */
    public function getErpSalesRepName(int $companyId): string
    {
        try {
            $company = $this->companyRepository->get($companyId);
            $erpSalesRepName = $company->getExtensionAttributes()->getErpSalesRepName();

            return $erpSalesRepName;

        } catch (NoSuchEntityException $e) {
            $this->logger->error('Company not found', [
                'company_id' => $companyId,
                'error' => $e->getMessage()
            ]);
            throw new LocalizedException(__('Company not found with ID: %1', $companyId), $e);
        } catch (\Exception $e) {
            $this->logger->error('Error resolving ERP Sales Rep Name', [
                'company_id' => $companyId,
                'error' => $e->getMessage()
            ]);

            throw new LocalizedException(__('Error retrieving ERP Sales Rep Name: %1', $e->getMessage()), $e);
        }
    }

    /**
     * Check if company has ERP Sales Rep Name
     *
     * @param int $companyId
     * @return bool
     */
    public function hasErpSalesRepName(int $companyId): bool
    {
        try {
            $this->getErpSalesRepName($companyId);
            return true;
        } catch (LocalizedException $e) { }

        return false;
    }

    /**
     * @inheritDoc
     */
    public function getErpCreditStatus(int $companyId): string
    {
        try {
            $company = $this->companyRepository->get($companyId);
            $erpSalesRepName = $company->getExtensionAttributes()->getErpCreditStatus();

            return $erpSalesRepName;

        } catch (NoSuchEntityException $e) {
            $this->logger->error('Company not found', [
                'company_id' => $companyId,
                'error' => $e->getMessage()
            ]);
            throw new LocalizedException(__('Company not found with ID: %1', $companyId), $e);
        } catch (\Exception $e) {
            $this->logger->error('Error resolving ERP Credit Status', [
                'company_id' => $companyId,
                'error' => $e->getMessage()
            ]);

            throw new LocalizedException(__('Error retrieving ERP Credit Status: %1', $e->getMessage()), $e);
        }
    }

    /**
     * @inheritDoc
     */
    public function hasErpCreditStatus(int $companyId): bool
    {
        try {
            $this->getErpCreditStatus($companyId);
            return true;
        } catch (LocalizedException $e) { }

        return false;
    }
}
