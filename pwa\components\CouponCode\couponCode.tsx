import { clsx } from 'clsx'
import { isEmpty } from 'lodash-es'
import { useCallback } from 'react'
import { Button, Collapse, Form, Input } from 'antd'

import { MediaLayout } from '@/ui'
import { useCouponCode } from '@/hooks/CouponCode'

import { StyledCouponCode, StyledCouponCollapse } from './styled'

const CouponCode = () => {
  const [form] = Form.useForm()
  const { coupons, loading, handleApply, handleCancel } = useCouponCode(form)

  const expandIcon = useCallback(
    (panelProps) => (
      <svg
        className={clsx('expand-icon', { 'is-active': panelProps.isActive })}
        width="1.1em"
        height="1.1em"
        fill="currentColor"
        aria-hidden="true"
        focusable="false">
        <use xlinkHref="#icon-next" />
      </svg>
    ),
    []
  )

  return (
    <StyledCouponCode>
      <MediaLayout>
        <h3 className="coupon-title">
          Apply Discount Code <span>(optional)</span>
        </h3>
        <Form form={form}>
          <Form.Item name="coupon">
            <Input placeholder="Enter Discount Code" disabled={loading} />
          </Form.Item>
          {isEmpty(coupons) ? (
            <Button type="link" htmlType="submit" onClick={handleApply} loading={loading}>
              APPLY DISCOUNT
            </Button>
          ) : (
            <Button type="link" onClick={handleCancel} loading={loading}>
              CANCEL
            </Button>
          )}
        </Form>
      </MediaLayout>
      <MediaLayout type="mobile">
        <StyledCouponCollapse>
          <Collapse
            ghost
            // defaultActiveKey={['']}
            expandIconPosition="end"
            expandIcon={expandIcon}
            items={[
              {
                key: 'coupon',
                label: (
                  <>
                    Apply Discount Code <span>(optional)</span>
                  </>
                ),
                children: (
                  <Form form={form}>
                    <Form.Item name="coupon">
                      <Input placeholder="Enter Discount Code" />
                    </Form.Item>
                    {isEmpty(coupons) ? (
                      <Button type="link" htmlType="submit" onClick={handleApply} loading={loading}>
                        APPLY
                      </Button>
                    ) : (
                      <Button type="link" onClick={handleCancel} loading={loading}>
                        CANCEL
                      </Button>
                    )}
                  </Form>
                )
              }
            ]}
          />
        </StyledCouponCollapse>
      </MediaLayout>
    </StyledCouponCode>
  )
}

export default CouponCode
