# Ship To Quote GraphQL Module

This module adds `ship_to_id` and `customer_contact_id` fields to quotes and orders, with GraphQL support for setting these fields during the checkout process.

## Features

- Adds `ship_to_id` and `customer_contact_id` fields to both quote and order tables
- Provides GraphQL mutation `setShipToDataOnCart` to set ship_to_id after setting shipping addresses
- Automatically extracts `customer_contact_id` from customer's `erp_contact_id` attribute
- Transfers fields from quote to order during checkout
- Makes fields available via REST API through extension attributes

## Database Schema

The module adds the following fields:

### Quote Table (`quote`)
- `ship_to_id` (varchar 255, nullable) - Ship To ID
- `customer_contact_id` (varchar 255, nullable) - Customer Contact ID

### Order Table (`sales_order`)
- `ship_to_id` (varchar 255, nullable) - Ship To ID  
- `customer_contact_id` (varchar 255, nullable) - Customer Contact ID

## GraphQL Usage

### Setting Ship To Data

```graphql
mutation setShipToDataOnCart($input: SetShipToDataInput!) {
  setShipToDataOnCart(input: $input) {
    cart {
      id
      ship_to_id
      customer_contact_id
    }
  }
}
```

Variables:
```json
{
  "input": {
    "cart_id": "CART_ID_HERE",
    "ship_to_id": "SHIP_TO_ID_HERE"
  }
}
```

### Querying Cart Fields

```graphql
query getCart($cartId: String!) {
  cart(cart_id: $cartId) {
    id
    ship_to_id
    customer_contact_id
    # ... other cart fields
  }
}
```

### Querying Order Fields

```graphql
query getCustomerOrders {
  customer {
    orders {
      items {
        id
        number
        ship_to_id
        customer_contact_id
        # ... other order fields
      }
    }
  }
}
```

## REST API

The fields are available through extension attributes:

### Quote API
- GET `/V1/carts/:cartId` - Returns quote with `ship_to_id` and `customer_contact_id` in extension attributes
- PUT `/V1/carts/:cartId` - Can update extension attributes

### Order API  
- GET `/V1/orders/:orderId` - Returns order with `ship_to_id` and `customer_contact_id` in extension attributes
- GET `/V1/orders` - Search orders, fields available in extension attributes

## Workflow

1. Customer sets shipping address using `setShippingAddressesOnCart`
2. Frontend calls `setShipToDataOnCart` with the selected ship_to_id
3. Module automatically extracts `customer_contact_id` from customer's `erp_contact_id` attribute
4. Both fields are stored on the quote
5. During checkout/order placement, fields are transferred from quote to order
6. Fields are available via GraphQL and REST API on both cart and order

## Installation

1. Place module files in `app/code/Silksoftwarecorp/ShipToQuoteGraphQl/`
2. Run `bin/magento setup:upgrade` to create database fields
3. Run `bin/magento setup:di:compile` to generate extension attributes
4. Clear cache with `bin/magento cache:clean`

## Dependencies

- Magento_Quote
- Magento_Sales  
- Magento_QuoteGraphQl
- Magento_Customer
- Silksoftwarecorp_ShipToGraphQl



