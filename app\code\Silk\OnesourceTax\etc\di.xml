<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Silk\OnesourceTax\Api\TaxCalculatorInterface" type="Silk\OnesourceTax\Model\Tax\Calculator"/>
    
    <type name="Magento\Tax\Model\Sales\Total\Quote\Tax">
        <plugin name="onesource_tax_quote_tax" type="Silk\OnesourceTax\Plugin\Quote\Address"/>
    </type>
    
    <type name="Magento\Checkout\Model\ShippingInformationManagement">
        <plugin name="onesource_tax_shipping_information" type="Silk\OnesourceTax\Plugin\Checkout\ShippingInformationManagement"/>
    </type>
    
    <type name="Silk\OnesourceTax\Model\Api\Client">
        <arguments>
            <argument name="clientFactory" xsi:type="object">Magento\Framework\HTTP\Client\CurlFactory</argument>
        </arguments>
    </type>

 <type name="Silk\OnesourceTax\Model\Logger">
        <arguments>
            <argument name="name" xsi:type="string">OnesourceTax Logger</argument>
            <argument name="handlers" xsi:type="array">
                <item name="onesource_tax" xsi:type="object">OnesourceTaxLoggerHandler</item>
            </argument>
        </arguments>
    </type>
    <virtualType name="OnesourceTaxLoggerHandler" type="Magento\Framework\Logger\Handler\Base">
        <arguments>
            <argument name="fileName" xsi:type="string">/var/log/onesource_tax.log</argument>
        </arguments>
    </virtualType>
</config>