<?php

namespace Silksoftwarecorp\EDI\Model\FrequentlyOrderedProducts;

use Silksoftwarecorp\EDI\Model\Product\OnlyXLeftInStockReader;
use Silksoftwarecorp\EDI\Model\Product\ProductIdsReader;
use Silksoftwarecorp\EDI\Model\Product\StockStatusReader;

class ProductsProcessor
{
    protected array $_items = [];

    /**
     * @var StockStatusReader
     */
    protected $stockStatusReader;

    /**
     * @var OnlyXLeftInStockReader
     */
    protected $onlyXLeftInStockReader;

    /**
     * @var ProductIdsReader
     */
    protected $productIdsReader;

    /**
     * @param StockStatusReader $stockStatusReader
     * @param OnlyXLeftInStockReader $onlyXLeftInStockReader
     */
    public function __construct(
        StockStatusReader $stockStatusReader,
        OnlyXLeftInStockReader $onlyXLeftInStockReader,
        ProductIdsReader $productIdsReader
    ) {
        $this->stockStatusReader = $stockStatusReader;
        $this->onlyXLeftInStockReader = $onlyXLeftInStockReader;
        $this->productIdsReader = $productIdsReader;
    }

    public function execute(array $orders): array
    {
        $itemIds = $this->getItemIdsFromOrders($orders);
        $this->stockStatusReader->execute($itemIds);
        $this->productIdsReader->loadProductIds($itemIds);

        foreach ($orders as $order) {
            $this->processOrder($order);
        }

        return $this->_items;
    }

    protected function processOrder(array $order): void
    {
        $orderLines = $order['OrderLines'] ?? [];
        foreach ($orderLines as $orderLine) {
            if (!$this->isValidOrderLine($orderLine)) {
                continue;
            }

            $this->addItem([
                'sku' => $orderLine['ItemID'],
                'name' => $orderLine['ItemDesc'],
                'qty_ordered' => $orderLine['QtyOrdered'],
                'price' => $orderLine['UnitPrice'],
                'total_qty_ordered' => $orderLine['QtyOrdered'],
                'stock_status' => $this->getItemStockStatus($orderLine['ItemID']),
                'only_x_left_in_stock' => $this->onlyXLeftInStockReader->execute($orderLine['ItemID']),
            ]);
        }
    }

    protected function addItem(array $item): void
    {
        $sku = $item['sku'];
        $qty_ordered = $item['qty_ordered'] ?? 0;
        if ($this->hasItem($item)) {
            $this->_items[$sku]['total_qty_ordered'] += max((int)$qty_ordered, 0);
        } elseif ((int)$qty_ordered > 0) {
            $this->_items[$sku] = $item;
        }
    }

    protected function hasItem(array $item): bool
    {
        return isset($this->_items[$item['sku']]);
    }

    protected function getItemIdsFromOrders(array $orders): array
    {
        $itemIds = [];
        foreach ($orders as $order) {
            $orderLines = $order['OrderLines'] ?? [];
            foreach ($orderLines as $orderLine) {
                $itemIds[] = $orderLine['ItemID'];
            }
        }

        return array_unique($itemIds);
    }

    protected function getItemStockStatus($itemId): string
    {
        $result = $this->stockStatusReader->get($itemId);

        return $result && $result->isSalable() ? 'IN_STOCK' : 'OUT_OF_STOCK';
    }

    private function isValidOrderLine(array $orderLine): bool
    {
        if (!isset($orderLine['ItemID']) || empty($orderLine['ItemID'])) {
            return false;
        }

        if ($this->productIdsReader->getIdBySku($orderLine['ItemID']) === null) {
            return false;
        }

        return true;
    }
}
