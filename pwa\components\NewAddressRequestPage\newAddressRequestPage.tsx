import { Form, Button, notification } from 'antd'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { useRouter } from 'next/compat/router'

import { useMutation } from '@apollo/client'
import Breadcrumb from '@/components/Breadcrumb'
import CommonInput from '@/components/Common/CommonInput'
import CommonSelect from '@/components/Common/CommonSelect'
import CommonCheckbox from '@/components/Common/CommonCheckbox'
import CommonTextarea from '@/components/Common/CommonTextarea'
import CommonLoading from '@/components/Common/CommonLoading'
import { useCountries } from '@/hooks/Countries'
import { useCountryTransform } from '@/hooks/CountryTransform'
import { useValid } from '@/utils'
import { REQUEST_NEW_SHIP_TO } from '@/apis/mutations/requestNewShipTo'

import { StyledNewAddressRequest, StyledFormItem } from './styled'

const NewAddressRequestPage = () => {
  const router = useRouter()
  const [form] = Form.useForm()
  const { validateEmail } = useValid()
  const { countries, regions, handleCountryChange } = useCountries()
  const { getRegionCodeById } = useCountryTransform()
  const [requestNewShipTo] = useMutation(REQUEST_NEW_SHIP_TO)

  const [addressType, setAddressType] = useState<any>('')
  const [allValuesFilled, setAllValuesFilled] = useState<any>(false)
  const [loading, setLoading] = useState<any>(false)

  const regionsOptions = useMemo(() => {
    return regions.map((item) => {
      return {
        value: item?.id ?? '',
        label: item?.name ?? ''
      }
    })
  }, [regions])

  const validateConfirmEmail = useCallback(
    ({ getFieldValue }) => ({
      validator(_, value) {
        const emailValue = getFieldValue('email_address')

        if (!value || value === emailValue) {
          return Promise.resolve()
        }

        return Promise.reject(new Error('Please enter same Email.'))
      }
    }),
    []
  )

  const handleSelectAddressType = useCallback(
    (value) => {
      setAddressType(value)
      form.setFieldsValue({ address_types: [value] })
    },
    [form]
  )

  const onValuesChange = (changedValues, allValues) => {
    const hasAllValues = Object.keys(allValues).every((key) => {
      if (key === 'business_address2') {
        return true
      }
      return !!allValues[key]
    })
    setAllValuesFilled(hasAllValues)
  }

  const handleFormSubmit = useCallback(
    async (values: any) => {
      try {
        const stateId = values?.state ?? ''
        const stateCode = getRegionCodeById('US', stateId)
        const query = { ...values, state: stateCode }
        setLoading(true)
        const { data } = await requestNewShipTo({
          variables: {
            input: query
          }
        })
        if (data?.requestNewShipTo?.success) {
          router.push('/submission-confirmation')
        } else if (data?.requestNewShipTo?.message) {
          notification.warning({
            message: data.requestNewShipTo.message
          })
        }
      } catch (error) {
        console.info(error)
      } finally {
        setLoading(false)
      }
    },
    [getRegionCodeById, requestNewShipTo, router]
  )

  useEffect(() => {
    if (countries.length > 0) {
      handleCountryChange('US')
    }
  }, [countries, handleCountryChange])

  return (
    <StyledNewAddressRequest>
      <Breadcrumb items={[{ name: 'New Ship-To Address Request' }]} />
      <div className="request-container">
        <div className="request-banner">New Ship-To Address Request</div>
        <div className="request-desc">
          Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt
          ut labore et dolore magna aliqua. Ut enim ad minim veniam.
        </div>
        <CommonLoading spinning={loading}>
          <div className="request-form">
            <Form
              form={form}
              layout="vertical"
              initialValues={{
                business_address2: ''
              }}
              onValuesChange={onValuesChange}
              onFinish={handleFormSubmit}>
              <Form.Item name="business_name" label="Business Name" rules={[{ required: true }]}>
                <CommonInput />
              </Form.Item>
              <Form.Item
                name="business_address1"
                label="Business Address 1"
                rules={[{ required: true }]}>
                <CommonInput />
              </Form.Item>
              <Form.Item name="business_address2" label="Business Address 2 (optional)">
                <CommonInput />
              </Form.Item>
              <StyledFormItem>
                <Form.Item name="city" label="City" rules={[{ required: true }]}>
                  <CommonInput />
                </Form.Item>
                {regionsOptions.length > 0 ? (
                  <Form.Item name="state" label="State" rules={[{ required: true }]}>
                    <CommonSelect placeholder="Select" options={regionsOptions} />
                  </Form.Item>
                ) : (
                  <Form.Item name="state" label="State" rules={[{ required: true }]}>
                    <CommonInput />
                  </Form.Item>
                )}
              </StyledFormItem>
              <StyledFormItem>
                <Form.Item name="zip_code" label="Zip Code" rules={[{ required: true }]}>
                  <CommonInput />
                </Form.Item>
                <Form.Item name="phone_number" label="Phone Number" rules={[{ required: true }]}>
                  <CommonInput />
                </Form.Item>
              </StyledFormItem>
              <StyledFormItem>
                <Form.Item
                  name="email_address"
                  label="Email Address"
                  rules={[{ required: true }, validateEmail]}>
                  <CommonInput />
                </Form.Item>
                <Form.Item
                  name="confirm_email_address"
                  label="Confirm Email Address"
                  rules={[{ required: true }, validateConfirmEmail]}>
                  <CommonInput />
                </Form.Item>
              </StyledFormItem>

              <Form.Item
                name="address_types"
                label="What type of address is this?"
                rules={[{ required: true }]}>
                <div className="type-item-group">
                  <div>
                    <CommonCheckbox
                      checked={addressType === 'Commercial'}
                      onChange={() => {
                        handleSelectAddressType('Commercial')
                      }}
                    />
                    Commercial
                  </div>
                  <div>
                    <CommonCheckbox
                      checked={addressType === 'Residential'}
                      onChange={() => {
                        handleSelectAddressType('Residential')
                      }}
                    />
                    Residential
                  </div>
                </div>
              </Form.Item>
              <Form.Item
                name="delivery_instructions"
                label="Delivery Instructions (optional)"
                rules={[{ required: true }]}>
                <CommonTextarea rows={4} />
              </Form.Item>
              <Button
                htmlType="submit"
                className="submit-btn"
                loading={loading}
                disabled={!allValuesFilled || !addressType}>
                SUBMIT
              </Button>
            </Form>
          </div>
        </CommonLoading>
      </div>
    </StyledNewAddressRequest>
  )
}

export default NewAddressRequestPage
