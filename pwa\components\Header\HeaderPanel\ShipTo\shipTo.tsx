import { memo } from 'react'
import { Input } from 'antd'

import { useShipTo } from '@/hooks/ShipTo'
import { Modal, MediaLayout } from '@/ui'
import CommonButton from '@/components/Common/CommonButton'
import CommonTable from '@/components/Common/CommonTable/commonTable'
import CommonLoading from '@/components/Common/CommonLoading'
import ProductAvailabilityModal from '@/components/Common/ProductAvailabilityModal'
import { useCountryTransform } from '@/hooks/CountryTransform'

import ShipToForm from './ShipToForm'
import { StyledShipTo, StyledShipToAddress, StyledShipToTableRow } from './styled'

const ShipTo = () => {
  const {
    showShipToModal,
    modalRef,
    onSearchValueChange,
    searchValue,
    shipToCode,
    navigateToRequestPage,
    requestModalRef,
    handleSelectAddress,
    curShipToEntityId,
    totalText,
    addressesShow,
    shipToLoading,
    availabilityModalRef,
    closeAvailabilityModal,
    unavailabilityData,
    handleProceed,
    userLoading,
    handleAddressSelect,
    isMobile
  } = useShipTo()
  const { getRegionNameByCode } = useCountryTransform()

  const columns = [
    {
      title: 'Name',
      dataIndex: 'ship_to_name',
      key: 'ship_to_name',
      width: 140,
      render: (param, record) => (
        <StyledShipToTableRow
          onClick={() => {
            handleAddressSelect(record)
          }}>
          {param}
        </StyledShipToTableRow>
      )
    },
    {
      title: 'Street',
      dataIndex: 'street',
      key: 'street',
      render: (param, record) => (
        <StyledShipToTableRow
          onClick={() => {
            handleAddressSelect(record)
          }}>{`${record.street} ${record?.street_line2 ?? ''} ${record?.street_line3 ?? ''}`}</StyledShipToTableRow>
      )
    },
    {
      title: 'City',
      dataIndex: 'city',
      key: 'city',
      width: 140,
      render: (param, record) => (
        <StyledShipToTableRow
          onClick={() => {
            handleAddressSelect(record)
          }}>
          {param}
        </StyledShipToTableRow>
      )
    },
    {
      title: 'State',
      dataIndex: 'region_code',
      key: 'region_code',
      width: 140,
      render: (param, record) => {
        return (
          <StyledShipToTableRow
            onClick={() => {
              handleAddressSelect(record)
            }}>
            {getRegionNameByCode(record?.country_id, record?.region_code)}
          </StyledShipToTableRow>
        )
      }
    },
    {
      title: 'Zip Code',
      dataIndex: 'postcode',
      key: 'postcode',
      width: 140,
      render: (param, record) => (
        <StyledShipToTableRow
          onClick={() => {
            handleAddressSelect(record)
          }}>
          {param}
        </StyledShipToTableRow>
      )
    },
    {
      title: 'Action',
      dataIndex: 'postcode',
      key: 'postcode',
      align: 'center',
      width: 130,
      mobileContentFull: true,
      render: (param, record) => {
        const isSelected = String(record?.entity_id) === curShipToEntityId
        const onClickQuery = isMobile
          ? {}
          : {
              onClick: () => {
                handleAddressSelect(record)
              }
            }
        return (
          <StyledShipToTableRow className="row-action" {...onClickQuery}>
            <CommonButton
              className="select-btn"
              type="text"
              uppercase={false}
              underline
              onClick={() => {
                if (isMobile && !isSelected && record?.entity_id) {
                  handleSelectAddress(record)
                }
              }}>
              {isSelected && (
                <svg
                  width="13px"
                  height="10px"
                  fill="currentColor"
                  aria-hidden="true"
                  focusable="false">
                  <use xlinkHref="#icon-checkbox-check" />
                </svg>
              )}
              {isSelected ? 'Selected' : 'Select'}
            </CommonButton>
          </StyledShipToTableRow>
        )
      }
    }
  ]

  return (
    <StyledShipTo>
      {userLoading && <CommonLoading />}
      {shipToCode && (
        <>
          <svg
            className="bag"
            width="18px"
            height="24px"
            fill="currentColor"
            aria-hidden="true"
            focusable="false">
            <use xlinkHref="#icon-map" />
          </svg>
          <span className="ship-to-text">Ship to {shipToCode}</span>
          <span className="ship-edit" aria-hidden onClick={showShipToModal}>
            edit
          </span>
        </>
      )}
      <Modal ref={modalRef} width={980} maskClosable={false} closable={!!shipToCode}>
        <StyledShipToAddress id="ship-to-address">
          <CommonLoading spinning={shipToLoading}>
            <div className="address-header">
              <h2>Ship To Address</h2>
              <MediaLayout type="mobile">
                <div className="addresses-available">{totalText}</div>
              </MediaLayout>
              <CommonButton height={44} onClick={navigateToRequestPage}>
                REQUEST NEW SHIP TO ADDRESS
              </CommonButton>
            </div>
            <div className="address-search">
              <Input
                value={searchValue}
                placeholder="Search Addresses"
                onChange={onSearchValueChange}
                suffix={
                  <span aria-hidden className="search-icon">
                    <svg
                      width="19px"
                      height="17px"
                      fill="currentColor"
                      aria-hidden="true"
                      focusable="false">
                      <use xlinkHref="#icon-search" />
                    </svg>
                  </span>
                }
              />
              <MediaLayout>
                <div className="addresses-available">{totalText}</div>
              </MediaLayout>
            </div>

            <CommonTable
              lineStyle
              rowKey={(params) => params?.entity_id}
              columns={columns}
              dataSource={addressesShow}
              scroll={addressesShow.length > 5 ? { y: 365 } : {}}
              rowClassName={(params) => {
                return String(params?.entity_id) === curShipToEntityId ? 'is-selected' : ''
              }}
            />

            {/* TODO */}
            {/* <div className="view-all">VIEW ALL</div> */}
          </CommonLoading>
        </StyledShipToAddress>
      </Modal>

      {/* Modal: REQUEST NEW SHIP TO ADDRESS */}
      <Modal ref={requestModalRef} width={800} maskClosable={false}>
        <ShipToForm />
      </Modal>

      {/* Modal: Products availability */}
      <Modal ref={availabilityModalRef} width={626} maskClosable={false}>
        <ProductAvailabilityModal
          items={unavailabilityData.items}
          onCancel={closeAvailabilityModal}
          onProceed={handleProceed}
        />
      </Modal>
    </StyledShipTo>
  )
}

export default memo(ShipTo)
