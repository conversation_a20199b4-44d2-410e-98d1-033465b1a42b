<?php

namespace Silksoftwarecorp\RealTimeB2BPricing\Model\API;

use Silksoftwarecorp\RealTimeB2BPricing\Logger\LoggerInterface;

class RequestLog
{
    private $request;

    private $response;

    private $errors = [];

    private float $startTime = 0;

    private float $endTime = 0;

    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * @param LoggerInterface $logger
     */
    public function __construct(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }

    public function setRequest($message): static
    {
        $this->request = $message;

        return $this;
    }

    public function setResponse($response): static
    {
        $this->response = $response;

        return $this;
    }

    public function addError($error): static
    {
        $this->errors[] = $error;

        return $this;
    }

    public function setStartTime(): static
    {
        $this->startTime = microtime(true);

        return $this;
    }

    public function setEndTime(): static
    {
        $this->endTime = microtime(true);

        return $this;
    }

    private function getRequestTime(): string
    {
        return ($this->endTime - $this->startTime) . 's';
    }

    public function toString(): string
    {
        $result = [
            'request' => $this->request,
            'response' => $this->response,
            'request_time' => $this->getRequestTime(),
        ];

        if (!empty($this->errors)) {
            $result['errors'] = $this->errors;
        }

        return json_encode($result, JSON_PRETTY_PRINT);
    }

    public function recordErrorMessages(): void
    {
        foreach ($this->errors as $error) {
            $this->logger->critical($error);
        }

        $this->errors = [];
    }

    public function record(): void
    {
        $this->logger->debug($this->toString());
        $this->recordErrorMessages();

        $this->request = null;
        $this->response = null;
        $this->errors = [];
        $this->startTime = 0;
        $this->endTime = 0;
    }
}
