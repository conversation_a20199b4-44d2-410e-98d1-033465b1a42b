import { clsx } from 'clsx'
import { isEmpty } from 'lodash-es'

import { StyledMobileTable, StyledMobileTableItem } from './styled'

const MobileTable = ({ columns, dataSource, pagination, footer, rowKey, rowClassName }) => {
  // const dataGroup = []
  // if (pagination) {
  //   for (let i = 0; i < dataSource.length; ) {
  //     if (!isEmpty(dataSource[i])) {
  //       dataGroup.push(dataSource.slice(i, (i += pagination.pageSize)))
  //     }
  //   }
  // }
  // const getIndex = () => {
  //   return dataSource.length <= pagination.pageSize ? 0 : pagination.current - 1
  // }
  // const currentGroup = pagination ? dataGroup[getIndex()] : dataSource
  const currentGroup = dataSource

  return (
    <StyledMobileTable>
      {isEmpty(currentGroup) ? (
        <div className="mb-table-empty">No data</div>
      ) : (
        <>
          {currentGroup.map((dataItem) => {
            return (
              <StyledMobileTableItem
                key={rowKey(dataItem)}
                className={`mobile-table-item ${rowClassName?.(dataItem) || ''}`}>
                {columns.map((column) => {
                  const data = dataItem[column.dataIndex]
                  return (
                    <div
                      key={column.key}
                      className={clsx('data-item', {
                        'mobile-content-full': column.mobileContentFull
                      })}>
                      {column.dataIndex !== '' && (
                        <span className="data-item-title">{column.title}</span>
                      )}
                      {column.render ? (
                        column.render(data, dataItem)
                      ) : (
                        <span className={`${column?.className ?? ''}`}>{data}</span>
                      )}
                    </div>
                  )
                })}
              </StyledMobileTableItem>
            )
          })}
        </>
      )}
    </StyledMobileTable>
  )
}

export default MobileTable
