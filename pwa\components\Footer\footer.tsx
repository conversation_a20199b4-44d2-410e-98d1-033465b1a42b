import { clsx } from 'clsx'
import { useCallback } from 'react'
import { useSelector } from 'react-redux'
import { useRouter } from 'next/compat/router'
import { IntersectionScroll } from '@ranger-theme/ui'
import { events } from '@ranger-theme/utils'

import CmsBlock from '@/components/CmsBlock'

import { StyledFooter, StyledNewsletterBlock, StyledLinks } from './styled'

const Footer = () => {
  const router = useRouter()
  const isInvoiceHistoryPage = router?.route === '/account/invoice-history'
  const storeConfig = useSelector((state: Store) => state.app.storeConfig)
  const isLogin = useSelector((state: Store) => state.user.isLogin)
  const { copyright } = storeConfig

  const newsletterBlockClick = useCallback((e) => {
    const innerHTML = e?.target?.innerHTML || ''
    if (
      innerHTML === 'SIGN UP' ||
      (e?.target?.localName === 'button' && innerHTML.indexOf('SIGN UP') > -1)
    ) {
      // Open Sign In modal
      events.emit('triggerSignIn')
    }
  }, [])

  // Mobile Footer link list collapse
  const footerLinksClick = useCallback((e) => {
    if (e?.target?.nodeName === 'H3') {
      e?.target?.classList?.toggle('active')
    }
  }, [])

  return (
    <StyledFooter className={clsx({ 'is-invoice-history-page': isInvoiceHistoryPage })}>
      <IntersectionScroll>
        {!isLogin && (
          <StyledNewsletterBlock onClick={newsletterBlockClick}>
            <CmsBlock identifiers={['global_newsletter_block']} />
          </StyledNewsletterBlock>
        )}
        <StyledLinks onClick={footerLinksClick} className={clsx({ 'is-login': isLogin })}>
          <CmsBlock identifiers={['global_footer_links']} />
        </StyledLinks>
      </IntersectionScroll>
      <div className="copyright">
        <p dangerouslySetInnerHTML={{ __html: copyright }} />
      </div>
    </StyledFooter>
  )
}

export default Footer
