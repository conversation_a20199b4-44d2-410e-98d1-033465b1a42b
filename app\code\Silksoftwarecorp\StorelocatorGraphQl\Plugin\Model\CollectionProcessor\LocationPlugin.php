<?php

namespace Silksoftwarecorp\StorelocatorGraphQl\Plugin\Model\CollectionProcessor;

use Amasty\Storelocator\Model\ResourceModel\Location\Collection;
use Amasty\StoreLocatorGraphql\Model\CollectionProcessor\Location as Subject;
use Magento\Framework\DB\Helper\Mysql\Fulltext;

/**
 * Class LocationPlugin
 */
class LocationPlugin
{
    /**
     * @var Fulltext
     */
    private Fulltext $fulltextHelper;

    /**
     * @param Fulltext $fulltextHelper
     */
    public function __construct(Fulltext $fulltextHelper)
    {
        $this->fulltextHelper = $fulltextHelper;
    }

    public function aroundProcess(
        Subject $subject,
        \Closure $proceed,
        Collection $collection,
        array $dataToFilter
    ): void {
        $proceed($collection, $dataToFilter);

        if (isset($dataToFilter['state']) && $dataToFilter['state']) {
            if (is_numeric($dataToFilter['state'])) {
                $collection->addFieldToFilter('main_table.state', $dataToFilter['state']);
            } else {
                $collection->addFieldToFilter('main_table.state', ['like' => '%' . $dataToFilter['state'] . '%']);
            }
        }

        if (isset($dataToFilter['country']) && $dataToFilter['country']) {
            $collection->addFieldToFilter('main_table.country', $dataToFilter['country']);
        }

        if (isset($dataToFilter['zip_code']) && $dataToFilter['zip_code']) {
            $collection->addFieldToFilter('main_table.zip', ['like' => '%' . $dataToFilter['zip_code'] . '%']);
        }

        if (isset($dataToFilter['search']) && $dataToFilter['search']) {
            $this->addFullTextFilter($collection, $dataToFilter['search']);
        }
    }

    private function addFullTextFilter(Collection $collection, string $searchQuery): void
    {
        if ($searchQuery) {
            $conditions = [];

            $fields = ['city', 'zip'];

            $searchTerms = explode(',', $searchQuery);
            if (count($searchTerms) > 1) {
                $searchTerm = array_shift($searchTerms);
            } else {
                $searchTerm = $searchQuery;
            }

            $term = trim($searchTerm);
            if (!empty($term)) {
                foreach ($fields as $field) {
                    $field = 'main_table.' . $field;
                    $conditions[] = $collection->getConnection()->prepareSqlCondition(
                        $collection->getConnection()->quoteIdentifier($field),
                        ['like' => "%$term%"]
                    );
                }
            }

            if (!empty($conditions)) {
                $whereClause = '(' . implode(' OR ', $conditions) . ')';
                $collection->getSelect()->where($whereClause);
            }
        }
    }

    public function getFulltextRawQueryCondition(Collection $collection, string $searchQuery): string
    {
        $columns = array_map(function($column) {
            return 'main_table.' . $column;
        }, $this->getFulltextIndexColumns($collection));

        return $this->fulltextHelper->getMatchQuery(
            $columns,
            $searchQuery,
            Fulltext::FULLTEXT_MODE_BOOLEAN
        );
    }

    private function getFulltextIndexColumns(Collection $collection): array
    {
        $indexes = $collection->getConnection()->getIndexList($collection->getMainTable());
        foreach ($indexes as $index) {
            if (strtoupper($index['INDEX_TYPE']) == 'FULLTEXT') {
                return $index['COLUMNS_LIST'];
            }
        }

        return [];
    }
}
