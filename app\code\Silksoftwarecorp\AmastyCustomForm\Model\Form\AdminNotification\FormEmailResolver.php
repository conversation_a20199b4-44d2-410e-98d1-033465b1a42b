<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */

namespace Silksoftwarecorp\AmastyCustomForm\Model\Form\AdminNotification;

use Laminas\Validator\EmailAddress as EmailAddressValidator;
use Magento\Framework\Serialize\Serializer\Json;
use Psr\Log\LoggerInterface;
use Silksoftwarecorp\AmastyCustomForm\Helper\Data as Helper;

class FormEmailResolver
{
    /**
     * @var Helper
     */
    private $helper;

    /**
     * @var Json
     */
    private $serializer;

    /**
     * @var EmailAddressValidator
     */
    private $emailAddressValidator;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var array|null
     */
    private $configCache;

    /**
     * @param Helper $helper
     * @param Json $serializer
     * @param EmailAddressValidator $emailAddressValidator
     * @param LoggerInterface $logger
     */
    public function __construct(
        Helper $helper,
        <PERSON>son $serializer,
        EmailAddressValidator $emailAddressValidator,
        LoggerInterface $logger
    ) {
        $this->helper = $helper;
        $this->serializer = $serializer;
        $this->emailAddressValidator = $emailAddressValidator;
        $this->logger = $logger;
    }

    /**
     * Get matched email recipient for given branch and form
     *
     * @param string $branchCode
     * @param string $formId
     * @return string|null
     */
    public function resolve(string $branchCode, string $formId): ?string
    {
        if (!$this->helper->enableFormCustomEmailRecipient()) {
            return null;
        }

        if (empty($branchCode) || empty($formId)) {
            $this->logger->debug('[Forms: Email Recipient]: Branch code or Form ID is empty', [
                'branch_code' => $branchCode,
                'form_id' => $formId
            ]);

            return null;
        }

        $matchedEmail = $this->findMatchingConfiguration($branchCode, $formId);
        if ($matchedEmail) {
            $this->logger->info('[Forms: Email Recipient]: Found matching email recipient', [
                'branch_code' => $branchCode,
                'form_id' => $formId,
                'matched_email' => $matchedEmail
            ]);
        } else {
            $this->logger->debug('[Forms: Email Recipient]: No matching configuration found', [
                'branch_code' => $branchCode,
                'form_id' => $formId
            ]);
        }

        return $matchedEmail;
    }

    /**
     * Get email configuration from system config
     *
     * @return array
     */
    private function getEmailConfiguration(): array
    {
        if ($this->configCache !== null) {
            return $this->configCache;
        }

        try {
            $configValue = $this->helper->getFormCustomEmailRecipients();
            if (!$configValue) {
                return [];
            }

            // Parse and sanitize configuration
            $config = $this->serializer->unserialize($configValue);
            if (!is_array($config)) {
                $this->logger->warning('[Forms: Email Recipient]: Configuration is not an array');

                return [];
            }

            // Parse configuration into indexed array for faster lookup
            $customEmailRecipients = [];
            foreach ($config as $row) {
                if (!is_array($row)) {
                    continue;
                }

                $branch = trim($row['branch_code'] ?? '');
                $form = trim($row['form_id'] ?? '');
                $emailRecipients = trim($row['emails'] ?? '');

                // Skip invalid configurations
                if (empty($branch) || empty($form) || empty($emailRecipients)) {
                    continue;
                }

                // Validate email recipients (supports multiple emails separated by comma)
                $emails = array_map('trim', explode(',', $emailRecipients));
                $validEmails = array_filter($emails, function($email) {
                    return $this->emailAddressValidator->isValid($email);
                });

                $invalidEmails = array_diff($emails, $validEmails);
                if (!empty($invalidEmails)) {
                    $this->logger->warning('[Forms: Email Recipient]: Invalid email format in configuration', [
                        'branch' => $branch,
                        'form' => $form,
                        'invalid_emails' => implode(', ', $invalidEmails),
                        'all_emails' => $emailRecipients
                    ]);
                }

                if ($validEmails) {
                    // Support multi-branch configuration in a single row (comma-separated)
                    $branchCodes = array_map('trim', explode(',', $branch));
                    foreach ($branchCodes as $branchCode) {
                        if ((string)$branchCode === '') {
                            continue;
                        }
                        // Later entries will overwrite earlier ones if duplicate
                        $customEmailRecipients[$branchCode][$form] = implode(',', $validEmails);
                    }
                }
            }

            $this->configCache = $customEmailRecipients;
        } catch (\Exception $e) {
            $this->configCache = [];

            $this->logger->error('[Forms: Email Recipient]: Failed to get configuration', [
                'error' => $e->getMessage()
            ]);
        }

        return $this->configCache;
    }

    /**
     * Find matching configuration for branch and form
     *
     * @param string $branchCode
     * @param string $formId
     * @return string|null
     */
    private function findMatchingConfiguration(string $branchCode, string $formId): ?string
    {
        $emailConfig = $this->getEmailConfiguration();
        if (empty($emailConfig)) {
            $this->logger->debug('[Forms: Email Recipient]: No email configuration available');
            return null;
        }

        return $emailConfig[$branchCode][$formId] ?? null;
    }

    /**
     * Clear configuration cache (useful for testing)
     *
     * @return void
     */
    public function clearConfigCache(): void
    {
        $this->configCache = null;
    }
}
