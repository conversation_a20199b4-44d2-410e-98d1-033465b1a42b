<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */

namespace Silksoftwarecorp\AmastyCustomForm\Model\Customer\DataProvider;

use Magento\Company\Api\CompanyManagementInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Psr\Log\LoggerInterface;
use Silksoftwarecorp\ERPCompany\Model\Company\ErpResolverInterface;

class SalesRepDataProvider
{
    /**
     * @var CustomerRepositoryInterface
     */
    private $customerRepository;

    /**
     * @var CompanyManagementInterface
     */
    private $companyManagement;

    /**
     * @var ErpResolverInterface
     */
    private $erpResolver;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @param CustomerRepositoryInterface $customerRepository
     * @param CompanyManagementInterface $companyManagement
     * @param ErpResolverInterface $erpResolver
     * @param LoggerInterface $logger
     */
    public function __construct(
        CustomerRepositoryInterface $customerRepository,
        CompanyManagementInterface $companyManagement,
        ErpResolverInterface $erpResolver,
        LoggerInterface $logger
    ) {
        $this->customerRepository = $customerRepository;
        $this->companyManagement = $companyManagement;
        $this->erpResolver = $erpResolver;
        $this->logger = $logger;
    }

    /**
     * Get sales representative information by customer ID
     *
     * @param int $customerId
     * @return array
     */
    public function getSalesRepByCustomerId(int $customerId): array
    {
        try {
            // Step 1: Check if customer is a company user
            if (!$this->isCompanyUser($customerId)) {
                return [];
            }

            // Step 2: Get company by customer ID
            $company = $this->companyManagement->getByCustomerId($customerId);
            if (!$company || !$company->getId()) {
                return [];
            }

            // Step 3: Get sales rep information using company ID
            return $this->getSalesRepByCompanyId((int)$company->getId());
        } catch (\Exception $e) {
            $this->logger->error('[SalesRepDataProvider]: Error getting sales rep information', [
                'customer_id' => $customerId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }

    /**
     * Get sales representative information by company ID
     *
     * @param int $companyId
     * @return array
     */
    public function getSalesRepByCompanyId(int $companyId): array
    {
        try {
            $salesRepData = [];

            // Get sales rep ID
            if ($this->erpResolver->hasErpSalesRepId($companyId)) {
                $salesRepData['id'] = $this->erpResolver->getErpSalesRepId($companyId);
            }

            // Get sales rep name
            if ($this->erpResolver->hasErpSalesRepName($companyId)) {
                $salesRepData['name'] = $this->erpResolver->getErpSalesRepName($companyId);
            }

            return $salesRepData;
        } catch (\Exception $e) {
            $this->logger->critical('Error getting sales rep by company ID', [
                'company_id' => $companyId,
                'error' => $e->getMessage()
            ]);
        }

        return [];
    }

    /**
     * Check if customer is a company user
     *
     * @param int $customerId
     * @return bool
     */
    private function isCompanyUser(int $customerId): bool
    {
        try {
            $customer = $this->customerRepository->getById($customerId);
            $extensionAttributes = $customer->getExtensionAttributes();

            // Check if customer has company attributes
            if ($extensionAttributes && method_exists($extensionAttributes, 'getCompanyAttributes')) {
                $companyAttributes = $extensionAttributes->getCompanyAttributes();
                return $companyAttributes && $companyAttributes->getCompanyId();
            }

            // Alternative check: try to get company directly
            $company = $this->companyManagement->getByCustomerId($customerId);

            return $company && $company->getId();
        } catch (\Exception $e) {
            $this->logger->critical(__('Error getting sales rep information. Message: %1', $e->getMessage()));
        }

        return false;
    }
}
