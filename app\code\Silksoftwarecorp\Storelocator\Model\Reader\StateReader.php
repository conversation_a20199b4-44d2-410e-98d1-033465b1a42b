<?php

namespace Silksoftwarecorp\Storelocator\Model\Reader;

use Magento\Directory\Model\Region;
use Magento\Directory\Model\ResourceModel\Region\CollectionFactory;

class StateReader
{
    /**
     * @var CollectionFactory
     */
    private $collectionFactory;

    /**
     * @var array
     */
    private array $stateNameByCode = [];

    private array $stateNameById = [];

    /**
     * @param CollectionFactory $collectionFactory
     */
    public function __construct(CollectionFactory $collectionFactory)
    {
        $this->collectionFactory = $collectionFactory;

        $this->initialize();
    }

    public function getName($id)
    {
        if (!$id && !is_numeric($id)) {
           return '';
        }

        return $this->stateNameById[$id] ?? '';
    }

    public function getNameByCode(string $countryCode, string $code)
    {
        return $this->stateNameByCode[$countryCode][$code] ?? '';
    }

    protected function initialize(): void
    {
        $collection = $this->collectionFactory->create();
        /**@var $region Region*/
        foreach ($collection->getItems() as $region) {
            $this->stateNameById[$region->getId()] = $region->getName();
            $this->stateNameByCode[$region->getCountryId()][$region->getCode()] = $region->getName();
        }
    }
}
