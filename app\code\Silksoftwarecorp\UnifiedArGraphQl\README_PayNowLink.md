# Pay Now Link GraphQL Query

## Overview

The `payNowLink` GraphQL query allows frontend applications to generate payment links for specific invoices. This query calls the UnifiedAR API to create a PayNow session and returns a payment URL.

## GraphQL Query

```graphql
query GetPayNowLink($companyId: String!, $invoiceNumbers: [String]!) {
  payNowLink(company_id: $companyId, invoice_numbers: $invoiceNumbers) {
    pay_now_url
    success
  }
}
```

## Parameters

- `company_id` (String, required): The encoded company ID
- `invoice_numbers` ([String], required): Array of invoice numbers to include in the payment session

## Response

```json
{
  "data": {
    "payNowLink": {
      "pay_now_url": "https://paynow-dev-eus.gounified-nonprod.com/qpay/blevinsinc?sc=6QWQ9WMC0A4IW2GB5407",
      "success": true
    }
  }
}
```

## Configuration

The following configuration values are required in the admin panel under **Stores > Configuration > Silk Software Corp > Unified A/R > Pay Now(Invoice) Setting**:

- **Pay Now Key**: The API key for pay now functionality
- **Pay Now Link**: The base URL for pay now links (e.g., `https://paynow-dev-eus.gounified-nonprod.com/qpay/blevinsinc?sc=`)
- **Completed Redirect Url**: The URL to redirect to after successful payment

## API Flow

1. The resolver validates the customer is authenticated and has access to the company
2. It retrieves the ERP Customer ID for the company
3. It determines the environment (sandbox/production) and uses appropriate configuration
4. It calls the UnifiedAR API to create a PayNow session with the specified invoices
5. If successful, it constructs the payment URL using the session ID
6. Returns the payment URL and success status

## Error Handling

- **Authentication Error**: If the customer is not logged in
- **Authorization Error**: If the customer doesn't have access to the company
- **Input Error**: If required parameters are missing or invalid
- **API Error**: If the UnifiedAR API call fails

## Example Usage

```javascript
// Frontend JavaScript example
const getPayNowLink = async (companyId, invoiceNumbers) => {
  const query = `
    query GetPayNowLink($companyId: String!, $invoiceNumbers: [String]!) {
      payNowLink(company_id: $companyId, invoice_numbers: $invoiceNumbers) {
        pay_now_url
        success
      }
    }
  `;

  const response = await fetch('/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      query,
      variables: {
        companyId,
        invoiceNumbers
      }
    })
  });

  const result = await response.json();
  return result.data.payNowLink;
};

// Usage
const payNowLink = await getPayNowLink('encoded_company_id', ['53255996', '53255367']);
if (payNowLink.success) {
  window.location.href = payNowLink.pay_now_url;
}
```

## Security

- Only authenticated customers can access this query
- Customers can only generate payment links for companies they have access to
- Invoice numbers are validated against the customer's company
- All API calls are logged for audit purposes

## Dependencies

- `Silksoftwarecorp\UnifiedAR\Service\PayNowService`: Handles the API calls and URL generation
- `Silksoftwarecorp\ERPCompany\Model\Company\ErpResolverInterface`: Retrieves ERP Customer ID
- `Silksoftwarecorp\UnifiedAR\Helper\Data`: Provides configuration values
- `Silksoftwarecorp\UnifiedAR\Http\ClientInterfaceFactory`: Creates HTTP client instances
- `Silksoftwarecorp\UnifiedAR\Model\API\Config`: Provides API configuration
