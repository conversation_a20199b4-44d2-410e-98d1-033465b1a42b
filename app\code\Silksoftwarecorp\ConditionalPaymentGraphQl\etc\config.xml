<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Store:etc/config.xsd">
    <default>
        <payment>
            <cashondelivery>
                <active>1</active>
                <model>Silksoftwarecorp\ConditionalPaymentGraphQl\Model\Payment\CashOnDelivery</model>
            </cashondelivery>
            <checkmo>
                <active>1</active>
                <model>Silksoftwarecorp\ConditionalPaymentGraphQl\Model\Payment\CheckMoneyOrder</model>
            </checkmo>
        </payment>
    </default>
</config>
