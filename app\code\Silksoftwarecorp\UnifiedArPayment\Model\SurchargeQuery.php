<?php

namespace Silksoftwarecorp\UnifiedArPayment\Model;

use Magento\Framework\HTTP\Client\Curl;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Directory\Model\RegionFactory;
use Magento\Store\Model\ScopeInterface;
use Silksoftwarecorp\UnifiedArPayment\Helper\Config;
use Silksoftwarecorp\UnifiedArPayment\Helper\Logger;

class SurchargeQuery
{
    /**
     * @var Curl
     */
    private $curl;

    /**
     * @var Config
     */
    private $configHelper;

    /**
     * @var Logger
     */
    private $logger;

    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * @var RegionFactory
     */
    private $regionFactory;

    /**
     * @param Curl $curl
     * @param Config $configHelper
     * @param Logger $logger
     * @param ScopeConfigInterface $scopeConfig
     * @param RegionFactory $regionFactory
     */
    public function __construct(
        Curl $curl,
        Config $configHelper,
        Logger $logger,
        ScopeConfigInterface $scopeConfig,
        RegionFactory $regionFactory
    ) {
        $this->curl = $curl;
        $this->configHelper = $configHelper;
        $this->logger = $logger;
        $this->scopeConfig = $scopeConfig;
        $this->regionFactory = $regionFactory;
    }

    /**
     * Query surcharge
     *
     * @param string $paymentAccountId
     * @param string $billingState
     * @param float $amount
     * @param string $country
     * @param int|null $storeId
     * @return array
     */
    public function querySurcharge($paymentAccountId, $billingState, $amount, $country, $storeId = null)
    {
        try {
            // Get origin state from Magento shipping configuration
            $originState = $this->getOriginState($storeId);
            
            if (empty($originState)) {
                return [
                    'success' => false,
                    'message' => 'Origin state not configured in shipping settings',
                    'surcharge_allowed' => false,
                    'surcharge_percent' => 0,
                    'error_code' => 'ORIGIN_STATE_NOT_CONFIGURED'
                ];
            }

            // Get configuration
            $envConfig = $this->configHelper->getEnvironmentConfig();

            // Build XML request
            $xml = $this->buildXmlRequest($envConfig, $paymentAccountId, $originState, $billingState, $amount, $country);

            // Make API call
            $response = $this->makeApiCall($envConfig['surcharge_query_endpoint'], $xml, 'SurchargeQuery');

            return $response;

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'surcharge_allowed' => false,
                'surcharge_percent' => 0,
                'error_code' => 'EXCEPTION'
            ];
        }
    }

    /**
     * Get origin state from Magento shipping configuration
     *
     * @param int|null $storeId
     * @return string
     */
    private function getOriginState($storeId = null)
    {
        try {
            // Get region_id from shipping origin configuration
            $regionId = $this->scopeConfig->getValue(
                \Magento\Shipping\Model\Config::XML_PATH_ORIGIN_REGION_ID,
                ScopeInterface::SCOPE_STORE,
                $storeId
            );

            if (empty($regionId)) {
                return '';
            }

            // Convert region_id to state code
            $region = $this->regionFactory->create()->load($regionId);
            
            if ($region->getId()) {
                return $region->getCode();
            }

            return '';
        } catch (\Exception $e) {
            $this->logger->logError('Error getting origin state: ' . $e->getMessage(), [], 'SurchargeQuery');
            return '';
        }
    }

    /**
     * Build XML request
     *
     * @param array $envConfig
     * @param string $paymentAccountId
     * @param string $originState
     * @param string $billingState
     * @param float $amount
     * @param string $country
     * @return string
     */
    private function buildXmlRequest($envConfig, $paymentAccountId, $originState, $billingState, $amount, $country)
    {
        $xml = '<CreditCardSurchargeCheck xmlns="https://services.elementexpress.com">';
        $xml .= '<Credentials>';
        $xml .= '<AccountID>' . htmlspecialchars($envConfig['account_id']) . '</AccountID>';
        $xml .= '<AccountToken>' . htmlspecialchars($envConfig['account_token']) . '</AccountToken>';
        $xml .= '<AcceptorID>' . htmlspecialchars($envConfig['acceptor_id']) . '</AcceptorID>';
        $xml .= '</Credentials>';
        $xml .= '<Application>';
        $xml .= '<ApplicationID>' . htmlspecialchars($envConfig['application_id']) . '</ApplicationID>';
        $xml .= '<ApplicationName>' . htmlspecialchars($envConfig['application_name']) . '</ApplicationName>';
        $xml .= '<ApplicationVersion>' . htmlspecialchars($envConfig['application_version']) . '</ApplicationVersion>';
        $xml .= '</Application>';
        $xml .= '<PaymentAccount>';
        $xml .= '<PaymentAccountID>' . htmlspecialchars($paymentAccountId) . '</PaymentAccountID>';
        $xml .= '</PaymentAccount>';
        $xml .= '<OriginState>' . htmlspecialchars($originState) . '</OriginState>';
        $xml .= '<BillingState>' . htmlspecialchars($billingState) . '</BillingState>';
        $xml .= '<Amount>' . intval(round($amount * 100)) . '</Amount>';
        $xml .= '<Country>' . htmlspecialchars($country) . '</Country>';
        $xml .= '</CreditCardSurchargeCheck>';
        return $xml;
    }

    /**
     * Make API call
     *
     * @param string $endpoint
     * @param string $xml
     * @param string $operation
     * @return array
     */
    private function makeApiCall($endpoint, $xml, $operation = 'API Call')
    {
        // Log request
        $this->logger->logRequest($endpoint, $xml, $operation);

        $this->curl->setOption(CURLOPT_URL, $endpoint);
        $this->curl->setOption(CURLOPT_POST, true);
        $this->curl->setOption(CURLOPT_POSTFIELDS, $xml);
        $this->curl->setOption(CURLOPT_HTTPHEADER, [
            'Content-Type: text/xml'
        ]);
        $this->curl->setOption(CURLOPT_RETURNTRANSFER, true);
        $this->curl->setOption(CURLOPT_TIMEOUT, 30);

        $this->curl->post($endpoint, $xml);
        
        $response = $this->curl->getBody();
        $httpCode = $this->curl->getStatus();

        // Log response
        $this->logger->logResponse($response, $httpCode, $operation);

        // Parse XML response
        $xmlResponse = simplexml_load_string($response);
        
        if ($xmlResponse === false) {
            $this->logger->logError('Invalid XML response from API', ['response' => $response], $operation);
            return [
                'success' => false,
                'message' => 'Invalid XML response from API',
                'surcharge_allowed' => false,
                'surcharge_percent' => 0,
                'error_code' => 'INVALID_RESPONSE'
            ];
        }

        // Check for errors in response
        if (isset($xmlResponse->Response)) {
            $responseCode = (string)$xmlResponse->Response->ExpressResponseCode;
            
            if ($responseCode !== '0') {
                $this->logger->logError('API Error: ' . $responseCode, [
                    'response_code' => $responseCode
                ], $operation);
                return [
                    'success' => false,
                    'message' => 'API Error: ' . $responseCode,
                    'surcharge_allowed' => false,
                    'surcharge_percent' => 0,
                    'error_code' => $responseCode
                ];
            }
        }

        // Extract surcharge information
        $surchargeAllowed = false;
        $surchargePercent = 0;

        if (isset($xmlResponse->SurchargeAllowed)) {
            $surchargeAllowed = strtolower((string)$xmlResponse->SurchargeAllowed) === 'true';
        }

        if (isset($xmlResponse->SurchargePercent)) {
            $surchargePercent = (float)$xmlResponse->SurchargePercent;
        }

        return [
            'success' => true,
            'message' => 'Surcharge query successful',
            'surcharge_allowed' => $surchargeAllowed,
            'surcharge_percent' => $surchargePercent
        ];
    }
} 