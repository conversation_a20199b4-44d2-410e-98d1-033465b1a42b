type Customer {
    customer_type: CustomerType! @doc(description: "The type of the customer.") @resolver(class: "Silksoftwarecorp\\CustomerGraphQl\\Model\\Resolver\\Customer\\Type")
    customer_status: String @doc(description: "Customer status.") @resolver(class: "Silksoftwarecorp\\CustomerGraphQl\\Model\\Resolver\\Customer\\CustomerStatus")
}

enum CustomerType {
    INDIVIDUAL_USER @doc(description: "Individual user.")
    COMPANY_USER @doc(description: "company user.")
}
