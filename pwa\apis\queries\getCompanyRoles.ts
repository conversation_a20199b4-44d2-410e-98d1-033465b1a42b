import { gql } from '@apollo/client'

export const GET_B2B_COMPANY_ROLES = gql`
  query getCompanyRoles($pageSize: Int = 20, $currentPage: Int = 1) {
    company {
      roles(pageSize: $pageSize, currentPage: $currentPage) {
        items {
          id
          name
          permissions {
            key: id
            title: text
            children {
              key: id
              title: text
              children {
                key: id
                title: text
                children {
                  key: id
                  title: text
                }
              }
            }
          }
          users_count
        }
        pages: page_info {
          current_page
          page_size
          total_pages
        }
        total_count
      }
    }
  }
`
