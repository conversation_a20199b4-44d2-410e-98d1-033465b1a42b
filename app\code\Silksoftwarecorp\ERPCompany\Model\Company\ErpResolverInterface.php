<?php

namespace Silksoftwarecorp\ERPCompany\Model\Company;

use Magento\Framework\Exception\LocalizedException;

/**
 * Company ERP Resolver Interface
 *
 * Resolves ERP customer identifiers from Magento company data
 */
interface ErpResolverInterface
{
    /**
     * Get ERP Customer ID by Company ID
     *
     * @param int $companyId
     * @return string
     * @throws LocalizedException
     */
    public function getErpCustomerId(int $companyId): string;

    /**
     * Check if company has ERP Customer ID
     *
     * @param int $companyId
     * @return bool
     */
    public function hasErpCustomerId(int $companyId): bool;

    /**
     * Get ERP Sales Rep ID by Company ID
     *
     * @param int $companyId
     * @return string
     * @throws LocalizedException
     */
    public function getErpSalesRepId(int $companyId): string;

    /**
     * Check if company has ERP Sales Rep ID
     *
     * @param int $companyId
     * @return bool
     */
    public function hasErpSalesRepId(int $companyId): bool;

    /**
     * Get ERP Sales Rep Name by Company ID
     *
     * @param int $companyId
     * @return string
     * @throws LocalizedException
     */
    public function getErpSalesRepName(int $companyId): string;

    /**
     * Check if company has ERP Sales Rep Name
     *
     * @param int $companyId
     * @return bool
     */
    public function hasErpSalesRepName(int $companyId): bool;

    /**
     * Get ERP Credit Status by Company ID
     *
     * @param int $companyId
     * @return string
     * @throws LocalizedException
     */
    public function getErpCreditStatus(int $companyId): string;

    /**
     * Check if company has ERP Credit Status
     *
     * @param int $companyId
     * @return bool
     */
    public function hasErpCreditStatus(int $companyId): bool;

}
