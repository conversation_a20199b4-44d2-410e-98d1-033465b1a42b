<?php
namespace Silksoftwarecorp\UnifiedArPayment\Model\Payment;

use Magento\Payment\Model\Method\AbstractMethod;
use Magento\Payment\Model\InfoInterface;
use Magento\Framework\Exception\LocalizedException;
use Silksoftwarecorp\UnifiedArPayment\Model\ProcessAuthorization;
use Magento\Directory\Helper\Data as DirectoryHelper;

class UnifiedAr extends AbstractMethod
{
    /**
     * Payment method code
     *
     * @var string
     */
    protected $_code = 'unifiedarpayment';

    /**
     * Availability option
     *
     * @var bool
     */
    protected $_isOffline = false;

    /**
     * @var ProcessAuthorization
     */
    protected $processAuthorization;

    public function __construct(
        \Magento\Framework\Model\Context $context,
        \Magento\Framework\Registry $registry,
        \Magento\Framework\Api\ExtensionAttributesFactory $extensionFactory,
        \Magento\Framework\Api\AttributeValueFactory $customAttributeFactory,
        \Magento\Payment\Helper\Data $paymentData,
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        \Magento\Payment\Model\Method\Logger $logger,
        ProcessAuthorization $processAuthorization,
        \Magento\Framework\Model\ResourceModel\AbstractResource $resource = null,
        \Magento\Framework\Data\Collection\AbstractDb $resourceCollection = null,
        array $data = [],
        DirectoryHelper $directory = null,
    ) {
        parent::__construct(
            $context,
            $registry,
            $extensionFactory,
            $customAttributeFactory,
            $paymentData,
            $scopeConfig,
            $logger,
            $resource,
            $resourceCollection,
            $data,
            $directory
        );
        $this->processAuthorization = $processAuthorization;
    }

    /**
     * Check if payment method is properly configured
     *
     * @return bool
     */
    public function isAvailable(\Magento\Quote\Api\Data\CartInterface $quote = null)
    {
        $isAvailable = parent::isAvailable($quote);

        if ($isAvailable) {
            $this->_logger->info('UnifiedAr payment method is available');
        } else {
            $this->_logger->info('UnifiedAr payment method is not available');
        }

        return $isAvailable;
    }

    /**
     * Authorize payment
     *
     * @param InfoInterface $payment
     * @param float $amount
     * @return $this
     * @throws LocalizedException
     */
    public function authorize(InfoInterface $payment, $amount)
    {
        // Debug logging
        $this->_logger->info('UnifiedAr authorize method called', [
            'amount' => $amount,
            'payment_method' => $payment->getMethod(),
            'order_id' => $payment->getOrder() ? $payment->getOrder()->getIncrementId() : 'no_order'
        ]);

        if ($amount <= 0) {
            throw new LocalizedException(__('Invalid amount for authorization.'));
        }

        $order = $payment->getOrder();
        if (!$order) {
            throw new LocalizedException(__('Order not found for payment authorization.'));
        }

        $this->_logger->info('Calling doProcessAuthorization', [
            'order_id' => $order->getIncrementId(),
            'quote_id' => $order->getQuoteId()
        ]);

        $result = $this->processAuthorization->doProcessAuthorization($order);

        $this->_logger->info('doProcessAuthorization result', [
            'success' => $result['success'] ?? false,
            'message' => $result['message'] ?? 'no_message'
        ]);

        if (empty($result['success']) || empty($result['response']['Response'])) {
            throw new LocalizedException(__('Authorization failed: %1', $result['message'] ?? 'Unknown error'));
        }

        $response = $result['response']['Response'];
        if (($response['ExpressResponseCode'] ?? null) !== '0') {
            throw new LocalizedException(__('Authorization declined: %1', $response['ExpressResponseMessage'] ?? 'Unknown error'));
        }

        $transactionId = $response['Transaction']['TransactionID'] ?? 0;
        $payment->setTransactionId($transactionId);
        $payment->setIsTransactionClosed(0); // 0 = open, 1 = closed
        $payment->setAdditionalInformation('unifiedar_auth_response', $result['response']);

        $this->_logger->info('Authorization completed successfully', [
            'transaction_id' => $transactionId
        ]);

        return $this;
    }

    public function capture(
        InfoInterface $payment,
                      $amount
    ) {
        return $this->authorize($payment, $amount);
    }
}
