<?php
namespace Silk\OnesourceTax\Plugin\Quote;

use Silk\OnesourceTax\Model\Config;
use Silk\OnesourceTax\Model\Tax\Calculator;
use Silk\OnesourceTax\Model\Logger;

class Address
{
    protected $config;
    protected $taxCalculator;
    protected $logger;
    protected $json;

    public function __construct(
        Config $config,
        Calculator $taxCalculator,
        \Magento\Framework\Serialize\Serializer\Json $json,
        Logger $logger
    ) {
        $this->config = $config;
        $this->taxCalculator = $taxCalculator;
        $this->logger = $logger;
        $this->json = $json;
    }

    public function aroundCollect(
        \Magento\Tax\Model\Sales\Total\Quote\Tax $subject,
        callable $proceed,
        \Magento\Quote\Model\Quote $quote,
        \Magento\Quote\Api\Data\ShippingAssignmentInterface $shippingAssignment,
        \Magento\Quote\Model\Quote\Address\Total $total
    ) {
        if (!$this->config->isActive() ||  !$shippingAssignment->getShipping()->getAddress()->getPostcode()) {
            return $proceed($quote, $shippingAssignment, $total);
        }

        try {
            $response = $this->taxCalculator->calculate($quote, $shippingAssignment);
            $this->logger->info('Response Data: ' . $this->json->serialize($response));
            if (isset($response['documents'][0]['lines'])) {
                $totalTaxAmount = 0;
                $items = $shippingAssignment->getItems();
                $appliedTaxes = [];

                foreach ($response['documents'][0]['lines'] as $line) {
                    $lineNumber = $line['lineNumber'];
                    $lineTaxAmount = $line['totalTaxAmount'] ?? 0;
                    $taxPercent = $line['taxSummary']['effectiveTaxRate'] * 100 ?? 0;

                    // Find corresponding quote item
                    foreach ($items as $item) {
                        if ($item->getId() == $lineNumber || $item->getQuoteId() == $lineNumber) {
                            $item->setTaxAmount($lineTaxAmount);
                            $item->setBaseTaxAmount($lineTaxAmount);
                            $item->setTaxPercent($taxPercent);
                            $item->setPriceInclTax($item->getPrice() + ($lineTaxAmount / $item->getQty()));
                            $item->setRowTotalInclTax($item->getRowTotal() + $lineTaxAmount);
                            $totalTaxAmount += $lineTaxAmount;
                            break;
                        }
                    }
                }

                // Build applied_taxes array -this is what GraphQL needs
                if ($totalTaxAmount > 0) {
                    // Calculate weighted average tax rate from all lines
                    $totalTaxableAmount = 0;
                    $weightedTaxRate = 0;
                    $taxCode = 'ONESOURCE_TAX';
                    $taxTitle = 'Sales Tax';

                    foreach ($response['documents'][0]['lines'] as $line) {
                        if (isset($line['taxSummary']) && ($line['totalTaxAmount'] ?? 0) > 0) {
                            $lineTaxableAmount = $line['taxSummary']['taxableBasis'] ?? 0;
                            $lineEffectiveRate = $line['taxSummary']['effectiveTaxRate'] ?? 0;

                            if ($lineTaxableAmount > 0) {
                                $totalTaxableAmount += $lineTaxableAmount;
                                $weightedTaxRate += ($lineEffectiveRate * $lineTaxableAmount);
                            }

                            // Use tax info from first taxable line for code and title
                            if ($taxCode === 'ONESOURCE_TAX') {
                                $taxCode = $line['taxSummary']['taxCode'] ?? 'ONESOURCE_TAX';
                                $taxTitle = $line['taxSummary']['taxName'] ?? 'Sales Tax';
                            }
                        }
                    }

                    // Calculate final weighted average rate
                    $taxRate = $totalTaxableAmount > 0 ? ($weightedTaxRate / $totalTaxableAmount) : 0;

                    $appliedTaxes[$taxCode] = [
                        'id' => $taxCode,
                        'percent' => $taxRate * 100,
                        'amount' => $totalTaxAmount,
                        'base_amount' => $totalTaxAmount,
                        'rates' => [
                            [
                                'percent' => $taxRate * 100,
                                'code' => $taxCode,
                                'title' => $taxTitle,
                                'amount' => $totalTaxAmount,
                                'base_amount' => $totalTaxAmount
                            ]
                        ]
                    ];

                    // Set applied_taxes to total - this is what GraphQL needs
                    $total->setAppliedTaxes($appliedTaxes);
                }

                // Set totals
                $total->setTotalAmount('tax', $totalTaxAmount);
                $total->setBaseTotalAmount('tax', $totalTaxAmount);
                $total->setTaxAmount($totalTaxAmount);
                $total->setBaseTaxAmount($totalTaxAmount);

                // Set shipping tax if applicable
                if (isset($response['documents'][0]['shippingTaxAmount'])) {
                    $shippingAddress = $shippingAssignment->getShipping()->getAddress();
                    /** @var \Magento\Quote\Model\Quote\Address $shippingAddress */
                    $shippingAddress->setShippingTaxAmount($response['documents'][0]['shippingTaxAmount']);
                    $shippingAddress->setBaseShippingTaxAmount($response['documents'][0]['shippingTaxAmount']);
                }
            }
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
            return $proceed($quote, $shippingAssignment, $total);
        }

        return $this;
    }
}
