<?php

declare(strict_types=1);

namespace Silksoftwarecorp\CatalogGraphQl\Model\Resolver\Product;

use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Api\CategoryRepositoryInterface;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Catalog\Model\Product;
use Magento\Catalog\Model\Category;
use Magento\Catalog\Model\ResourceModel\Category as CategoryResource;

class HowToMeasure implements ResolverInterface
{
    /**
     * @var CategoryRepositoryInterface
     */
    private $categoryRepository;

    /**
     * @var CategoryResource
     */
    private $categoryResource;

    /**
     * @var ProductRepositoryInterface
     */
    private $productRepository;

    /**
     * @param CategoryRepositoryInterface $categoryRepository
     * @param CategoryResource $categoryResource
     * @param ProductRepositoryInterface $productRepository
     */
    public function __construct(
        CategoryRepositoryInterface $categoryRepository,
        CategoryResource $categoryResource,
        ProductRepositoryInterface $productRepository
    ) {
        $this->categoryRepository = $categoryRepository;
        $this->categoryResource = $categoryResource;
        $this->productRepository = $productRepository;
    }

    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
        if (!array_key_exists('model', $value) || !$value['model'] instanceof ProductInterface) {
            throw new LocalizedException(__('"model" value should be specified'));
        }

                /* @var $product Product */
        $product = $value['model'];
        
        // Get category IDs - use fallback method if getCategoryIds() is not available
        $categoryIds = $this->getProductCategoryIds($product);
        
        if (empty($categoryIds)) {
            return null;
        }

        // Loop through each product category and check its hierarchy
        foreach ($categoryIds as $categoryId) {
            // Convert to integer as getCategoryIds() returns strings
            $categoryIdInt = (int)$categoryId;
            $howToMeasure = $this->getHowToMeasureFromCategoryHierarchy($categoryIdInt);
            if (!empty($howToMeasure)) {
                return $howToMeasure;
            }
        }

        return null;
    }

    /**
     * Get product category IDs with fallback methods
     *
     * @param ProductInterface $product
     * @return array
     */
    private function getProductCategoryIds(ProductInterface $product): array
    {
        try {
            // Try the direct method first
            if (method_exists($product, 'getCategoryIds')) {
                /** @var Product $product */
                return $product->getCategoryIds() ?: [];
            }
            
            // Fallback: reload product and try again
            $productModel = $this->productRepository->get($product->getSku());
            if (method_exists($productModel, 'getCategoryIds')) {
                /** @var Product $productModel */
                return $productModel->getCategoryIds() ?: [];
            }
            
            // Final fallback: use getData method
            if (method_exists($product, 'getData')) {
                /** @var Product $product */
                $categoryIds = $product->getData('category_ids');
                if (is_string($categoryIds)) {
                    return explode(',', $categoryIds);
                }
                
                return is_array($categoryIds) ? $categoryIds : [];
            }
            
            return [];
            
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Traverse category hierarchy to find how_to_measure value
     * Checks current category, then parent, then parent's parent, etc.
     *
     * @param int $categoryId
     * @return string|null
     */
    private function getHowToMeasureFromCategoryHierarchy(int $categoryId): ?string
    {
        try {
            /** @var Category $category */
            $category = $this->categoryRepository->get($categoryId);
            
            // Check current category
            $howToMeasure = $category->getData('how_to_measure');
            if (!empty($howToMeasure)) {
                return $this->getOptionText($howToMeasure);
            }
            
            // Get parent category path and traverse up the hierarchy
            $pathIds = explode('/', $category->getPath());
            
            // Remove current category ID and root category ID (usually 1)
            array_pop($pathIds); // Remove current category
            if (count($pathIds) > 0 && $pathIds[0] == '1') {
                array_shift($pathIds); // Remove root category
            }
            
            // Check parent categories from immediate parent up to root
            for ($i = count($pathIds) - 1; $i >= 0; $i--) {
                $parentId = (int)$pathIds[$i];
                
                // Skip if same as current category or root
                if ($parentId == $categoryId || $parentId <= 1) {
                    continue;
                }
                
                try {
                    /** @var Category $parentCategory */
                    $parentCategory = $this->categoryRepository->get($parentId);
                    $parentHowToMeasure = $parentCategory->getData('how_to_measure');
                    
                    if (!empty($parentHowToMeasure)) {
                        return $this->getOptionText($parentHowToMeasure);
                    }
                } catch (\Exception $e) {
                    // Continue to next parent if this one fails
                    continue;
                }
            }
            
        } catch (\Exception $e) {
            // Return null if category loading fails
            return null;
        }
        
        return null;
    }

    /**
     * Get the text value for the option
     *
     * @param string $optionValue
     * @return string
     */
    private function getOptionText(string $optionValue): string
    {
        try {
            $attribute = $this->categoryResource->getAttribute('how_to_measure');
            if ($attribute && $attribute->getSource()) {
                $optionText = $attribute->getSource()->getOptionText($optionValue);
                // Convert Phrase objects to string
                if ($optionText instanceof \Magento\Framework\Phrase) {
                    return $optionText->render();
                }
                
                return $optionText ? (string)$optionText : $optionValue;
            }
        } catch (\Exception $e) {
            // Return original value if getting option text fails
        }
        
        return $optionValue;
    }
}
