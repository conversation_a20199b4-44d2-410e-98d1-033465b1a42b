import styled from '@emotion/styled'

export const StyledPdpMain = styled.div`
  display: grid;
  padding: 30px 0;
  grid-template-columns: 578px 1fr;
  grid-gap: 120px;
  justify-content: space-between;
  align-items: flex-start;

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    display: block;
    padding: 6px 16px 16px;
  }
`

export const StyledPdpDetail = styled.div`
  .stock {
    margin-top: 25px;
  }

  .prices {
    margin-top: 12px;
    min-height: 32px;

    .product__price {
      &--default,
      &--old,
      &--specail {
        font-weight: 700;
        font-size: 24px;
        line-height: 30px;
        letter-spacing: 0.02em;
      }

      &--specail {
        font-weight: 600;
      }

      &--old {
        font-weight: 400;
      }
    }
  }

  .order-help {
    margin-top: 22px;
  }
`
