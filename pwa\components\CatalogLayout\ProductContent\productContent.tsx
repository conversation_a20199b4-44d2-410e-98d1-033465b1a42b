import dynamic from 'next/dynamic'
import { useMemo, memo } from 'react'

import { useProductContext } from '@/route/ProductProvider'

import CollapseBox from './CollapseBox'
import SpecificationsTable from './SpecificationsTable'
import RelatedDocuments from './RelatedDocuments'
import { StyledProductContent } from './styled'

const PageBuilder = dynamic(() => import('@ranger-theme/pagebuilder'), {
  ssr: false
})

const ProductContent = ({ sku }: any) => {
  const { product } = useProductContext()

  const descHtmlList = useMemo(() => {
    const list = product?.description?.html?.split('* ')
    return list
      .filter((item) => {
        return !!item
      })
      .map((item) => {
        return `${item}`
      })
  }, [product])

  const warrantyHtml = useMemo(() => {
    return product?.pdp_ble_warranty_information?.html || ''
  }, [product])

  const videoHtml = useMemo(() => {
    return product?.pdp_ble_product_video?.html || ''
  }, [product])

  return (
    <StyledProductContent>
      <SpecificationsTable sku={sku} />
      {descHtmlList && (
        <CollapseBox
          title="Description"
          contentChildren={
            <ul>
              {descHtmlList.map((item, index) => {
                const key = `key_${index}`
                return (
                  <li key={key}>
                    <div style={{ position: 'relative', bottom: '-1px' }}>
                      <PageBuilder html={item} />
                    </div>
                  </li>
                )
              })}
            </ul>
          }
        />
      )}
      <div className="half-width">
        <RelatedDocuments sku={sku} />
        {warrantyHtml && <CollapseBox title="Warranty Information" contentHtml={warrantyHtml} />}
      </div>
      {videoHtml && (
        <div className="pdp-video">
          <PageBuilder html={videoHtml} />
        </div>
      )}
    </StyledProductContent>
  )
}

export default memo(ProductContent)
