<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */

declare(strict_types=1);

namespace Silksoftwarecorp\Inventory\Plugin\InventoryApi\SourceRepository;

use Magento\Framework\DataObject;
use Magento\InventoryApi\Api\Data\SourceExtensionInterface;
use Magento\InventoryApi\Api\Data\SourceInterface;
use Magento\InventoryApi\Api\SourceRepositoryInterface;

/**
 * Set data to Source itself from its extension attributes to save these values to `inventory_source` DB table.
 */
class SaveCustomFieldsPlugin
{
    /**
     * Persist the custom extension attributes on Source save
     *
     * @param SourceRepositoryInterface $subject
     * @param SourceInterface $source
     *
     * @return array
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function beforeSave(
        SourceRepositoryInterface $subject,
        SourceInterface $source
    ): array {
        $extensionAttributes = $source->getExtensionAttributes();

        if ($extensionAttributes !== null) {
            $this->setInstallRequestForm($source, $extensionAttributes);
            $this->setInstallApp($source, $extensionAttributes);
            $this->setTextNumber($source, $extensionAttributes);
            $this->setStreet1($source, $extensionAttributes);
            $this->setSchedule($source, $extensionAttributes);
            $this->setHolidayClosures($source, $extensionAttributes);
        }

        return [$source];
    }

    /**
     * Set install request form to Source
     *
     * @param SourceInterface $source
     * @param SourceExtensionInterface $extensionAttributes
     */
    private function setInstallRequestForm(SourceInterface $source, SourceExtensionInterface $extensionAttributes): void
    {
        if ($extensionAttributes->getInstallRequestForm() !== null) {
            $source->setData('install_request_form', $extensionAttributes->getInstallRequestForm());
        }
    }

    /**
     * Set install app to Source
     *
     * @param SourceInterface $source
     * @param SourceExtensionInterface $extensionAttributes
     */
    private function setInstallApp(SourceInterface $source, SourceExtensionInterface $extensionAttributes): void
    {
        if ($extensionAttributes->getInstallApp() !== null) {
            $source->setData('install_app', $extensionAttributes->getInstallApp());
        }
    }

    /**
     * Set text number to Source
     *
     * @param SourceInterface $source
     * @param SourceExtensionInterface $extensionAttributes
     */
    private function setTextNumber(SourceInterface $source, SourceExtensionInterface $extensionAttributes): void
    {
        if ($extensionAttributes->getTextNumber() !== null) {
            $source->setData('text_number', $extensionAttributes->getTextNumber());
        }
    }

    /**
     * Set street1 to Source
     *
     * @param SourceInterface $source
     * @param SourceExtensionInterface $extensionAttributes
     */
    private function setStreet1(SourceInterface $source, SourceExtensionInterface $extensionAttributes): void
    {
        if ($extensionAttributes->getStreet1() !== null) {
            $source->setData('street1', $extensionAttributes->getStreet1());
        }
    }

    /**
     * Set schedule to Source
     *
     * @param SourceInterface $source
     * @param SourceExtensionInterface $extensionAttributes
     */
    private function setSchedule(SourceInterface $source, SourceExtensionInterface $extensionAttributes): void
    {
        if ($extensionAttributes->getSchedule() !== null) {
            $schedule = $extensionAttributes->getSchedule();
            if (is_array($schedule)) {
                $schedule = json_encode($schedule);
            }

            $source->setData('schedule', $schedule);
        }
    }

    /**
     * Set holiday closures to Source
     *
     * @param SourceInterface $source
     * @param SourceExtensionInterface $extensionAttributes
     */
    private function setHolidayClosures(SourceInterface $source, SourceExtensionInterface $extensionAttributes): void
    {
        if ($extensionAttributes->getHolidayClosures() !== null) {
            $source->setData('holiday_closures', $extensionAttributes->getHolidayClosures());
        }
    }
}
