<?php

namespace Silksoftwarecorp\BlogGraphql\Model\Resolver\Post;

use Amasty\Blog\Api\Data\PostInterface;
use Amasty\Blog\Api\PostRepositoryInterface;
use Amasty\Blog\Helper\Image;
use Amasty\Blog\Model\Repository\PostRepository;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Store\Model\StoreManagerInterface;

class OriginalListThumbnail implements ResolverInterface
{
    /**
     * @var Image
     */
    private $imageHelper;

    /**
     * @var StoreManagerInterface
     */
    private $storeManager;

    /**
     * @var PostRepositoryInterface
     */
    protected $postRepository;

    /**
     * @param Image $imageHelper
     * @param StoreManagerInterface $storeManager
     * @param PostRepository $postRepository
     */
    public function __construct(
        Image $imageHelper,
        StoreManagerInterface $storeManager,
        PostRepository $postRepository
    ) {
        $this->imageHelper = $imageHelper;
        $this->storeManager = $storeManager;
        $this->postRepository = $postRepository;
    }

    public function resolve(
        Field $field,
              $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        if (!isset($value['post_id']) || empty($value['post_id'])) {
            throw new LocalizedException(__('"post_id" value should be specified'));
        }

        if (isset($value['list_thumbnail']) && !empty($value['list_thumbnail'])) {
            $post = $this->postRepository->getById($value['post_id']);
            $storeId = $context->getExtensionAttributes()->getStore()->getId();

            return $this->getOriginalListImage($post, $storeId);
        }

        return null;
    }

    /**
     * @param string $src
     * @param $storeId
     * @return string
     * @throws NoSuchEntityException
     */
    protected function getRelativePath(string $src, $storeId = null): string
    {
        $baseUrl = trim($this->storeManager->getStore($storeId)->getBaseUrl(), '/');

        return str_replace($baseUrl, '', $src);
    }

    /**
     * @param PostInterface $post
     * @param $storeId
     * @return string
     *
     * @throws NoSuchEntityException
     */
    private function getOriginalListImage(PostInterface $post, $storeId = null): string
    {
        $src = $post->getListThumbnail() ?: $post->getPostThumbnail();
        $src = $src ? $this->imageHelper->getImageUrl($src) : $src;

        return $src ? $this->getRelativePath($src, $storeId) : '';
    }
}
