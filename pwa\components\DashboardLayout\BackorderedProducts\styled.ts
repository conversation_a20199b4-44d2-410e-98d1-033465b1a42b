import styled from '@emotion/styled'

export const StyledBackorderedProducts = styled.div`
  table thead th {
    padding: 0 !important;

    .item-search {
      height: 76px;
    }
  }

  &.action-inactive {
    .common-table-component {
      .common-table-sort {
        display: none;
      }
      table thead th {
        p {
          cursor: initial;
        }
        .item-search {
          display: none;
        }
      }
    }
    .${({ theme }) => theme.namespace}-table-column-title {
      p {
        padding-right: 0;
      }
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    .common-table-sort {
      display: none;
    }
  }
`
