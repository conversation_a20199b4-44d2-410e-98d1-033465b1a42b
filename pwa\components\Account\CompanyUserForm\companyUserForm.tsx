import { memo } from 'react'
import { isEmpty } from 'lodash-es'

import { Modal, Form, Input, Spin, Select } from 'antd'

import { useCompanyUserForm } from '@/hooks/CompanyUserForm'
import CommonButton from '@/components/Common/CommonButton'

import { StyledUserButton } from './styled'

const CompanyUserForm = ({ userData, handleSubmit, onCancel, ...rest }) => {
  const [form] = Form.useForm()
  const { isFetching, isLoading, roleList, statusList, handleFormSubmit } = useCompanyUserForm({
    form,
    userData,
    handleSubmit
  })

  return (
    <Modal
      centered
      open
      footer={null}
      width={500}
      title={isEmpty(userData) ? 'Add New User' : 'Edit User'}
      onCancel={onCancel}
      {...rest}>
      {isFetching ? (
        <Spin />
      ) : (
        <>
          <Spin spinning={isLoading}>
            <Form form={form} layout="vertical" onFinish={handleFormSubmit}>
              <Form.Item
                name="job_title"
                label="Job Title"
                rules={[
                  {
                    required: true
                  }
                ]}>
                <Input />
              </Form.Item>
              <Form.Item
                label="User Role"
                name="role_id"
                rules={[
                  {
                    required: true
                  }
                ]}>
                <Select>
                  {roleList.map((item) => {
                    const { id, name } = item
                    return (
                      <Select.Option key={id} value={id}>
                        {name}
                      </Select.Option>
                    )
                  })}
                </Select>
              </Form.Item>
              <Form.Item
                name="firstname"
                label="First Name"
                rules={[
                  {
                    required: true
                  }
                ]}>
                <Input />
              </Form.Item>
              <Form.Item
                name="lastname"
                label="Last Name"
                rules={[
                  {
                    required: true
                  }
                ]}>
                <Input />
              </Form.Item>
              <Form.Item
                name="email"
                label="Email"
                rules={[
                  {
                    required: true
                  }
                ]}>
                <Input />
              </Form.Item>
              <Form.Item
                name="telephone"
                label="Phone Number"
                rules={[
                  {
                    required: true
                  }
                ]}>
                <Input />
              </Form.Item>
              <Form.Item name="status" label="Status">
                <Select>
                  {statusList.map((item) => {
                    const { id, name } = item
                    return (
                      <Select.Option key={id} value={id}>
                        {name}
                      </Select.Option>
                    )
                  })}
                </Select>
              </Form.Item>
              <StyledUserButton>
                <CommonButton type="primary" height={40} htmlType="submit">
                  Save
                </CommonButton>
                <CommonButton
                  type="default"
                  height={40}
                  ph={22}
                  htmlType="button"
                  onClick={onCancel}>
                  Cancel
                </CommonButton>
              </StyledUserButton>
            </Form>
          </Spin>
        </>
      )}
    </Modal>
  )
}

export default memo(CompanyUserForm)
