<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */

namespace Silksoftwarecorp\AmastyCustomForm\Block\Adminhtml\Form\Field\Renderer;

use Magento\Framework\View\Element\Html\Select;
use Magento\Framework\View\Element\Context;
use Magento\InventoryApi\Api\SourceRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Psr\Log\LoggerInterface;

class Branch extends Select
{
    /**
     * @var SourceRepositoryInterface
     */
    private $sourceRepository;

    /**
     * @var SearchCriteriaBuilder
     */
    private $searchCriteriaBuilder;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @param Context $context
     * @param SourceRepositoryInterface $sourceRepository
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param LoggerInterface $logger
     * @param array $data
     */
    public function __construct(
        Context $context,
        SourceRepositoryInterface $sourceRepository,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        LoggerInterface $logger,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->sourceRepository = $sourceRepository;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->logger = $logger;
    }

    /**
     * Set "name" for <select> element
     *
     * @param string $value
     * @return $this
     */
    public function setInputName($value)
    {
        if ($this->isMultipleSelect()) {
            // Use array syntax for multiselect values
            return $this->setName($value . '[]');
        }

        return $this->setName($value);
    }

    public function getClass()
    {
        $class = $this->getData('class');
        if ($this->isMultipleSelect()) {
            $class .= ' select multiselect admin__control-multiselect';
        }

        return $class;
    }

    public function setIsMultipleSelect(bool $isMultipleSelect): static
    {
        $this->setData('is_multiple', $isMultipleSelect);

        return $this;
    }

    public function isMultipleSelect(): bool
    {
        return (bool)$this->getData('is_multiple');
    }

    public function getExtraParams()
    {
        $extraParams = $this->getData('extra_params');
        if ($this->isMultipleSelect()) {
            $extraParams .= ' multiple="multiple" size="3"';
        }

        return $extraParams;
    }

    /**
     * Set "id" for <select> element
     *
     * @param string $value
     * @return $this
     */
    public function setInputId($value)
    {
        return $this->setId($value);
    }

    /**
     * Render branch options
     *
     * @return string
     */
    public function _toHtml(): string
    {
        if (!$this->getOptions()) {
            $this->setOptions($this->getBranchOptions());
        }

        return parent::_toHtml();
    }

    /**
     * Get branch options from inventory sources
     *
     * @return array
     */
    private function getBranchOptions(): array
    {
//        $options = [['label' => __('-- Please Select --'), 'value' => '']];
        $options = [];

        try {
            $searchCriteria = $this->searchCriteriaBuilder->create();
            $sources = $this->sourceRepository->getList($searchCriteria);

            foreach ($sources->getItems() as $source) {
                if ($source->getSourceCode() == 'default') {
                    continue;
                }

                $options[] = [
                    'label' => $source->getName() . "(Code: {$source->getSourceCode()})",
                    'value' => $source->getSourceCode()
                ];
            }

            //sort options by source code, if source code is number sort by number
            usort($options, function ($a, $b) {
                $aCode = $a['value'];
                $bCode = $b['value'];
                if (is_numeric($aCode) && is_numeric($bCode)) {
                    return $aCode - $bCode;
                }

                return strcmp($aCode, $bCode);
            });
        } catch (\Exception $e) {
            $this->logger->error('[AmastyCustomForm]: Failed to load inventory sources for dropdown', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }

        return $options;
    }
}
