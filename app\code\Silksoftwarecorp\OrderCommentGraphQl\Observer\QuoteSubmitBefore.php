<?php
namespace Silksoftwarecorp\OrderCommentGraphQl\Observer;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;

class QuoteSubmitBefore implements ObserverInterface
{

    /**
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer)
    {
       $quote = $observer->getQuote();
       $order = $observer->getOrder();

       $order->setData('special_notes', $quote->getData('special_notes'));      
    }
}