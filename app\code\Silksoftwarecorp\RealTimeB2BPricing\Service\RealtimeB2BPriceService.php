<?php

namespace Silksoftwarecorp\RealTimeB2BPricing\Service;

use Magento\Framework\Exception\LocalizedException;
use Magento\Setup\Exception;
use Silksoftwarecorp\Customer\Model\B2BUserChecker;
use Silksoftwarecorp\RealTimeB2BPricing\Api\B2BPriceItemResultInterface;
use Silksoftwarecorp\RealTimeB2BPricing\Api\Data\B2BPriceItemInterface;
use Silksoftwarecorp\RealTimeB2BPricing\Api\PriceStorageInterface;
use Silksoftwarecorp\RealTimeB2BPricing\Api\RealtimeB2BPriceServiceInterface;
use Silksoftwarecorp\RealTimeB2BPricing\Helper\Data as DataHelper;
use Silksoftwarecorp\RealTimeB2BPricing\Logger\LoggerInterface;
use Silksoftwarecorp\RealTimeB2BPricing\Model\Company\ErpAttributesProvider;
use Silksoftwarecorp\RealTimeB2BPricing\Model\B2BPriceItemResult;
use Silksoftwarecorp\RealTimeB2BPricing\Model\B2BPriceItemResultFactory;

class RealtimeB2BPriceService implements RealtimeB2BPriceServiceInterface
{
    /**
     * @var RealtimeB2BPriceApiService
     */
    protected $realtimeB2bPriceApiService;

    /**
     * @var PriceStorageInterface
     */
    protected $storage;

    /**
     * @var B2BUserChecker
     */
    protected $b2bUserChecker;

    /**
     * @var ErpAttributesProvider
     */
    protected $erpAttributesProvider;

    /**
     * @var B2BPriceItemResultFactory
     */
    protected $b2bPriceItemResultFactory;

    /**
     * @var DataHelper
     */
    protected $helper;

    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * @param RealtimeB2BPriceApiService $realtimeB2bPriceApiService
     * @param PriceStorageInterface $storage
     * @param B2BUserChecker $b2bUserChecker
     * @param ErpAttributesProvider $erpAttributesProvider
     * @param B2BPriceItemResultFactory $b2bPriceItemResultFactory
     * @param DataHelper $helper
     * @param LoggerInterface $logger
     */
    public function __construct(
        RealtimeB2BPriceApiService $realtimeB2bPriceApiService,
        PriceStorageInterface $storage,
        B2BUserChecker $b2bUserChecker,
        ErpAttributesProvider $erpAttributesProvider,
        B2BPriceItemResultFactory  $b2bPriceItemResultFactory,
        DataHelper $helper,
        LoggerInterface $logger
    ) {
        $this->realtimeB2bPriceApiService = $realtimeB2bPriceApiService;
        $this->storage = $storage;
        $this->b2bUserChecker = $b2bUserChecker;
        $this->erpAttributesProvider = $erpAttributesProvider;
        $this->b2bPriceItemResultFactory = $b2bPriceItemResultFactory;
        $this->helper = $helper;
        $this->logger = $logger;
    }

    /**
     * @param $customerId
     * @param $locationId
     * @param $sku
     *
     * @return B2BPriceItemInterface|null
     *
     * @throws LocalizedException|Exception
     */
    public function getPriceItem($customerId, $locationId, $sku): ?B2BPriceItemInterface
    {
        $priceItemResult = $this->getPriceItems($customerId, $locationId, [$sku]);

        return $priceItemResult->getItem($sku);
    }

    /**
     * @param $customerId
     * @param $locationId
     * @param array $skus
     *
     * @return B2BPriceItemResultInterface
     *
     * @throws Exception
     * @throws LocalizedException
     */
    public function getPriceItems($customerId, $locationId, array $skus): B2BPriceItemResultInterface
    {
        $priceItemResult = $this->b2bPriceItemResultFactory->create();
        if (!$this->helper->isActive()) {
            $this->logger->info(__('Real-time pricing service is disabled.'));
            return $priceItemResult;
        }

        if (empty($locationId)) {
            throw new LocalizedException(__('Please specify location.'));
        }

        if (!$this->b2bUserChecker->isB2BUser($customerId)) {
            throw new LocalizedException(__('Real-time pricing is only available for B2B users.'));
        }

        $erpCustomerId = $this->erpAttributesProvider->getErpCustomerIdByCustomerId($customerId);
        if (!$erpCustomerId) {
            throw new Exception(__(
                '`ERP Customer ID` is missing for the current customer. customer_id: %1',
                $customerId
            ));
        }

        if (empty($skus)) {
            return $priceItemResult;
        }

        $skus = $this->filterAndUniqueSkus($skus);
        $priceItemResultFromStorage = $this->getPriceItemResultFromStorage(
            $erpCustomerId,
            $locationId,
            $skus
        );

        $priceItemResult->merge($priceItemResultFromStorage);

        $missingPriceSkus = array_diff($skus, $priceItemResult->getSkus());
        if ($missingPriceSkus) {
            $priceItemResultFromApi = $this->realtimeB2bPriceApiService->getPriceItems(
                $erpCustomerId,
                $locationId,
                $missingPriceSkus
            );

            $priceItemResult->merge($priceItemResultFromApi);
        }

        return $priceItemResult;
    }

    protected function getPriceItemResultFromStorage(
        $customerId,
        $locationId,
        array $skus
    ): B2BPriceItemResultInterface {
        $this->logger->debug(__(
            'Start fetching prices from storage. Info: %1',
            json_encode([
                'customer_id' => $customerId,
                'location_id' => $locationId,
                'skus' => $skus
            ])
        ));

        $priceItemResult = $this->b2bPriceItemResultFactory->create();
        foreach ($skus as $sku) {
            $priceItem = $this->storage->get($customerId, $locationId, $sku);
            if ($priceItem instanceof B2BPriceItemInterface && $priceItem->getSku() == $sku) {
                $priceItemResult->addItem($priceItem);
            }
        }

        if ($priceItemResult->count() > 0) {
            $this->logger->debug(__(
                "Successfully fetching prices from storage. Info: \n%1",
                json_encode([
                    'customer_id' => $customerId,
                    'location_id' => $locationId,
                    'data' => array_map(fn($item) => $item->toArray(), $priceItemResult->getItems())
                ], JSON_PRETTY_PRINT)
            ));
        }

        return $priceItemResult;
    }

    /**
     * Filter and unique an array by removing empty/null values.
     *
     * @param array $skus
     *
     * @return array
     */
    protected function filterAndUniqueSkus(array $skus): array
    {
        return array_values(
            array_unique(
                array_filter(
                    $skus,
                    fn ($sku) =>
                    !(
                        is_null($sku) ||
                        (is_string($sku) && trim($sku) === "") ||
                        is_array($sku)
                    )
                )
            )
        );
    }
}
