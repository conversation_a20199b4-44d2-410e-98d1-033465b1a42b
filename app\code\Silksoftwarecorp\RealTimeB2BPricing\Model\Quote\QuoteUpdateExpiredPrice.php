<?php

namespace Silksoftwarecorp\RealTimeB2BPricing\Model\Quote;

use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Stdlib\DateTime\DateTime;
use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Quote\Model\Quote;
use Magento\Quote\Model\Quote\Item;
use Silksoftwarecorp\Customer\Model\B2BUserChecker;
use Silksoftwarecorp\RealTimeB2BPricing\Api\Data\B2BPriceItemInterface;
use Silksoftwarecorp\RealTimeB2BPricing\Helper\Data as HelperData;
use Silksoftwarecorp\RealTimeB2BPricing\Logger\LoggerInterface;
use Silksoftwarecorp\RealTimeB2BPricing\Model\B2BPriceItemResult;
use Silksoftwarecorp\RealTimeB2BPricing\Model\ShipTo\LocationProvider;
use Silksoftwarecorp\RealTimeB2BPricing\Api\RealtimeB2BPriceServiceInterface;

class QuoteUpdateExpiredPrice implements QuoteUpdateExpiredPriceInterface
{
    /**
     * @var CartRepositoryInterface
     */
    protected $quoteRepository;

    /**
     * @var B2BUserChecker
     */
    protected $b2bUserChecker;

    /**
     * @var RealtimeB2BPriceServiceInterface
     */
    protected $realtimeB2bPriceService;

    /**
     * @var LocationProvider
     */
    protected $locationProvider;

    /**
     * @var DateTime
     */
    protected $dateTime;

    /**
     * @var HelperData
     */
    protected $helper;

    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * Real-Time B2B Price is already updated flag
     *
     * @var bool
     */
    private bool $_rtpUpdatedFlag = false;

    /**
     * @param CartRepositoryInterface $quoteRepository
     * @param B2BUserChecker $b2bUserChecker
     * @param RealtimeB2BPriceServiceInterface $realtimeB2bPriceService
     * @param LocationProvider $locationProvider
     * @param DateTime $dateTime
     * @param HelperData $helper
     * @param LoggerInterface $logger
     */
    public function __construct(
        CartRepositoryInterface $quoteRepository,
        B2BUserChecker $b2bUserChecker,
        RealtimeB2BPriceServiceInterface $realtimeB2bPriceService,
        LocationProvider $locationProvider,
        DateTime $dateTime,
        HelperData $helper,
        LoggerInterface $logger
    ) {
        $this->quoteRepository = $quoteRepository;
        $this->b2bUserChecker = $b2bUserChecker;
        $this->realtimeB2bPriceService = $realtimeB2bPriceService;
        $this->locationProvider = $locationProvider;
        $this->dateTime = $dateTime;
        $this->helper = $helper;
        $this->logger = $logger;
    }

    public function execute(Quote $quote): void
    {
        if ($this->_rtpUpdatedFlag || $quote->getData('rtp_updated_flag')) {
            return;
        }

        if (!$this->helper->isActive($quote->getStoreId())) {
            return;
        }

        $customerId = $quote->getCustomerId();
        if (!$customerId || !$this->b2bUserChecker->isB2BUser($customerId)) {
            return;
        }

        try {
            $skus = $this->getPriceExpiredSkus($quote);
            if (!empty($skus)) {
                $priceItemResult = $this->realtimeB2bPriceService->getPriceItems(
                    $customerId,
                    $this->getLocationId($quote->getStoreId()),
                    $skus
                );

                foreach ($quote->getAllItems() as $item) {
                    try {
                        $this->_processQuoteItem($item, $priceItemResult);
                    } catch (\Exception $e) {
                        $this->logger->critical(__($e->getMessage()));
                    }
                }

                $this->_rtpUpdatedFlag = true;
                $quote->setData('rtp_updated_flag', true);
                $this->quoteRepository->save($quote);
            }
        } catch (\Exception $e) {
            $this->logger->critical(__($e->getMessage()));
        }
    }

    /**
     * @param Item $item
     * @param B2BPriceItemResult $priceItemResult
     * @return void
     *
     * @throws LocalizedException
     */
    protected function _processQuoteItem(Item $item, B2BPriceItemResult $priceItemResult): void
    {
        if (!$this->isItemPriceExpired($item)) {
            return;
        }

        $priceItem = $priceItemResult->getItem($item->getSku());
        if (!$priceItem instanceof B2BPriceItemInterface || $priceItem->getPrice() === null) {
            throw new LocalizedException(__(
                'Skip updating the product price. As the B2B price for ' .
                '[Customer Id: %1, Location id: %2, Sku: %3] was not found.',
                $item->getQuote()->getCustomerId(),
                $this->getLocationId($item->getStoreId()),
                $item->getSku()
            ));
        }

        $oldPrice = $item->getCustomPrice();
        $item->setCustomPrice($priceItem->getPrice());
        $item->setOriginalCustomPrice($priceItem->getPrice());
        $item->setData('b2b_price_updated_at', $this->dateTime->gmtTimestamp());
        $item->getProduct()->setIsSuperMode(true);

        $this->logger->debug(__(
            'Update expired B2B Price. Info: %1',
            json_encode([
                'quote_item_id' => $item->getId(),
                'sku' => $item->getSku(),
                'old_price' => $oldPrice,
                'price' => $item->getCustomPrice(),
            ])
        ));
    }

    protected function isItemPriceExpired(Item $item): bool
    {
        $priceExpireTime = $this->helper->getCartPriceValidityTime($item->getQuote()->getStoreId());
        $lastB2BPriceUpdatedAt = $item->getData('b2b_price_updated_at');

        return !$lastB2BPriceUpdatedAt ||
            $this->dateTime->gmtTimestamp() - (int)$lastB2BPriceUpdatedAt >= $priceExpireTime;
    }

    protected function getLocationId($storeId = null): string
    {
        return $this->locationProvider->getLocationId($storeId);
    }

    /**
     * @param Quote $quote
     * @return array|string[]
     */
    protected function getPriceExpiredSkus(Quote $quote): array
    {
        return array_filter(
            array_map(
                fn($item) => $this->isItemPriceExpired($item) ? $item->getSku() : null,
                $quote->getAllItems()
            ),
            fn($sku) => !is_null($sku)
        );
    }
}
