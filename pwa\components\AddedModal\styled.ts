import styled from '@emotion/styled'

export const StyledAddedModal = styled.div`
  position: relative;
`

export const StyledProducts = styled.div`
  display: grid;
  margin-bottom: 40px;
  padding-bottom: 40px;
  grid-template-columns: 1fr 292px;
  grid-column-gap: 190px;
  border-bottom: 1px solid #d9d9d9;
  justify-content: space-between;
  align-items: flex-start;

  .main {
    display: grid;
    grid-template-columns: 200px 1fr;
    grid-column-gap: 24px;
    justify-content: space-between;
    align-items: flex-start;
  }

  .image {
    width: 200px;
    height: 200px;
    background-color: #fff;
  }

  .name {
    line-height: 31px;
    margin-bottom: 8px;
    color: #1b060f;
    font-family: var(--font-poppins-medium);
    font-size: 22px;
    font-weight: 500;
  }

  .ships,
  .instock {
    line-height: 24px;
    color: var(--color-primary);
    font-family: var(--font-montserrat);
    font-size: 15px;
    font-weight: 400;
  }

  .instock {
    color: #1d8d36;
  }

  .total {
    line-height: 27px;
    color: #1b060f;
    font-size: 20px;
    font-weight: 500;
    text-align: center;
  }

  p {
    margin-bottom: 4px;

    > span {
      line-height: 24px;
      font-family: var(--font-montserrat);
      font-size: 15px;
      font-weight: 400;

      &:first-of-type {
        margin-right: 5px;
      }

      &:last-of-type {
        font-weight: 400;
      }
    }
  }
`

export const StyledActions = styled.div`
  display: grid;
  margin-top: 7px;
  grid-row-gap: 15.65px;

  .${({ theme }) => theme.namespace} {
    &-btn {
      line-height: 22px;
      height: 54px;
      font-family: var(--font-poppins-medium);
      font-size: 16px;
      font-weight: 600;
      border-radius: 50px;
      border-width: 2px;
      text-transform: uppercase;
    }

    &-btn-default {
      border-color: #667071;

      &:hover {
        color: #1b060f !important;
        border-color: #667071 !important;
      }
    }
  }
`
