<?php

namespace Silksoftwarecorp\CompanyB2b\Model;
use Silksoftwarecorp\CompanyB2b\Api\Data\ErpCompanyShipToInterface;
use Magento\Framework\Model\AbstractExtensibleModel;
class ErpCompanyShipTo extends  AbstractExtensibleModel implements ErpCompanyShipToInterface
{
    /**
     * Cache tag.
     */
    const CACHE_TAG = 'erpcompanyshopto';

    /**
     * Prefix of model events names.
     *
     * @var string
     */
    protected $_eventPrefix = 'erpcompanyshopto';
    /**
     * Initialize resource model.
     *
     * @return void
     */
    protected function _construct(): void
    {
        $this->_init(\Silksoftwarecorp\CompanyB2b\Model\ResourceModel\ErpCompanyShipTo::class);
    }

    /**
     * @param string $erpCustomerNum
     * @return $this
     */
    public function setErpCustNum(string $erpCustomerNum):static
    {
        // TODO: Implement setErpCustomerNum() method.
        $this->setData(self::ERP_CUST_NUM, $erpCustomerNum);
        return $this;
    }

    /**
     * @return string|null
     */
    public function getErpCustNum(): string|null
    {
        // TODO: Implement getErpCustomerNum() method.
        return $this->getData(self::ERP_CUST_NUM);
    }

    /**
     * @param string $shipToNum
     * @return ErpCompanyShipToInterface
     */
    public function setShipToNum(string $shipToNum): static
    {
        // TODO: Implement setShipToNum() method.
        $this->setData(self::SHIP_TO_NUM, $shipToNum);
        return $this;
    }

    /**
     * @return string|null
     */
    public function getShipToNum(): ?string
    {
        // TODO: Implement getShipToNum() method.
        return $this->getData(self::SHIP_TO_NUM);
    }

    /**
     * @param string $email
     * @return $this
     */
    public function setEmail(string $email): static
    {
        // TODO: Implement setEmail() method.
        $this->setData(self::EMAIL, $email);
        return $this;
    }

    /**
     * @return string|null
     */
    public function getEmail(): ?string
    {
        // TODO: Implement getEmail() method.
        return $this->getData(self::EMAIL);
    }

    /**
     * @param string $regionCode
     * @return $this
     */
    public function setRegionCode(string $regionCode): static
    {
        // TODO: Implement setRegionCode() method.
        $this->setData(self::REGION_CODE, $regionCode);
        return $this;
    }

    /**
     * @return string|null
     */
    public function getRegionCode(): ?string
    {
        // TODO: Implement getRegionCode() method.
        return $this->getData(self::REGION_CODE);
    }

    /**
     * @param string $street
     * @return $this
     */
    public function setStreet(string $street): static
    {
        // TODO: Implement setStreet() method.
        $this->setData(self::STREET, $street);
        return $this;
    }

    /**
     * @return string|null
     */
    public function getStreet(): ?string
    {
        // TODO: Implement getStreet() method.
        return $this->getData(self::STREET);
    }

    /**
     * @param int $company_id
     * @return $this
     */
    public function setCompanyId(int $company_id): static
    {
        // TODO: Implement setCompany() method.
        $this->setData(self::COMPANY_ID, $company_id);
        return $this;
    }

    /**
     * @return int|null
     */
    public function getCompanyId(): ?int
    {
        // TODO: Implement getCompany() method.
        return $this->getData(self::COMPANY_ID);

    }

    /**
     * @param string $telephone
     * @return $this
     */
    public function setTelephone(string $telephone): static
    {
        // TODO: Implement setTelephone() method.
        $this->setData(self::TELEPHONE, $telephone);
        return $this;
    }

    /**
     * @return string|null
     */
    public function getTelephone(): ?string
    {
        // TODO: Implement getTelephone() method.
        return $this->getData(self::TELEPHONE);
    }

    /**
     * @param string $postcode
     * @return $this
     */
    public function setPostcode(string $postcode): static
    {
        // TODO: Implement setPostcode() method.
        $this->setData(self::POSTCODE, $postcode);
        return $this;
    }

    /**
     * @return string|null
     */
    public function getPostcode(): ?string
    {
        // TODO: Implement getPostcode() method.
        return $this->getData(self::POSTCODE);
    }

    /**
     * @param string $city
     * @return $this
     */
    public function setCity(string $city): static
    {
        // TODO: Implement setCity() method.
        $this->setData(self::CITY, $city);
        return $this;
    }

    /**
     * @return string|null
     */
    public function getCity(): ?string
    {
        // TODO: Implement getCity() method.
        return $this->getData(self::CITY);
    }

    /**
     * @param ?int $region_id
     * @return $this
     */
    public function setRegionId(?int $region_id): static
    {
        // TODO: Implement setRegion() method.
        $this->setData(self::REGION_ID, $region_id);
        return $this;
    }

    /**
     * @return int|null
     */
    public function getRegionId(): ?int
    {
        // TODO: Implement getRegion() method.
        return $this->getData(self::REGION_ID);
    }

    /**
     * @param string $countryId
     * @return $this
     */
    public function setCountryId(string $countryId): static
    {
        // TODO: Implement setCountry() method.
        $this->setData(self::COUNTRY_ID,  strtoupper( $countryId));
        return $this;
    }

    /**
     * @return string|null
     */
    public function getCountryId(): ?string
    {
        // TODO: Implement getCountry() method.
        return $this->getData(self::COUNTRY_ID);
    }

    /**
     * @param string $locationId
     * @return $this
     */
    public function setLocationId(string $locationId): static
    {
        // TODO: Implement setLocationId() method.
        $this->setData(self::LOCATION_ID,  strtoupper( $locationId));
        return $this;
    }

    /**
     * @return string|null
     */
    public function getLocationId(): ?string
    {
        // TODO: Implement getLocationId() method.
        return $this->getData(self::LOCATION_ID);
    }


    /**
     * @return \Magento\Framework\Api\ExtensionAttributesInterface|null
     */
    public function getExtensionAttributes()
    {

        return $this->_getExtensionAttributes();
    }

    /**
     * @inheritdoc
     */
    public function setExtensionAttributes(\Silksoftwarecorp\CompanyB2b\Api\Data\ErpCompanyShipToExtensionInterface $extensionAttributes)
    {
        return $this->_setExtensionAttributes($extensionAttributes);
    }

    /**
     * @inheritDoc
     */
    public function setShipToName(string $shipToName): static
    {
        $this->setData(self::SHIP_TO_NAME, $shipToName);
        return $this;
    }

    /**
     * @inheritDoc
     */
    public function getShipToName(): ?string
    {
        return $this->getData(self::SHIP_TO_NAME);
    }

    /**
     * @inheritDoc
     */
    public function setStreetLine2(string $street): static
    {
        $this->setData(self::STREET_LINE_2, $street);
        return $this;
    }

    /**
     * @inheritDoc
     */
    public function getStreetLine2(): ?string
    {
        return $this->getData(self::STREET_LINE_2);
    }

    /**
     * @inheritDoc
     */
    public function setStreetLine3(string $street): static
    {
        $this->setData(self::STREET_LINE_3, $street);
        return $this;
    }

    /**
     * @inheritDoc
     */
    public function getStreetLine3(): ?string
    {
        return $this->getData(self::STREET_LINE_3);
    }

    /**
     * @inheritDoc
     */
    public function setIsDefault(bool $isDefault): static
    {
        $this->setData(self::IS_DEFAULT, $isDefault);
        return $this;
    }

    /**
     * @inheritDoc
     */
    public function getIsDefault(): bool
    {
        return (bool) $this->getData(self::IS_DEFAULT);
    }
}
