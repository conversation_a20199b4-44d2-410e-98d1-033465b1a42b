import styled from '@emotion/styled'

export const StyledSorters = styled.div`
  display: grid;
  grid-template-columns: auto 188px auto;
  align-items: center;

  .sort-label {
    margin-right: 8px;
    font-weight: 400;
    font-size: 15px;
    line-height: 20px;
    letter-spacing: 0;
  }

  .sort-select {
    width: 188px !important;
    height: 38px !important;

    .${({ theme }) => theme.namespace} {
      &-select-selector {
        padding: 0 0 0 15px !important;
        border: 1px solid #D9D9D9 !important;
        border-radius: 3px !important;
        font-weight: 400;
        font-size: 15px;
        line-height: 20px;
        letter-spacing: 0;
        color: var(--color-font);
      }
      &-select-arrow {
        right: 15px !important;
      }
    }
  }

  .sort-icon {
    margin-left: 4px;
    margin-right: -3px;
    width: 20px;
    height: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: all 0.3s;
    cursor: pointer;

    &.is-desc {
      transform: rotate(180deg);
    }
  }
`

export const StyledMobileSorters = styled.div`
  padding-bottom: 70px;

  .mb-sort-header {
    display: grid;
    grid-template-columns: 1fr auto;
    grid-column-gap: 10px;
    align-items: center;
    padding: 14px 16px;
    border-bottom: 1px solid #d9d9d9;

    h3 {
      margin: 0;
      font-weight: 700;
      font-size: 24px;
      line-height: 30px;
      letter-spacing: 0;
    }

    .close-btn {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 48px;
      height: 48px;
      border-radius: 50%;
      background-color: var(--color-bg-base);
    }
  }

  .apply-sort {
    position: fixed;
    left: 0;
    bottom: 0;
    z-index: 1;
    width: 100%;
    height: 53px;
    border-radius: 0;
    border: none;
    background: var(--color-primary) !important;

    span {
      font-weight: 700;
      font-size: 16px;
      line-height: 21px;
      letter-spacing: 0.03em;
      color: var(--color-white);
    }
  }
`

export const StyledMobileSortersList = styled.div`
  padding: 16px 0;
  display: flex;
  flex-direction: column;

  &#mobile-sorters-list {
    .${({ theme }) => theme.namespace}-space {
      row-gap: 12px;
    }
    .${({ theme }) => theme.namespace}-radio {
      align-self: initial;
      top: 3px;

      &-group {
        display: block;
        font-size: inherit;
        padding: 0 16px;
      }

      &-wrapper {
        display: inline-flex;

        h3 {
          margin-bottom: 0;
          font-weight: 400;
          font-size: 15px;
          line-height: 25px;
          letter-spacing: 0.01em;
        }
      }

      &-inner {
        width: 18px;
        height: 18px;
        background-color: var(--color-white) !important;

        &::after {
          width: 10px;
          height: 10px;
          margin-block-start: -5px;
          margin-inline-start: -5px;
          background: var(--color-primary);
        }
      }

      &-checked {
        .${({ theme }) => theme.namespace}-radio-inner {
          border: 2px solid var(--color-primary) !important;

          &::after {
            transform: scale(1) !important;
          }
        }
      }
    }

    .mb-sort-direction {
      padding-top: 18px;
      margin-top: 18px;
      border-top: 1px solid #d9d9d9;

      .mb-direction-icon {
        margin-top: -4px;

        &.is-desc {
          transform: rotate(180deg);
        }
      }
    }
  }
`
