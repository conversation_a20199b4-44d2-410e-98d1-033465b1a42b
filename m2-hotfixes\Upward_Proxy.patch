diff --git a/vendor/magento/upward/src/Resolver/Proxy.php b/vendor/magento/upward/src/Resolver/Proxy.php
index 9953fe9..7d29929 100644
--- a/vendor/magento/upward/src/Resolver/Proxy.php
+++ b/vendor/magento/upward/src/Resolver/Proxy.php
@@ -52,11 +52,28 @@ class Proxy extends AbstractResolver
         $request            = new \Laminas\Http\PhpEnvironment\Request();
         $originalRequestURI = clone $request->getUri();
         $request->setUri($target);
-        $request->getUri()->setPath($originalRequestURI->getPath())->setQuery($originalRequestURI->getQuery());
+        $request->getUri()->setPath($originalRequestURI->getPath());
         $requestHeaders = $request->getHeaders();
-        if ($requestHeaders && $requestHeaders->has('Host')) {
-            $requestHeaders->removeHeader($request->getHeader('Host'));
-            $requestHeaders->addHeaderLine('Host', parse_url($target, \PHP_URL_HOST));
+        if ($requestHeaders) {
+            if($requestHeaders->has('Host')){
+                $origionalHost = $request->getHeader('Host');
+                $requestHeaders->removeHeader($origionalHost);
+
+                //Keep original host as original-host header
+                $scheme = (($request->getEnv('HTTP_HTTPS') == "on") || ($request->getEnv('HTTPS') == "on")) ? 'https': 'https';
+                $requestHeaders->addHeaderLine(
+                    'origin-host',
+                    sprintf('%s://%s', $scheme, $origionalHost->getFieldValue())
+                );
+                $requestHeaders->addHeaderLine('Host', parse_url($target, \PHP_URL_HOST));
+            }
+            if( $originalPath = $originalRequestURI->getPath()){
+                if(preg_match('/login/',$originalPath) || $originalPath == '/'){
+                }else{
+                    $requestHeaders->addHeaderLine('origin-uri', $originalPath);
+                }
+
+            }
         }

         $client = new Client(null, [
@@ -65,6 +82,9 @@ class Proxy extends AbstractResolver
                 \CURLOPT_SSL_VERIFYHOST => $ignoreSSLErrors ? 0 : 2,
                 \CURLOPT_SSL_VERIFYPEER => !$ignoreSSLErrors,
+                \CURLOPT_TIMEOUT => 0,
             ],
+            'maxredirects' => -1,
+            'timeout' => 0,
         ]);

         return $client->send($request);
