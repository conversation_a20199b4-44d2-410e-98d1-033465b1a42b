<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */

namespace Silksoftwarecorp\Inventory\Ui\Component\Form;

use Magento\Framework\Data\OptionSourceInterface;

/**
 * Class ScheduleStatus
 */
class ScheduleStatus implements OptionSourceInterface
{
    public const OPEN_STATUS = 1;

    public const CLOSE_STATUS = 0;

    /**
     * Getting schedule status options
     *
     * @return array
     */
    public function toOptionArray()
    {
        return [
            ['value' => self::OPEN_STATUS, 'label' => __('Open')],
            ['value' => self::CLOSE_STATUS, 'label' => __('Closed')]
        ];
    }
}
