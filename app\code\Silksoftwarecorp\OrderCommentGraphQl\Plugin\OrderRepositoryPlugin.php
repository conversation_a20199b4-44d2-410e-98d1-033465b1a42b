<?php
namespace Silksoftwarecorp\OrderCommentGraphQl\Plugin;

use Magento\Sales\Api\Data\OrderExtensionFactory;
use Magento\Sales\Api\OrderRepositoryInterface;

class OrderRepositoryPlugin
{
    private $extensionFactory;

    public function __construct(OrderExtensionFactory $extensionFactory)
    {
        $this->extensionFactory = $extensionFactory;
    }

    public function afterGet(OrderRepositoryInterface $subject, $order)
    {
        $extensionAttributes = $order->getExtensionAttributes();
        if ($extensionAttributes === null) {
            $extensionAttributes = $this->extensionFactory->create();
        }
        $extensionAttributes->setSpecialNotes($order->getData('special_notes'));
        $order->setExtensionAttributes($extensionAttributes);
        return $order;
    }

    public function afterGetList(OrderRepositoryInterface $subject, $orders)
    {
        foreach ($orders->getItems() as $order) {
            $this->afterGet($subject, $order);
        }
        return $orders;
    }

    public function beforeSave(OrderRepositoryInterface $subject, $order)
    {
        $extensionAttributes = $order->getExtensionAttributes();
        if ($extensionAttributes && $extensionAttributes->getSpecialNotes() !== null) {
            $order->setData('special_notes', $extensionAttributes->getSpecialNotes());
        }
        return [$order];
    }
} 