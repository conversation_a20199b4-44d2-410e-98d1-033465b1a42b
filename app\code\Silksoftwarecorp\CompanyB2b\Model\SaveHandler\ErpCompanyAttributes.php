<?php

namespace Silksoftwarecorp\CompanyB2b\Model\SaveHandler;
use Magento\Company\Api\Data\CompanyInterface;
use Magento\Company\Model\SaveHandlerInterface;
use Magento\Framework\Exception\AlreadyExistsException;
use Silksoftwarecorp\CompanyB2b\Api\Data\CompanyCustomAttributesInterface;
class ErpCompanyAttributes implements SaveHandlerInterface
{

    public function __construct(
        private  \Magento\Company\Model\ResourceModel\Company $companyResource,
    )
    {

    }

    /**
     * @throws AlreadyExistsException
     */
    public function execute(CompanyInterface $company, CompanyInterface $initialCompany): void
    {
        //return;
        // TODO: Implement execute() method.
        /**
         * @var CompanyCustomAttributesInterface $erpAttributes
         */
        $erpAttributes = $company->getExtensionAttributes()->getErpAttributes();
        if($erpAttributes) {
            if (
                $erpAttributes->getErpCustNum() !== $initialCompany->getData(CompanyCustomAttributesInterface::ERP_CUST_NUM)
                || $erpAttributes->getErpCustCode() !== $initialCompany->getData(CompanyCustomAttributesInterface::ERP_CUST_CODE)
                || $erpAttributes->getErpConNum() !== $initialCompany->getData(CompanyCustomAttributesInterface::ERP_CON_NUM)
            ){
             $company->setData(CompanyCustomAttributesInterface::ERP_CUST_NUM, $erpAttributes->getErpCustNum());
             $company->setData(CompanyCustomAttributesInterface::ERP_CUST_CODE, $erpAttributes->getErpCustCode());
             $company->setData(CompanyCustomAttributesInterface::ERP_CON_NUM,$erpAttributes->getErpConNum());
             $this->companyResource->save($company);
            }
         }

    }
}
