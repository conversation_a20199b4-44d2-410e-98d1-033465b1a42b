type Company {
    all_users(
        filter: CompanyUsersFilterInput @doc(description: "The type of company users to return.")
    ): CompanyUsers @resolver(class: "Silksoftwarecorp\\CompanyGraphQl\\Model\\Resolver\\Company\\AllUsers") @doc(description: "An object that contains a list of all company users without pagination.")
    erp_customer_id: String @resolver(class: "Silksoftwarecorp\\CompanyGraphQl\\Model\\Resolver\\Company\\ErpCustomerId") @doc(description: "The erp_customer_id of the company.")
    erp_credit_status: String @resolver(class: "Silksoftwarecorp\\CompanyGraphQl\\Model\\Resolver\\Company\\ErpCreditStatus") @doc(description: "The erp_credit_status of the company.")
}

type CompanyBasicInfo {
    erp_customer_id: String @resolver(class: "Silksoftwarecorp\\CompanyGraphQl\\Model\\Resolver\\Company\\ErpCustomerId") @doc(description: "The erp_customer_id of the company.")
    erp_credit_status: String @resolver(class: "Silksoftwarecorp\\CompanyGraphQl\\Model\\Resolver\\Company\\ErpCreditStatus") @doc(description: "The erp_credit_status of the company.")
}
