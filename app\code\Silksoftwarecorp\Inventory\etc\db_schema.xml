<?xml version="1.0"?>
<!--
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */
-->
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="inventory_source">
        <!-- General fields -->
        <column xsi:type="smallint" name="install_request_form" unsigned="true" nullable="false"
                identity="false" default="0" comment="Install Request form (Fast Fax)"/>
        <column xsi:type="smallint" name="install_app" unsigned="true" nullable="false"
                identity="false" default="0" comment="Install App"/>

        <!-- Contact info fields -->
        <column xsi:type="varchar" name="text_number" nullable="true" length="255" comment="Text Number"/>

        <!-- Address fields -->
        <column xsi:type="varchar" name="street1" nullable="true" length="255" comment="Street Line 1"/>

        <!-- Schedule fields -->
        <column xsi:type="text" name="schedule" nullable="true" comment="Hours of Operation"/>
        <column xsi:type="text" name="holiday_closures" nullable="true" comment="Holiday Closures"/>
    </table>

    <table name="inventory_source_branch_manager_profile" resource="default" engine="innodb">
        <column xsi:type="int" name="id" unsigned="true" nullable="false" identity="true" comment="Manager Profile ID"/>
        <column xsi:type="varchar" name="source_code" nullable="false" length="255" comment="Source Code"/>
        <column xsi:type="smallint" name="is_general" unsigned="true" nullable="false" identity="false"
                default="0" comment="Is General Manager (1) or Territory Manager (0)"/>
        <column xsi:type="varchar" name="image" nullable="true" length="255" comment="Manager Image"/>
        <column xsi:type="varchar" name="name" nullable="true" length="255" comment="Manager Name"/>
        <column xsi:type="varchar" name="phone" nullable="true" length="255" comment="Manager Phone"/>
        <column xsi:type="text" name="region" nullable="true" comment="Manager Region"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="id"/>
        </constraint>
        <constraint xsi:type="foreign" referenceId="INV_SOURCE_BRANCH_MGR_PROFILE_SOURCE_CODE_INV_SOURCE_SOURCE_CODE"
                    table="inventory_source_branch_manager_profile" column="source_code" referenceTable="inventory_source"
                    referenceColumn="source_code" onDelete="CASCADE"/>
        <index referenceId="INV_SOURCE_BRANCH_MGR_PROFILE_SOURCE_CODE" indexType="btree">
            <column name="source_code"/>
        </index>
        <index referenceId="INV_SOURCE_BRANCH_MGR_PROFILE_IS_GENERAL" indexType="btree">
            <column name="is_general"/>
        </index>
    </table>
</schema>
