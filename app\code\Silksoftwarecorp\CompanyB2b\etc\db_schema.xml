<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="company" resource="default">
        <column xsi:type="varchar" name="erp_cust_num" nullable="true" length="255" comment="Cust Num"/>
        <column xsi:type="varchar" name="erp_cust_code" nullable="true" length="255" comment="Cust ID"/>
        <column xsi:type="varchar" name="erp_con_num" nullable="true" length="255" comment="Cust ID"/>
    </table>
    <table name="company_shipto" resource="default" engine="innodb" comment="Company Ship To">
        <column xsi:type="int" name="entity_id" unsigned="true" nullable="false" identity="true"
                comment="Id"/>
        <column xsi:type="int" name="erp_cust_num" unsigned="false" nullable="false" identity="false"
                comment="Erp Cust Num"/>
        <column xsi:type="varchar" name="ship_to_num"  nullable="false"
                comment="Ship To Num"/>
        <column xsi:type="varchar" name="ship_to_name"  nullable="true"
                comment="Ship To Name"/>
        <column xsi:type="varchar" name="email" nullable="true" length="255" comment="Email Address"/>
        <column xsi:type="varchar" name="region_code" nullable="true" length="255" comment="Region Code"/>
        <column xsi:type="varchar" name="street" nullable="true" length="255" comment="Street"/>
        <column xsi:type="varchar" name="street_line2" nullable="true" length="255" comment="Street line 2"/>
        <column xsi:type="varchar" name="street_line3" nullable="true" length="255" comment="Street line 3"/>
        <column xsi:type="int" unsigned="true" name="company_id" nullable="false"  comment="Company"/>
        <column xsi:type="varchar" name="telephone" nullable="true" length="255" comment="Telephone"/>
        <column xsi:type="varchar" name="postcode" nullable="true" length="255" comment="Post Code"/>
        <column xsi:type="varchar" name="city" nullable="true" length="255" comment="City"/>
        <column xsi:type="int" unsigned="true" name="region_id"  nullable="false"  comment="Region"/>
        <column xsi:type="varchar" name="country_id"   nullable="false" comment="Country"/>
        <column xsi:type="varchar" name="location_id"   nullable="false" comment="Location ID"/>
        <column xsi:type="boolean" name="is_default" nullable="false" default="0" comment="Is Default Ship To"/>
        <column xsi:type="timestamp" name="created_at" on_update="false" nullable="false" default="CURRENT_TIMESTAMP"
                comment="Created At"/>
        <column xsi:type="timestamp" name="updated_at" on_update="true" nullable="false" default="CURRENT_TIMESTAMP"
                comment="Updated At"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="entity_id"/>
        </constraint>
        <index referenceId="COMPANY_SHIP_TO_INX_CUST_NUM" indexType="btree">
            <column name="erp_cust_num"/>
        </index>
        <index referenceId="COMPANY_SHIP_TO_INX_EMAIL" indexType="btree">
            <column name="email"/>
        </index>
        <constraint xsi:type="foreign" referenceId="ERP_SHIP_TO_COMPANY_ID" table="company_shipto" column="company_id"
                    referenceTable="company" referenceColumn="entity_id" onDelete="CASCADE"/>
    </table>

</schema>
