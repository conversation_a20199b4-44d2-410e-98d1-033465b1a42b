<?php

namespace Silksoftwarecorp\CustomerGraphQl\Model\Resolver\Customer;

use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;

class Type implements ResolverInterface
{

    /**
     * @inheritDoc
     */
    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
        if (!isset($value['model'])) {
            throw new LocalizedException(__('"model" value should be specified'));
        }

        $customer = $value['model'];
        $companyAttributes = $customer->getExtensionAttributes()?->getCompanyAttributes();
        if ($companyAttributes &&
            $companyAttributes->getCompanyId() > 0) {
            $customerType = 'COMPANY_USER';
        } else {
            $customerType = 'INDIVIDUAL_USER';
        }

        return $customerType;
    }
}
