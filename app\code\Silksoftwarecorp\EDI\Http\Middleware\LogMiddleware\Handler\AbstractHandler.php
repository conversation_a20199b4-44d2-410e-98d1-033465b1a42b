<?php

namespace Silksoftwarecorp\EDI\Http\Middleware\LogMiddleware\Handler;

use Silksoftwarecorp\EDI\Http\Middleware\LogMiddleware\Handler\LogLevelStrategy\FixedStrategy;
use Silksoftwarecorp\EDI\Http\Middleware\LogMiddleware\Handler\LogLevelStrategy\LogLevelStrategyInterface;

/**
 * <AUTHOR> <<EMAIL>>
 */
abstract class AbstractHandler implements HandlerInterface
{
    /**
     * @var LogLevelStrategyInterface
     */
    protected $logLevelStrategy;

    protected function getDefaultStrategy(): LogLevelStrategyInterface
    {
        return new FixedStrategy();
    }
}
