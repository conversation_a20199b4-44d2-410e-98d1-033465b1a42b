<?php

namespace Silksoftwarecorp\RealTimeB2BPricing\Observer;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Silksoftwarecorp\RealTimeB2BPricing\Model\Quote\QuoteUpdateExpiredPriceInterface;

class QuoteUpdateExpiredPriceObserver implements ObserverInterface
{
    /**
     * @var QuoteUpdateExpiredPriceInterface
     */
    protected $quoteUpdateExpiredPrice;

    public function __construct(
        QuoteUpdateExpiredPriceInterface $quoteUpdateExpiredPrice
    ) {
        $this->quoteUpdateExpiredPrice = $quoteUpdateExpiredPrice;
    }

    public function execute(Observer $observer)
    {
        $quote = $observer->getEvent()->getQuote();
        $this->quoteUpdateExpiredPrice->execute($quote);
    }
}
