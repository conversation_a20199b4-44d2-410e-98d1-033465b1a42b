import { gql } from '@apollo/client'
import type { DocumentNode } from '@apollo/client'

import { cartItems } from '../fragment/cartItems'
import { cart_prices } from '../fragment/cartPrices'

export const GET_CHECKOUT_CART: DocumentNode = gql`
  query getCheckoutCart {
    cart: customerCart {
      quantity: total_quantity
      email
      prices {
        ...cart_prices
        __typename
      }
      coupons: applied_coupons {
        code
      }
      ...cartItems
      shipping_addresses {
        city
        company
        firstname
        lastname
        postcode
        street
        telephone
        country {
          code
          label
        }
        region {
          region_id
          label
        }
      }
      __typename
    }
  }
  ${cartItems}
  ${cart_prices}
`
