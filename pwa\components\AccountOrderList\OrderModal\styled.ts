import styled from '@emotion/styled'
import { Modal } from 'antd'

export const ModalTitle = styled.div`
  .modal__title {
    display: inline-block;
    padding-top: 0.9rem;
    margin-right: 1rem;
    vertical-align: middle;
  }

  .${({ theme }) => theme.namespace}-tag {
    padding: 0.2rem 0.5rem;
    text-transform: uppercase;
    vertical-align: middle;
    border-radius: 3px;
  }

  .modal__date {
    margin: 0.5rem 0 0 0;
  }

  .order__modal--actions {
    display: flex;
    justify-content: space-between;

    .modal__link {
      padding-left: 0;
    }
  }

  @media print {
    .order__modal--actions {
      display: none;
    }
  }

  @media screen and (max-width: ${({ theme }) => theme.breakPoint.m}px) {
    .modal__title {
      font-size: 1.2rem;
    }

    .modal__link {
      padding-left: 0;
    }
  }
`

export const ModalContent = styled(Modal)`
  @media print {
    .ant-modal-close {
      display: none;
    }
  }
`

export const OrderModalWrap = styled.div`
  min-height: 10rem;
`
