<?php
/**
 *
 * @package     Silklab_LoginAsCustomer
 * @copyright   Copyright © 2021 Silk Software Corp (https://www.silksoftware.com)
 * <AUTHOR> <EMAIL>
 */

declare(strict_types=1);

namespace Silklab\LoginAsCustomer\Controller\Adminhtml\Login;

use Magento\Backend\App\Action\Context;
use Magento\Backend\Model\Auth\Session;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Customer\Model\Config\Share;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\App\ObjectManager;
use Magento\Framework\Controller\Result\Json as JsonResult;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Url;
use Magento\LoginAsCustomerApi\Api\ConfigInterface;
use Magento\LoginAsCustomerApi\Api\Data\AuthenticationDataInterface;
use Magento\LoginAsCustomerApi\Api\Data\AuthenticationDataInterfaceFactory;
use Magento\LoginAsCustomerApi\Api\DeleteAuthenticationDataForUserInterface;
use Magento\LoginAsCustomerApi\Api\IsLoginAsCustomerEnabledForCustomerInterface;
use Magento\LoginAsCustomerApi\Api\SaveAuthenticationDataInterface;
use Magento\LoginAsCustomerApi\Api\SetLoggedAsCustomerCustomerIdInterface;
use Magento\LoginAsCustomerApi\Api\GenerateAuthenticationSecretInterface;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Store\Model\StoreSwitcher\ManageStoreCookie;

use Magento\LoginAsCustomerAdminUi\Controller\Adminhtml\Login\Login as LoginAsCustomerLogin;

use Magento\Integration\Model\Oauth\TokenFactory as TokenModelFactory;
use Magento\Framework\App\Config\ScopeConfigInterface;

class Login extends LoginAsCustomerLogin{
    /**
     * @var Session
     */
    private $authSession;
    
    /**
     * @var StoreManagerInterface
     */
    private $storeManager;
    
    /**
     * @var CustomerRepositoryInterface
     */
    private $customerRepository;
    
    /**
     * @var ConfigInterface
     */
    private $config;
    
    /**
     * @var AuthenticationDataInterfaceFactory
     */
    private $authenticationDataFactory;
    
    /**
     * @var SaveAuthenticationDataInterface
     */
    private $saveAuthenticationData;
    
    /**
     * @var DeleteAuthenticationDataForUserInterface
     */
    private $deleteAuthenticationDataForUser;
    
    /**
     * @var Url
     */
    private $url;
    
    /**
     * @var Share
     */
    private $share;
    
    /**
     * @var ManageStoreCookie
     */
    private $manageStoreCookie;
    
    /**
     * @var SetLoggedAsCustomerCustomerIdInterface
     */
    private $setLoggedAsCustomerCustomerId;
    
    /**
     * @var IsLoginAsCustomerEnabledForCustomerInterface
     */
    private $isLoginAsCustomerEnabled;
    
    /**
     * @var GenerateAuthenticationSecretInterface
     */
    private $generateAuthenticationSecret;
    
    /**
     * @var TokenFactory
     */
    private $tokenModelFactory;
    
    /**
     * @var \Magento\Framework\App\Config\ScopeConfigInterface
     */
    protected $scopeConfig;
    
    const XML_PATH_REDIRECT_URL = 'login_as_customer/general/base_redirect_url';
    
    /**
     * @param Context $context
     * @param Session $authSession
     * @param StoreManagerInterface $storeManager
     * @param CustomerRepositoryInterface $customerRepository
     * @param ConfigInterface $config
     * @param AuthenticationDataInterfaceFactory $authenticationDataFactory
     * @param SaveAuthenticationDataInterface $saveAuthenticationData
     * @param DeleteAuthenticationDataForUserInterface $deleteAuthenticationDataForUser
     * @param Url $url
     * @param TokenModelFactory $tokenModelFactory
     * @param ScopeConfigInterface $scopeConfig
     * @param Share|null $share
     * @param ManageStoreCookie|null $manageStoreCookie
     * @param SetLoggedAsCustomerCustomerIdInterface|null $setLoggedAsCustomerCustomerId
     * @param IsLoginAsCustomerEnabledForCustomerInterface|null $isLoginAsCustomerEnabled
     * @param GenerateAuthenticationSecretInterface|null $generateAuthenticationSecret
     * @SuppressWarnings(PHPMD.ExcessiveParameterList)
     */
    public function __construct(
        Context $context,
        Session $authSession,
        StoreManagerInterface $storeManager,
        CustomerRepositoryInterface $customerRepository,
        ConfigInterface $config,
        AuthenticationDataInterfaceFactory $authenticationDataFactory,
        SaveAuthenticationDataInterface $saveAuthenticationData,
        DeleteAuthenticationDataForUserInterface $deleteAuthenticationDataForUser,
        Url $url,
        TokenModelFactory $tokenModelFactory,
        ScopeConfigInterface $scopeConfig,
        ?Share $share = null,
        ?ManageStoreCookie $manageStoreCookie = null,
        ?SetLoggedAsCustomerCustomerIdInterface $setLoggedAsCustomerCustomerId = null,
        ?IsLoginAsCustomerEnabledForCustomerInterface $isLoginAsCustomerEnabled = null,
        ?GenerateAuthenticationSecretInterface $generateAuthenticationSecret = null
        ) {
            $this->tokenModelFactory = $tokenModelFactory;
            $this->scopeConfig = $scopeConfig;
            
            $this->authSession = $authSession;
            $this->storeManager = $storeManager;
            $this->customerRepository = $customerRepository;
            $this->config = $config;
            $this->authenticationDataFactory = $authenticationDataFactory;
            $this->saveAuthenticationData = $saveAuthenticationData;
            $this->deleteAuthenticationDataForUser = $deleteAuthenticationDataForUser;
            $this->url = $url;
            $this->share = $share ?? ObjectManager::getInstance()->get(Share::class);
            $this->manageStoreCookie = $manageStoreCookie ?? ObjectManager::getInstance()->get(ManageStoreCookie::class);
            $this->setLoggedAsCustomerCustomerId = $setLoggedAsCustomerCustomerId
            ?? ObjectManager::getInstance()->get(SetLoggedAsCustomerCustomerIdInterface::class);
            $this->isLoginAsCustomerEnabled = $isLoginAsCustomerEnabled
            ?? ObjectManager::getInstance()->get(IsLoginAsCustomerEnabledForCustomerInterface::class);
            $this->generateAuthenticationSecret = $generateAuthenticationSecret
            ?? ObjectManager::getInstance()->get(GenerateAuthenticationSecretInterface::class);
            
            parent::__construct($context,$authSession,$storeManager,$customerRepository,$config,$authenticationDataFactory,$saveAuthenticationData,$deleteAuthenticationDataForUser,$url,
                $share,$manageStoreCookie,$setLoggedAsCustomerCustomerId,$isLoginAsCustomerEnabled,$generateAuthenticationSecret);
    }
    
    /**
     * Login as customer
     *
     * @return ResultInterface
     * @throws NoSuchEntityException
     * @throws LocalizedException
     */
    public function execute(): ResultInterface
    {
        $messages = [];
        
        $customerId = (int)$this->_request->getParam('customer_id');
        if (!$customerId) {
            $customerId = (int)$this->_request->getParam('entity_id');
        }
        
        $isLoginAsCustomerEnabled = $this->isLoginAsCustomerEnabled->execute($customerId);
        if (!$isLoginAsCustomerEnabled->isEnabled()) {
            foreach ($isLoginAsCustomerEnabled->getMessages() as $message) {
                $messages[] = __($message);
            }
            
            return $this->prepareJsonResult($messages);
        }
        
        try {
            $customer = $this->customerRepository->getById($customerId);
        } catch (NoSuchEntityException $e) {
            $messages[] = __('Customer with this ID no longer exists.');
            return $this->prepareJsonResult($messages);
        }
        
        if ($this->config->isStoreManualChoiceEnabled()) {
            $storeId = (int)$this->_request->getParam('store_id');
            if (empty($storeId)) {
                $messages[] = __('Please select a Store View to login in.');
                return $this->prepareJsonResult($messages);
            }
        } elseif ($this->share->isGlobalScope()) {
            $storeId = (int)$this->storeManager->getDefaultStoreView()->getId();
        } else {
            $storeId = (int)$customer->getStoreId();
        }
        
        $adminUser = $this->authSession->getUser();
        $userId = (int)$adminUser->getId();
        
        /** @var AuthenticationDataInterface $authenticationData */
        $authenticationData = $this->authenticationDataFactory->create(
            [
                'customerId' => $customerId,
                'adminId' => $userId,
                'extensionAttributes' => null,
            ]
            );
        
        $this->deleteAuthenticationDataForUser->execute($userId);
        $this->saveAuthenticationData->execute($authenticationData);
        $this->setLoggedAsCustomerCustomerId->execute($customerId);
        
        $secret = $this->generateAuthenticationSecret->execute($authenticationData);
        $redirectUrl = $this->getLoginProceedRedirectUrl($secret, $storeId, $customer);
        
        return $this->prepareJsonResult($messages, $redirectUrl);
    }
    
    /**
     * Get login proceed redirect url
     *
     * @param string $secret
     * @param int $storeId
     * @return string
     * @throws NoSuchEntityException
     */
    protected function getLoginProceedRedirectUrl(string $secret, int $storeId, $customer): string
    {
        $targetStore = $this->storeManager->getStore($storeId);
        $queryParameters = ['secret' => $secret];
        $redirectUrl = $this->url
        ->setScope($targetStore)
        ->getUrl('loginascustomer/login/index', ['_query' => $queryParameters, '_nosid' => true]);
        
        if (!$targetStore->isUseStoreInUrl()) {
            $fromStore = $this->storeManager->getStore();
            $redirectUrl = $this->manageStoreCookie->switch($fromStore, $targetStore, $redirectUrl);
        }
        
        $adminUser = $this->authSession->getUser();
        $adminToken = $this->tokenModelFactory->create()->createAdminToken($adminUser->getId())->getToken();
        $redirectUrl .= '&token='.$adminToken.'&email='.$customer->getEmail();
        
        //replace the redirect url's base url path if there are settings for different redirect url settings for front store (might be the pwa front store)
        if($baseRedirectUrl = $this->getBaseRedirectUrl($storeId)){
            $redirectUrlBasePath = $this->url->setScope($targetStore)->getUrl('');
            $redirectUrl = str_replace($redirectUrlBasePath, $baseRedirectUrl, $redirectUrl);
        }
        return $redirectUrl;
    }
    
    /**
     * Prepare JSON result
     *
     * @param array $messages
     * @param string|null $redirectUrl
     * @return JsonResult
     */
    protected function prepareJsonResult(array $messages, ?string $redirectUrl = null)
    {
        /** @var JsonResult $jsonResult */
        $jsonResult = $this->resultFactory->create(ResultFactory::TYPE_JSON);
        
        $jsonResult->setData([
            'redirectUrl' => $redirectUrl,
            'messages' => $messages,
        ]);
        
        return $jsonResult;
    }
    
    private function getBaseRedirectUrl($storeId = null)
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_REDIRECT_URL,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
            $storeId
            );
    }
}