import styled from '@emotion/styled'

export const StyledGoogleMapMarkerPage = styled.div`
  position: relative;
  width: 100%;

  & > div {
    width: 100% !important;
  }

  .location-popup {
    padding: 0 10px 10px;
    width: 300px;

    .location-item {
      a {
        word-break: break-all;
      }
    }
  }

  @media (max-width: 768px) {
    height: 402px;

    .location-popup {
      padding: 0 10px 10px;
      max-width: 100%;
    }

    .gm-style-iw {
      max-width: 90% !important;
    }
  }
`
