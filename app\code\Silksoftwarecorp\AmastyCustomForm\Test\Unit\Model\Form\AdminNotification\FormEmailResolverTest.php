<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */

namespace Silksoftwarecorp\AmastyCustomForm\Test\Unit\Model\Form\AdminNotification;

use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Magento\Framework\Serialize\Serializer\Json;
use Psr\Log\LoggerInterface;
use Silksoftwarecorp\AmastyCustomForm\Helper\Data as Helper;
use Silksoftwarecorp\AmastyCustomForm\Model\Form\AdminNotification\FormEmailResolver;

class FormEmailResolverTest extends TestCase
{
    /**
     * @var FormEmailResolver
     */
    private $formEmailResolver;

    /**
     * @var Helper|MockObject
     */
    private $helperMock;

    /**
     * @var Json|MockObject
     */
    private $serializerMock;

    /**
     * @var LoggerInterface|MockObject
     */
    private $loggerMock;

    protected function setUp(): void
    {
        $this->helperMock = $this->createMock(Helper::class);
        $this->serializerMock = $this->createMock(Json::class);
        $this->loggerMock = $this->createMock(LoggerInterface::class);

        $this->formEmailResolver = new FormEmailResolver(
            $this->helperMock,
            $this->serializerMock,
            $this->loggerMock
        );
    }

    public function testResolveReturnsNullWhenFeatureDisabled()
    {
        $this->helperMock->expects($this->once())
            ->method('enableFormCustomEmailRecipient')
            ->willReturn(false);

        $result = $this->formEmailResolver->resolve('branch1', 'form1');
        $this->assertNull($result);
    }

    public function testResolveReturnsNullWhenBranchCodeEmpty()
    {
        $this->helperMock->expects($this->once())
            ->method('enableFormCustomEmailRecipient')
            ->willReturn(true);

        $this->loggerMock->expects($this->once())
            ->method('debug');

        $result = $this->formEmailResolver->resolve('', 'form1');
        $this->assertNull($result);
    }

    public function testResolveReturnsNullWhenFormIdEmpty()
    {
        $this->helperMock->expects($this->once())
            ->method('enableFormCustomEmailRecipient')
            ->willReturn(true);

        $this->loggerMock->expects($this->once())
            ->method('debug');

        $result = $this->formEmailResolver->resolve('branch1', '');
        $this->assertNull($result);
    }

    public function testResolveReturnsEmailWhenMatchFound()
    {
        $branchCode = 'branch1';
        $formId = 'form1';
        $expectedEmail = '<EMAIL>';
        
        $config = [
            [
                'branch' => $branchCode,
                'form' => $formId,
                'email_recipients' => $expectedEmail
            ]
        ];

        $this->helperMock->expects($this->once())
            ->method('enableFormCustomEmailRecipient')
            ->willReturn(true);

        $this->helperMock->expects($this->once())
            ->method('getFormCustomEmailRecipients')
            ->willReturn(json_encode($config));

        $this->serializerMock->expects($this->once())
            ->method('unserialize')
            ->willReturn($config);

        $this->loggerMock->expects($this->once())
            ->method('info');

        $result = $this->formEmailResolver->resolve($branchCode, $formId);
        $this->assertEquals($expectedEmail, $result);
    }

    public function testResolveHandlesMultipleEmails()
    {
        $branchCode = 'branch1';
        $formId = 'form1';
        $expectedEmails = '<EMAIL>,<EMAIL>';
        
        $config = [
            [
                'branch' => $branchCode,
                'form' => $formId,
                'email_recipients' => $expectedEmails
            ]
        ];

        $this->helperMock->expects($this->once())
            ->method('enableFormCustomEmailRecipient')
            ->willReturn(true);

        $this->helperMock->expects($this->once())
            ->method('getFormCustomEmailRecipients')
            ->willReturn(json_encode($config));

        $this->serializerMock->expects($this->once())
            ->method('unserialize')
            ->willReturn($config);

        $this->loggerMock->expects($this->once())
            ->method('info');

        $result = $this->formEmailResolver->resolve($branchCode, $formId);
        $this->assertEquals($expectedEmails, $result);
    }

    public function testResolveSkipsInvalidEmailConfiguration()
    {
        $branchCode = 'branch1';
        $formId = 'form1';
        
        $config = [
            [
                'branch' => $branchCode,
                'form' => $formId,
                'email_recipients' => 'invalid-email'
            ]
        ];

        $this->helperMock->expects($this->once())
            ->method('enableFormCustomEmailRecipient')
            ->willReturn(true);

        $this->helperMock->expects($this->once())
            ->method('getFormCustomEmailRecipients')
            ->willReturn(json_encode($config));

        $this->serializerMock->expects($this->once())
            ->method('unserialize')
            ->willReturn($config);

        $this->loggerMock->expects($this->once())
            ->method('warning');

        $this->loggerMock->expects($this->once())
            ->method('debug');

        $result = $this->formEmailResolver->resolve($branchCode, $formId);
        $this->assertNull($result);
    }
}