<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <event name="sales_quote_collect_totals_before">
        <observer name="updateExpiredB2BPrices" instance="Silksoftwarecorp\RealTimeB2BPricing\Observer\QuoteUpdateExpiredPriceObserver" />
    </event>
    <event name="checkout_submit_before">
        <observer name="updateExpiredB2BPrices" instance="Silksoftwarecorp\RealTimeB2BPricing\Observer\QuoteUpdateExpiredPriceObserver" />
    </event>
    <event name="sales_quote_load_after">
        <observer name="updateExpiredB2BPricesTrigger" instance="Silksoftwarecorp\RealTimeB2BPricing\Observer\SalesQuoteLoadAfterObserver" />
    </event>
</config>
