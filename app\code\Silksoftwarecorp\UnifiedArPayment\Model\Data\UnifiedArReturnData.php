<?php
namespace Silksoftwarecorp\UnifiedArPayment\Model\Data;

use Silksoftwarecorp\UnifiedArPayment\Api\Data\UnifiedArReturnDataInterface;
use Magento\Framework\DataObject;

class UnifiedArReturnData extends DataObject implements UnifiedArReturnDataInterface
{
    /**
     * {@inheritdoc}
     */
    public function getElementTransactionId()
    {
        return $this->getData('element_transaction_id');
    }

    /**
     * {@inheritdoc}
     */
    public function setElementTransactionId($transactionId)
    {
        return $this->setData('element_transaction_id', $transactionId);
    }

    /**
     * {@inheritdoc}
     */
    public function getAvsResponseCode()
    {
        return $this->getData('avs_response_code');
    }

    /**
     * {@inheritdoc}
     */
    public function setAvsResponseCode($avsResponseCode)
    {
        return $this->setData('avs_response_code', $avsResponseCode);
    }

    /**
     * {@inheritdoc}
     */
    public function getCardNumber()
    {
        return $this->getData('card_number');
    }

    /**
     * {@inheritdoc}
     */
    public function setCardNumber($cardNumber)
    {
        return $this->setData('card_number', $cardNumber);
    }

    /**
     * {@inheritdoc}
     */
    public function getAuthorizationCode()
    {
        return $this->getData('authorization_code');
    }

    /**
     * {@inheritdoc}
     */
    public function setAuthorizationCode($authorizationCode)
    {
        return $this->setData('authorization_code', $authorizationCode);
    }

    /**
     * {@inheritdoc}
     */
    public function getCardType()
    {
        return $this->getData('card_type');
    }

    /**
     * {@inheritdoc}
     */
    public function setCardType($cardType)
    {
        return $this->setData('card_type', $cardType);
    }

    /**
     * {@inheritdoc}
     */
    public function getElementPaymentAccountId()
    {
        return $this->getData('element_payment_account_id');
    }

    /**
     * {@inheritdoc}
     */
    public function setElementPaymentAccountId($paymentAccountId)
    {
        return $this->setData('element_payment_account_id', $paymentAccountId);
    }

    /**
     * {@inheritdoc}
     */
    public function getExpirationMonth()
    {
        return $this->getData('expiration_month');
    }

    /**
     * {@inheritdoc}
     */
    public function setExpirationMonth($expirationMonth)
    {
        return $this->setData('expiration_month', $expirationMonth);
    }

    /**
     * {@inheritdoc}
     */
    public function getExpirationYear()
    {
        return $this->getData('expiration_year');
    }

    /**
     * {@inheritdoc}
     */
    public function setExpirationYear($expirationYear)
    {
        return $this->setData('expiration_year', $expirationYear);
    }

    /**
     * {@inheritdoc}
     */
    public function getFirstName()
    {
        return $this->getData('first_name');
    }

    /**
     * {@inheritdoc}
     */
    public function setFirstName($firstName)
    {
        return $this->setData('first_name', $firstName);
    }

    /**
     * {@inheritdoc}
     */
    public function getLastName()
    {
        return $this->getData('last_name');
    }

    /**
     * {@inheritdoc}
     */
    public function setLastName($lastName)
    {
        return $this->setData('last_name', $lastName);
    }

    /**
     * {@inheritdoc}
     */
    public function getCompanyName()
    {
        return $this->getData('company_name');
    }

    /**
     * {@inheritdoc}
     */
    public function setCompanyName($companyName)
    {
        return $this->setData('company_name', $companyName);
    }

    /**
     * {@inheritdoc}
     */
    public function getAddress1()
    {
        return $this->getData('address1');
    }

    /**
     * {@inheritdoc}
     */
    public function setAddress1($address1)
    {
        return $this->setData('address1', $address1);
    }

    /**
     * {@inheritdoc}
     */
    public function getAddress2()
    {
        return $this->getData('address2');
    }

    /**
     * {@inheritdoc}
     */
    public function setAddress2($address2)
    {
        return $this->setData('address2', $address2);
    }

    /**
     * {@inheritdoc}
     */
    public function getCity()
    {
        return $this->getData('city');
    }

    /**
     * {@inheritdoc}
     */
    public function setCity($city)
    {
        return $this->setData('city', $city);
    }

    /**
     * {@inheritdoc}
     */
    public function getState()
    {
        return $this->getData('state');
    }

    /**
     * {@inheritdoc}
     */
    public function setState($state)
    {
        return $this->setData('state', $state);
    }

    /**
     * {@inheritdoc}
     */
    public function getZip()
    {
        return $this->getData('zip');
    }

    /**
     * {@inheritdoc}
     */
    public function setZip($zip)
    {
        return $this->setData('zip', $zip);
    }

    /**
     * {@inheritdoc}
     */
    public function getCountry()
    {
        return $this->getData('country');
    }

    /**
     * {@inheritdoc}
     */
    public function setCountry($country)
    {
        return $this->setData('country', $country);
    }

    /**
     * {@inheritdoc}
     */
    public function getChargeAmount()
    {
        return $this->getData('charge_amount');
    }

    /**
     * {@inheritdoc}
     */
    public function setChargeAmount($chargeAmount)
    {
        return $this->setData('charge_amount', $chargeAmount);
    }

    /**
     * @inheritDoc
     */
    public function getTransactionId()
    {
        return $this->getData('transaction_id');
    }

    /**
     * @inheritDoc
     */
    public function setTransactionId($transactionId)
    {
        return $this->setData('transaction_id', $transactionId);
    }

    /**
     * @inheritDoc
     */
    public function getApprovalNumber()
    {
        return $this->getData('approval_number');
    }

    /**
     * @inheritDoc
     */
    public function setApprovalNumber($approvalNumber)
    {
        return $this->setData('approval_number', $approvalNumber);
    }

    /**
     * @inheritDoc
     */
    public function getReferenceNumber()
    {
        return $this->getData('reference_number');
    }

    /**
     * @inheritDoc
     */
    public function setReferenceNumber($referenceNumber)
    {
        return $this->setData('reference_number', $referenceNumber);
    }

    /**
     * @inheritDoc
     */
    public function getAcquirerData()
    {
        return $this->getData('acquirer_data');
    }

    /**
     * @inheritDoc
     */
    public function setAcquirerData($acquirerData)
    {
        return $this->setData('acquirer_data', $acquirerData);
    }

    /**
     * @inheritDoc
     */
    public function getProcessorName()
    {
        return $this->getData('processor_name');
    }

    /**
     * @inheritDoc
     */
    public function setProcessorName($processorName)
    {
        return $this->setData('processor_name', $processorName);
    }

    /**
     * @inheritDoc
     */
    public function getTransactionStatus()
    {
        return $this->getData('transaction_status');
    }

    /**
     * @inheritDoc
     */
    public function setTransactionStatus($transactionStatus)
    {
        return $this->setData('transaction_status', $transactionStatus);
    }

    /**
     * @inheritDoc
     */
    public function getTransactionStatusCode()
    {
        return $this->getData('transaction_status_code');
    }

    /**
     * @inheritDoc
     */
    public function setTransactionStatusCode($transactionStatusCode)
    {
        return $this->setData('transaction_status_code', $transactionStatusCode);
    }

    /**
     * @inheritDoc
     */
    public function getApprovedAmount()
    {
        return $this->getData('approved_amount');
    }

    /**
     * @inheritDoc
     */
    public function setApprovedAmount($approvedAmount)
    {
        return $this->setData('approved_amount', $approvedAmount);
    }

    /**
     * @inheritDoc
     */
    public function getBalanceAmount()
    {
        return $this->getData('balance_amount');
    }

    /**
     * @inheritDoc
     */
    public function setBalanceAmount($balanceAmount)
    {
        return $this->setData('balance_amount', $balanceAmount);
    }

    /**
     * @inheritDoc
     */
    public function getSurchargeAmount()
    {
        return $this->getData('surcharge_amount');
    }

    /**
     * @inheritDoc
     */
    public function setSurchargeAmount($surchargeAmount)
    {
        return $this->setData('surcharge_amount', $surchargeAmount);
    }

    /**
     * @inheritDoc
     */
    public function getTotalTransactionAmount()
    {
        return $this->getData('total_transaction_amount');
    }

    /**
     * @inheritDoc
     */
    public function setTotalTransactionAmount($totalTransactionAmount)
    {
        return $this->setData('total_transaction_amount', $totalTransactionAmount);
    }

    /**
     * @inheritDoc
     */
    public function getBaseAmount()
    {
        return $this->getData('base_amount');
    }

    /**
     * @inheritDoc
     */
    public function setBaseAmount($baseAmount)
    {
        return $this->setData('base_amount', $baseAmount);
    }

    /**
     * @inheritDoc
     */
    public function getCapturedAmount()
    {
        return $this->getData('captured_amount');
    }

    /**
     * @inheritDoc
     */
    public function setCapturedAmount($capturedAmount)
    {
        return $this->setData('captured_amount', $capturedAmount);
    }

    /**
     * @inheritDoc
     */
    public function getRefundedAmount()
    {
        return $this->getData('refunded_amount');
    }

    /**
     * @inheritDoc
     */
    public function setRefundedAmount($refundedAmount)
    {
        return $this->setData('refunded_amount', $refundedAmount);
    }

    /**
     * @inheritDoc
     */
    public function getCVVResponseCode()
    {
        return $this->getData('cvv_response_code');
    }

    /**
     * @inheritDoc
     */
    public function setCVVResponseCode($code)
    {
        return $this->setData('cvv_response_code', $code);
    }
}
