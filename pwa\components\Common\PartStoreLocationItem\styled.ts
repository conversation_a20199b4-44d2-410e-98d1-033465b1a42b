import styled from '@emotion/styled'

export const StyledPartStoreLocationItem = styled.div`
  position: relative;
  font-weight: 700;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0.01em;

  & > div {
    display: grid;
    grid-template-columns: auto 1fr;
    grid-gap: 4px;
    color: var(--color-font);

    & > span {
      font-weight: bold;
      color: var(--color-font);
      word-break: break-all;
    }
    a {
      word-break: break-all;
    }
  }

  span {
    font-weight: 400;
  }

  a {
    color: var(--color-primary) !important;
    text-decoration: underline;
  }

  .buy-online-icon {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 1;
    cursor: pointer;
  }

  .item-title {
    display: block;
    margin-bottom: 8px;
    font-weight: 700;
    font-size: 18px;
    line-height: 24px;
    letter-spacing: 0.01em;
    color: #003865;

    &.is-buy-online {
      padding-right: 40px;
    }

    span {
      position: relative;
      top: -3px;
      right: -5px;
      font-size: 13px;
      line-height: 23px;
      letter-spacing: 0.01em;
      color: #003865;
      white-space: nowrap;
    }

    &::after {
      content: '';
      position: absolute;
      top: 2px;
      left: -24px;
      display: block;
      width: 5px;
      height: 19px;
      background: #003865;
    }
  }

  .directions-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 12px;
    width: 100%;
    height: 40px;
    border-radius: 3px;
    font-weight: 400;
    font-size: 16px !important;
    line-height: 21px;
    letter-spacing: 0.03em;
    background-color: var(--color-primary);
    color: var(--color-white) !important;
    text-transform: uppercase !important;
    text-decoration: none !important;
  }

  @media (max-width: 768px) {
    .item-title {
      &::after {
        left: -18px;
        width: 4px;
      }
    }
  }
`
