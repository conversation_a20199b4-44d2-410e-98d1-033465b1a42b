<?php

namespace Silksoftwarecorp\ERPCompany\Plugin\Company;

use Magento\Company\Api\Data\CompanyInterface;
use Magento\Company\Model\Company\DataProvider;

class DataProviderPlugin
{
    public function aroundGetCompanyResultData(
        DataProvider $subject,
        \Closure $proceed,
        CompanyInterface $company
    ): array
    {
        $result = $proceed($company);

        $result['erp_settings'] = $this->getSettingsData($company);

        return $result;
    }

    protected function getSettingsData(CompanyInterface $company): array
    {
        /**@var \Magento\Company\Api\Data\CompanyExtensionInterface $extensionAttributes*/
        $extensionAttributes = $company->getExtensionAttributes();

        return [
            'extension_attributes' => [
                'erp_customer_id' => $extensionAttributes->getErpCustomerId(),
                'erp_sales_rep_id' => $extensionAttributes->getErpSalesRepId(),
                'erp_sales_rep_name' => $extensionAttributes->getErpSalesRepName(),
                'erp_credit_status' => $extensionAttributes->getErpCreditStatus(),
            ]
        ];
    }
}
