<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */
declare(strict_types=1);

namespace Silksoftwarecorp\Inventory\Model\ResourceModel;

use Magento\Framework\Exception\FileSystemException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Model\AbstractModel;
use Magento\Framework\Model\ResourceModel\Db\AbstractDb;
use Magento\Framework\Model\ResourceModel\Db\Context;
use Psr\Log\LoggerInterface;
use Silksoftwarecorp\Inventory\Model\ImageProcessor;
use Silksoftwarecorp\Inventory\Model\ManagerProfileImageUrlService;

/**
 * Branch Manager Profile Resource Model
 */
class BranchManagerProfile extends AbstractDb
{
    /**
     * @var ImageProcessor
     */
    private $imageProcessor;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var ManagerProfileImageUrlService
     */
    private $imageUrlService;

    /**
     * @param Context $context
     * @param ImageProcessor $imageProcessor
     * @param LoggerInterface $logger
     * @param ManagerProfileImageUrlService $imageUrlService
     * @param string|null $connectionName
     */
    public function __construct(
        Context $context,
        ImageProcessor $imageProcessor,
        LoggerInterface $logger,
        ManagerProfileImageUrlService $imageUrlService,
        $connectionName = null
    ) {
        parent::__construct($context, $connectionName);
        $this->imageProcessor = $imageProcessor;
        $this->logger = $logger;
        $this->imageUrlService = $imageUrlService;
    }

    /**
     * Initialize resource model
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init('inventory_source_branch_manager_profile', 'id');
    }

    /**
     * Perform actions before object save
     *
     * @param AbstractModel $object
     * @return $this
     * @throws FileSystemException
     */
    protected function _beforeSave(\Magento\Framework\Model\AbstractModel $object)
    {
        // Handle image deletion if image was changed
        if (($object->getOrigData('image') && $object->getOrigData('image') != $object->getImage())) {
            $this->imageProcessor->deleteImage($object->getOrigData('image'));
            $object->setImage($object->getImage() ? $object->getImage() : '');
        }

        return $this;
    }

    /**
     * Perform actions after object save
     *
     * @param AbstractModel $object
     * @return $this
     * @throws FileSystemException
     * @throws LocalizedException
     */
    protected function _afterSave(\Magento\Framework\Model\AbstractModel $object)
    {
        // Process image if it exists and was changed
        if ($object->getImage() && ($image = $object->getData('image'))
            && $object->getOrigData('image') != $object->getImage()
        ) {
            try {
                $this->imageProcessor->processImage(
                    $object->getImage(),
                    $object->getId(),
                    $object->isObjectNew()
                );
            } catch (\Exception $e) {
                // Log error but don't prevent save
                $this->logger->critical($e);
            }
        }

        return $this;
    }

    /**
     * Perform actions after object load
     *
     * @param AbstractModel $object
     * @return $this
     */
    protected function _afterLoad(\Magento\Framework\Model\AbstractModel $object)
    {
        // Set image URL after loading
        if ($object->getImage() && $object->getId()) {
            $imageUrl = $this->imageUrlService->getFullImageUrl((int)$object->getId(), $object->getImage());
            $object->setData('image_url', $imageUrl);
        }

        return $this;
    }

    /**
     * Perform actions before object delete
     *
     * @param AbstractModel $object
     * @return $this
     * @throws FileSystemException
     */
    protected function _beforeDelete(\Magento\Framework\Model\AbstractModel $object)
    {
        // Remove manager image
        if ($image = $object->getImage()) {
            $this->imageProcessor->setBasePaths(
                $object->getId(),
                $object->isObjectNew()
            );
            $this->imageProcessor->deleteImage($image);
        }

        return $this;
    }
}
