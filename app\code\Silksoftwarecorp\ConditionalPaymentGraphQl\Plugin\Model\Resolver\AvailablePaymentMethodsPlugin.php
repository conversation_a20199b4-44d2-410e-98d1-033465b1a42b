<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */
declare(strict_types=1);

namespace Silksoftwarecorp\ConditionalPaymentGraphQl\Plugin\Model\Resolver;

use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\QuoteGraphQl\Model\Resolver\AvailablePaymentMethods;
use Magento\Quote\Api\Data\CartInterface;
use Magento\Quote\Model\Quote;
use Psr\Log\LoggerInterface;

/**
 * Plugin to modify available payment methods based on selected shipping method
 */
class AvailablePaymentMethodsPlugin
{
    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @param LoggerInterface $logger
     */
    public function __construct(
        LoggerInterface $logger
    ) {
        $this->logger = $logger;
    }

    /**
     * Filter payment methods based on shipping method
     *
     * @param AvailablePaymentMethods $subject
     * @param array $result
     * @param Field $field
     * @param $context
     * @param ResolveInfo $info
     * @param array|null $value
     * @param array|null $args
     * @return array
     */
    public function afterResolve(
        AvailablePaymentMethods $subject,
        array $result,
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ): array {
        try {
            // Get cart from the value array
            if (!isset($value['model'])) {
                $this->logger->info('ConditionalPaymentGraphQl: Cart model not found in value array');
                return $result;
            }

            /** @var \Magento\Quote\Model\Quote $cart */
            $cart = $value['model'];
            
            // Check if cart is virtual (no shipping needed)
            if ($cart->isVirtual()) {
                $this->logger->info('ConditionalPaymentGraphQl: Cart is virtual, no shipping method filtering needed');
                return $result;
            }
            
            $shippingAddress = $cart->getShippingAddress();
            
            if (!$shippingAddress) {
                $this->logger->info('ConditionalPaymentGraphQl: No shipping address found');
                return $result;
            }
            $selectedShippingMethod = $shippingAddress->getShippingMethod();
            
            if (!$selectedShippingMethod) {
                $this->logger->info('ConditionalPaymentGraphQl: No shipping method selected');
                return $result;
            }

            $this->logger->info('ConditionalPaymentGraphQl: Selected shipping method: ' . $selectedShippingMethod);

            // Parse shipping method to get carrier code
            $shippingMethodParts = explode('_', $selectedShippingMethod);
            $carrierCode = $shippingMethodParts[0] ?? '';

            $this->logger->info('ConditionalPaymentGraphQl: Carrier code: ' . $carrierCode);

            // Filter payment methods based on shipping method
            $filteredPaymentMethods = $this->filterPaymentMethods($result, $carrierCode);

            $this->logger->info('ConditionalPaymentGraphQl: Original payment methods count: ' . count($result));
            $this->logger->info('ConditionalPaymentGraphQl: Filtered payment methods count: ' . count($filteredPaymentMethods));

            return $filteredPaymentMethods;

        } catch (\Exception $e) {
            $this->logger->error('ConditionalPaymentGraphQl Plugin Error: ' . $e->getMessage());
            return $result;
        }
    }

    /**
     * Filter payment methods based on carrier code
     *
     * @param array $paymentMethods
     * @param string $carrierCode
     * @return array
     */
    private function filterPaymentMethods(array $paymentMethods, string $carrierCode): array
    {
        $filteredMethods = [];

        // Always include unifiedarpayment (Credit Card)
        $alwaysAvailableMethods = ['unifiedarpayment'];

        // Define conditional methods based on carrier codes
        $conditionalMethods = [
            'flatrate' => ['cashondelivery'], // Blevins Delivery
            'instore' => ['checkmo']          // In Store Pickup
        ];

        foreach ($paymentMethods as $method) {
            $methodCode = $method['code'] ?? '';

            // Always include unifiedarpayment
            if (in_array($methodCode, $alwaysAvailableMethods)) {
                $filteredMethods[] = $method;
                $this->logger->info('ConditionalPaymentGraphQl: Including always available method: ' . $methodCode);
                continue;
            }

            // Include conditional methods based on carrier code
            if (isset($conditionalMethods[$carrierCode]) && 
                in_array($methodCode, $conditionalMethods[$carrierCode])) {
                $filteredMethods[] = $method;
                $this->logger->info('ConditionalPaymentGraphQl: Including conditional method: ' . $methodCode . ' for carrier: ' . $carrierCode);
            } else {
                $this->logger->info('ConditionalPaymentGraphQl: Excluding method: ' . $methodCode . ' for carrier: ' . $carrierCode);
            }
        }

        return $filteredMethods;
    }
}
