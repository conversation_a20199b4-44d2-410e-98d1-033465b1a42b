<?php
namespace Silksoftwarecorp\UnifiedArPayment\Model;

use Silksoftwarecorp\UnifiedArPayment\Api\UnifiedArReturnDataInterface;
use Silksoftwarecorp\UnifiedArPayment\Api\Data\UnifiedArReturnDataInterface as ReturnDataInterface;
use Silksoftwarecorp\UnifiedArPayment\Model\Data\UnifiedArReturnData as ReturnDataModel;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Silksoftwarecorp\UnifiedArPayment\Model\ResourceModel\BlevinsOrderPayment as BlevinsOrderPaymentResource;

class UnifiedArReturnData implements UnifiedArReturnDataInterface
{
    /**
     * @var OrderRepositoryInterface
     */
    protected $orderRepository;

    /**
     * @var SearchCriteriaBuilder
     */
    protected $searchCriteriaBuilder;

    /**
     * @var ReturnDataModel
     */
    protected $returnDataModel;

    private $blevinsOrderPaymentResource;

    public function __construct(
        OrderRepositoryInterface $orderRepository,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        ReturnDataModel $returnDataModel,
        BlevinsOrderPaymentResource $BlevinsOrderPayment,
    ) {
        $this->orderRepository = $orderRepository;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->returnDataModel = $returnDataModel;
        $this->blevinsOrderPaymentResource = $BlevinsOrderPayment;
    }

    /**
     * {@inheritdoc}
     */
    public function getUnifiedArReturnData($orderIncrementId)
    {
        $searchCriteria = $this->searchCriteriaBuilder
            ->addFilter('increment_id', $orderIncrementId)
            ->create();
        $orders = $this->orderRepository->getList($searchCriteria)->getItems();
        if (!$orders) {
            return null;
        }
        $order = reset($orders);
        $data = $order->getData('unified_ar_return_data');
        $surChargeFee = $order->getData('unified_ar_surcharge_amount');
        $quoteId = $order->getQuoteId();

        if (!$data) {
            return null;
        }

        $resultPre = [];
        parse_str($data, $resultPre);

        // URL decode all values to handle encoded characters like %20 for spaces
        foreach ($resultPre as $key => $value) {
            $resultPre[$key] = urldecode($value);
        }

        $result = $this->mapKeys($resultPre);

        // Add billing address information
        $billingAddress = $order->getBillingAddress();
        if ($billingAddress) {
            $streetLines = $billingAddress->getStreet();

            $result['FirstName'] = $billingAddress->getFirstname();
            $result['LastName'] = $billingAddress->getLastname();
            $result['CompanyName'] = $billingAddress->getCompany();
            $result['Address1'] = isset($streetLines[0]) ? $streetLines[0] : '';
            $result['Address2'] = isset($streetLines[1]) ? $streetLines[1] : '';
            $result['City'] = $billingAddress->getCity();
            $result['State'] = $billingAddress->getRegionCode();
            $result['Zip'] = $billingAddress->getPostcode();
            $result['Country'] = $billingAddress->getCountryId();
        }

        $result['ChargeAmount'] = $surChargeFee;

        // Create and populate the data object
        $returnData = clone $this->returnDataModel;
        $returnData->setElementTransactionId($result['ElementTransactionID'] ?? null);
        $returnData->setAvsResponseCode($result['AVSResponseCode'] ?? null);
        $returnData->setCardNumber($result['CardNumber'] ?? null);
        //$returnData->setAuthorizationCode($result['AuthorizationCode'] ?? null);
        // Card type mapping (name to number and number to name)
        $cardTypeNameToNumber = [
            'visa' => '1',
            'mastercard' => '2',
            'discover' => '3',
            'diners club' => '4',
            'jcb' => '5',
            'carte blanche' => '6',
            'enroute' => '7',
            'american express' => '8',
            'unionpay' => '9',
            'other' => '10',
        ];
        $cardTypeNumberToName = [
            '1' => 'Visa',
            '2' => 'MasterCard',
            '3' => 'Discover',
            '4' => 'Diners Club',
            '5' => 'JCB',
            '6' => 'Carte Blanche',
            '7' => 'EnRoute',
            '8' => 'American Express',
            '9' => 'UnionPay',
            '10' => 'Other',
        ];
        $cardTypeValue = $result['CardType'] ?? null;
        $cardTypeKey = is_string($cardTypeValue) ? strtolower($cardTypeValue) : $cardTypeValue;
        // Only return the number
        if (isset($cardTypeNameToNumber[$cardTypeKey])) {
            $cardType = $cardTypeNameToNumber[$cardTypeKey];
        } elseif (isset($cardTypeNumberToName[$cardTypeKey])) {
            $cardType = $cardTypeKey;
        } else {
            $cardType = null;
        }

        $returnData->setCardType($result['CardType'] ?? null);//todo, if number use $cardType
        $returnData->setElementPaymentAccountId($result['ElementPaymentAccountID'] ?? null);
        $returnData->setExpirationMonth($result['ExpirationMonth'] ?? null);
        $returnData->setExpirationYear($result['ExpirationYear'] ?? null);
        $returnData->setFirstName($result['FirstName'] ?? null);
        $returnData->setLastName($result['LastName'] ?? null);
        $returnData->setCompanyName($result['CompanyName'] ?? null);
        $returnData->setAddress1($result['Address1'] ?? null);
        $returnData->setAddress2($result['Address2'] ?? null);
        $returnData->setCity($result['City'] ?? null);
        $returnData->setState($result['State'] ?? null);
        $returnData->setZip($result['Zip'] ?? null);
        $returnData->setCountry($result['Country'] ?? null);
        $returnData->setChargeAmount($result['ChargeAmount'] ?? null);

        $authorizationData = $this->getAuthorizationData($quoteId);

        // Set additional fields from authorizationData (Transaction and CVVResponseCode)
        if (!empty($authorizationData)) {
            if (isset($authorizationData['TransactionID'])) {
                $returnData->setTransactionId($authorizationData['TransactionID']);
            }
            if (isset($authorizationData['ApprovalNumber'])) {
                $returnData->setApprovalNumber($authorizationData['ApprovalNumber']);
            }
            if (isset($authorizationData['ReferenceNumber'])) {
                $returnData->setReferenceNumber($authorizationData['ReferenceNumber']);
            }
            if (isset($authorizationData['AcquirerData'])) {
                $returnData->setAcquirerData($authorizationData['AcquirerData']);
            }
            if (isset($authorizationData['ProcessorName'])) {
                $returnData->setProcessorName($authorizationData['ProcessorName']);
            }
            if (isset($authorizationData['TransactionStatus'])) {
                $returnData->setTransactionStatus($authorizationData['TransactionStatus']);
            }
            if (isset($authorizationData['TransactionStatusCode'])) {
                $returnData->setTransactionStatusCode($authorizationData['TransactionStatusCode']);
            }
            if (isset($authorizationData['ApprovedAmount'])) {
                $returnData->setApprovedAmount($authorizationData['ApprovedAmount']);
            }
            if (isset($authorizationData['BalanceAmount'])) {
                $returnData->setBalanceAmount($authorizationData['BalanceAmount']);
            }
            if (isset($authorizationData['SurchargeAmount'])) {
                $returnData->setSurchargeAmount($authorizationData['SurchargeAmount']);
            }
            if (isset($authorizationData['TotalTransactionAmount'])) {
                $returnData->setTotalTransactionAmount($authorizationData['TotalTransactionAmount']);
            }
            if (isset($authorizationData['BaseAmount'])) {
                $returnData->setBaseAmount($authorizationData['BaseAmount']);
            }
            if (isset($authorizationData['CapturedAmount'])) {
                $returnData->setCapturedAmount($authorizationData['CapturedAmount']);
            }
            if (isset($authorizationData['RefundedAmount'])) {
                $returnData->setRefundedAmount($authorizationData['RefundedAmount']);
            }
            if (isset($authorizationData['CVVResponseCode'])) {
                $returnData->setCVVResponseCode($authorizationData['CVVResponseCode']);
            }
            if (isset($authorizationData['authorization_code'])) {
                $returnData->setAuthorizationCode($authorizationData['authorization_code']);
            }
        }

        return $returnData;
    }

    /**
     * Map keys to new format
     *
     * @param array $result
     * @return array
     */
    private function mapKeys($result)
    {
        $keyMapping = [
            'TransactionID' => 'ElementTransactionId',
            'AVSResponseCode' => 'AVSResponseCode',
            'LastFour' => 'CardNumber',
            //'ValidationCode' => 'AuthorizationCode',
            'CardLogo' => 'CardType',
            'PaymentAccountID' => 'ElementPaymentAccountID',
            'ExpirationMonth' => 'ExpirationMonth',
            'ExpirationYear' => 'ExpirationYear'
        ];

        $formattedResult = [];
        foreach ($result as $key => $value) {
            if (isset($keyMapping[$key])) {
                $newKey = $keyMapping[$key];

                // Special formatting for CardNumber
                if ($newKey === 'CardNumber' && $key === 'LastFour') {
                    $formattedResult[$newKey] = 'xxxxxxxxxxxx' . $value;
                } else {
                    $formattedResult[$newKey] = $value;
                }
            }
        }

        return $formattedResult;
    }

    private function getAuthorizationData($quoteId)
    {
        $result = [];
        try {
            $rowData = $this->blevinsOrderPaymentResource->getBlePaymentDataByQuoteId($quoteId);
            if ($rowData && !empty($rowData['unified_ar_authorization_data'])) {
                $authorizationData = json_decode($rowData['unified_ar_authorization_data'], true);
                $authorizationData = $authorizationData['Response']??[];

                // Extract Transaction fields
                if (isset($authorizationData['Transaction']) && is_array($authorizationData['Transaction'])) {
                    foreach ($authorizationData['Transaction'] as $key => $value) {
                        $result[$key] = $value;
                    }
                }
                $result['authorization_code'] = $authorizationData['Transaction']['ApprovalNumber'] ?? '';
                // Extract CVVResponseCode from Card
                if (isset($authorizationData['Card']['CVVResponseCode'])) {
                    $cvv = $authorizationData['Card']['CVVResponseCode'];
                    $result['CVVResponseCode'] = is_array($cvv) ? '' : (string)$cvv;
                }
            }
        } catch (\Throwable $e) {
            // Optionally log error
        }
        return $result;
    }
}
