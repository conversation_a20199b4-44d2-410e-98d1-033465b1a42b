type Query {
    productPrices(
        input: ProductPricesInput!
    ): ProductPricesOutput @resolver(class: "Silksoftwarecorp\\RealTimeB2BPricingGraphQl\\Model\\Resolver\\ProductRealtimeB2BPrices")
}

type ProductRealtimeB2BPrice {
    sku: String!
    price: Float
    last_invoice_date: String
}

type ProductPricesOutput {
    items: [ProductRealtimeB2BPrice]
}

input ProductPricesInput {
    location_id: String!
    skus: [String!]
}
