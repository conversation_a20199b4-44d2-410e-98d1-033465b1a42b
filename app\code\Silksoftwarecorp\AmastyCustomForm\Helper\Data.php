<?php

namespace Silksoftwarecorp\AmastyCustomForm\Helper;

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Store\Model\ScopeInterface;

class Data extends AbstractHelper
{
    /**
     * Configuration path for email recipients
     */
    private const XML_PATH_ENABLE_CUSTOM_FORMS_EMAIL_RECIPIENT = 'custom_forms/email/enable_email_recipient';

    private const XML_PATH_CUSTOM_FORMS_EMAIL_RECIPIENT = 'custom_forms/email/email_recipient';

    private const XML_PATH_SEND_SALESREP_FORMS = 'custom_forms/email/send_salesrep_forms';

    public function enableFormCustomEmailRecipient(): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_ENABLE_CUSTOM_FORMS_EMAIL_RECIPIENT,
            ScopeInterface::SCOPE_STORE
        );
    }

    public function getFormCustomEmailRecipients()
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_CUSTOM_FORMS_EMAIL_RECIPIENT,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Get sales rep forms configuration
     *
     * @return string
     */
    public function getSendSalesRepForms(): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_SEND_SALESREP_FORMS,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Get sales rep forms as array
     *
     * @return array
     */
    public function getSendSalesRepFormsArray(): array
    {
        $forms = $this->getSendSalesRepForms();
        if (empty($forms)) {
            return [];
        }

        return array_filter(array_map('trim', explode(',', $forms)));
    }

    /**
     * Check if form should include sales rep information
     *
     * @param string $formId
     * @return bool
     */
    public function shouldIncludeSalesRepInfo(string $formId): bool
    {
        $salesRepForms = $this->getSendSalesRepFormsArray();
        return in_array($formId, $salesRepForms, true);
    }

    public function getLocationId(): string
    {
        return (string)$this->_request->getParam('X-Location-Id');
    }
}
