<?php
namespace Silk\OnesourceTax\Api;

use Magento\Quote\Model\Quote;
use Magento\Quote\Api\Data\ShippingAssignmentInterface;

/**
 * Interface for calculating tax using OneSource API
 */
interface TaxCalculatorInterface
{
    /**
     * Calculate tax for a quote using OneSource API
     *
     * @param Quote $quote
     * @param ShippingAssignmentInterface $shippingAssignment
     * @return array
     */
    public function calculate(Quote $quote, ShippingAssignmentInterface $shippingAssignment);
}