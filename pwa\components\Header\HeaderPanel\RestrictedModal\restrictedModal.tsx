import { useEffect, memo, useRef } from 'react'

import { Modal } from '@/ui'
import CommonButton from '@/components/Common/CommonButton'

import { StyledRestrictedModal } from './styled'

const RestrictedModal = () => {
  const modalRef = useRef(null)

  useEffect(() => {
    modalRef.current?.open()
  }, [modalRef])

  return (
    <Modal ref={modalRef} width={778}>
      <StyledRestrictedModal>
        <h2>This Account has Restricted Ordering</h2>
        <p>
          Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt
          ut labore et dolore magna aliqua.
        </p>
        <CommonButton>MAKE A PAYMENT</CommonButton>
      </StyledRestrictedModal>
    </Modal>
  )
}

export default memo(RestrictedModal)
