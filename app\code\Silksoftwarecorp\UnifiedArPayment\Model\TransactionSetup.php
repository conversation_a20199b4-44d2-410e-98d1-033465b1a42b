<?php

namespace Silksoftwarecorp\UnifiedArPayment\Model;

use Magento\Framework\HTTP\Client\Curl;
use Silksoftwarecorp\UnifiedArPayment\Helper\Config;
use Silksoftwarecorp\UnifiedArPayment\Helper\Logger;
use Guz<PERSON>Http\Client as GuzzleClient;
use Guz<PERSON>Http\Psr7\Request as GuzzleRequest;

class TransactionSetup
{
    /**
     * @var Curl
     */
    private $curl;

    /**
     * @var Config
     */
    private $configHelper;

    /**
     * @var Logger
     */
    private $logger;

    /**
     * @param Curl $curl
     * @param Config $configHelper
     * @param Logger $logger
     */
    public function __construct(
        Curl $curl,
        Config $configHelper,
        Logger $logger
    ) {
        $this->curl = $curl;
        $this->configHelper = $configHelper;
        $this->logger = $logger;
    }

    /**
     * Create transaction setup
     *
     * @param array $address
     * @param string $erpCustomerId
     * @return array
     */
    public function createTransactionSetup($address, $erpCustomerId = null)
    {
        try {
            // Get configuration
            $envConfig = $this->configHelper->getEnvironmentConfig();
            $publicConfig = $this->configHelper->getPublicConfig();

            // Build XML request
            $xml = $this->buildXmlRequest(
                $envConfig,
                $publicConfig,
                $address,
                $erpCustomerId
            );

            // Make API call
            $response = $this->makeApiCall($envConfig['endpoint'], $xml, $publicConfig['ui_iframe_page_endpoint'], 'CreateTransactionSetup');

            return $response;

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'transaction_setup_url' => null,
                'iframe_url' => null,
                'error_code' => 'EXCEPTION'
            ];
        }
    }

    /**
     * Build XML request
     *
     * @param array $envConfig
     * @param array $publicConfig
     * @param array $address
     * @param string|null $erpCustomerId
     * @return string
     */
    private function buildXmlRequest($envConfig, $publicConfig, $address, $erpCustomerId = null)
    {
        $xml = '<TransactionSetup xmlns="https://transaction.elementexpress.com">';
        $xml .= '<Credentials>';
        $xml .= '<AccountID>' . htmlspecialchars($envConfig['account_id']) . '</AccountID>';
        $xml .= '<AccountToken>' . htmlspecialchars($envConfig['account_token']) . '</AccountToken>';
        $xml .= '<AcceptorID>' . htmlspecialchars($envConfig['acceptor_id']) . '</AcceptorID>';
        $xml .= '</Credentials>';
        $xml .= '<Application>';
        $xml .= '<ApplicationID>' . htmlspecialchars($envConfig['application_id']) . '</ApplicationID>';
        $xml .= '<ApplicationName>' . htmlspecialchars($envConfig['application_name']) . '</ApplicationName>';
        $xml .= '<ApplicationVersion>' . htmlspecialchars($envConfig['application_version']) . '</ApplicationVersion>';
        $xml .= '</Application>';
        $xml .= '<TransactionSetup>';
        $xml .= '<TransactionSetupMethod>7</TransactionSetupMethod>';
        $xml .= '<Embedded>1</Embedded>';
        $xml .= '<Device>0</Device>';
        $xml .= '<DeviceInputCode>3</DeviceInputCode>';
        $xml .= '<ProcessTransactionTitle>Continue</ProcessTransactionTitle>';
        if (!empty($publicConfig['custom_css'])) {
            $xml .= '<CustomCss>' . htmlspecialchars($publicConfig['custom_css']) . '</CustomCss>';
        }
        $xml .= '<ReturnURL>' . htmlspecialchars($publicConfig['return_url']) . '</ReturnURL>';
        $xml .= '<ReturnURLTitle>' . htmlspecialchars($publicConfig['return_url_title']) . '</ReturnURLTitle>';
        $xml .= '<WelcomeMessage>' . htmlspecialchars($publicConfig['welcome_message']) . '</WelcomeMessage>';
        $xml .= '<CompanyName>' . htmlspecialchars($publicConfig['company_name']) . '</CompanyName>';
        $xml .= '<AutoReturn>1</AutoReturn>';
        $xml .= '<EnabledSavedPayments>false</EnabledSavedPayments>';
        $xml .= '<UserIdentifier></UserIdentifier>';
        $xml .= '</TransactionSetup>';
        $xml .= '<PaymentAccount>';
        $xml .= '<PaymentAccountType>0</PaymentAccountType>';
        $xml .= '<PaymentAccountReferenceNumber>' . htmlspecialchars($erpCustomerId) . '</PaymentAccountReferenceNumber>';
        $xml .= '</PaymentAccount>';
        $xml .= '<Address>';
        $xml .= '<AddressEditAllowed>0</AddressEditAllowed>';
        $xml .= '<BillingAddress1>' . htmlspecialchars($address['billing_address1']) . '</BillingAddress1>';
        if (!empty($address['billing_address2'])) {
            $xml .= '<BillingAddress2>' . htmlspecialchars($address['billing_address2']) . '</BillingAddress2>';
        }
        $xml .= '<BillingCity>' . htmlspecialchars($address['billing_city']) . '</BillingCity>';
        $xml .= '<BillingState>' . htmlspecialchars($address['billing_state']) . '</BillingState>';
        $xml .= '<BillingZipcode>' . htmlspecialchars($address['billing_zipcode']) . '</BillingZipcode>';
        $xml .= '</Address>';
        $xml .= '</TransactionSetup>';
        return $xml;
    }

    /**
     * Make API call
     *
     * @param string $endpoint
     * @param string $xml
     * @param string $uiIframePageEndpoint
     * @param string $operation
     * @return array
     */
    private function makeApiCall($endpoint, $xml, $uiIframePageEndpoint, $operation = 'API Call')
    {
        // Log request
        $this->logger->logRequest($endpoint, $xml, $operation);

        $this->curl->setOption(CURLOPT_URL, $endpoint);
        $this->curl->setOption(CURLOPT_POST, true);
        $this->curl->setOption(CURLOPT_POSTFIELDS, $xml);
        $this->curl->setOption(CURLOPT_HTTPHEADER, [
            'Content-Type: text/xml'
        ]);
        $this->curl->setOption(CURLOPT_RETURNTRANSFER, true);
        $this->curl->setOption(CURLOPT_TIMEOUT, 30);

        $this->curl->post($endpoint, $xml);
        $response = $this->curl->getBody();
        $httpCode = $this->curl->getStatus();

        // Log response
        $this->logger->logResponse($response, $httpCode, $operation);

        // Parse XML response
        $xmlResponse = simplexml_load_string($response);
        if ($xmlResponse === false) {
            $this->logger->logError('Invalid XML response from API', ['response' => $response], $operation);
            return [
                'success' => false,
                'message' => 'Invalid XML response from API',
                'error_code' => 'INVALID_RESPONSE'
            ];
        }

        // Check for errors in response
        if (isset($xmlResponse->Response)) {
            $responseCode = (string)$xmlResponse->Response->ExpressResponseCode;
            $responseMessage = (string)$xmlResponse->Response->ExpressResponseMessage;
            if ($responseCode !== '0') {
                $this->logger->logError('API Error: ' . $responseMessage, [
                    'response_code' => $responseCode,
                    'response_message' => $responseMessage
                ], $operation);
                return [
                    'success' => false,
                    'message' => $responseMessage,
                    'error_code' => $responseCode
                ];
            }
        }

        // Extract TransactionSetupID and build iframe URL
        $transactionSetupId = null;
        $iframeUrl = null;
        if (isset($xmlResponse->Response->TransactionSetup->TransactionSetupID)) {
            $transactionSetupId = (string)$xmlResponse->Response->TransactionSetup->TransactionSetupID;
            if ($transactionSetupId && $uiIframePageEndpoint) {
                $iframeUrl = rtrim($uiIframePageEndpoint, '/') . '/iframe?TransactionSetupId=' . $transactionSetupId;
            }
        }

        return [
            'success' => true,
            'message' => 'Transaction setup created successfully',
            'transaction_setup_url' => $transactionSetupId,
            'iframe_url' => $iframeUrl
        ];
    }
}
 