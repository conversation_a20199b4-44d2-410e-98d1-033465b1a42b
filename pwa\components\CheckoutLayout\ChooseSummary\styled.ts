import styled from '@emotion/styled'

export const StyledSummary = styled.div`
  margin-top: 24px;
  padding: 24px;
  background-color: #f2f2f2;
  border-radius: 4px;

  div,
  p,
  a {
    font-weight: 400;
    font-size: 15px;
    line-height: 23px;
    letter-spacing: 0.01em;
    color: var(--color-text);
  }

  a:hover {
    color: var(--color-text);
  }

  .summary-title {
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid #d9d9d9;
    font-weight: 700;
    font-size: 24px;
    line-height: 30px;
    letter-spacing: 0;
  }

  .summary-item-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 18px;
    font-weight: 700;
    font-size: 15px;
    line-height: 23px;
    letter-spacing: 0.01em;
    color: var(--color-font);

    .edit-btn {
      padding: 0;
      gap: 6px;

      svg {
        margin-top: -4px;
      }

      span {
        font-weight: 700;
        font-size: 16px;
        line-height: 26px;
        letter-spacing: 0.02em;
        color: var(--color-primary);
      }
    }
  }
`
