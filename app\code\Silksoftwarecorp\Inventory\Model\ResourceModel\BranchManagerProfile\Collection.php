<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */
declare(strict_types=1);

namespace Silksoftwarecorp\Inventory\Model\ResourceModel\BranchManagerProfile;

use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;

/**
 * Branch Manager Profile Collection
 */
class Collection extends AbstractCollection
{
    /**
     * Initialize resource model
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init(
            \Silksoftwarecorp\Inventory\Model\BranchManagerProfile::class,
            \Silksoftwarecorp\Inventory\Model\ResourceModel\BranchManagerProfile::class
        );
    }
} 