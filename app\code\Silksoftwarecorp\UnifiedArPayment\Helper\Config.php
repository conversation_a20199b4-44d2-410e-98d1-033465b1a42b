<?php

namespace Silksoftwarecorp\UnifiedArPayment\Helper;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;

class Config
{
    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(
        ScopeConfigInterface $scopeConfig
    ) {
        $this->scopeConfig = $scopeConfig;
    }

    /**
     * Get configuration value
     *
     * @param string $path
     * @return string
     */
    public function getConfigValue($path)
    {
        return $this->scopeConfig->getValue($path, ScopeInterface::SCOPE_STORE);
    }

    /**
     * Get environment configuration
     *
     * @return array
     */
    public function getEnvironmentConfig()
    {
        $environment = $this->getConfigValue('payment/unifiedarpayment/environment');
        
        if ($environment === 'sandbox') {
            return [
                'endpoint' => $this->getConfigValue('payment/unifiedarpayment/sandbox_create_transaction_setup_endpoint'),
                'payment_account_query_endpoint' => $this->getConfigValue('payment/unifiedarpayment/sandbox_payment_account_query_endpoint'),
                'surcharge_query_endpoint' => $this->getConfigValue('payment/unifiedarpayment/sandbox_surcharge_query_endpoint'),
                'account_id' => $this->getConfigValue('payment/unifiedarpayment/sandbox_account_id'),
                'account_token' => $this->getConfigValue('payment/unifiedarpayment/sandbox_account_token'),
                'acceptor_id' => $this->getConfigValue('payment/unifiedarpayment/sandbox_acceptor_id'),
                'application_id' => $this->getConfigValue('payment/unifiedarpayment/sandbox_application_id'),
                'application_name' => $this->getConfigValue('payment/unifiedarpayment/sandbox_application_name'),
                'application_version' => $this->getConfigValue('payment/unifiedarpayment/sandbox_application_version'),
            ];
        } else {
            return [
                'endpoint' => $this->getConfigValue('payment/unifiedarpayment/production_create_transaction_setup_endpoint'),
                'payment_account_query_endpoint' => $this->getConfigValue('payment/unifiedarpayment/production_payment_account_query_endpoint'),
                'surcharge_query_endpoint' => $this->getConfigValue('payment/unifiedarpayment/production_surcharge_query_endpoint'),
                'account_id' => $this->getConfigValue('payment/unifiedarpayment/production_account_id'),
                'account_token' => $this->getConfigValue('payment/unifiedarpayment/production_account_token'),
                'acceptor_id' => $this->getConfigValue('payment/unifiedarpayment/production_acceptor_id'),
                'application_id' => $this->getConfigValue('payment/unifiedarpayment/production_application_id'),
                'application_name' => $this->getConfigValue('payment/unifiedarpayment/production_application_name'),
                'application_version' => $this->getConfigValue('payment/unifiedarpayment/production_application_version'),
            ];
        }
    }

    /**
     * Get payment method title
     *
     * @return string
     */
    public function getTitle()
    {
        return $this->getConfigValue('payment/unifiedarpayment/title');
    }

    /**
     * Get public configuration
     *
     * @return array
     */
    public function getPublicConfig()
    {
        return [
            'title' => $this->getTitle(),
            'return_url' => $this->getConfigValue('payment/unifiedarpayment/return_url'),
            'return_url_title' => $this->getConfigValue('payment/unifiedarpayment/return_url_title'),
            'welcome_message' => $this->getConfigValue('payment/unifiedarpayment/welcome_message'),
            'company_name' => $this->getConfigValue('payment/unifiedarpayment/company_name'),
            'custom_css' => $this->getConfigValue('payment/unifiedarpayment/custom_css'),
            'payment_account_reference_number' => $this->getConfigValue('payment/unifiedarpayment/payment_account_reference_number'),
            'ui_iframe_page_endpoint' => $this->getConfigValue('payment/unifiedarpayment/ui_iframe_page_endpoint'),
            'market_code' => $this->getConfigValue('payment/unifiedarpayment/market_code'),
        ];
    }
} 