interface AmBlogPostInterface {
    tags: [AmBlogTag] @doc(description: "The post tags.") @resolver(class: "Silksoftwarecorp\\BlogGraphql\\Model\\Resolver\\Post\\Tags")
    original_post_thumbnail: String @doc(description: "The post original thumbnail.") @resolver(class: "Silksoftwarecorp\\BlogGraphql\\Model\\Resolver\\Post\\OriginalPostThumbnail")
    original_list_thumbnail: String @doc(description: "The post original thumbnail on list.") @resolver(class: "Silksoftwarecorp\\BlogGraphql\\Model\\Resolver\\Post\\OriginalListThumbnail")
}

extend type AmBlogSetting {
    home_page_description: String @doc(description: "Blog home page description HTML content from selected CMS block.") @resolver(class: "Silksoftwarecorp\\BlogGraphql\\Model\\Resolver\\BlogHomePageDescription")
    url_postfix: String @resolver(class: "Silksoftwarecorp\\BlogGraphql\\Model\\Resolver\\UrlPostfix")
}
