<?php

namespace Silksoftwarecorp\UnifiedArGraphQl\Model\Resolver;

use Magento\CustomerGraphQl\Model\Customer\GetCustomer;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlAuthenticationException;
use Magento\Framework\GraphQl\Exception\GraphQlAuthorizationException;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Exception\GraphQlNoSuchEntityException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Query\Uid;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Silksoftwarecorp\ERPCompany\Model\Company\ErpResolverInterface;
use Silksoftwarecorp\UnifiedAR\Service\FinancialInformationService;

/**
 * Financial Information GraphQL Resolver
 */
class FinancialInformation implements ResolverInterface
{
    /**
     * @var FinancialInformationService
     */
    protected $financialInformationService;

    /**
     * @var GetCustomer
     */
    private $getCustomer;

    /**
     * @var ErpResolverInterface
     */
    private $companyErpResolver;

    /**
     * @var Uid
     */
    private $idEncoder;

    /**
     * Constructor
     * @param FinancialInformationService $financialInformationService
     * @param GetCustomer $getCustomer
     * @param Uid $idEncoder
     * @param ErpResolverInterface $companyErpResolver
     */
    public function __construct(
        FinancialInformationService $financialInformationService,
        GetCustomer $getCustomer,
        Uid $idEncoder,
        ErpResolverInterface $companyErpResolver
    ) {
        $this->financialInformationService = $financialInformationService;
        $this->getCustomer = $getCustomer;
        $this->idEncoder = $idEncoder;
        $this->companyErpResolver = $companyErpResolver;
    }

    /**
     * Resolve financial information GraphQL query
     * @param Field $field
     * @param $context
     * @param ResolveInfo $info
     * @param array|null $value
     * @param array|null $args
     * @return array
     * @throws GraphQlAuthorizationException
     * @throws GraphQlInputException
     * @throws GraphQlAuthenticationException
     * @throws GraphQlNoSuchEntityException
     */
    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        if (false === $context->getExtensionAttributes()->getIsCustomer()) {
            throw new GraphQlAuthorizationException(__('The current customer isn\'t authorized.'));
        }

        if (empty($args['company_id'])) {
            throw new GraphQlInputException(__('Please specify the `company_id`.'));
        }

        $this->getCustomer->execute($context);

        try {
            $companyId = (int) $this->idEncoder->decode($args['company_id']);

            // Get ERP Customer ID using the resolver
            $erpCustomerId = $this->companyErpResolver->getErpCustomerId($companyId);

            return $this->financialInformationService->getFinancialInformation($erpCustomerId);
        } catch (\Exception $e) {
            throw new GraphQlInputException(__($e->getMessage()), $e);
        }
    }
}
