import styled from '@emotion/styled'

export const StyledPaymentView = styled.div`
  .title {
    margin-top: 48px;
    font-weight: 700;
    font-size: 24px;
    line-height: 30px;
    letter-spacing: 0;
  }

  #payment-form-items {
    .${({ theme }) => theme.namespace}-form-item-control-input {
      display: block;
    }
    .${({ theme }) => theme.namespace}-radio-wrapper {
      display: inline-block;
    }
  }

  .actions {
    margin-top: 26px;

    .${({ theme }) => theme.namespace} {
      &-btn {
        width: 186px;
        height: 49px;
        border-radius: 3px;

        span {
          font-weight: 700;
          font-size: 16px;
          line-height: 21px;
          letter-spacing: 0.03em;
        }
      }
    }
  }

  .text-form-item {
    margin-top: 40px;

    h3 {
      margin-bottom: 20px;
      font-weight: 700;
      font-size: 24px;
      line-height: 30px;
      letter-spacing: 0;

      span {
        font-weight: 400;
        font-size: 13px;
        line-height: 19px;
        letter-spacing: 0;
      }
    }

    .${({ theme }) => theme.namespace} {
      &-form-item {
        margin-bottom: 0;
      }

      &-input {
        height: 44px;
        padding: 8px 14px;
        border-radius: 3px;
        border: 1px solid #d9d9d9 !important;
        box-shadow: none !important;
        font-weight: 400;
        font-size: 16px;
        line-height: 24px;
        letter-spacing: 0.02em;
        color: var(--color-font);

        &::placeholder {
          font-weight: 400;
          font-size: 16px;
          line-height: 24px;
          letter-spacing: 0.02em;
          color: #777;
        }
      }
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    .${({ theme }) => theme.namespace} {
      &-radio-group {
        padding-bottom: 23px;
        border-bottom: 1px solid #d9d9d9;
      }

      &-space {
        row-gap: 24px !important;

        &-item {
          .method-code-radio {
            padding: 20px 0 0;
            border-top: 1px solid #d9d9d9;
            width: 100%;
          }
        }
      }
    }

    .text-form-item {
      h3 {
        font-weight: 700;
        font-size: 20px;
        line-height: 26px;
        letter-spacing: 0;
      }
    }

    .actions {
      margin-top: 36px;

      .${({ theme }) => theme.namespace}-btn {
        width: 100%;
      }
    }
  }
`

export const StyledPaymentItemDesc = styled.div`
  margin-left: 24px;

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    margin-top: -10px;
    margin-left: 0;
  }
`
