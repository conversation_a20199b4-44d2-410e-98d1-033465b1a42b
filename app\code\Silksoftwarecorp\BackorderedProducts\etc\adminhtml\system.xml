<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Config/etc/system_file.xsd">
    <system>
        <section id="edi" translate="label" type="text" sortOrder="900" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>EDI API</label>
            <tab>general</tab>
            <resource>Magento_Config::config</resource>
            <group id="backordered" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Backordered Products</label>
                <field id="days_back" translate="label" type="text" sortOrder="100" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Days Back</label>
                    <comment>Number of days to look back for backordered products (default: 180)</comment>
                    <validate>validate-number validate-greater-than-zero</validate>
                    <frontend_class>validate-number</frontend_class>
                </field>
            </group>
        </section>
    </system>
</config> 