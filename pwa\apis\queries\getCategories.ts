import { gql } from '@apollo/client'
import type { DocumentNode } from '@apollo/client'

export const GET_CATEGORIES: DocumentNode = gql`
  query getCategories($filters: CategoryFilterInput) {
    categories(filters: $filters) {
      items {
        children {
          children {
            children {
              include_in_menu
              level
              name
              type
              uid
              url_path
            }
            include_in_menu
            name
            uid
            url_path
          }
          include_in_menu
          name
          uid
          url_path
          label_text_color
        }
        include_in_menu
        level
        name
        uid
        url_path
      }
      page_info {
        current_page
        page_size
        total_pages
      }
      total_count
    }
  }
`
