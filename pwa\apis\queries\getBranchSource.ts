import { gql } from '@apollo/client'
import type { DocumentNode } from '@apollo/client'

export const GET_BRANCH_SOURCE: DocumentNode = gql`
  query getBranchSource($code: String) {
    getBranchSource(code: $code) {
      active
      address {
        city
        postcode
        region {
          region_code
        }
        street
      }
      code
      email
      fax
      general_manager {
        image
        name
        phone
        region
      }
      lat
      lng
      name
      phone
      territory_managers {
        image
        name
        phone
        region
      }
      text_number
      schedules {
        office {
          enabled
          friday {
            break_form
            break_to
            from
            status
            to
          }
          monday {
            break_form
            break_to
            from
            status
            to
          }
          saturday {
            break_form
            break_to
            from
            status
            to
          }
          sunday {
            break_form
            break_to
            from
            status
            to
          }
          thursday {
            break_form
            break_to
            from
            status
            to
          }
          tuesday {
            break_form
            break_to
            from
            status
            to
          }
          wednesday {
            break_form
            break_to
            from
            status
            to
          }
        }
        store {
          enabled
          friday {
            break_form
            break_to
            from
            status
            to
          }
          monday {
            break_form
            break_to
            from
            status
            to
          }
          saturday {
            break_form
            break_to
            from
            status
            to
          }
          sunday {
            break_form
            break_to
            from
            status
            to
          }
          thursday {
            break_form
            break_to
            from
            status
            to
          }
          tuesday {
            break_form
            break_to
            from
            status
            to
          }
          wednesday {
            break_form
            break_to
            from
            status
            to
          }
        }
      }
    }
  }
`
