import { gql } from '@apollo/client'
import type { DocumentNode } from '@apollo/client'

export const SURCHARGE_QUERY: DocumentNode = gql`
  mutation surchargeQuery(
    $payment_account_id: String!
    $billing_state: String!
    $amount: Float!
    $country: String!
  ) {
    surchargeQuery(
      payment_account_id: $payment_account_id
      billing_state: $billing_state
      amount: $amount
      country: $country
    ) {
      error_code
      message
      success
      surcharge_allowed
      surcharge_amount
    }
  }
`
