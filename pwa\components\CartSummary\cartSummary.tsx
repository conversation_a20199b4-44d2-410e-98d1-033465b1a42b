import { useMemo, memo } from 'react'
import { useSelector } from 'react-redux'
import { FormattedMessage } from 'react-intl'
import { useRouter } from 'next/compat/router'

import BasePrice from '@/components/BasePrice'

import { StyledSummary } from './styled'

const CartSummary = ({ isCartPage }: any) => {
  const router = useRouter()
  const isCheckout: boolean = router?.route === '/checkout'
  const cartDetail = useSelector((state: Store) => state.cart.cartDetail)
  const shippingMethod = useSelector((state: Store) => state.checkout.shippingMethod)

  const discountPrice = useMemo(() => {
    let counts = 0
    const discounts: any[] = cartDetail?.prices?.discounts ?? []
    discounts.map((item) => {
      counts += item?.amount?.value || 0
      return item
    })
    return counts
  }, [cartDetail])

  const grandTotalPrice = useMemo(() => {
    return cartDetail?.prices?.grand_total?.value ?? 0
  }, [cartDetail])

  const subtotalExcludingTaxPrice = useMemo(() => {
    return cartDetail?.prices?.subtotal_excluding_tax?.value ?? 0
  }, [cartDetail])

  // const subtotalIncludingTaxPrice = useMemo(() => {
  //   return cartDetail?.prices?.subtotal_including_tax?.value ?? 0
  // }, [cartDetail])

  const appliedTaxes = useMemo(() => {
    return cartDetail?.prices?.applied_taxes ?? []
  }, [cartDetail])

  return (
    <StyledSummary>
      <div className="item">
        <span>
          <FormattedMessage id="global.subtotal" />
        </span>
        <BasePrice value={subtotalExcludingTaxPrice} />
      </div>
      {isCheckout ? (
        <>
          {appliedTaxes.map((item) => (
            <div className="item" key={item?.label}>
              <span>{item?.label ?? ''}</span>
              <BasePrice value={item?.amount?.value ?? 0} />
            </div>
          ))}
          {shippingMethod?.carrier_title && (
            <div className="item">
              <span>Shipping ({shippingMethod.carrier_title})</span>
              <BasePrice value={shippingMethod?.amount?.value ?? ''} />
            </div>
          )}
        </>
      ) : (
        <div className="item">
          <span>
            <FormattedMessage id="global.tax" />
          </span>
          <span className="available-in-checkout">Available in checkout</span>
        </div>
      )}
      {discountPrice > 0 && (
        <div className="item">
          <span>
            <FormattedMessage id="global.discount" />
          </span>
          <span>
            -
            <BasePrice value={discountPrice} />
          </span>
        </div>
      )}
      <div className="item total">
        <span>{isCartPage ? 'Order Subtotal' : 'Order Total'}</span>
        <BasePrice value={isCartPage ? subtotalExcludingTaxPrice : grandTotalPrice} />
      </div>
    </StyledSummary>
  )
}

export default memo(CartSummary)
