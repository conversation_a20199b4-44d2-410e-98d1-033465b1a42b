import { gql } from '@apollo/client'

export const MOVE_ITEMS_BETWEEN_REQUISITION_LISTS = gql`
  mutation moveItemsBetweenRequisitionLists(
    $sourceId: ID!
    $destinationId: ID
    $items: MoveItemsBetweenRequisitionListsInput
  ) {
    moveItemsBetweenRequisitionLists(
      sourceRequisitionListUid: $sourceId
      destinationRequisitionListUid: $destinationId
      requisitionListItem: $items
    ) {
      destination_requisition_list {
        name
        uid
      }
    }
  }
`
