<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */
-->
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <fieldset name="general" sortOrder="10">
        <field name="install_request_form" formElement="checkbox" sortOrder="235">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="default" xsi:type="number">0</item>
                </item>
            </argument>
            <settings>
                <dataType>boolean</dataType>
                <label translate="true">Install Request form (Fast Fax)</label>
                <dataScope>extension_attributes.install_request_form</dataScope>
            </settings>
            <formElements>
                <checkbox>
                    <settings>
                        <valueMap>
                            <map name="false" xsi:type="number">0</map>
                            <map name="true" xsi:type="number">1</map>
                        </valueMap>
                        <prefer>toggle</prefer>
                    </settings>
                </checkbox>
            </formElements>
        </field>
        <field name="install_app" formElement="checkbox" sortOrder="240">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="default" xsi:type="number">0</item>
                </item>
            </argument>
            <settings>
                <dataType>boolean</dataType>
                <label translate="true">Install App</label>
                <dataScope>extension_attributes.install_app</dataScope>
            </settings>
            <formElements>
                <checkbox>
                    <settings>
                        <valueMap>
                            <map name="false" xsi:type="number">0</map>
                            <map name="true" xsi:type="number">1</map>
                        </valueMap>
                        <prefer>toggle</prefer>
                    </settings>
                </checkbox>
            </formElements>
        </field>
    </fieldset>
    <fieldset name="contact_info" sortOrder="20">
        <field name="text_number" formElement="input" sortOrder="50">
            <settings>
                <dataType>text</dataType>
                <label translate="true">Text Number</label>
                <dataScope>extension_attributes.text_number</dataScope>
            </settings>
        </field>
    </fieldset>
    <fieldset name="address" sortOrder="30">
        <field name="street1" formElement="input" sortOrder="55">
            <settings>
                <dataType>text</dataType>
                <label translate="true"/>
                <dataScope>extension_attributes.street1</dataScope>
            </settings>
        </field>
    </fieldset>
    <fieldset name="schedule" sortOrder="40">
        <settings>
            <label translate="true">Schedule</label>
            <collapsible>true</collapsible>
            <opened>false</opened>
            <dataScope>general</dataScope>
        </settings>
        <fieldset name="schedule_office" sortOrder="10">
            <settings>
                <label translate="true">Hours of Operation(Office)</label>
                <collapsible>true</collapsible>
                <opened>false</opened>
                <dataScope>extension_attributes.schedule.office</dataScope>
                <additionalClasses>
                    <class name="schedule_fill_wrapper">true</class>
                </additionalClasses>
            </settings>
            <field name="active" formElement="checkbox" sortOrder="1">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="default" xsi:type="number">1</item>
                    </item>
                </argument>
                <settings>
                    <dataType>boolean</dataType>
                    <label translate="true">Enabled</label>
                    <additionalClasses>
                        <class name="admin_field-small">true</class>
                    </additionalClasses>
                    <dataScope>active</dataScope>
                </settings>
                <formElements>
                    <checkbox>
                        <settings>
                            <valueMap>
                                <map name="false" xsi:type="number">0</map>
                                <map name="true" xsi:type="number">1</map>
                            </valueMap>
                            <prefer>toggle</prefer>
                        </settings>
                    </checkbox>
                </formElements>
            </field>
            <fieldset name="monday" sortOrder="10">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="initializeFieldsetDataByDefault" xsi:type="boolean">true</item>
                        <item name="imports" xsi:type="array">
                            <item name="visible" xsi:type="string">${ $.parentName }.active:checked</item>
                        </item>
                    </item>
                </argument>
                <settings>
                    <label translate="true">Monday</label>
                    <collapsible>true</collapsible>
                    <opened>true</opened>
                    <dataScope>monday</dataScope>
                    <visible>true</visible>
                </settings>
                <field name="monday_status" formElement="select" sortOrder="10">
                    <argument name="data" xsi:type="array">
                        <item name="config" xsi:type="array">
                            <item name="default" xsi:type="number">1</item>
                        </item>
                    </argument>
                    <settings>
                        <dataType>boolean</dataType>
                        <label translate="true">Monday Schedule</label>
                        <additionalClasses>
                            <class name="admin_field-small">true</class>
                        </additionalClasses>
                        <dataScope>status</dataScope>
                    </settings>
                    <formElements>
                        <select>
                            <settings>
                                <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleStatus"/>
                            </settings>
                        </select>
                    </formElements>
                </field>
                <container name="form" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Open Time</item>
                            <item name="dataScope" xsi:type="string">form</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <argument name="data" xsi:type="array">
                            <item name="config" xsi:type="array">
                                <item name="required" xsi:type="boolean">true</item>
                            </item>
                        </argument>
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Open Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <argument name="data" xsi:type="array">
                            <item name="config" xsi:type="array">
                                <item name="required" xsi:type="boolean">true</item>
                            </item>
                        </argument>
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Open Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleMinutesTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="break_form" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Start Of Break</item>
                            <item name="dataScope" xsi:type="string">break_form</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Start Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Start Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleMinutesTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="break_to" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">End Of Break</item>
                            <item name="dataScope" xsi:type="string">break_to</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">End Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">End Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleMinutesTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="to" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Close Time</item>
                            <item name="dataScope" xsi:type="string">to</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <argument name="data" xsi:type="array">
                            <item name="config" xsi:type="array">
                                <item name="required" xsi:type="boolean">true</item>
                            </item>
                        </argument>
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Close Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <argument name="data" xsi:type="array">
                            <item name="config" xsi:type="array">
                                <item name="required" xsi:type="boolean">true</item>
                            </item>
                        </argument>
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Close Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleMinutesTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <field name="fill_schedule_button" formElement="input">
                    <argument name="data" xsi:type="array">
                        <item name="config" xsi:type="array">
                            <item name="elementTmpl" xsi:type="string">Silksoftwarecorp_Inventory/form/element/fillschedule</item>
                        </item>
                    </argument>
                </field>
            </fieldset>
            <fieldset name="tuesday" sortOrder="20">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="initializeFieldsetDataByDefault" xsi:type="boolean">true</item>
                        <item name="imports" xsi:type="array">
                            <item name="visible" xsi:type="string">${ $.parentName }.active:checked</item>
                        </item>
                    </item>
                </argument>
                <settings>
                    <label translate="true">Tuesday</label>
                    <collapsible>true</collapsible>
                    <dataScope>tuesday</dataScope>
                    <visible>true</visible>
                </settings>
                <field name="tuesday_status" formElement="select" sortOrder="10">
                    <argument name="data" xsi:type="array">
                        <item name="config" xsi:type="array">
                            <item name="default" xsi:type="number">1</item>
                        </item>
                    </argument>
                    <settings>
                        <dataType>boolean</dataType>
                        <label translate="true">Tuesday Schedule</label>
                        <additionalClasses>
                            <class name="admin_field-small">true</class>
                        </additionalClasses>
                        <dataScope>status</dataScope>
                    </settings>
                    <formElements>
                        <select>
                            <settings>
                                <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleStatus"/>
                            </settings>
                        </select>
                    </formElements>
                </field>
                <container name="form" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Open Time</item>
                            <item name="dataScope" xsi:type="string">form</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Open Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Open Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleMinutesTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="break_form" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Start Of Break</item>
                            <item name="dataScope" xsi:type="string">break_form</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Start Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Start Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="break_to" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">End Of Break</item>
                            <item name="dataScope" xsi:type="string">break_to</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">End Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">End Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="to" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Close Time</item>
                            <item name="dataScope" xsi:type="string">to</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Close Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Close Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleMinutesTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
            </fieldset>
            <fieldset name="wednesday" sortOrder="30">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="initializeFieldsetDataByDefault" xsi:type="boolean">true</item>
                        <item name="imports" xsi:type="array">
                            <item name="visible" xsi:type="string">${ $.parentName }.active:checked</item>
                        </item>
                    </item>
                </argument>
                <settings>
                    <label translate="true">Wednesday</label>
                    <collapsible>true</collapsible>
                    <dataScope>wednesday</dataScope>
                    <visible>true</visible>
                </settings>
                <field name="wednesday_status" formElement="select" sortOrder="10">
                    <argument name="data" xsi:type="array">
                        <item name="config" xsi:type="array">
                            <item name="default" xsi:type="number">1</item>
                        </item>
                    </argument>
                    <settings>
                        <dataType>boolean</dataType>
                        <label translate="true">Wednesday Schedule</label>
                        <additionalClasses>
                            <class name="admin_field-small">true</class>
                        </additionalClasses>
                        <dataScope>status</dataScope>
                    </settings>
                    <formElements>
                        <select>
                            <settings>
                                <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleStatus"/>
                            </settings>
                        </select>
                    </formElements>
                </field>
                <container name="form" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Open Time</item>
                            <item name="dataScope" xsi:type="string">form</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Open Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Open Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleMinutesTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="break_form" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Start Of Break</item>
                            <item name="dataScope" xsi:type="string">break_form</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Start Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Start Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="break_to" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">End Of Break</item>
                            <item name="dataScope" xsi:type="string">break_to</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">End Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">End Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="to" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Close Time</item>
                            <item name="dataScope" xsi:type="string">to</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Close Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Close Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleMinutesTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
            </fieldset>
            <fieldset name="thursday" sortOrder="40">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="initializeFieldsetDataByDefault" xsi:type="boolean">true</item>
                        <item name="imports" xsi:type="array">
                            <item name="visible" xsi:type="string">${ $.parentName }.active:checked</item>
                        </item>
                    </item>
                </argument>
                <settings>
                    <label translate="true">Thursday</label>
                    <collapsible>true</collapsible>
                    <dataScope>thursday</dataScope>
                    <visible>true</visible>
                </settings>
                <field name="thursday_status" formElement="select" sortOrder="10">
                    <argument name="data" xsi:type="array">
                        <item name="config" xsi:type="array">
                            <item name="default" xsi:type="number">1</item>
                        </item>
                    </argument>
                    <settings>
                        <dataType>boolean</dataType>
                        <label translate="true">Thursday Schedule</label>
                        <additionalClasses>
                            <class name="admin_field-small">true</class>
                        </additionalClasses>
                        <dataScope>status</dataScope>
                    </settings>
                    <formElements>
                        <select>
                            <settings>
                                <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleStatus"/>
                            </settings>
                        </select>
                    </formElements>
                </field>
                <container name="form" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Open Time</item>
                            <item name="dataScope" xsi:type="string">form</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Open Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Open Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleMinutesTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="break_form" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Start Of Break</item>
                            <item name="dataScope" xsi:type="string">break_form</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Start Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Start Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="break_to" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">End Of Break</item>
                            <item name="dataScope" xsi:type="string">break_to</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">End Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">End Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="to" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Close Time</item>
                            <item name="dataScope" xsi:type="string">to</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Close Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Close Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleMinutesTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
            </fieldset>
            <fieldset name="friday" sortOrder="50">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="initializeFieldsetDataByDefault" xsi:type="boolean">true</item>
                        <item name="imports" xsi:type="array">
                            <item name="visible" xsi:type="string">${ $.parentName }.active:checked</item>
                        </item>
                    </item>
                </argument>
                <settings>
                    <label translate="true">Friday</label>
                    <collapsible>true</collapsible>
                    <dataScope>friday</dataScope>
                    <visible>true</visible>
                </settings>
                <field name="friday_status" formElement="select" sortOrder="10">
                    <argument name="data" xsi:type="array">
                        <item name="config" xsi:type="array">
                            <item name="default" xsi:type="number">1</item>
                        </item>
                    </argument>
                    <settings>
                        <dataType>boolean</dataType>
                        <label translate="true">Friday Schedule</label>
                        <additionalClasses>
                            <class name="admin_field-small">true</class>
                        </additionalClasses>
                        <dataScope>status</dataScope>
                    </settings>
                    <formElements>
                        <select>
                            <settings>
                                <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleStatus"/>
                            </settings>
                        </select>
                    </formElements>
                </field>
                <container name="form" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Open Time</item>
                            <item name="dataScope" xsi:type="string">form</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Open Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Open Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleMinutesTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="break_form" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Start Of Break</item>
                            <item name="dataScope" xsi:type="string">break_form</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Start Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Start Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="break_to" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">End Of Break</item>
                            <item name="dataScope" xsi:type="string">break_to</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">End Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">End Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="to" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Close Time</item>
                            <item name="dataScope" xsi:type="string">to</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Close Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Close Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleMinutesTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
            </fieldset>
            <fieldset name="saturday" sortOrder="60">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="initializeFieldsetDataByDefault" xsi:type="boolean">true</item>
                        <item name="imports" xsi:type="array">
                            <item name="visible" xsi:type="string">${ $.parentName }.active:checked</item>
                        </item>
                    </item>
                </argument>
                <settings>
                    <label translate="true">Saturday</label>
                    <collapsible>true</collapsible>
                    <dataScope>saturday</dataScope>
                    <visible>true</visible>
                </settings>
                <field name="saturday_status" formElement="select" sortOrder="10">
                    <argument name="data" xsi:type="array">
                        <item name="config" xsi:type="array">
                            <item name="default" xsi:type="number">1</item>
                        </item>
                    </argument>
                    <settings>
                        <dataType>boolean</dataType>
                        <label translate="true">Saturday Schedule</label>
                        <additionalClasses>
                            <class name="admin_field-small">true</class>
                        </additionalClasses>
                        <dataScope>status</dataScope>
                    </settings>
                    <formElements>
                        <select>
                            <settings>
                                <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleStatus"/>
                            </settings>
                        </select>
                    </formElements>
                </field>
                <container name="form" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Open Time</item>
                            <item name="dataScope" xsi:type="string">form</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Open Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Open Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleMinutesTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="break_form" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Start Of Break</item>
                            <item name="dataScope" xsi:type="string">break_form</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Start Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Start Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="break_to" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">End Of Break</item>
                            <item name="dataScope" xsi:type="string">break_to</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">End Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">End Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="to" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Close Time</item>
                            <item name="dataScope" xsi:type="string">to</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Close Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Close Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleMinutesTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
            </fieldset>
            <fieldset name="sunday" sortOrder="70">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="initializeFieldsetDataByDefault" xsi:type="boolean">true</item>
                        <item name="imports" xsi:type="array">
                            <item name="visible" xsi:type="string">${ $.parentName }.active:checked</item>
                        </item>
                    </item>
                </argument>
                <settings>
                    <label translate="true">Sunday</label>
                    <collapsible>true</collapsible>
                    <dataScope>sunday</dataScope>
                    <visible>true</visible>
                </settings>
                <field name="sunday_status" formElement="select" sortOrder="10">
                    <argument name="data" xsi:type="array">
                        <item name="config" xsi:type="array">
                            <item name="default" xsi:type="number">1</item>
                        </item>
                    </argument>
                    <settings>
                        <dataType>boolean</dataType>
                        <label translate="true">Sunday Schedule</label>
                        <additionalClasses>
                            <class name="admin_field-small">true</class>
                        </additionalClasses>
                        <dataScope>status</dataScope>
                    </settings>
                    <formElements>
                        <select>
                            <settings>
                                <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleStatus"/>
                            </settings>
                        </select>
                    </formElements>
                </field>
                <container name="form" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Open Time</item>
                            <item name="dataScope" xsi:type="string">form</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Open Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Open Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleMinutesTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="break_form" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Start Of Break</item>
                            <item name="dataScope" xsi:type="string">break_form</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Start Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Start Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="break_to" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">End Of Break</item>
                            <item name="dataScope" xsi:type="string">break_to</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">End Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">End Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="to" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Close Time</item>
                            <item name="dataScope" xsi:type="string">to</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Close Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Close Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleMinutesTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
            </fieldset>
        </fieldset>
        <fieldset name="schedule_store" sortOrder="15">
            <settings>
                <label translate="true">Hours of Operation(Store)</label>
                <collapsible>true</collapsible>
                <opened>false</opened>
                <additionalClasses>
                    <class name="schedule_fill_wrapper">true</class>
                </additionalClasses>
                <dataScope>extension_attributes.schedule.store</dataScope>
            </settings>
            <field name="active" formElement="checkbox" sortOrder="1">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="default" xsi:type="number">1</item>
                    </item>
                </argument>
                <settings>
                    <dataType>boolean</dataType>
                    <label translate="true">Enabled</label>
                    <additionalClasses>
                        <class name="admin_field-small">true</class>
                    </additionalClasses>
                    <dataScope>active</dataScope>
                </settings>
                <formElements>
                    <checkbox>
                        <settings>
                            <valueMap>
                                <map name="false" xsi:type="number">0</map>
                                <map name="true" xsi:type="number">1</map>
                            </valueMap>
                            <prefer>toggle</prefer>
                        </settings>
                    </checkbox>
                </formElements>
            </field>
            <fieldset name="monday" sortOrder="10">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="initializeFieldsetDataByDefault" xsi:type="boolean">true</item>
                        <item name="imports" xsi:type="array">
                            <item name="visible" xsi:type="string">${ $.parentName }.active:checked</item>
                        </item>
                    </item>
                </argument>
                <settings>
                    <label translate="true">Monday</label>
                    <collapsible>true</collapsible>
                    <opened>true</opened>
                    <dataScope>monday</dataScope>
                    <visible>true</visible>
                </settings>
                <field name="monday_status" formElement="select" sortOrder="10">
                    <argument name="data" xsi:type="array">
                        <item name="config" xsi:type="array">
                            <item name="default" xsi:type="number">1</item>
                        </item>
                    </argument>
                    <settings>
                        <dataType>boolean</dataType>
                        <label translate="true">Monday Schedule</label>
                        <additionalClasses>
                            <class name="admin_field-small">true</class>
                        </additionalClasses>
                        <dataScope>status</dataScope>
                    </settings>
                    <formElements>
                        <select>
                            <settings>
                                <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleStatus"/>
                            </settings>
                        </select>
                    </formElements>
                </field>
                <container name="form" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Open Time</item>
                            <item name="dataScope" xsi:type="string">form</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Open Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Open Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleMinutesTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="break_form" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Start Of Break</item>
                            <item name="dataScope" xsi:type="string">break_form</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Start Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Start Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="break_to" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">End Of Break</item>
                            <item name="dataScope" xsi:type="string">break_to</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">End Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">End Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="to" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Close Time</item>
                            <item name="dataScope" xsi:type="string">to</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Close Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Close Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleMinutesTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <field name="fill_schedule_button" formElement="input">
                    <argument name="data" xsi:type="array">
                        <item name="config" xsi:type="array">
                            <item name="elementTmpl" xsi:type="string">Silksoftwarecorp_Inventory/form/element/fillschedule</item>
                        </item>
                    </argument>
                </field>
            </fieldset>
            <fieldset name="tuesday" sortOrder="20">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="initializeFieldsetDataByDefault" xsi:type="boolean">true</item>
                        <item name="imports" xsi:type="array">
                            <item name="visible" xsi:type="string">${ $.parentName }.active:checked</item>
                        </item>
                    </item>
                </argument>
                <settings>
                    <label translate="true">Tuesday</label>
                    <collapsible>true</collapsible>
                    <dataScope>tuesday</dataScope>
                    <visible>true</visible>
                </settings>
                <field name="tuesday_status" formElement="select" sortOrder="10">
                    <argument name="data" xsi:type="array">
                        <item name="config" xsi:type="array">
                            <item name="default" xsi:type="number">1</item>
                        </item>
                    </argument>
                    <settings>
                        <dataType>boolean</dataType>
                        <label translate="true">Tuesday Schedule</label>
                        <additionalClasses>
                            <class name="admin_field-small">true</class>
                        </additionalClasses>
                        <dataScope>status</dataScope>
                    </settings>
                    <formElements>
                        <select>
                            <settings>
                                <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleStatus"/>
                            </settings>
                        </select>
                    </formElements>
                </field>
                <container name="form" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Open Time</item>
                            <item name="dataScope" xsi:type="string">form</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Open Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Open Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleMinutesTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="break_form" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Start Of Break</item>
                            <item name="dataScope" xsi:type="string">break_form</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Start Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Start Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="break_to" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">End Of Break</item>
                            <item name="dataScope" xsi:type="string">break_to</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">End Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">End Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="to" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Close Time</item>
                            <item name="dataScope" xsi:type="string">to</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Close Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Close Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleMinutesTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
            </fieldset>
            <fieldset name="wednesday" sortOrder="30">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="initializeFieldsetDataByDefault" xsi:type="boolean">true</item>
                        <item name="imports" xsi:type="array">
                            <item name="visible" xsi:type="string">${ $.parentName }.active:checked</item>
                        </item>
                    </item>
                </argument>
                <settings>
                    <label translate="true">Wednesday</label>
                    <collapsible>true</collapsible>
                    <dataScope>wednesday</dataScope>
                    <visible>true</visible>
                </settings>
                <field name="wednesday_status" formElement="select" sortOrder="10">
                    <argument name="data" xsi:type="array">
                        <item name="config" xsi:type="array">
                            <item name="default" xsi:type="number">1</item>
                        </item>
                    </argument>
                    <settings>
                        <dataType>boolean</dataType>
                        <label translate="true">Wednesday Schedule</label>
                        <additionalClasses>
                            <class name="admin_field-small">true</class>
                        </additionalClasses>
                        <dataScope>status</dataScope>
                    </settings>
                    <formElements>
                        <select>
                            <settings>
                                <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleStatus"/>
                            </settings>
                        </select>
                    </formElements>
                </field>
                <container name="form" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Open Time</item>
                            <item name="dataScope" xsi:type="string">form</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Open Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Open Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleMinutesTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="break_form" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Start Of Break</item>
                            <item name="dataScope" xsi:type="string">break_form</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Start Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Start Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="break_to" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">End Of Break</item>
                            <item name="dataScope" xsi:type="string">break_to</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">End Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">End Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="to" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Close Time</item>
                            <item name="dataScope" xsi:type="string">to</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Close Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Close Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleMinutesTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
            </fieldset>
            <fieldset name="thursday" sortOrder="40">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="initializeFieldsetDataByDefault" xsi:type="boolean">true</item>
                        <item name="imports" xsi:type="array">
                            <item name="visible" xsi:type="string">${ $.parentName }.active:checked</item>
                        </item>
                    </item>
                </argument>
                <settings>
                    <label translate="true">Thursday</label>
                    <collapsible>true</collapsible>
                    <dataScope>thursday</dataScope>
                    <visible>true</visible>
                </settings>
                <field name="thursday_status" formElement="select" sortOrder="10">
                    <argument name="data" xsi:type="array">
                        <item name="config" xsi:type="array">
                            <item name="default" xsi:type="number">1</item>
                        </item>
                    </argument>
                    <settings>
                        <dataType>boolean</dataType>
                        <label translate="true">Thursday Schedule</label>
                        <additionalClasses>
                            <class name="admin_field-small">true</class>
                        </additionalClasses>
                        <dataScope>status</dataScope>
                    </settings>
                    <formElements>
                        <select>
                            <settings>
                                <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleStatus"/>
                            </settings>
                        </select>
                    </formElements>
                </field>
                <container name="form" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Open Time</item>
                            <item name="dataScope" xsi:type="string">form</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Open Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Open Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleMinutesTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="break_form" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Start Of Break</item>
                            <item name="dataScope" xsi:type="string">break_form</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Start Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Start Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="break_to" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">End Of Break</item>
                            <item name="dataScope" xsi:type="string">break_to</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">End Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">End Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="to" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Close Time</item>
                            <item name="dataScope" xsi:type="string">to</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Close Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Close Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleMinutesTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
            </fieldset>
            <fieldset name="friday" sortOrder="50">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="initializeFieldsetDataByDefault" xsi:type="boolean">true</item>
                        <item name="imports" xsi:type="array">
                            <item name="visible" xsi:type="string">${ $.parentName }.active:checked</item>
                        </item>
                    </item>
                </argument>
                <settings>
                    <label translate="true">Friday</label>
                    <collapsible>true</collapsible>
                    <dataScope>friday</dataScope>
                    <visible>true</visible>
                </settings>
                <field name="friday_status" formElement="select" sortOrder="10">
                    <argument name="data" xsi:type="array">
                        <item name="config" xsi:type="array">
                            <item name="default" xsi:type="number">1</item>
                        </item>
                    </argument>
                    <settings>
                        <dataType>boolean</dataType>
                        <label translate="true">Friday Schedule</label>
                        <additionalClasses>
                            <class name="admin_field-small">true</class>
                        </additionalClasses>
                        <dataScope>status</dataScope>
                    </settings>
                    <formElements>
                        <select>
                            <settings>
                                <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleStatus"/>
                            </settings>
                        </select>
                    </formElements>
                </field>
                <container name="form" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Open Time</item>
                            <item name="dataScope" xsi:type="string">form</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Open Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Open Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleMinutesTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="break_form" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Start Of Break</item>
                            <item name="dataScope" xsi:type="string">break_form</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Start Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Start Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="break_to" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">End Of Break</item>
                            <item name="dataScope" xsi:type="string">break_to</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">End Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">End Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="to" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Close Time</item>
                            <item name="dataScope" xsi:type="string">to</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Close Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Close Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleMinutesTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
            </fieldset>
            <fieldset name="saturday" sortOrder="60">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="initializeFieldsetDataByDefault" xsi:type="boolean">true</item>
                        <item name="imports" xsi:type="array">
                            <item name="visible" xsi:type="string">${ $.parentName }.active:checked</item>
                        </item>
                    </item>
                </argument>
                <settings>
                    <label translate="true">Saturday</label>
                    <collapsible>true</collapsible>
                    <dataScope>saturday</dataScope>
                    <visible>true</visible>
                </settings>
                <field name="saturday_status" formElement="select" sortOrder="10">
                    <argument name="data" xsi:type="array">
                        <item name="config" xsi:type="array">
                            <item name="default" xsi:type="number">1</item>
                        </item>
                    </argument>
                    <settings>
                        <dataType>boolean</dataType>
                        <label translate="true">Saturday Schedule</label>
                        <additionalClasses>
                            <class name="admin_field-small">true</class>
                        </additionalClasses>
                        <dataScope>status</dataScope>
                    </settings>
                    <formElements>
                        <select>
                            <settings>
                                <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleStatus"/>
                            </settings>
                        </select>
                    </formElements>
                </field>
                <container name="form" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Open Time</item>
                            <item name="dataScope" xsi:type="string">form</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Open Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Open Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleMinutesTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="break_form" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Start Of Break</item>
                            <item name="dataScope" xsi:type="string">break_form</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Start Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Start Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="break_to" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">End Of Break</item>
                            <item name="dataScope" xsi:type="string">break_to</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">End Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">End Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="to" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Close Time</item>
                            <item name="dataScope" xsi:type="string">to</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Close Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Close Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleMinutesTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
            </fieldset>
            <fieldset name="sunday" sortOrder="70">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="initializeFieldsetDataByDefault" xsi:type="boolean">true</item>
                        <item name="imports" xsi:type="array">
                            <item name="visible" xsi:type="string">${ $.parentName }.active:checked</item>
                        </item>
                    </item>
                </argument>
                <settings>
                    <label translate="true">Sunday</label>
                    <collapsible>true</collapsible>
                    <dataScope>sunday</dataScope>
                    <visible>true</visible>
                </settings>
                <field name="sunday_status" formElement="select" sortOrder="10">
                    <argument name="data" xsi:type="array">
                        <item name="config" xsi:type="array">
                            <item name="default" xsi:type="number">1</item>
                        </item>
                    </argument>
                    <settings>
                        <dataType>boolean</dataType>
                        <label translate="true">Sunday Schedule</label>
                        <additionalClasses>
                            <class name="admin_field-small">true</class>
                        </additionalClasses>
                        <dataScope>status</dataScope>
                    </settings>
                    <formElements>
                        <select>
                            <settings>
                                <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleStatus"/>
                            </settings>
                        </select>
                    </formElements>
                </field>
                <container name="form" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Open Time</item>
                            <item name="dataScope" xsi:type="string">form</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Open Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Open Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleMinutesTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="break_form" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Start Of Break</item>
                            <item name="dataScope" xsi:type="string">break_form</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Start Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Start Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="break_to" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">End Of Break</item>
                            <item name="dataScope" xsi:type="string">break_to</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">End Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">End Of Break</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTimeWithEmpty"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
                <container name="to" component="Magento_Ui/js/form/components/group">
                    <argument name="data" xsi:type="array">
                        <item name="type" xsi:type="string">group</item>
                        <item name="config" xsi:type="array">
                            <item name="additionalClasses" xsi:type="string">admin__control-grouped-date</item>
                            <item name="required" xsi:type="boolean">false</item>
                            <item name="breakLine" xsi:type="boolean">false</item>
                            <item name="label" xsi:type="string">Close Time</item>
                            <item name="dataScope" xsi:type="string">to</item>
                        </item>
                    </argument>
                    <field name="hours" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Close Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleHoursTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                    <field name="minutes" formElement="select">
                        <settings>
                            <dataType>text</dataType>
                            <label translate="true">Close Time</label>
                            <additionalClasses>
                                <class name="admin__field-small">true</class>
                            </additionalClasses>
                        </settings>
                        <formElements>
                            <select>
                                <settings>
                                    <options class="Silksoftwarecorp\Inventory\Ui\Component\Form\ScheduleMinutesTime"/>
                                </settings>
                            </select>
                        </formElements>
                    </field>
                </container>
            </fieldset>
        </fieldset>
        <field name="holiday_closures" formElement="textarea" sortOrder="20">
            <settings>
                <dataType>text</dataType>
                <label translate="true">Holiday Closures</label>
                <dataScope>extension_attributes.holiday_closures</dataScope>
            </settings>
        </field>
    </fieldset>
    <fieldset name="manager_profile" sortOrder="50">
        <settings>
            <label translate="true">Manager Profile</label>
            <collapsible>true</collapsible>
            <opened>false</opened>
            <dataScope>manager_profile</dataScope>
        </settings>
        <fieldset name="general" sortOrder="10">
            <settings>
                <label translate="true">General Manager Profile</label>
                <collapsible>true</collapsible>
                <opened>false</opened>
                <dataScope>general</dataScope>
            </settings>
            <field name="id" formElement="input" sortOrder="5">
                <settings>
                    <dataType>text</dataType>
                    <visible>false</visible>
                </settings>
            </field>
            <field name="image" formElement="imageUploader" sortOrder="10">
                <settings>
                    <label translate="true">Image</label>
                    <componentType>imageUploader</componentType>
                </settings>
                <formElements>
                    <imageUploader>
                        <settings>
                            <allowedExtensions>jpg jpeg gif png</allowedExtensions>
                            <maxFileSize>2097152</maxFileSize>
                            <uploaderConfig>
                                <param xsi:type="string" name="url">inventory/media/upload</param>
                            </uploaderConfig>
                        </settings>
                    </imageUploader>
                </formElements>
            </field>
            <field name="name" formElement="input" sortOrder="20">
                <settings>
                    <dataType>text</dataType>
                    <label translate="true">Name</label>
                </settings>
            </field>
            <field name="phone" formElement="input" sortOrder="30">
                <settings>
                    <dataType>text</dataType>
                    <label translate="true">Phone</label>
                </settings>
            </field>
            <field name="region" formElement="input" sortOrder="40">
                <settings>
                    <dataType>text</dataType>
                    <label translate="true">Region</label>
                </settings>
            </field>
        </fieldset>
        <fieldset name="territory_manager1" sortOrder="20">
            <settings>
                <label translate="true">Territory Manager 1</label>
                <collapsible>true</collapsible>
                <opened>false</opened>
                <dataScope>territory_manager1</dataScope>
            </settings>
            <field name="id" formElement="input" sortOrder="1">
                <settings>
                    <dataType>text</dataType>
                    <visible>false</visible>
                </settings>
            </field>
            <field name="image" formElement="imageUploader" sortOrder="5">
                <settings>
                    <label translate="true">Image</label>
                    <componentType>imageUploader</componentType>
                </settings>
                <formElements>
                    <imageUploader>
                        <settings>
                            <allowedExtensions>jpg jpeg gif png</allowedExtensions>
                            <maxFileSize>2097152</maxFileSize>
                            <uploaderConfig>
                                <param xsi:type="string" name="url">inventory/media/upload</param>
                            </uploaderConfig>
                        </settings>
                    </imageUploader>
                </formElements>
            </field>
            <field name="name" formElement="input" sortOrder="10">
                <settings>
                    <dataType>text</dataType>
                    <label translate="true">Name</label>
                </settings>
            </field>
            <field name="region" formElement="input" sortOrder="20">
                <settings>
                    <dataType>text</dataType>
                    <label translate="true">Region</label>
                </settings>
            </field>
            <field name="phone" formElement="input" sortOrder="30">
                <settings>
                    <dataType>text</dataType>
                    <label translate="true">Phone</label>
                </settings>
            </field>
        </fieldset>
        <fieldset name="territory_manager2" sortOrder="30">
            <settings>
                <label translate="true">Territory Manager 2</label>
                <collapsible>true</collapsible>
                <opened>false</opened>
                <dataScope>territory_manager2</dataScope>
            </settings>
            <field name="id" formElement="input" sortOrder="1">
                <settings>
                    <dataType>text</dataType>
                    <visible>false</visible>
                </settings>
            </field>
            <field name="image" formElement="imageUploader" sortOrder="5">
                <settings>
                    <label translate="true">Image</label>
                    <componentType>imageUploader</componentType>
                </settings>
                <formElements>
                    <imageUploader>
                        <settings>
                            <allowedExtensions>jpg jpeg gif png</allowedExtensions>
                            <maxFileSize>2097152</maxFileSize>
                            <uploaderConfig>
                                <param xsi:type="string" name="url">inventory/media/upload</param>
                            </uploaderConfig>
                        </settings>
                    </imageUploader>
                </formElements>
            </field>
            <field name="name" formElement="input" sortOrder="10">
                <settings>
                    <dataType>text</dataType>
                    <label translate="true">Name</label>
                </settings>
            </field>
            <field name="region" formElement="input" sortOrder="20">
                <settings>
                    <dataType>text</dataType>
                    <label translate="true">Region</label>
                </settings>
            </field>
            <field name="phone" formElement="input" sortOrder="30">
                <settings>
                    <dataType>text</dataType>
                    <label translate="true">Phone</label>
                </settings>
            </field>
        </fieldset>
        <fieldset name="territory_manager3" sortOrder="40">
            <settings>
                <label translate="true">Territory Manager 3</label>
                <collapsible>true</collapsible>
                <opened>false</opened>
                <dataScope>territory_manager3</dataScope>
            </settings>
            <field name="id" formElement="input" sortOrder="1">
                <settings>
                    <dataType>text</dataType>
                    <visible>false</visible>
                </settings>
            </field>
            <field name="image" formElement="imageUploader" sortOrder="5">
                <settings>
                    <label translate="true">Image</label>
                    <componentType>imageUploader</componentType>
                </settings>
                <formElements>
                    <imageUploader>
                        <settings>
                            <allowedExtensions>jpg jpeg gif png</allowedExtensions>
                            <maxFileSize>2097152</maxFileSize>
                            <uploaderConfig>
                                <param xsi:type="string" name="url">inventory/media/upload</param>
                            </uploaderConfig>
                        </settings>
                    </imageUploader>
                </formElements>
            </field>
            <field name="name" formElement="input" sortOrder="10">
                <settings>
                    <dataType>text</dataType>
                    <label translate="true">Name</label>
                </settings>
            </field>
            <field name="region" formElement="input" sortOrder="20">
                <settings>
                    <dataType>text</dataType>
                    <label translate="true">Region</label>
                </settings>
            </field>
            <field name="phone" formElement="input" sortOrder="30">
                <settings>
                    <dataType>text</dataType>
                    <label translate="true">Phone</label>
                </settings>
            </field>
        </fieldset>
    </fieldset>
</form>
