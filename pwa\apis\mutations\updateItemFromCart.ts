import { gql, DocumentNode } from '@apollo/client'

import { cartItems } from '../fragment/cartItems'
import { cart_prices } from '../fragment/cartPrices'

export const POST_UPDATE_CART_ITEM: DocumentNode = gql`
  mutation updateItemFromCart($cartId: String!, $cartItems: [CartItemUpdateInput]!) {
    updateCart: updateCartItems(input: { cart_id: $cartId, cart_items: $cartItems }) {
      cart {
        quantity: total_quantity
        prices {
          ...cart_prices
          __typename
        }
        coupons: applied_coupons {
          code
        }
        ...cartItems
        __typename
      }
    }
  }
  ${cartItems}
  ${cart_prices}
`
