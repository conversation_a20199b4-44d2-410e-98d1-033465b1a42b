import { memo, useCallback, useMemo } from 'react'
import { useSelector } from 'react-redux'

import { StyledContactYourBranchButton } from './styled'

const ContactYourBranchButton = (props) => {
  const isShowroomView = useSelector((state: Store) => state.user.showroomView)
  const isLogin = useSelector((state: Store) => state.user.isLogin)

  const disabled = useMemo(() => {
    return !isLogin || isShowroomView
  }, [isLogin, isShowroomView])

  // TODO
  const handleContact = useCallback(() => {}, [])

  return (
    <StyledContactYourBranchButton dark onClick={handleContact} disabled={disabled} {...props}>
      CONTACT YOUR BRANCH
    </StyledContactYourBranchButton>
  )
}

export default memo(ContactYourBranchButton)
