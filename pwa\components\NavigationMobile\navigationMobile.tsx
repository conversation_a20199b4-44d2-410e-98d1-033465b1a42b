import { Drawer } from 'antd'
import { events } from '@ranger-theme/utils'
import { useEffect, useState, useCallback, memo } from 'react'

import MenuList from './menuList'
import { StyledNavigationMobile } from './styled'

const NavigationMobile = () => {
  const [visible, setVisible] = useState<boolean>(false)

  const handleToggle = useCallback(() => {
    setVisible((prev: boolean) => !prev)
  }, [])

  const onClose = useCallback(() => {
    setVisible(false)
  }, [])

  useEffect(() => {
    const hideNav = () => {
      setVisible(false)
    }

    events.on('hideNav', hideNav)

    return () => {
      events.off('hideNav', hideNav)
    }
  }, [])

  return (
    <StyledNavigationMobile>
      <div className="nav-icon" aria-hidden="true" onClick={handleToggle}>
        <svg
          width={visible ? '16px' : '18px'}
          height="16px"
          fill="currentColor"
          aria-hidden="true"
          focusable="false">
          <use xlinkHref={visible ? '#icon-close-white' : '#icon-category-white'} />
        </svg>
      </div>
      <Drawer
        rootClassName="mb-navigation"
        placement="left"
        open={visible}
        closable={false}
        width="100%"
        zIndex={visible ? 998 : 1000}
        onClose={onClose}
        style={{ marginTop: 172, height: 'calc(100vh - 172px)', width: '100vw' }}>
        <MenuList menuOpen={visible} />
      </Drawer>
    </StyledNavigationMobile>
  )
}

export default memo(NavigationMobile)
