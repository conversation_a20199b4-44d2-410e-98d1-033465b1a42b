<?php

namespace Silksoftwarecorp\RealTimeB2BPricing\Model\API\Http;

use Guz<PERSON><PERSON>ttp\Client as GuzzleClient;
use Guz<PERSON><PERSON>ttp\ClientFactory;
use GuzzleHttp\Exception\GuzzleException;
use Guz<PERSON>Http\Exception\ConnectException;
use GuzzleHttp\Middleware;
use GuzzleHttp\HandlerStack;
use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\ResponseInterface;
use Magento\Framework\Serialize\SerializerInterface;
use Silksoftwarecorp\RealTimeB2BPricing\Logger\LoggerInterface;
use Silksoftwarecorp\RealTimeB2BPricing\Model\API\Config;
use Silksoftwarecorp\RealTimeB2BPricing\Model\API\Exception\ApiResponseException;
use Silksoftwarecorp\RealTimeB2BPricing\Model\API\Request;
use Silksoftwarecorp\RealTimeB2BPricing\Model\API\RequestLog;

class Client implements ClientInterface
{
    /**
     * @var Config
     */
    protected $config;

    /**
     * @var ClientFactory
     */
    protected $clientFactory;

    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * @var SerializerInterface
     */
    protected $serializer;

    /**
     * @var GuzzleClient
     */
    protected $_client;

    /**
     * @var RequestLog
     */
    protected $_requestLog;

    private int $maxRetries = 3;

    /**
     * @param Config $config
     * @param ClientFactory $clientFactory
     * @param SerializerInterface $serializer
     * @param LoggerInterface $logger
     * @param RequestLog $requestLog
     */
    public function __construct(
        Config $config,
        ClientFactory $clientFactory,
        SerializerInterface $serializer,
        LoggerInterface $logger,
        RequestLog $requestLog
    ) {
        $this->config = $config;
        $this->clientFactory = $clientFactory;
        $this->serializer = $serializer;
        $this->logger = $logger;
        $this->_requestLog = $requestLog;
    }

    public function doRequest(Request $request): array
    {
        $this->_requestLog
            ->setRequest($request->getHeaders())
            ->setStartTime();

        try {
            $response = $this->getClient()->request(
                $request->getMethod(),
                $request->getEndpoint(),
                $request->build()
            );

            $statusCode = $response->getStatusCode();
            if ($statusCode < 200 || $statusCode >= 300) {
                throw new ApiResponseException(__('Invalid HTTP status: %1', $statusCode));
            }

            $data = $response->getBody()->getContents();
            $this->_requestLog->setResponse($data);
            if (empty($data)) {
                throw new ApiResponseException(__('Response data is empty.'));
            }

            $data = $this->_cleanResponseData($data);

            $data = $this->serializer->unserialize($data);
            $this->_requestLog->setResponse($data);
        } catch (GuzzleException $e) {
            $this->_requestLog->addError($e->getMessage());

            $data = [];
        } catch (\Exception $e) {
            $this->_requestLog->addError($e->getMessage());

            $data = [];
        }

        $this->_requestLog
            ->setEndTime()
            ->record();

        return $data;
    }

    private function _cleanResponseData(string $data): string
    {
        $data = $this->_encodeConvert($data);

        return $this->_removeInvalidChar($data);
    }

    private function _encodeConvert($data): string
    {
        try {
            $encode = mb_detect_encoding($data, ['UTF-8', 'ISO-8859-1', 'Windows-1252', 'ASCII'], true);
            if ($encode !== false && $encode !== 'UTF-8') {
                return mb_convert_encoding($data, 'UTF-8', $encode);
            }
        } catch (\Exception $e) {
            $this->_requestLog->addError($e->getMessage());
        }

        return $data;
    }

    private function _removeInvalidChar(string $data): string
    {
        if (mb_detect_encoding($data, 'UTF-8', true) === false) {
            return preg_replace('/[\x00-\x1F\x80-\xFF]/', '', $data);
        }

        return $data;
    }

    private function getClient(): GuzzleClient
    {
        if (!$this->_client instanceof GuzzleClient) {
            return $this->clientFactory->create([
                'config' => array_merge_recursive([
                    'handler' => $this->getHandlerStack(),
                ], $this->config->getConfig())
            ]);
        }

        return $this->_client;
    }

    private function retryDecider(): callable
    {
        return function (
            $retries,
            RequestInterface $request,
            ?ResponseInterface $response,
            ?\RuntimeException $e
        ) {
            if ($retries >= $this->maxRetries) {
                return false;
            }

            if ($e instanceof ConnectException) {
                $this->logger->critical(__(
                    'Unable to connect to %1. Retrying (%2/%3)...',
                    $request->getUri(),
                    $retries + 1,
                    $this->maxRetries
                ));

                return true;
            }

            if ($response && in_array($response->getStatusCode(), [249, 429, 500, 502, 503, 504], true)) {
                $this->logger->critical(__(
                    'Something went wrong on the server. Retrying (%2/%3)...',
                    $retries + 1,
                    $this->maxRetries
                ));

                return true;
            }

            return false;
        };
    }

    private function retryDelay(): callable
    {
        return function ($retries) {
            /**
             * Exponential Backoff: Wait longer with each retry
             */
            return 1000 * $retries;
        };
    }

    private function getHandlerStack()
    {
        $handlerStack = HandlerStack::create();
        $retryMiddleware = Middleware::retry(
            $this->retryDecider(),
            $this->retryDelay()
        );
        $handlerStack->push($retryMiddleware);

        return $handlerStack;
    }
}
