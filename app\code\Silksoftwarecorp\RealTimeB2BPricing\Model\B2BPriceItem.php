<?php

namespace Silksoftwarecorp\RealTimeB2BPricing\Model;

use Magento\Framework\DataObject;
use Silksoftwarecorp\RealTimeB2BPricing\Api\Data\B2BPriceItemInterface;

/**
 * @method getItemId
 * @method getLocation
 * @method getItemDescription
 * @method getExtendedDescription
 * @method getCustomerPartNumber
 * @method getManufacturerName
 * @method getManufacturerPartNumber
 * @method getUnitOfMeasure
 * @method getLeadTime
 * @method getQuantityOnHand
 * @method getQuantityBackordered
 * @method getQuantityAvailable
 */
class B2BPriceItem extends DataObject implements B2BPriceItemInterface
{
    public function getSku()
    {
        return $this->getItemId();
    }

    public function getLocationId()
    {
        return $this->getLocation();
    }

    public function getPrice(): ?float
    {
        $price = $this->getData('price');

        return $price === null ? null : (float)$price;
    }

    public function getLastInvoiceDate(): ?string
    {
        return $this->getData('last_invoice_date');
    }
}
