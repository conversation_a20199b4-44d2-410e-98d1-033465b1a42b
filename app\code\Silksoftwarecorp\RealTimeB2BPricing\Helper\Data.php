<?php

namespace Silksoftwarecorp\RealTimeB2BPricing\Helper;

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Store\Model\ScopeInterface;

class Data extends AbstractHelper
{
    const XML_PATH_GENERAL_ACTIVE = 'realtime_b2b_pricing/general/active';
    const XML_PATH_GENERAL_DEBUG = 'realtime_b2b_pricing/general/debug';

    const XML_PATH_API_ENVIRONMENT = 'realtime_b2b_pricing/api/environment';

    const XML_PATH_API_URL ='realtime_b2b_pricing/api/url';

    const XML_PATH_API_USERNAME ='realtime_b2b_pricing/api/username';

    const XML_PATH_API_PASSWORD ='realtime_b2b_pricing/api/password';

    const XML_PATH_API_REQUEST_TYPE = 'realtime_b2b_pricing/api/request_type';

    const XML_PATH_API_SANDBOX_URL = 'realtime_b2b_pricing/api/sandbox_url';

    const XML_PATH_API_SANDBOX_USERNAME ='realtime_b2b_pricing/api/sandbox_username';

    const XML_PATH_API_SANDBOX_PASSWORD ='realtime_b2b_pricing/api/sandbox_password';

    const XML_PATH_API_SANDBOX_REQUEST_TYPE = 'realtime_b2b_pricing/api/sandbox_request_type';

    const XML_PATH_API_ENABLE_SSL_CERTIFICATE_VERIFY = 'realtime_b2b_pricing/api/enable_ssl_certificate_verify';

    const XML_PATH_API_TIMEOUT = 'realtime_b2b_pricing/api/timeout';

    const XML_PATH_API_CACHE_LIFETIME = 'realtime_b2b_pricing/api/cache_lifetime';

    const XML_PATH_CART_PRICE_VALIDITY_TIME = 'realtime_b2b_pricing/cart/price_validity_time';

    public function isActive($storeId = null): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_GENERAL_ACTIVE,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function isDebugged($storeId = null): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_GENERAL_DEBUG,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getApiEnvironment($storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_API_ENVIRONMENT,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getApiUrl($storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_API_URL,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getApiUsername($storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_API_USERNAME,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getApiPassword($storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_API_PASSWORD,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getApiRequestType($storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_API_REQUEST_TYPE,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getSandboxApiUrl($storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_API_SANDBOX_URL,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getSandboxApiUsername($storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_API_SANDBOX_USERNAME,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getSandboxApiPassword($storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_API_SANDBOX_PASSWORD,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getSandboxApiRequestType($storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_API_SANDBOX_REQUEST_TYPE,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function enableSSLCertificateVerify($storeId = null): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_API_ENABLE_SSL_CERTIFICATE_VERIFY,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getApiTimeout($storeId = null): int
    {
        return (int)$this->scopeConfig->getValue(
            self::XML_PATH_API_TIMEOUT,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getCacheLifetime($storeId = null): int
    {
        return (int)$this->scopeConfig->getValue(
            self::XML_PATH_API_CACHE_LIFETIME,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getCartPriceValidityTime($storeId = null): int
    {
        return (int)$this->scopeConfig->getValue(
            self::XML_PATH_CART_PRICE_VALIDITY_TIME,
            ScopeInterface::SCOPE_STORE,
        );
    }
}
