import styled from '@emotion/styled'

export const StyledShortDescription = styled.div`
  margin-top: 36px;

  h3 span {
    font-weight: 700;
    font-size: 20px !important;
    line-height: 26px;
    letter-spacing: 0.02em;
  }

  ul {
    padding-left: 24px;

    li {
      display: none;
      font-weight: 400;
      font-size: 16px;
      line-height: 26px;
      letter-spacing: 0.02em;
      color: var(--color-text);
      list-style-type: disc;

      &:nth-of-type(-n + 3) {
        display: list-item;
      }
    }
  }

  &.is-view-all ul li {
    display: list-item;
  }

  .view-all {
    margin-top: 2px;
    padding: 0;
    font-weight: 700;
    font-size: 14px;
    line-height: 20px;
    letter-spacing: 0.02em;
    text-decoration: underline;
    text-decoration-style: solid;
    text-decoration-offset: Auto;
    text-decoration-thickness: Auto;
    color: var(--color-primary) !important;
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    margin-top: 26px;
  }
`
