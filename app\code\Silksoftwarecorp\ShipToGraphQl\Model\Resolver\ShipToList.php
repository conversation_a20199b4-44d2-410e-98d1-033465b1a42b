<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Silksoftwarecorp\ShipToGraphQl\Model\Resolver;

use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlAuthorizationException;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Framework\GraphQl\Query\Uid;
use Magento\CustomerGraphQl\Model\Customer\GetCustomer;

/**
 * Fetches the customer gift registry
 */
class ShipToList implements ResolverInterface
{
    protected $companyShipToModel;
    protected $uidEncoder;
    protected $getCustomer;
    
    public function __construct(
        \Silksoftwarecorp\ShipToGraphQl\Model\ResourceModel\CompanyShipTo $companyShipTo,
        Uid $uidEncoder,
        GetCustomer $getCustomer
    ) {
        $this->companyShipToModel = $companyShipTo;
        $this->uidEncoder = $uidEncoder;
        $this->getCustomer = $getCustomer;
    }

    /**
     * @inheritdoc
     */
    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        /** @phpstan-ignore-next-line */
        if (false === $context->getExtensionAttributes()->getIsCustomer()) {
            throw new GraphQlAuthorizationException(__('The current customer isn\'t authorized.'));
        }
        
        $this->getCustomer->execute($context);

        $companyId = $args['company_id'];
        if (!is_numeric($companyId)) {
            $companyId = filter_var(
                $this->uidEncoder->decode(trim($companyId)),
                FILTER_VALIDATE_INT
            );
        }

        /**
         * $companyId === 0 should not be used explicitly in the request
         */
        if ($companyId <= 0) {
            throw new GraphQlInputException(__('Invalid company ID.'));
        }

        $items = [];
        $list = $this->companyShipToModel->getListByCompanyId($companyId);
        foreach ($list as $item) {
            $items[] = [
                "entity_id" => $item['entity_id'],
                "erp_cust_num" => $item['erp_cust_num'],
                "ship_to_num" => $item['ship_to_num'],
                "ship_to_name" => $item['ship_to_name'],
                "email" => $item['email'],
                "region_code" => $item['region_code'],
                "street" => $item['street'],
                "street_line2" => $item['street_line2'],
                "street_line3" => $item['street_line3'],
                "company_id" => $item['company_id'],
                "telephone" => $item['telephone'],
                "postcode" => $item['postcode'],
                "city" => $item['city'],
                "country_id" => $item['country_id'],
                "location_id" => $item['location_id'],
                "is_default" => (bool) $item['is_default'],
            ];
        }

        return [
            'company_id' => $companyId,
            'items' => $items
        ];
    }
}
