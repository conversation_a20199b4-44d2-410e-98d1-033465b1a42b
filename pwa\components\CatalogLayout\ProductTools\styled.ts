import styled from '@emotion/styled'

export const StyledProductTools = styled.div`
  display: flex;
  align-items: center;

  .list-select {
    width: 277px !important;
    height: 44px !important;

    .${({ theme }) => theme.namespace} {
      &-select-selector {
        padding: 0 16px !important;
        width: 277px;
        border-radius: 3px !important;
        border-color: #d9d9d9;

        input {
          height: 44px !important;
        }

        .option-title {
          display: none !important;
        }
      }

      &-select-selection-item {
        font-weight: 400;
        font-size: 15px;
        line-height: 21px;
        letter-spacing: 0.01em;
        color: var(--color-font);
      }
    }
  }

  .btn-measure {
    margin-left: 16px;
  }

  &.is-desabled {
    .select-icon,
    .${({ theme }) => theme.namespace}-select-selection-item {
      opacity: 0.3;
    }
  }

  &.is-simple {
    .btn-measure {
      display: none;
    }

    .list-select {
      width: auto !important;

      .option-title-text {
        font-weight: 700;
        font-size: 15px;
        line-height: 21px;
        letter-spacing: 0;
        color: var(--color-primary);
      }
    }
    .${({ theme }) => theme.namespace}-select {
      &-selector {
        padding-left: 0 !important;
        border: none !important;
        width: 135px !important;
        box-shadow: none !important;
      }
      &-item {
        padding: 5px 6px;
      }
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    display: block;

    .list-select {
      width: 100% !important;
      .${({ theme }) => theme.namespace} {
        &-select-selector {
          width: 100%;
        }
      }
    }

    .btn-measure {
      margin-top: 20px;
      margin-left: 0;
    }
  }
`
