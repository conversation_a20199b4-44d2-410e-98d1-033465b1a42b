import { memo } from 'react'
import Link from 'next/link'

import { StyledDashboardNav } from './styled'

const DashboardNav = () => {
  const navList = [
    {
      name: 'Make A Payment',
      href: '/account',
      icon: '#icon-credit-card',
      target: '_blank'
    },
    {
      name: 'Invoice history',
      href: '/account/invoice-history',
      icon: '#icon-invoice-history',
      target: '_blank'
    },
    {
      name: 'Backorders',
      href: '/account/backordered-products',
      icon: '#icon-backorders'
    },
    {
      name: 'Purchase History',
      href: '/account/orders',
      icon: '#icon-product-history'
    },
    {
      name: 'Order Pad',
      href: '/account/order-pad',
      icon: '#icon-order-pad'
    },
    {
      name: 'Lists',
      href: '/account/requisition-lists',
      icon: '#icon-to-do-list'
    }
  ]

  return (
    <StyledDashboardNav>
      {navList.map((item) => {
        const { name, href, icon } = item
        return (
          <Link href={href} title={name} key={name} target={item?.target || '_self'}>
            <svg width="32px" height="32px" fill="currentColor" focusable="false">
              <use xlinkHref={icon} />
            </svg>
            {name}
          </Link>
        )
      })}
    </StyledDashboardNav>
  )
}

export default memo(DashboardNav)
