/*
 * @Copyright: @ Silk Software Corp. All Rights Reserved
 */
import styled from 'styled-components'

export const StyledRequisition = styled.span`
  display: flex;
  width: 1.75rem;
  height: 1.75rem;
  cursor: ${(props) => (props.allowed ? 'progress' : 'pointer')};
  background-color: ${({ theme }) => theme.colors.white};
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgb(0 0 0 / 25%);
`

export const StyledOverlay = styled.ul`
  width: 11rem;
  font-size: 0.65rem;
  background-color: ${({ theme }) => theme.colors.white};
  box-shadow:
    0 3px 6px -4px rgb(0 0 0 / 12%),
    0 6px 16px 0 rgb(0 0 0 / 8%),
    0 9px 28px 8px rgb(0 0 0 / 5%);

  > li {
    display: flex;
    padding: 0.35rem 0.5rem;
    align-items: center;

    &:hover {
      cursor: pointer;
      background-color: ${({ theme }) => theme.colors.gray};
    }
  }
`

export const StyledCreated = styled.span`
  display: grid;
  grid-auto-flow: column;
  grid-gap: 0.25rem;
`
