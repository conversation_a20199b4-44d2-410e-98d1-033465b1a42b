import styled from '@emotion/styled'

export const StyledMiniAccount = styled.div`
  display: grid;
  cursor: pointer;
  grid-auto-flow: column;
  justify-content: flex-start;
  align-items: center;

  .icon {
    width: 44px;
    height: 44px;
    display: flex;
    background-color: #968c83;
    border-radius: 50%;
    justify-content: center;
    align-items: center;
  }

  .user {
    font-size: 15px;
  }

  .login-text {
    color: var(--color-font);
    font-size: 15px;
    font-weight: 600;
    cursor: pointer;
    margin-left: 10px;

    &:hover {
      color: var(--color-primary);
    }
  }

  .account-item {
    display: flex;
    align-items: center;
    color: var(--color-font);
    font-size: 15px;
    font-weight: 600;
    cursor: pointer;

    .account-text {
      margin-left: 10px;
    }

    &:hover {
      color: var(--color-primary);
    }
  }
`
export const StyledAccountDrawer = styled.div`
  .account-drawer-close {
    display: flex;
    justify-content: flex-end;
    cursor: pointer;
  }

  .account-drawer-title {
    font-weight: 700;
    font-size: 16px;
    line-height: 22px;
    letter-spacing: 0;
  }

  .account-drawer-logout {
    margin-top: 8px;
    font-weight: 700;
    font-size: 15px;
    line-height: 23px;
    letter-spacing: 0;
    text-decoration: underline;
    text-decoration-style: solid;
    text-decoration-thickness: 0;
    color: var(--color-primary);
    cursor: pointer;
  }

  .account-drawer-showroom {
    margin-top: 32px;

    .view-title {
      color: var(--color-font);
    }
  }

  .line {
    margin-top: 32px;
    width: 100%;
    height: 1px;
    background: #d9d9d9;
  }

  .account-list {
    h3 {
      margin-top: 24px;
      font-weight: 700;
      font-size: 20px;
      line-height: 26px;
      letter-spacing: 0;
      color: #101010;
    }
    ul {
      li {
        position: relative;

        a {
          font-weight: 400;
          font-size: 15px;
          line-height: 32px;
          letter-spacing: 0;
          color: #565656;
        }

        &.active {
          a {
            font-weight: 700;
            color: var(--color-primary);
          }
          &::after {
            content: '';
            position: absolute;
            top: 2px;
            left: -25px;
            width: 5px;
            height: 24px;
            background: var(--color-primary);
          }
        }
      }
    }
  }
`
