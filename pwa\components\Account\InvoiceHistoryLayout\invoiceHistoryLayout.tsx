import dynamic from 'next/dynamic'
import { Input, DatePicker, Select } from 'antd'
import { clsx } from 'clsx'
import dayjs from 'dayjs'

import { LineContainer } from '@ranger-theme/ui'
import CommonTable from '@/components/Common/CommonTable'
import CommonLoading from '@/components/Common/CommonLoading'
import CommonLink from '@/components/Common/CommonLink'
import CommonAccountPageLayout from '@/components/Common/CommonAccountPageLayout'
import CommonCheckbox from '@/components/Common/CommonCheckbox'
import CommonButton from '@/components/Common/CommonButton'
import BasePrice from '@/components/BasePrice'
import { useInvoiceHistory } from '@/hooks/InvoiceHistory'

import {
  StyledInvoiceHistory,
  StyledPayNowPanel,
  StyledPaySelect,
  StyledSelectItem
} from './styled'

const TableHeaderSearchItem = dynamic(() => import('@/components/Common/TableHeaderSearchItem'))
const CommonTableSort = dynamic(() => import('@/components/Common/CommonTableSort'))

const InvoiceHistoryLayout = () => {
  const {
    loading,
    dataItems,
    page,
    handlePageChange,
    handlePageSizeChange,
    onSortChange,
    sort,
    onFilterInputChange,
    onToDateChange,
    onFromDateChange,
    onPaySelect,
    paySelectedList,
    columnSelectedType,
    handleColumnSelect,
    onPaidSelectChange,
    dateValues,
    curPaySelectedItems,
    isMobile
  } = useInvoiceHistory()

  const baseColumns = [
    {
      title: (
        <TableHeaderSearchItem>
          <p
            aria-hidden
            onClick={() => {
              onSortChange('invoice_number')
            }}>
            Invoice Number
            <CommonTableSort sortOrder={sort.code === 'invoice_number' ? sort.direction : ''} />
          </p>
          <div className="item-search">
            <Input
              allowClear
              onChange={(e) => {
                onFilterInputChange(e, 'invoice_number')
              }}
            />
          </div>
        </TableHeaderSearchItem>
      ),
      dataIndex: 'invoice_number',
      key: 'invoice_number',
      width: 162,
      className: 'primary-text',
      render: (param, record) => (
        <CommonLink href={record.pdf_url} title={param} target="_blank">
          <span dangerouslySetInnerHTML={{ __html: param || '' }} />
        </CommonLink>
      )
    },
    {
      title: (
        <TableHeaderSearchItem>
          <p
            aria-hidden
            onClick={() => {
              onSortChange('order_number')
            }}>
            Order Number
            <CommonTableSort sortOrder={sort.code === 'order_number' ? sort.direction : ''} />
          </p>
          <div className="item-search">
            <Input
              allowClear
              onChange={(e) => {
                onFilterInputChange(e, 'order_number')
              }}
            />
          </div>
        </TableHeaderSearchItem>
      ),
      dataIndex: 'order_number',
      key: 'order_number',
      width: 162
    },
    {
      title: (
        <TableHeaderSearchItem>
          <p
            aria-hidden
            onClick={() => {
              onSortChange('order_date')
            }}>
            Order Date
            <CommonTableSort sortOrder={sort.code === 'order_date' ? sort.direction : ''} />
          </p>
          <div className="item-search">
            <DatePicker
              className="date-from"
              placeholder="From"
              suffixIcon={null}
              format="MM/DD/YYYY"
              showNow={false}
              value={dateValues.from}
              onChange={onFromDateChange}
            />
            <DatePicker
              placeholder="To"
              suffixIcon={null}
              format="MM/DD/YYYY"
              showNow={false}
              value={dateValues.to}
              onChange={onToDateChange}
            />
          </div>
        </TableHeaderSearchItem>
      ),
      dataIndex: 'order_date',
      key: 'order_date',
      width: 182,
      render: (param) => <div>{dayjs(param).format('MM/DD/YYYY')}</div>
    },
    {
      title: (
        <TableHeaderSearchItem>
          <p
            aria-hidden
            onClick={() => {
              onSortChange('total_amount')
            }}>
            Total
            <CommonTableSort sortOrder={sort.code === 'total_amount' ? sort.direction : ''} />
          </p>
          <div className="item-search">
            <Input
              allowClear
              onChange={(e) => {
                onFilterInputChange(e, 'total_amount')
              }}
            />
          </div>
        </TableHeaderSearchItem>
      ),
      dataIndex: 'total_amount',
      key: 'total_amount',
      width: 130,
      render: (param) => <BasePrice value={param} />
    },
    {
      title: (
        <TableHeaderSearchItem>
          <p
            aria-hidden
            onClick={() => {
              onSortChange('po_number')
            }}>
            PO Number
            <CommonTableSort sortOrder={sort.code === 'po_number' ? sort.direction : ''} />
          </p>
          <div className="item-search">
            <Input
              allowClear
              onChange={(e) => {
                onFilterInputChange(e, 'po_number')
              }}
            />
          </div>
        </TableHeaderSearchItem>
      ),
      dataIndex: 'po_number',
      key: 'po_number'
    },
    {
      title: (
        <TableHeaderSearchItem>
          <p>Paid In Full</p>
          <div className="item-search">
            <Select
              placeholder="Select"
              onChange={onPaidSelectChange}
              allowClear
              suffixIcon={
                <svg
                  width="1em"
                  height="1em"
                  fill="currentColor"
                  aria-hidden="true"
                  focusable="false">
                  <use xlinkHref="#icon-select-suffix" />
                </svg>
              }>
              <Select.Option key="yes" value="yes">
                Yes
              </Select.Option>
              <Select.Option key="no" value="no">
                No
              </Select.Option>
            </Select>
          </div>
        </TableHeaderSearchItem>
      ),
      dataIndex: 'paid_in_full',
      key: 'invoice_number',
      width: 182,
      render: (param) => <div>{param ? 'Yes' : 'No'}</div>
    }
  ]

  const PaySelectItem = (
    <StyledSelectItem>
      {columnSelectedType === 'all' ? (
        <CommonCheckbox checked onChange={handleColumnSelect} />
      ) : (
        <span
          aria-hidden
          onClick={handleColumnSelect}
          className={clsx({
            'selected-some': columnSelectedType === 'some'
          })}
        />
      )}
      <p className="mob-select-title">Select</p>
    </StyledSelectItem>
  )

  const paySelectColumn = {
    title: (
      <TableHeaderSearchItem>
        <p className="header-select">Select</p>
        <div className="item-search">{PaySelectItem}</div>
      </TableHeaderSearchItem>
    ),
    dataIndex: 'paid_in_full',
    key: 'invoice_number',
    width: 146,
    mobileContentFull: true,
    render: (param, record) => {
      const selected = paySelectedList.includes(record?.invoice_number)
      const paySelectVisible = !param
      return (
        <div style={{ height: isMobile && !paySelectVisible ? 0 : '50px' }}>
          {paySelectVisible && (
            <StyledPaySelect
              className={clsx({ selected })}
              onClick={() => {
                onPaySelect(record?.invoice_number)
              }}>
              <CommonCheckbox checked={selected} />
              PAY
            </StyledPaySelect>
          )}
        </div>
      )
    }
  }

  const columns = isMobile ? [paySelectColumn, ...baseColumns] : [...baseColumns, paySelectColumn]

  return (
    <CommonAccountPageLayout title="Invoice History">
      <CommonLoading spinning={loading}>
        <StyledInvoiceHistory>
          {!loading && (
            <>
              {isMobile && PaySelectItem}
              <CommonTable
                rowKey={(params) => params.invoice_number}
                columns={columns}
                dataSource={dataItems.items}
                total={dataItems.total}
                totalCountVisible
                current={page.currentPage}
                pageSize={page.pageSize}
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
              />
              {columnSelectedType !== 'empty' && (
                <StyledPayNowPanel>
                  <LineContainer>
                    <p>
                      <b>{curPaySelectedItems.length}</b> invoice(s) selected for payment
                    </p>
                    <CommonButton height={61}>PAY NOW</CommonButton>
                  </LineContainer>
                </StyledPayNowPanel>
              )}
            </>
          )}
        </StyledInvoiceHistory>
      </CommonLoading>
    </CommonAccountPageLayout>
  )
}

export default InvoiceHistoryLayout
