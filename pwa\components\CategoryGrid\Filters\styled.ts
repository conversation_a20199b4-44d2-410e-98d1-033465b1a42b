import styled from '@emotion/styled'

export const StyledFilters = styled.div`
  .${({ theme }) => theme.namespace} {
    &-select {
      .${({ theme }) => theme.namespace} {
        &-select-selection-placeholder {
          color: var(--color-font);
          font-size: 16px;
          font-weight: 600;
        }
      }
    }

    &-select-selection-item {
      font-size: 15px;
      font-weight: 400;
    }

    &-select-item-option-content {
      font-size: 15px;
      font-weight: 400;
    }
  }

  .filter-by-title {
    margin-top: 12px;
    margin-bottom: 0;
    display: flex;
    align-items: center;
    font-weight: 700;
    font-size: 20px;
    line-height: 26px;
    letter-spacing: 0;
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    padding-bottom: 70px;
    min-height: 100vh;

    .apply-filter {
      position: fixed;
      left: 0;
      bottom: 0;
      z-index: 1;
      width: 100%;
      height: 53px;
      border-radius: 0;
      border: none;
      background: var(--color-primary) !important;

      span {
        font-weight: 700;
        font-size: 16px;
        line-height: 21px;
        letter-spacing: 0.03em;
        color: var(--color-white);
      }
    }
  }
`

export const StyledFilterChoose = styled.div`
  margin-top: 20px;
  border-top: 1px solid #d9d9d9;

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    margin-top: 0;
  }
`

export const StyledMobileFilterHeader = styled.div`
  display: grid;
  grid-template-columns: auto 1fr auto;
  grid-column-gap: 10px;
  align-items: center;
  padding: 14px 16px;

  h3 {
    margin: 0;
    font-weight: 700;
    font-size: 24px;
    line-height: 30px;
    letter-spacing: 0;
  }

  .clear-btn {
    padding: 0;
    font-weight: 700;
    font-size: 15px;
    line-height: 21px;
    letter-spacing: 0.01em;
    text-decoration: underline;
    text-decoration-style: solid;
    text-decoration-thickness: 0;
    color: var(--color-primary) !important;
  }

  .close-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background-color: var(--color-bg-base);
  }
`
