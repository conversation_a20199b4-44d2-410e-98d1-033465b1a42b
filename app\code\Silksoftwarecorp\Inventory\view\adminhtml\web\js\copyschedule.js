require(
    [
        'jquery',
        "Magento_Ui/js/lib/view/utils/dom-observer",
        'mage/translate'
    ],
    function ($, domObserver) {
        domObserver.get('.inventory_branck_schedule_fill', function () {
            $('.inventory_branck_schedule_fill').click(function (e) {
                const scheduleWrapper = $(this).parents('.schedule_fill_wrapper');
                console.log(scheduleWrapper);
                var monday = scheduleWrapper.find('div[data-index="monday"]').find('.admin__field-control'),
                    mondayData = [],
                    days = ['tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
                monday.each(function () {
                    var field = $(this).find('select');
                    if (field.length !== null && !$(this).hasClass('admin__control-grouped')) {
                        mondayData.push(field.val());
                    }
                });

                days.forEach(function (day, index) {
                    var day = scheduleWrapper.find('div[data-index="' + day + '"]').find('.admin__field-control'),
                        step = 0;
                    day.each(function () {
                        var dayField = $(this).find('select');
                        if (dayField.length !== null && !$(this).hasClass('admin__control-grouped')) {
                            dayField.val(mondayData[step]).change();
                            step++;
                        }
                    });
                });
            });
        });
    }
);
