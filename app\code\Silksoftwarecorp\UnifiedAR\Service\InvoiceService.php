<?php

namespace Silksoftwarecorp\UnifiedAR\Service;

use Silksoftwarecorp\UnifiedAR\Model\Invoice\Mapper as InvoiceMapper;
use Silksoftwarecorp\UnifiedAR\Model\UnifiedArApiService;

/**
 * Invoice Service
 */
class InvoiceService
{
    /**
     * @var UnifiedArApiService
     */
    protected $unifiedArApiService;

    /**
     * @var InvoiceMapper
     */
    protected $invoiceMapper;

    /**
     * Constructor
     * @param UnifiedArApiService $unifiedArApiService
     * @param InvoiceMapper $invoiceMapper
     */
    public function __construct(
        UnifiedArApiService $unifiedArApiService,
        InvoiceMapper $invoiceMapper
    ) {
        $this->unifiedArApiService = $unifiedArApiService;
        $this->invoiceMapper = $invoiceMapper;
    }

    /**
     * Get invoice list for customer
     * @param string $customerId
     * @return array
     */
    public function getList(string $customerId): array
    {
        if (empty($customerId)) {
            return [];
        }

        $invoices = $this->unifiedArApiService->fetchInvoices($customerId);

        $data = [];
        foreach ($invoices as $invoice) {
            $data[] = $this->invoiceMapper->execute($invoice);
        }

        return $data;
    }
}
