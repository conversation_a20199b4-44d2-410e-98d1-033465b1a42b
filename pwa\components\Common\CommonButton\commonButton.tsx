import { memo } from 'react'
import { clsx } from 'clsx'

import { StyledCommonButton } from './styled'

const CommonButton = ({
  children,
  className = '',
  type = 'primary',
  dark = false,
  height = 53,
  ph = 32,
  fontSize = 16,
  uppercase = true,
  underline = false,
  ...commonButtonProps
}) => {
  return (
    <StyledCommonButton
      className={clsx(`${className} common-button-component`, {
        'is-dark': dark,
        'is-underline': underline,
        'is-uppercase': uppercase
      })}
      type={type}
      height={height}
      ph={ph}
      fontSize={fontSize}
      {...commonButtonProps}>
      {children}
    </StyledCommonButton>
  )
}

export default memo(CommonButton)
