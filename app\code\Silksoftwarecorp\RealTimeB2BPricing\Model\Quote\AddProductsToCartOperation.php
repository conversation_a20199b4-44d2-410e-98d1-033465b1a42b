<?php

namespace Silksoftwarecorp\RealTimeB2BPricing\Model\Quote;

use Magento\Catalog\Model\Product;
use Magento\Framework\Stdlib\DateTime\DateTime;
use Magento\Quote\Model\Cart\ProductReaderInterface;
use Magento\Quote\Model\Quote;
use Magento\Quote\Model\Quote\Item;
use Silksoftwarecorp\Customer\Model\B2BUserChecker;
use Silksoftwarecorp\RealTimeB2BPricing\Api\Data\B2BPriceItemInterface;
use Silksoftwarecorp\RealTimeB2BPricing\Logger\LoggerInterface;
use Silksoftwarecorp\RealTimeB2BPricing\Model\ShipTo\LocationProvider;
use Silksoftwarecorp\RealTimeB2BPricing\Api\RealtimeB2BPriceServiceInterface;
use Silksoftwarecorp\RealTimeB2BPricing\Helper\Data as HelperData;

class AddProductsToCartOperation
{
    /**
     * @var B2BUserChecker
     */
    protected $b2bUserChecker;

    /**
     * @var RealtimeB2BPriceServiceInterface
     */
    protected $realtimeB2bPriceService;

    /**
     * @var ProductReaderInterface
     */
    private $productReader;

    /**
     * @var LocationProvider
     */
    protected $locationProvider;

    /**
     * @var DateTime
     */
    protected $dateTime;

    /**
     * @var HelperData
     */
    protected $helper;

    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * @param B2BUserChecker $b2bUserChecker
     * @param RealtimeB2BPriceServiceInterface $realtimeB2bPriceService
     * @param ProductReaderInterface $productReader
     * @param LocationProvider $locationProvider
     * @param DateTime $dateTime
     * @param HelperData $helper
     * @param LoggerInterface $logger
     */
    public function __construct(
        B2BUserChecker $b2bUserChecker,
        RealtimeB2BPriceServiceInterface $realtimeB2bPriceService,
        ProductReaderInterface $productReader,
        LocationProvider $locationProvider,
        DateTime $dateTime,
        HelperData $helper,
        LoggerInterface $logger
    ) {
        $this->b2bUserChecker = $b2bUserChecker;
        $this->realtimeB2bPriceService = $realtimeB2bPriceService;
        $this->productReader = $productReader;
        $this->locationProvider = $locationProvider;
        $this->dateTime = $dateTime;
        $this->helper = $helper;
        $this->logger = $logger;
    }

    public function preloadB2BPrices(
        Quote $quote,
        array $cartItems
    ): void {
        $storeId = $quote->getStoreId();
        if (!$this->helper->isActive($storeId)) {
            return;
        }

        $customerId = $quote->getCustomerId();
        if (!$customerId || !$this->b2bUserChecker->isB2BUser($customerId)) {
            return;
        }

        try {
            $skus = $this->_filterProductSkus($cartItems, $storeId);
            if ($skus) {
                $this->realtimeB2bPriceService->getPriceItems(
                    $customerId,
                    $this->getLocationId($storeId),
                    $skus
                );
            }
        } catch (\Exception $e) {
            $this->logger->critical(__($e->getMessage()));
        }
    }

    /**
     * Filter out the SKUs that do not exist product
     *
     * @param array $cartItems
     * @param $storeId
     * @return array
     */
    protected function _filterProductSkus(array $cartItems, $storeId): array
    {
        $skus = array_map(function ($cartItem) {
            /**@var \Magento\Quote\Model\Cart\Data\CartItem $cartItem*/
            return $cartItem->getSku();
        }, $cartItems);

        $this->productReader->loadProducts($skus, $storeId);

        $filteredSkus = [];
        foreach ($cartItems as $cartItem) {
            $sku = $cartItem->getSku();
            $product = $this->productReader->getProductBySku($sku);
            if (!$product || !$product->isSaleable() || !$product->isAvailable()) {
                continue;
            }

            $filteredSkus[] = $sku;
        }

        return $filteredSkus;
    }

    public function setB2BPriceToQuoteItem(Item $item, Product $candidate): void
    {
        $storeId = $item->getQuote()->getStoreId();
        if (!$this->helper->isActive($storeId)) {
            return;
        }

        $customerId = $item->getQuote()->getCustomerId();
        if (!$customerId || !$this->b2bUserChecker->isB2BUser($customerId)) {
            return;
        }

        try {
            $priceItem = $this->realtimeB2bPriceService->getPriceItem(
                $customerId,
                $this->getLocationId($storeId),
                $item->getSku()
            );

            if ($priceItem instanceof B2BPriceItemInterface) {
                if (!$candidate->getParentProductId() && $priceItem->getPrice() !== null) {
                    $item->setCustomPrice($priceItem->getPrice());
                    $item->setOriginalCustomPrice($priceItem->getPrice());
                    $item->setData('b2b_price_updated_at', $this->dateTime->gmtTimestamp());
                    $item->getProduct()->setIsSuperMode(true);
                }
            }
        } catch (\Exception $e) {
            $this->logger->critical(__($e->getMessage()));

            $item->addErrorInfo(
                message: __('Unable to add product to cart. Sku: %1', $candidate->getSku()),
            );
        }
    }

    private function getLocationId($storeId = null): string
    {
        return $this->locationProvider->getLocationId($storeId);
    }
}
