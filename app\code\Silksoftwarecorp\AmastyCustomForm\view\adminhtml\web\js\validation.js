/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */

require([
    'jquery',
    'mage/validation'
], function ($) {
    'use strict';

    $.validator.addMethod(
        'validate-multiple-emails',
        function (value) {
            if (!value) {
                return true; // Let required-entry handle empty values
            }

            // Split by comma and validate each email
            var emails = value.split(',');
            var emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

            let valid = true;
            emails.forEach(function (email) {
                if (!emailPattern.test(email.trim())) {
                    valid = false;
                }
            });

            return valid;
        },
        $.mage.__('Please enter valid email addresses separated by commas.')
    );
});
