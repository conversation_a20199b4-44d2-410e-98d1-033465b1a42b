import styled from '@emotion/styled'

export const StyledGlobalMsg = styled.span`
  position: fixed;
  z-index: 999;
  top: 212px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  justify-content: center;
  align-items: center;
  width: 578px;
  min-height: 53px;
  background-color: #ddecdd;
  border-radius: 3px;
  transition: 300ms all;

  .global-msg-cart-success {
    display: grid;
    grid-template-columns: auto 1fr auto;
    grid-column-gap: 4px;
    align-items: center;
    font-weight: 700;
    font-size: 14px;
    line-height: 20px;
    letter-spacing: 0;
    color: #21881f;

    .success-icon {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: #21881f;
    }

    a {
      font-weight: 700;
      font-size: 14px;
      line-height: 20px;
      letter-spacing: 0;
      color: #21881f !important;
      text-decoration: underline !important;
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    top: 136px;
    width: calc(100vw - 32px);
    min-height: 42px;
    border-radius: 4px;
  }
`
