import Link from 'next/link'
import { useSelector } from 'react-redux'

import { StyledMiniCart } from './styled'

const MiniCart = () => {
  const cartQty = useSelector((state: Store) => state.cart.cartQty)

  return (
    <StyledMiniCart>
      <Link href="/checkout/cart" className="cart">
        <svg
          className="bag"
          width="20px"
          height="18px"
          fill="currentColor"
          aria-hidden="true"
          focusable="false">
          <use xlinkHref="#icon-cart" />
        </svg>
        <span className="count">{cartQty}</span>
      </Link>
    </StyledMiniCart>
  )
}

export default MiniCart
