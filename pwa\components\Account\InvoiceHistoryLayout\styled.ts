import styled from '@emotion/styled'

export const StyledInvoiceHistory = styled.div`
  min-height: 200px;

  table thead th {
    padding: 0 !important;

    .header-select {
      padding-left: 36px;
    }
  }

  table tbody tr td {
    min-height: 170px;
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    .common-table-sort {
      display: none;
    }
  }
`

export const StyledPaySelect = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  width: 114px;
  height: 50px;
  border-radius: 3px;
  font-weight: 700;
  font-size: 16px;
  line-height: 21px;
  letter-spacing: 0.03em;
  cursor: pointer;
  transition: all 0.3s;

  div {
    margin-right: 14px;
  }

  &.selected {
    background: var(--color-primary);
    color: var(--color-white);
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    width: 98px;
  }
`

export const StyledSelectItem = styled.div`
  display: flex;
  align-items: center;
  height: 44px;
  margin-left: 20px;

  span {
    position: relative;
    display: block;
    width: 26px;
    height: 26px;
    border: 1px solid #d9d9d9;
    border-radius: 3px;
    background: var(--color-white);
    cursor: pointer;

    &.selected-some::after {
      position: absolute;
      top: 5px;
      left: 5px;
      z-index: 1;
      content: '';
      width: 14px;
      height: 14px;
      border-radius: 1px;
      background: var(--color-primary);
    }
  }

  .mob-select-title {
    display: none;
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    margin-left: 0;
    margin-bottom: 16px;
    height: auto;

    .mob-select-title {
      margin-left: 10px;
      display: block;
      font-weight: 700;
      font-size: 15px;
      line-height: 21px;
      letter-spacing: 0;
      color: var(--color-black);
    }
  }
`

export const StyledPayNowPanel = styled.div`
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  margin: 0 auto;
  z-index: 9;
  width: 100%;
  max-width: 1440px;
  background: #012746;

  .container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 85px;
  }

  p {
    font-weight: 700;
    font-size: 18px;
    line-height: 24px;
    letter-spacing: 0;
    color: var(--color-white);
  }

  button {
    margin-left: 40px;
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    .container {
      padding: 0 16px;
      height: 69px;
    }

    p {
      font-weight: 400;
      font-size: 16px;
      line-height: 19px;
      letter-spacing: 0;
    }

    button {
      height: 45px !important;
    }
  }
`
