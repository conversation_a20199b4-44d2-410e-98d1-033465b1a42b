import { memo, useCallback } from 'react'
import { clsx } from 'clsx'

import { StyledCommonCheckbox } from './styled'

const CommonCheckbox = ({ checked = false, onChange }) => {
  const handleCheck = useCallback(() => {
    if (onChange) {
      onChange(!checked)
    }
  }, [checked, onChange])

  return (
    <StyledCommonCheckbox className={clsx({ checked })} onClick={handleCheck}>
      <svg width="14px" height="11px" fill="currentColor" aria-hidden="true" focusable="false">
        <use xlinkHref="#icon-checkbox-check" />
      </svg>
    </StyledCommonCheckbox>
  )
}

export default memo(CommonCheckbox)
