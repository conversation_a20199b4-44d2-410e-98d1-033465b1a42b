<?php

namespace Silksoftwarecorp\Catalog\Model\Category\Attribute\Source;

use Magento\Eav\Model\Entity\Attribute\Source\AbstractSource;

class HowToMeasure extends AbstractSource
{
    /**
     * Get all options
     *
     * @return array
     */
    public function getAllOptions()
    {
        if (!$this->_options) {
            $this->_options = [
                ['value' => '', 'label' => __('-- Please Select --')],
                ['value' => 'doors', 'label' => __('Doors')],
                ['value' => 'windows', 'label' => __('Windows')],
                ['value' => 'skirting', 'label' => __('Skirting')],
                ['value' => 'tubs', 'label' => __('Tubs')],
            ];
        }
        return $this->_options;
    }

    /**
     * Get option text by value
     *
     * @param string|integer $value
     * @return string|bool
     */
    public function getOptionText($value)
    {
        foreach ($this->getAllOptions() as $option) {
            if ($option['value'] == $value) {
                return $option['label'];
            }
        }
        return false;
    }
}
