import { useState, memo } from 'react'
import { Modal } from 'antd'
import { FormattedMessage } from 'react-intl'
import type { FC } from 'react'

interface ConfirmModalProps {
  onOk?: () => void
  onCancel?: () => {}
  trigger?: any
  children?: any
}

const ConfirmModal: FC<ConfirmModalProps> = memo(
  ({ children, trigger = '', onOk, onCancel, ...props }) => {
    const [open, setOpen] = useState(false)
    const showModal = () => {
      setOpen(true)
    }

    const handleOk = async () => {
      setOpen(false)
      onOk?.()
    }

    const handleCancel = () => {
      setOpen(false)
      onCancel?.()
    }

    return (
      <>
        <span onClick={showModal} onKeyDown={() => {}} role="button" tabIndex={0}>
          {trigger}
        </span>
        <Modal
          centered
          open={open}
          okText={<FormattedMessage id="global.confirm" />}
          cancelText={<FormattedMessage id="global.cancel" />}
          onOk={handleOk}
          onCancel={handleCancel}
          {...props}
        >
          {children}
        </Modal>
      </>
    )
  }
)

export default ConfirmModal
