import Link from 'next/link'
import { useSelector } from 'react-redux'
import { FormattedMessage } from 'react-intl'

import { StyledSubCategories } from './styled'

const SubCategories = ({ categories }) => {
  const storeConfig = useSelector((state: any) => state.app.storeConfig)
  const suffix: string = storeConfig?.category_url ?? ''

  return (
    <StyledSubCategories>
      <h4>
        <FormattedMessage id="plp.subCategories" />
      </h4>
      {categories.length > 0 && (
        <ul className="list">
          {categories.map((category: any) => {
            const { name, url_path } = category
            return (
              <li key={url_path}>
                <Link href={`/${url_path}${suffix}`} title={name}>
                  <span dangerouslySetInnerHTML={{ __html: name }} />
                </Link>
              </li>
            )
          })}
        </ul>
      )}
    </StyledSubCategories>
  )
}

export default SubCategories
