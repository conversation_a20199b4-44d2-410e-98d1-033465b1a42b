import styled from '@emotion/styled'

export const StyledNavigationMobile = styled.div`
  .nav-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 44px;
    height: 44px;
    background: #968c83;
    border-radius: 50%;
  }
`

export const StyledMbNavigation = styled.div`
  padding-bottom: 86px;

  .mb-navigation-header {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: 16px;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #d9d9d9;

    div {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 92px;
      border-radius: 4px;
      background: var(--color-primary);
      font-weight: 700;
      font-size: 14px;
      line-height: 20px;
      letter-spacing: 0.01em;
      text-transform: capitalize;
      color: var(--color-white);

      svg {
        margin-bottom: 10px;
      }
    }
  }

  .all-products-drawer-header {
    border-bottom: 1px solid #d9d9d9;

    .back {
      display: grid;
      padding: 13px 16px;
      grid-auto-flow: column;
      grid-column-gap: 8px;
      justify-content: flex-start;
      align-items: center;
      margin-top: 0;

      span {
        font-weight: 700;
        font-size: 14px;
        line-height: 20px;
        letter-spacing: 0;
      }
    }

    h3 {
      padding: 16px;
      margin-bottom: 0;
      font-weight: 700;
      font-size: 24px;
      line-height: 30px;
      letter-spacing: 0;
    }
  }

  .item {
    display: flex;
    padding: 16px;
    border-bottom: 1px solid #d9d9d9;
    justify-content: space-between;
    align-items: center;

    .first-menu {
      font-weight: 700;
      font-size: 15px;
      line-height: 23px;
      letter-spacing: 0.02em;
      vertical-align: middle;
      color: var(--color-font);
    }

    .arrow-wrapper {
      flex: 1;
      margin-left: 24px;
      text-align: right;
    }
  }
`

export const StyledSubMenu = styled.div`
  .back {
    display: grid;
    padding: 13px 16px;
    grid-auto-flow: column;
    grid-column-gap: 8px;
    justify-content: flex-start;
    align-items: center;
    margin-top: 0;

    span {
      font-weight: 700;
      font-size: 14px;
      line-height: 20px;
      letter-spacing: 0;
    }
  }

  h3 {
    padding: 16px;
    margin-bottom: 0;
    font-weight: 700;
    font-size: 24px;
    line-height: 30px;
    letter-spacing: 0;
  }

  .lowmenu-item {
    padding: 16px 0 16px 16px;
    border-bottom: unset;
    border-top: 1px solid #d9d9d9;

    .title {
      margin-bottom: 0;
      display: inline-block;
      font-weight: 400;
      font-size: 15px;
      line-height: 23px;
      letter-spacing: 0.02em;
      vertical-align: middle;
      color: var(--color-font);
    }

    &.lowmenu-item-only {
      .title {
        display: block;
      }
    }

    .content {
      margin-top: 8px;

      .submenu-item {
        a {
          display: inline-block;
          padding: 8px;
          font-weight: 400;
          font-size: 14px;
          line-height: 23px;
          letter-spacing: 0.02em;
          vertical-align: middle;
          color: var(--color-text);
        }
      }
    }
  }

  .${({ theme }) => theme.namespace} {
    &-collapse {
      border-width: 0;
      border-radius: unset;
      background-color: transparent;
    }

    &-collapse-header {
      padding: 0 !important;
    }

    &-collapse-expand-icon {
      position: absolute;
      top: 50%;
      right: 16px;
      z-index: 10;
      transform: translateY(-50%);
    }

    &-collapse-content-box {
      padding: 0 !important;
    }

    &-collapse-content {
      border-top: unset !important;
    }
  }
`
