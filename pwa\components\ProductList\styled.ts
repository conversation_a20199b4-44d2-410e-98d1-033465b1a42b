import styled from '@emotion/styled'

export const StyledMatch = styled.div`
  margin-bottom: 30px;
  font-size: 16px;
`

export const StyledProductCounter = styled.div`
  flex: 1;
  padding-left: 32px;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0;

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    padding-top: 16px;
    padding-left: 0;
  }
`

export const StyledPlpPanel = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 38px;

  .product-layout-controller {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-column-gap: 15px;
    align-items: center;

    svg:hover {
      cursor: pointer;
    }
  }
`

export const StyledProductList = styled.div<{ isHorizontal: boolean }>`
  display: grid;
  grid-template-columns: ${(props) =>
    props.isHorizontal ? 'minmax(0, 1fr)' : 'repeat(3, minmax(0, 1fr))'};
  grid-gap: 24px;
  margin-top: 20px;
  margin-bottom: 24px;

  .empty {
    font-size: 18px;
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    grid-template-columns: 1fr;
  }
`

export const StyledPagination = styled.div`
  display: flex;
  justify-content: center;

  .${({ theme }) => theme.namespace} {
    &-pagination {
      .${({ theme }) => theme.namespace} {
        &-pagination-item-active {
          background-color: #003865 !important;

          a {
            color: ${({ theme }) => theme.colors.white} !important;
          }
        }

        &-pagination-disabled {
          display: none;
        }

        &-pagination-item {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 41px;
          line-height: 40px;
          height: 41px;
          margin-right: 10px;
          border-width: 0;
          background-color: rgba(150, 140, 131, 0.1);
          border-radius: 3px;

          a {
            padding: 0;
            font-weight: 700;
            font-size: 16px;
            line-height: 22px;
            letter-spacing: 0.02em;
            color: var(--color-font);
          }
        }

        &-pagination-next,
        &-pagination-prev {
          width: 41px;
          line-height: 41px;
          height: 41px;
          background-color: rgba(150, 140, 131, 0.1);
          border-radius: 3px;

          .prev-icon {
            transform: rotate(180deg);
          }
        }
      }
    }
  }
`
