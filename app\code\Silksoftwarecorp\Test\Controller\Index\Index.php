<?php
namespace Silksoftwarecorp\Test\Controller\Index;
use Magento\Framework\App\ObjectManager;
use Silksoftwarecorp\EDI\Model\EdiApiManagement;
use Silksoftwarecorp\EDI\Service\FrequentlyOrderedProductsService;
use Silksoftwarecorp\UnifiedAR\Model\InvoiceApi;

class Index extends \Magento\Framework\App\Action\Action
{
    protected $resultPageFactory;

    public function __construct(
        \Magento\Framework\App\Action\Context $context,
        \Magento\Framework\View\Result\PageFactory $resultPageFactory)
    {
        $this->resultPageFactory = $resultPageFactory;
        parent::__construct($context);
    }

    public function execute()
    {
        $this->testFrequentlyOrderedProducts();
    }

    protected function testItemsApi()
    {
        $service = ObjectManager::getInstance()->get(EdiApiManagement::class);
        $data = $service->getItems(
            36787,
            100,
            ['0377702', '0377998','0379033','0377700','0377721','0377711','0377974','0377722','0377701']
        );

        echo json_encode($data);
    }

    protected function testFrequentlyOrderedProducts()
    {
        $service = ObjectManager::getInstance()->get(FrequentlyOrderedProductsService::class);
//        $data = $service->getList(22050);
        $data = $service->getList(37831);

        echo json_encode($data);
    }

    protected function testInvoiceApi()
    {
        $invoiceApi = ObjectManager::getInstance()->get(InvoiceApi::class);
        $data = $invoiceApi->fetchInvoices(17640);
        echo json_encode($data);
    }
}
