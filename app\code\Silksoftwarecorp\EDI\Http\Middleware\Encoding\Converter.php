<?php

namespace Silksoftwarecorp\EDI\Http\Middleware\Encoding;

use Psr\Http\Message\MessageInterface;
use Psr\Http\Message\ResponseInterface;

/**
 * Convert content to utf-8
 */
class Converter
{
    public function execute(string $content, $charset): string
    {
        if (strtolower($charset) !== 'utf-8') {
            try {
                if (!$charset) {
                    $charset = mb_detect_encoding(
                        $content,
                        ['UTF-8', 'ISO-8859-1', 'Windows-1252', 'ASCII'],
                        true
                    );
                }

                if ($charset !== false && $charset !== 'utf-8') {
                    return mb_convert_encoding($content, 'utf-8' , $charset);
                }
            } catch (\Exception $e) {
                return $this->_removeInvalidChar($content);
            }
        }

        return $content;
    }

    private function _removeInvalidChar(string $data): string
    {
        if (mb_detect_encoding($data, 'UTF-8', true) === false) {
            return preg_replace('/[\x00-\x1F\x80-\xFF]/', '', $data);
        }

        return $data;
    }

    public function getCharset(MessageInterface|ResponseInterface $response): ?string
    {
        $contentType = $response->getHeaderLine('Content-Type');
        if (stripos($contentType, 'charset=') !== false) {
            $charset = substr($contentType, strpos($contentType, 'charset=') + 8);
            return strtolower($charset);
        }

        return null;
    }
}
