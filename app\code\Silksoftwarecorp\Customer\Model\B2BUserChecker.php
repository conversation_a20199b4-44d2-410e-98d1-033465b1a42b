<?php

namespace Silksoftwarecorp\Customer\Model;

use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Psr\Log\LoggerInterface;

class B2BUserChecker
{
    /**
     * @var CustomerRepositoryInterface
     */
    protected $customerRepository;

    /**
     * @var LoggerInterface
     */
    protected $logger;

    protected $_b2bUsers = [];

    /**
     * @param CustomerRepositoryInterface $customerRepository
     * @param LoggerInterface $logger
     */
    public function __construct(
        CustomerRepositoryInterface $customerRepository,
        LoggerInterface $logger
    ) {
        $this->customerRepository = $customerRepository;
        $this->logger = $logger;
    }

    /**
     * @param $customerId
     *
     * @return bool
     */
    public function isB2BUser($customerId): bool
    {
        if (!isset($this->_b2bUsers[$customerId])) {
            try {
                $customer = $this->customerRepository->getById($customerId);
                /**@var $companyAttributes \Magento\Company\Api\Data\CompanyCustomerInterface*/
                $companyAttributes = $customer->getExtensionAttributes()?->getCompanyAttributes();

                $isB2BUser = $companyAttributes->getCustomerId() == $customerId &&
                    $companyAttributes->getCompanyId() > 0;
                $this->_b2bUsers[$customerId] = $isB2BUser;
            } catch (LocalizedException $e) {
                $this->logger->critical(__($e->getMessage()));
            }
        }

        return $this->_b2bUsers[$customerId] ?? false;
    }
}
