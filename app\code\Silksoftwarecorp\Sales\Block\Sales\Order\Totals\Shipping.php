<?php

namespace Silksoftwarecorp\Sales\Block\Sales\Order\Totals;

use Magento\Framework\DataObject;
use Magento\Framework\View\Element\Template;
use Magento\Sales\Model\Order;

class Shipping extends Template
{
    /**
     * @var Order
     */
    protected $_order;

    /**
     * @var \Magento\Framework\DataObject
     */
    protected $_source;

    /**
     * Get data (totals) source model
     *
     * @return \Magento\Framework\DataObject
     */
    public function getSource()
    {
        return $this->_source;
    }

    public function initTotals()
    {
        /** @var $parent \Magento\Sales\Block\Order\Totals */
        $parent = $this->getParentBlock();
        $this->_order = $parent->getOrder();
        $this->_source = $parent->getSource();

        $this->addShippingTotal();

        return $this;
    }

    /**
     * Add shipping total
     *
     * @retrurn void
     */
    private function addShippingTotal(): void
    {
        /**@var Order|Order\Invoice $source*/
        $source = $this->getSource();
        if (!$source->getIsVirtual()
            && ($source->getShippingAmount() !== null
                || $source->getShippingDescription())
        ) {
            $shippingLabel = __('Shipping');

            $shippingLabelSuffix = [];
            if ($source->getShippingDescription()) {
                $shippingLabelSuffix[] = $source->getShippingDescription();
            }

            if ($source->getCouponCode()) {
                $shippingLabelSuffix[] = $source->getCouponCode();
            }

            if ($source->getCouponCode()) {
                $shippingLabelSuffix[] = $source->getCouponCode();
            } elseif ($source->getDiscountDescription()) {
                $shippingLabelSuffix[] = $source->getDiscountDescription();
            }

            if ($shippingLabelSuffix) {
               $shippingLabel .= '(' . implode(', ', $shippingLabelSuffix) . ')';
            }

            $shippingTotal = new DataObject(
                [
                    'code' => '_shipping',
                    'field' => 'shipping_amount',
                    'value' => $source->getShippingAmount(),
                    'label' => $shippingLabel,
                ]
            );

            $this->getParentBlock()->addTotal($shippingTotal, 'shipping');
            $this->getParentBlock()->removeTotal('shipping');
        }
    }
}
