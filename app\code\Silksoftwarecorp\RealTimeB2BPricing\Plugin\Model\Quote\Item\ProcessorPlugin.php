<?php
namespace Silksoftwarecorp\RealTimeB2BPricing\Plugin\Model\Quote\Item;

use Magento\Catalog\Model\Product;
use Magento\Framework\DataObject;
use Magento\Quote\Model\Quote\Item;
use Magento\Quote\Model\Quote\Item\Processor as Subject;
use Silksoftwarecorp\RealTimeB2BPricing\Model\Quote\AddProductsToCartOperation;

/**
 * Class ProcessorPlugin
 */
class ProcessorPlugin
{
    /**
     * @var AddProductsToCartOperation
     */
    protected $operation;

    /**
     * @param AddProductsToCartOperation $operation
     */
    public function __construct(AddProductsToCartOperation $operation)
    {
        $this->operation = $operation;
    }

    public function aroundPrepare(
        Subject $subject,
        \Closure $proceed,
        Item $item,
        DataObject $request,
        Product $candidate
    ): void {
        $proceed($item, $request, $candidate);

        $this->operation->setB2BPriceToQuoteItem($item, $candidate);
    }
}
