import dynamic from 'next/dynamic'
import { memo, useEffect, useState } from 'react'
import { FormattedMessage } from 'react-intl'
import { isArray } from 'lodash-es'

import { useAwaitQuery } from '@ranger-theme/apollo'
import { GET_CMS_BLOCK } from '@/apis/queries/getCmsBlock'

import { StyledCmsBlock } from './styled'

const PageBuilder = dynamic(() => import('@ranger-theme/pagebuilder'), {
  ssr: false
})

interface CmsBlockProps {
  identifiers: string[]
  handleLoaded?: () => void
}

const CmsBlock: React.FC<CmsBlockProps> = ({ identifiers, handleLoaded }) => {
  const [blocks, setBlocks] = useState(null)
  const cmsBlockQuery = useAwaitQuery(GET_CMS_BLOCK)

  useEffect(() => {
    const fetchCmsBlock = async () => {
      try {
        const { data } = await cmsBlockQuery({
          variables: { identifiers }
        })

        const { items } = data?.cmsBlocks ?? {}
        if (!isArray(items) || !items.length) {
          const empty = (
            <div>
              <FormattedMessage id="noBlocks" />
            </div>
          )
          setBlocks(empty)
        } else {
          const contents = items.map((item) => (
            <PageBuilder key={item.identifier} className={item.identifier} html={item.content} />
          ))
          setBlocks(contents)
        }
        if (handleLoaded) {
          handleLoaded?.()
        }
      } catch (error) {
        setBlocks(null)
      }
    }

    fetchCmsBlock()
  }, [cmsBlockQuery, identifiers, handleLoaded])

  return (
    <StyledCmsBlock>
      <div className="cms__block">{blocks}</div>
    </StyledCmsBlock>
  )
}

export default memo(CmsBlock)
