import { memo, useState, useCallback } from 'react'
import { Checkbox } from 'antd'

import CommonInput from '@/components/Common/CommonInput'

import { StyledMixedItemLinesetLength } from './styled'

const MixedItemLinesetLength = ({ handleSaveVal }: any) => {
  const [checkedVal, setCheckedVal] = useState<any>('')
  const [value, setValue] = useState<any>(null)

  const onCheckChange = useCallback(
    (val) => {
      if (checkedVal !== val) {
        setCheckedVal(val)
        handleSaveVal(val === 'other' ? '' : val)
        setValue('')
      }
    },
    [checkedVal, handleSaveVal]
  )

  return (
    <StyledMixedItemLinesetLength>
      <div className="mixed-item-box">
        <div>
          <Checkbox
            checked={checkedVal === '20'}
            onChange={() => {
              onCheckChange('20')
            }}>
            20'
          </Checkbox>
        </div>
        <div>
          <Checkbox
            checked={checkedVal === '30'}
            onChange={() => {
              onCheckChange('30')
            }}>
            30'
          </Checkbox>
        </div>
        <div className="mixed-item-flex">
          <Checkbox
            checked={checkedVal === 'other'}
            onChange={() => {
              onCheckChange('other')
            }}>
            Other:
          </Checkbox>
          <CommonInput
            value={value}
            placeholder=""
            onChange={(v) => {
              const val = v?.target?.value || ''
              setValue(val)
              handleSaveVal(val)
              if (checkedVal !== 'other') {
                setCheckedVal('other')
              }
            }}
          />
        </div>
      </div>
    </StyledMixedItemLinesetLength>
  )
}

export default memo(MixedItemLinesetLength)
