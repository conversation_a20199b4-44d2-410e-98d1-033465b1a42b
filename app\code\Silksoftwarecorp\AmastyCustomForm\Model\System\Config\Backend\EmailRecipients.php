<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */

namespace Silksoftwarecorp\AmastyCustomForm\Model\System\Config\Backend;

use Magento\Framework\App\Config\Value;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Serialize\Serializer\Json as SerializeJson;
use Magento\Framework\Math\Random;

class EmailRecipients extends Value
{
    /**
     * @var array
     */
    protected $mandatoryFields = [
        'branch', 'form', 'email_recipients'
    ];

    /**
     * @var SerializeJson
     */
    protected $serializer;

    /**
     * @var Random
     */
    protected $mathRandom;

    /**
     * @param \Magento\Framework\Model\Context $context
     * @param \Magento\Framework\Registry $registry
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $config
     * @param \Magento\Framework\App\Cache\TypeListInterface $cacheTypeList
     * @param SerializeJson $serializer
     * @param Random $mathRandom
     * @param \Magento\Framework\Model\ResourceModel\AbstractResource $resource
     * @param \Magento\Framework\Data\Collection\AbstractDb $resourceCollection
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\Model\Context $context,
        \Magento\Framework\Registry $registry,
        \Magento\Framework\App\Config\ScopeConfigInterface $config,
        \Magento\Framework\App\Cache\TypeListInterface $cacheTypeList,
        SerializeJson $serializer,
        Random $mathRandom,
        \Magento\Framework\Model\ResourceModel\AbstractResource $resource = null,
        \Magento\Framework\Data\Collection\AbstractDb $resourceCollection = null,
        array $data = []
    ) {
        parent::__construct(
            $context,
            $registry,
            $config,
            $cacheTypeList,
            $resource,
            $resourceCollection,
            $data
        );

        $this->serializer = $serializer;
        $this->mathRandom = $mathRandom;
    }

    /**
     * Process data before save
     *
     * @return $this
     * @throws \Exception
     */
    public function beforeSave()
    {
        $value = $this->getValue();
        $value = $this->makeStorableArrayFieldValue($value);
        $this->setValue($value);

        return parent::beforeSave();
    }

    /**
     * Process data after load
     *
     * @return $this
     * @throws LocalizedException
     */
    public function afterLoad()
    {
        $value = $this->getValue();
        $value = $this->makeArrayFieldValue($value);
        $this->setValue($value);

        return parent::afterLoad();
    }

    /**
     * Make storable array field value
     *
     * @param mixed $value
     * @return string
     * @throws \Exception
     */
    protected function makeStorableArrayFieldValue($value): string
    {
        if ($this->isEncodedArrayFieldValue($value)) {
            $value = $this->decodeArrayFieldValue($value);
        }

        return $this->serializeValue($value);
    }

    /**
     * Make array field value
     *
     * @param mixed $value
     * @return array
     * @throws LocalizedException
     */
    protected function makeArrayFieldValue($value)
    {
        $value = $this->unserializeValue($value);
        if (!$this->isEncodedArrayFieldValue($value)) {
            $value = $this->encodeArrayFieldValue($value);
        }

        return $value;
    }

    /**
     * Serialize value
     *
     * @param mixed $value
     * @return string
     * @throws \Exception
     */
    protected function serializeValue($value): string
    {
        if (is_array($value)) {
            $data = [];
            foreach ($value as $code => $row) {
                if (!array_key_exists($code, $data)) {
                    $data[$code] = $row;
                }
            }

            return (string)$this->serializer->serialize($data);
        }

        return '';
    }

    /**
     * Unserialize value
     *
     * @param mixed $value
     * @return array
     */
    protected function unserializeValue($value)
    {
        if (is_string($value) && !empty($value)) {
            try {
                return $this->serializer->unserialize($value);
            } catch (\Exception $e) {
                return [];
            }
        } else {
            return [];
        }
    }

    /**
     * Check if value is encoded array field value
     *
     * @param mixed $value
     * @return bool
     */
    protected function isEncodedArrayFieldValue($value): bool
    {
        if (!is_array($value)) {
            return false;
        }

        unset($value['__empty']);
        foreach ($value as $row) {
            if (!$this->_isAvailableArrayFieldValue($row)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check if array field value is available
     *
     * @param mixed $data
     * @return bool
     */
    protected function _isAvailableArrayFieldValue($data): bool
    {
        if (!is_array($data)) {
            return false;
        }

        foreach ($this->mandatoryFields as $field) {
            if (!array_key_exists($field, $data)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Encode array field value
     *
     * @param array $value
     * @return array
     * @throws \Exception
     */
    protected function encodeArrayFieldValue(array $value): array
    {
        unset($value['__empty']);

        $result = [];
        foreach ($value as $key => $data) {
            $branch = $data['branch_code'] ?? '';
            $form = $data['form_id'] ?? '';
            $emailRecipients = $data['emails'] ?? '';

            if (empty($branch) || empty($form) || empty($emailRecipients)) {
                continue;
            }

            $resultId = $this->mathRandom->getUniqueHash('_');
            $result[$resultId] = [
                'branch' => trim($branch),
                'form' => trim($form),
                'email_recipients' => trim($emailRecipients),
            ];
        }

        return $result;
    }

    /**
     * Decode array field value
     *
     * @param array $value
     * @return array
     */
    protected function decodeArrayFieldValue(array $value): array
    {
        $result = [];
        unset($value['__empty']);
        foreach ($value as $row) {
            if (!$this->_isAvailableArrayFieldValue($row)) {
                continue;
            }

            if (empty($row['branch']) || empty($row['form']) || empty($row['email_recipients'])) {
                continue;
            }

            // Validate email
            $emailRecipients = (string)$row['email_recipients'];
            $emails = array_map('trim', explode(',', $emailRecipients));
            foreach ($emails as $email) {
                if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    throw new LocalizedException(__('Invalid email address: %1', $email));
                }
            }

            // Normalize branch values to comma-separated string for storage
            $branchValue = $row['branch'];
            if (is_array($branchValue)) {
                $branchValue = implode(',', array_filter(array_map('trim', $branchValue)));
            } else {
                $branchValue = (string)$branchValue;
            }

            $result[] = [
                'branch_code' => trim($branchValue),
                'form_id' => trim((string)$row['form']),
                'emails' => trim($emailRecipients),
            ];
        }

        return $result;
    }
}
