import styled from '@emotion/styled'

export const StyledCategoryGrid = styled.div`
  display: grid;
  grid-template-columns: 277px 1fr;
  grid-column-gap: 22px;

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    display: block;
    padding: 0 16px;
  }
`

export const StyledProductGrid = styled.div`
  .product-grid-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100px;
    font-size: 32px;
    line-height: 38px;
    text-align: center;
  }
`
