<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */

namespace Silksoftwarecorp\Inventory\Ui\Component\Form;

/**
 * Class ScheduleMinutesTimeWithEmpty
 */
class ScheduleMinutesTimeWithEmpty implements \Magento\Framework\Option\ArrayInterface
{
    /**
     * Get minute options with empty option
     *
     * @return array
     */
    public function toOptionArray()
    {
        $minutesArray = [
            [
                'value' => '',
                'label' => ' '
            ]
        ];
        
        for ($i = 0; $i < 60; $i++) {
            $minutesArray[] = [
                'value' => str_pad($i, 2, '0', STR_PAD_LEFT),
                'label' => str_pad($i, 2, '0', STR_PAD_LEFT)
            ];
        }

        return $minutesArray;
    }
} 