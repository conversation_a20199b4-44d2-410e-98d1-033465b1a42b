<?php

namespace Silksoftwarecorp\RealTimeB2BPricing\Model;

use Silksoftwarecorp\RealTimeB2BPricing\Api\B2BPriceItemResultInterface;
use Silksoftwarecorp\RealTimeB2BPricing\Api\Data\B2BPriceItemInterface;

class B2BPriceItemResult implements B2BPriceItemResultInterface
{
    protected array $items = [];

    protected array $skus = [];

    /**
     * @param B2BPriceItemInterface $item
     * @return $this
     */
    public function addItem(B2BPriceItemInterface $item): static
    {
        $this->items[$item->getSku()] = $item;
        $this->skus[] = $item->getSku();

        return $this;
    }

    /**
     * @param B2BPriceItemInterface ...$items
     * @return $this
     */
    public function addItems(B2BPriceItemInterface ...$items): static
    {
        foreach ($items as $item) {
            $this->addItem($item);
        }

        return $this;
    }

    /**
     * @param $sku
     * @return B2BPriceItemInterface|null
     */
    public function getItem($sku): ?B2BPriceItemInterface
    {
        return $this->items[$sku] ?? null;
    }

    /**
     * @return B2BPriceItemInterface[]
     */
    public function getItems(): array
    {
        return array_values($this->items);
    }

    /**
     * @return array
     */
    public function getSkus(): array
    {
        return $this->skus;
    }

    /**
     * @param B2BPriceItemResultInterface $priceItemResult
     * @param bool $overwrite
     * @return $this
     */
    public function merge(B2BPriceItemResultInterface $priceItemResult, bool $overwrite = true): static
    {
        foreach ($priceItemResult->getItems() as $item) {
            $sku = $item->getSku();
            if (!$overwrite && isset($this->items[$sku])) {
                continue;
            }
            $this->addItem($item);
        }
        return $this;
    }

    /**
     * @return int
     */
    public function count(): int
    {
        return count($this->items);
    }
}
