import { memo, useMemo } from 'react'
import { clsx } from 'clsx'

import { StyledProductStock } from './styled'

const ProductStock = ({ product }) => {
  const productStock = useMemo(() => {
    return product?.only_x_left_in_stock || 0
  }, [product])

  const inStock = useMemo(() => {
    return product?.stock_status === 'IN_STOCK'
  }, [product])

  const stockText = useMemo(() => {
    return inStock ? `${productStock} in Stock` : `On Order, ${productStock} in Stock`
  }, [inStock, productStock])

  const isEmptyStockNumber = useMemo(() => {
    return inStock && productStock === 0
  }, [inStock, productStock])

  return (
    <StyledProductStock
      className={clsx({
        'in-stock': inStock,
        'is-empty-stock': isEmptyStockNumber
      })}>
      {stockText}
    </StyledProductStock>
  )
}

export default memo(ProductStock)
