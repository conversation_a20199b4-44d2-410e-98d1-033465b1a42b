<?php
namespace Silk\OnesourceTax\Model\Api;

use Silk\OnesourceTax\Model\Config;
use Silk\OnesourceTax\Model\Logger;

class Calculator
{
    protected $client;
    protected $auth;
    protected $config;
    protected $json;
    protected $logger;

    public function __construct(
        Client $client,
        Auth $auth,
        Config $config,
        \Magento\Framework\Serialize\Serializer\Json $json,
        Logger $logger
    ) {
        $this->client = $client;
        $this->auth = $auth;
        $this->config = $config;
        $this->json = $json;
        $this->logger = $logger;
    }

    public function calculateTax(array $requestData)
    {
        $url = $this->config->getCalculateUrl();
        $token = $this->auth->getAccessToken();
        $this->logger->info('Access Token: ' . $token);

        $headers = [
            'Authorization' => 'Bearer ' . $token,
            'Content-Type' => 'application/vnd.tri.onesource.idt+json',
            'Correlation-Id' => 'INV-12345',
            'Accept' => 'application/vnd.tri.onesource.idt+json'
        ];

        // Add default values if not provided
        if (!isset($requestData['companyRole'])) {
            $requestData['companyRole'] = $this->config->getCompanyRole();
        }

        if (!isset($requestData['externalCompanyId'])) {
            $requestData['externalCompanyId'] = $this->config->getExternalCompanyId();
        }

        if (!isset($requestData['processingOptions'])) {
            $requestData['processingOptions'] = $this->config->getProcessingOptions();
        }

        $this->logger->info('Request Data: ' . $this->json->serialize($requestData));

        return $this->client->post($url, json_encode($requestData), $headers);
    }
}