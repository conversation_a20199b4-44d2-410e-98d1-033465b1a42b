import { useIntl } from 'react-intl'
import { LineContainer } from '@ranger-theme/ui'
import { Button, Form, Checkbox, Spin } from 'antd'
import { nanoid } from 'nanoid'

import { useCompanyLayout } from '@/hooks/CompanyLayout'
import { TextField, TextSelect } from '@/ui'
import Breadcrumb from '@/components/Breadcrumb'
import { StyledCompany, StyledLayout, StyledFormLine } from './styled'

const CompanyLayout = () => {
  const { formatMessage } = useIntl()
  const [form] = Form.useForm()
  const { countries, loading, regions, validateEmail, handleCountryChange, handleSubmit } =
    useCompanyLayout(form)

  const crumbs: any[] = [
    { url: '/registration', name: formatMessage({ id: 'register.title' }) },
    { name: 'Company' }
  ]

  return (
    <>
      <Breadcrumb items={crumbs} />
      <LineContainer>
        <StyledCompany>
          <StyledLayout>
            <Spin spinning={loading}>
              <Form form={form} initialValues={{ sameas: true }} onFinish={handleSubmit}>
                <h1>Create a New Company Account</h1>
                <h4>Company Information</h4>
                <div className="personal">
                  <Form.Item name="company_name" rules={[{ required: true }]}>
                    <TextField label="Company Name" />
                  </Form.Item>
                  <Form.Item name="street" rules={[{ required: true }]}>
                    <TextField label="Street Address" />
                  </Form.Item>
                  <Form.Item name="building_apartment">
                    <TextField label="Building, Apartment, Suite etc. (Optional)" />
                  </Form.Item>
                  <StyledFormLine>
                    <Form.Item name="city" rules={[{ required: true }]}>
                      <TextField label="City" />
                    </Form.Item>
                    {regions.length > 0 ? (
                      <Form.Item name="region" rules={[{ required: true }]}>
                        <TextSelect label="State" elementId={1}>
                          {regions.map((region: any) => {
                            return (
                              <TextSelect.Option key={nanoid()} value={region.id} param={region.id}>
                                <span dangerouslySetInnerHTML={{ __html: region.name }} />
                              </TextSelect.Option>
                            )
                          })}
                        </TextSelect>
                      </Form.Item>
                    ) : (
                      <Form.Item name="region" rules={[{ required: true }]}>
                        <TextField label="State" />
                      </Form.Item>
                    )}
                    <Form.Item name="postcode" rules={[{ required: true }]}>
                      <TextField label="Zip Code" />
                    </Form.Item>
                  </StyledFormLine>
                  <Form.Item name="country" rules={[{ required: true }]}>
                    <TextSelect label="Country" onChange={handleCountryChange}>
                      {countries.map((country: any) => {
                        return (
                          <TextSelect.Option
                            key={country.id}
                            value={country.id}
                            param={country.name}>
                            <span dangerouslySetInnerHTML={{ __html: country.name }} />
                          </TextSelect.Option>
                        )
                      })}
                    </TextSelect>
                  </Form.Item>
                  <Form.Item name="telephone" rules={[{ required: true }]}>
                    <TextField label="Phone Number" />
                  </Form.Item>
                  <Form.Item name="company_email">
                    <TextField label="Billing Email Address (Optional)" />
                  </Form.Item>
                  <Form.Item className="checkbox" name="sameas" valuePropName="checked">
                    <Checkbox>My legal and billing addresses are the same</Checkbox>
                  </Form.Item>
                </div>
                <h4>Company Administration</h4>
                <div className="information">
                  <Form.Item name="firstname" rules={[{ required: true }]}>
                    <TextField label="First Name" />
                  </Form.Item>
                  <Form.Item name="lastname" rules={[{ required: true }]}>
                    <TextField label="Last Name" />
                  </Form.Item>
                  <Form.Item name="email" rules={[{ required: true }, validateEmail]}>
                    <TextField label="Email Address" />
                  </Form.Item>
                  <Form.Item name="job_title">
                    <TextField label="Job Title (Optional)" />
                  </Form.Item>
                </div>
                <div className="action">
                  <Button type="primary" htmlType="submit">
                    <span>Create Account</span>
                  </Button>
                </div>
              </Form>
            </Spin>
          </StyledLayout>
          <div className="placeholder" />
        </StyledCompany>
      </LineContainer>
    </>
  )
}

export default CompanyLayout
