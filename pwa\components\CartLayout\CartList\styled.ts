import styled from '@emotion/styled'

export const StyledCartList = styled.div`
  display: grid;
  padding-bottom: 16px;
  grid-template-columns: 1fr 82px 124px 82px;
  grid-column-gap: 24px;
  justify-content: space-between;
  align-items: flex-start;
  border-bottom: 1px solid #d9d9d9;

  div {
    font-weight: 700;
    font-size: 16px;
    line-height: 22px;
    letter-spacing: 0.02em;

    &.cart-head__qty {
      text-align: center;
    }

    &.cart-head__price {
      text-align: right;
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    display: block;
    margin-top: 40px;
    padding-bottom: 7px;
  }
`
