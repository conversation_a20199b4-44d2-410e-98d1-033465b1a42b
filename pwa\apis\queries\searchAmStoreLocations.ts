import { gql } from '@apollo/client'

export const SEARCH_AM_STORE_LOCATIONS = gql`
  query searchAmStoreLocations(
    $filter: AmStoreLocationsFilterInput
    $pageSize: Int
    $currentPage: Int
  ) {
    searchAmStoreLocations(filter: $filter, pageSize: $pageSize, currentPage: $currentPage) {
      items {
        id
        name
        address
        city
        state
        zip
        country
        phone
        email
        url_key
        website
        lat
        lng
        distance
        attributes {
          attribute_code
          attribute_id
          entity_id
          frontend_input
          frontend_label
          option_title
          value
        }
      }
      page_info {
        current_page
        page_size
        total_pages
      }
      total_count
    }
  }
`
