<?php

namespace Silksoftwarecorp\RealTimeB2BPricing\Model\Config\Source;

use Magento\Framework\Data\OptionSourceInterface;

class Environment implements OptionSourceInterface
{
    public const ENVIRONMENT_PRODUCTION = 'production';
    public const ENVIRONMENT_SANDBOX = 'sandbox';

    public function toOptionArray(): array
    {
        return [
            ['value' => self::ENVIRONMENT_SANDBOX, 'label' => __('Sandbox')],
            ['value' => self::ENVIRONMENT_PRODUCTION, 'label' => __('Production')]
        ];
    }
}
