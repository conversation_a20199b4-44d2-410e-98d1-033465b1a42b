import { Form } from 'antd'
import { FormattedMessage } from 'react-intl'
import { nanoid } from 'nanoid'
import { useState, useEffect } from 'react'

import StreetFields from '@/components/StreetFields'
import CommonButton from '@/components/Common/CommonButton'
import CommonLoading from '@/components/Common/CommonLoading'
import { TextField, TextSelect } from '@/ui'
import { useCountries } from '@/hooks/Countries'

import { StyledAddressModal, StyledFormItem } from './styled'

const ShipToForm = () => {
  const [form] = Form.useForm()
  const { countries, defaultCountry, regions, handleCountryChange } = useCountries(form)
  const [loading, setLoading] = useState(false)

  const handleFormSubmit = (values) => {
    // TODO
    console.log(values)
  }

  useEffect(() => {
    if (countries.length > 0) {
      form.setFieldsValue({
        country: defaultCountry
      })
      handleCountryChange(defaultCountry)
    }
  }, [countries, defaultCountry, form, handleCountryChange])

  return (
    <StyledAddressModal>
      <CommonLoading spinning={loading}>
        <Form
          form={form}
          initialValues={{
            street: ['', ''],
            default_billing: false,
            default_shipping: false
          }}
          onFinish={handleFormSubmit}>
          <StyledFormItem>
            <Form.Item name="firstname" rules={[{ required: true }]}>
              <TextField label="First Name" />
            </Form.Item>
            <Form.Item name="lastname" rules={[{ required: true }]}>
              <TextField label="Last Name" />
            </Form.Item>
          </StyledFormItem>
          <Form.Item name="company">
            <TextField label="Company (Optional)" />
          </Form.Item>
          <StreetFields />
          <StyledFormItem>
            <Form.Item name="city" rules={[{ required: true }]}>
              <TextField label="City" />
            </Form.Item>
            {regions.length > 0 ? (
              <Form.Item name="region" rules={[{ required: true }]}>
                <TextSelect label="State" elementId={1}>
                  {regions.map((region: any) => {
                    return (
                      <TextSelect.Option key={nanoid()} value={region.id} param={region.id}>
                        <span dangerouslySetInnerHTML={{ __html: region.name }} />
                      </TextSelect.Option>
                    )
                  })}
                </TextSelect>
              </Form.Item>
            ) : (
              <Form.Item name="region" rules={[{ required: true }]}>
                <TextField label="State" />
              </Form.Item>
            )}
          </StyledFormItem>
          <StyledFormItem>
            <Form.Item name="country" rules={[{ required: true }]}>
              <TextSelect label="Country" onChange={handleCountryChange}>
                {countries.map((country: any) => {
                  return (
                    <TextSelect.Option key={country.id} value={country.id} param={country.name}>
                      <span dangerouslySetInnerHTML={{ __html: country.name }} />
                    </TextSelect.Option>
                  )
                })}
              </TextSelect>
            </Form.Item>
            <Form.Item name="postcode" rules={[{ required: true }]}>
              <TextField label="Zip Code" />
            </Form.Item>
          </StyledFormItem>
          <Form.Item name="telephone" rules={[{ required: true }]}>
            <TextField label="Phone Number" />
          </Form.Item>
          <CommonButton htmlType="submit" className="addressModal__checkButton" height={44}>
            <FormattedMessage id="global.save" />
          </CommonButton>
        </Form>
      </CommonLoading>
    </StyledAddressModal>
  )
}

export default ShipToForm
