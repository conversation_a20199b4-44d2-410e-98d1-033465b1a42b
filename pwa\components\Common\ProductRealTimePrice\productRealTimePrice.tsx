import { memo } from 'react'
import type { FC } from 'react'

import BasePrice from '@/components/BasePrice'
import ProductPrice from '@/components/ProductPrice/productPrice'

interface ProductRealTimePriceProps {
  priceRange: any
  realTimePrice: number
}

const ProductRealTimePrice: FC<ProductRealTimePriceProps> = ({ priceRange, realTimePrice }) => {
  return realTimePrice === null ? (
    <ProductPrice {...priceRange} />
  ) : (
    <BasePrice unit value={realTimePrice} />
  )
}

export default memo(ProductRealTimePrice)
