import styled from '@emotion/styled'

export const StyledCommonTableFooter = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;

  .table-footer-total {
    display: flex;
    align-items: center;
    height: 41px;
    font-weight: 400;
    font-size: 15px;
    line-height: 21px;
    letter-spacing: 0;
    color: var(--color-black);
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    display: grid;
    grid-template-areas:
      'item1 item3'
      'item2 item2';
    margin-top: 24px;

    .table-footer-total {
      grid-area: item1;
    }

    .common-pagination {
      grid-area: item2;
      margin-top: 30px;
    }

    .common-page-size {
      grid-area: item3;
    }
  }
`
