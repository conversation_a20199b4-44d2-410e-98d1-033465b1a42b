<?php

namespace Silksoftwarecorp\RealTimeB2BPricing\Logger\Handler;

use Monolog\Logger;

class Debug extends \Magento\Framework\Logger\Handler\Base
{
    /**
     * Logging level
     * @var int
     */
    protected $loggerType = Logger::DEBUG;

    /**
     * File name
     * @var string
     */
    protected $fileName = '/var/log/realtime_b2b_pricing/debug.log';

    public function isHandling(array $record): bool
    {
        return $record['level'] == $this->level;
    }
}
