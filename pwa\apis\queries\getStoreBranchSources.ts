import { gql } from '@apollo/client'
import type { DocumentNode } from '@apollo/client'

export const GET_STORE_BRANCH_SOURCES: DocumentNode = gql`
  query storeBranchSources {
    storeBranchSources {
      address {
        city
        country_code
        postcode
        region {
          region_code
        }
        street
      }
      general_manager {
        name
      }
      territory_managers {
        name
        phone
        region
      }
      code
      lat
      lng
      name
      phone
    }
  }
`
