<?php

namespace Silksoftwarecorp\UnifiedAR\Service;

use Silksoftwarecorp\UnifiedAR\Helper\Data as Helper;
use Silksoftwarecorp\UnifiedAR\Http\ClientInterfaceFactory;
use Silksoftwarecorp\UnifiedAR\Http\ClientInterface;
use Silksoftwarecorp\UnifiedAR\Model\API\Config as APIConfig;
use Psr\Log\LoggerInterface;

/**
 * Pay Now Service
 */
class PayNowService
{
    /**
     * @var ClientInterfaceFactory
     */
    protected $httpClientFactory;

    /**
     * @var Helper
     */
    protected $helper;

    /**
     * @var APIConfig
     */
    protected $apiConfig;

    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * Constructor
     * @param ClientInterfaceFactory $httpClientFactory
     * @param Helper $helper
     * @param APIConfig $apiConfig
     * @param LoggerInterface $logger
     */
    public function __construct(
        ClientInterfaceFactory $httpClientFactory,
        Helper $helper,
        APIConfig $apiConfig,
        LoggerInterface $logger
    ) {
        $this->httpClientFactory = $httpClientFactory;
        $this->helper = $helper;
        $this->apiConfig = $apiConfig;
        $this->logger = $logger;
    }

    /**
     * Generate pay now link for invoices
     * @param string $erpCustomerId
     * @param array $invoiceNumbers
     * @return string|null
     */
    public function generatePayNowLink(string $erpCustomerId, array $invoiceNumbers): ?string
    {
        if (empty($erpCustomerId) || empty($invoiceNumbers)) {
            return null;
        }

        try {
            // Get configuration values based on environment
            $environment = $this->helper->getApiEnvironment();
            $isProduction = ($environment === 'production');
            
            $this->logger->info('PayNow API Call Started', [
                'environment' => $environment,
                'is_production' => $isProduction,
                'erp_customer_id' => $erpCustomerId,
                'invoice_numbers' => $invoiceNumbers
            ]);
            
            // Get environment-specific configuration
            if ($isProduction) {
                $merchant = $this->helper->getMerchantKey();
                $apiKey = $this->helper->getApiKey();
                $apiUrl = $this->helper->getApiUrl();
            } else {
                $merchant = $this->helper->getSandboxMerchantKey();
                $apiKey = $this->helper->getSandboxApiKey();
                $apiUrl = $this->helper->getSandboxApiUrl();
            }

            $payNowKey = $this->helper->getPayNowKey();
            $completedRedirectUrl = $this->helper->getCompletedRedirectUrl();
            $payNowLink = $this->helper->getPayNowLink();

            $this->logger->info('PayNow Configuration Retrieved', [
                'merchant' => $merchant,
                'api_url' => $apiUrl,
                'api_key_length' => strlen($apiKey),
                'pay_now_key' => $payNowKey,
                'completed_redirect_url' => $completedRedirectUrl,
                'pay_now_link' => $payNowLink
            ]);

            // Validate required configuration values
            $missingConfig = [];
            if (empty($merchant)) $missingConfig[] = 'merchant';
            if (empty($apiKey)) $missingConfig[] = 'apiKey';
            if (empty($apiUrl)) $missingConfig[] = 'apiUrl';
            if (empty($payNowKey)) $missingConfig[] = 'payNowKey';
            if (empty($completedRedirectUrl)) $missingConfig[] = 'completedRedirectUrl';
            if (empty($payNowLink)) $missingConfig[] = 'payNowLink';

            if (!empty($missingConfig)) {
                $this->logger->error('PayNow missing required configuration', [
                    'missing_config' => $missingConfig,
                    'environment' => $environment
                ]);
                return null;
            }

            // Prepare request data
            $requestData = [
                'PayNowKey' => $payNowKey,
                'MerchantCustomerNumber' => $erpCustomerId,
                'completedRedirectUrl' => $completedRedirectUrl,
                'InvoiceNumbers' => $invoiceNumbers,
                'LimitAccessToSpecifiedInvoices' => true
            ];

            // Create HTTP client with proper headers
            $httpClient = $this->httpClientFactory->create();
            $httpClient = $httpClient->withBaseUri($apiUrl)
                ->withHeaders([
                    'ApiKey' => $apiKey,
                    'Content-Type' => 'application/json'
                ])
                ->timeout($this->apiConfig->getTimeout())
                ->withRetry()
                ->withLog();

            // Make API call to create pay now session
            $endpoint = sprintf('Merchant/%s/PayNowSession', $merchant);
            
            $this->logger->info('PayNow API Request', [
                'endpoint' => $endpoint,
                'request_data' => $requestData,
                'full_url' => rtrim($apiUrl, '/') . '/' . $endpoint
            ]);
            
            $response = $httpClient->post($endpoint, $requestData);

            $this->logger->info('PayNow API Response', [
                'status_code' => $response->status(),
                'response_body' => $response->body(),
                'is_success' => $response->isSuccess()
            ]);

            // Check if response is successful
            if ($response->isSuccess()) {
                $responseData = $response->json();
                
                $this->logger->info('PayNow API Response Parsed', [
                    'response_data' => $responseData
                ]);
                
                // Check if we got a valid response with payNowSessionId
                // The API returns payNowSessionId directly, not nested under 'result'
                if (isset($responseData['payNowSessionId']) && !empty($responseData['payNowSessionId'])) {
                    $payNowSessionId = $responseData['payNowSessionId'];
                    
                    // Generate the pay now URL
                    $payNowUrl = rtrim($payNowLink, '/') . $payNowSessionId;
                    
                    $this->logger->info('PayNow URL Generated', [
                        'pay_now_session_id' => $payNowSessionId,
                        'pay_now_url' => $payNowUrl
                    ]);
                    
                    return $payNowUrl;
                } else {
                    $this->logger->error('PayNow API response missing payNowSessionId', [
                        'response_data' => $responseData,
                        'expected_field' => 'payNowSessionId',
                        'available_fields' => array_keys($responseData)
                    ]);
                }
            } else {
                $this->logger->error('PayNow API call failed', [
                    'status_code' => $response->status(),
                    'response_body' => $response->body(),
                    'endpoint' => $endpoint,
                    'request_data' => $requestData,
                    'full_url' => rtrim($apiUrl, '/') . '/' . $endpoint
                ]);
            }

            return null;
        } catch (\Exception $e) {
            $this->logger->error('PayNow API exception', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return null;
        }
    }
}
