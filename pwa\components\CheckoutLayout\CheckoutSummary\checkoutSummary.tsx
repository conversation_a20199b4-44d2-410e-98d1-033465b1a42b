import { But<PERSON> } from 'antd'
import { memo } from 'react'
import { FormattedMessage } from 'react-intl'

import { MediaLayout } from '@/ui'
import CartSummary from '@/components/CartSummary'
import BasePrice from '@/components/BasePrice/basePrice'
import { useCheckoutSummary } from '@/hooks/CheckoutLayout'
import ChooseSummary from '@/components/CheckoutLayout/ChooseSummary'
import OrderProductList from '@/components/CheckoutLayout/OrderProductList'

import { StyledCheckoutSummary, StyledMobileOrderSummary, StyledOrderSummary } from './styled'

const CheckoutSummary = () => {
  const { stepIndex, totalPrice, SummaryComponent, summaryComponentProps, toggleSummaryDrawer } =
    useCheckoutSummary()

  return (
    <div>
      <MediaLayout type="mobile">
        <StyledMobileOrderSummary>
          <div className="order-summary-title">
            <div>Order Summary</div>
            <Button type="text" onClick={toggleSummaryDrawer}>
              View Details
            </Button>
          </div>
          <div className="order-summary-total">
            <span>Order Total: </span>
            <BasePrice value={totalPrice} />
          </div>
        </StyledMobileOrderSummary>
      </MediaLayout>
      <SummaryComponent {...summaryComponentProps}>
        <StyledCheckoutSummary>
          <StyledOrderSummary>
            <h3 className="title">
              <FormattedMessage id="checkout.summary" />
              <MediaLayout type="mobile">
                <Button type="text" onClick={toggleSummaryDrawer}>
                  Hide Details
                </Button>
              </MediaLayout>
            </h3>
            <CartSummary />
            <OrderProductList />
          </StyledOrderSummary>
          {stepIndex > 0 && <ChooseSummary />}
        </StyledCheckoutSummary>
      </SummaryComponent>
    </div>
  )
}

export default memo(CheckoutSummary)
