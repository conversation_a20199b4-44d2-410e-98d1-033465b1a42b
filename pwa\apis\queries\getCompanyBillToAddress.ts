import { gql } from '@apollo/client'
import type { DocumentNode } from '@apollo/client'

export const GET_COMPANY_BILL_TO_ADDRESS: DocumentNode = gql`
  query getCompanyBillToAddress {
    company {
      legal_address {
        city
        country_code
        postcode
        region {
          region
          region_id
          region_code
        }
        street
        telephone
      }
      legal_name
      name
    }
  }
`
