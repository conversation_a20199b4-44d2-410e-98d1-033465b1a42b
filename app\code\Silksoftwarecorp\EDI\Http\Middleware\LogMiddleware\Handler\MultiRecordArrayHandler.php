<?php

namespace Silksoftwarecorp\EDI\Http\Middleware\LogMiddleware\Handler;

use GuzzleHttp\Psr7\Query;
use GuzzleHttp\TransferStats;
use Psr\Http\Message\MessageInterface;
use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\ResponseInterface;
use Psr\Log\LoggerInterface;
use Silksoftwarecorp\EDI\Http\Middleware\Encoding\Converter;
use Throwable;
use Silksoftwarecorp\EDI\Http\Middleware\LogMiddleware\Handler\LogLevelStrategy\LogLevelStrategyInterface;

final class MultiRecordArrayHandler extends AbstractHandler
{
    /**
     * @var int
     */
    private $truncateSize;

    /**
     * @var int
     */
    private $summarySize;

    private string $requestId = '';

    /**
     * @param int $truncateSize If the body of the request/response is greater than the size of this integer the body will be truncated
     * @param int $summarySize The size to use for the summary of a truncated body
     */
    public function __construct(
        LogLevelStrategyInterface $logLevelStrategy = null,
        int $truncateSize = -1,
        int $summarySize = 200
    ) {
        $this->logLevelStrategy = $logLevelStrategy === null ? $this->getDefaultStrategy() : $logLevelStrategy;
        $this->truncateSize = $truncateSize;
        $this->summarySize = $summarySize;
    }

    public function log(
        LoggerInterface $logger,
        RequestInterface $request,
        ?ResponseInterface $response = null,
        ?Throwable $exception = null,
        ?TransferStats $stats = null,
        array $options = []
    ): void {
        $this->generateRequestId();

        $this->logRequest($logger, $request, $options);

        if ($stats !== null) {
            $this->logStats($logger, $stats, $options);
        }

        if ($response !== null) {
            $this->logResponse($logger, $response, $options);
        } else {
            $this->logReason($logger, $exception, $options);
        }
    }

    private function logRequest(
        LoggerInterface $logger,
        RequestInterface $request,
        array $options
    ): void {
        $headers = $request->getHeaders();
        if ($request->hasHeader('Password')) {
            $headers['Password'] = '***';
        }

        $context = [
            'requestId' => $this->getRequestId(),
            'method' => $request->getMethod(),
            'uri' => $request->getUri()->getPath(),
            'query' => $request->getUri()->getQuery(),
            'headers' => $headers,
            'version' => 'HTTP/' . $request->getProtocolVersion(),
        ];

        if ($request->getBody()->getSize() > 0) {
            $context['body'] = $this->formatBody($request, $options);
        }

        $level = $this->logLevelStrategy->getLevel($request, $options);
        $logger->debug('Request', $context);
    }

    private function logResponse(
        LoggerInterface $logger,
        ResponseInterface $response,
        array $options
    ): void {
        $headers = $response->getHeaders();
        $excludeHeaders = ['set-cookie', 'Set-Cookie'];
        foreach ($excludeHeaders as $key) {
            if (isset($headers[$key])) {
                unset($headers[$key]);
            }
        }

        $context = [
            'requestId' => $this->getRequestId(),
            'status_code' => $response->getStatusCode(),
            'version' => 'HTTP/' . $response->getProtocolVersion(),
            'headers' => $headers,
            'message' => $response->getReasonPhrase(),
        ];

        if ($response->getBody()->getSize() > 0) {
            $context['body'] = $this->formatBody($response, $options);
        }

        $level = $this->logLevelStrategy->getLevel($response, $options);
        $logger->log($level, 'Response', $context);
    }

    private function logReason(
        LoggerInterface $logger,
        ?Throwable $exception,
        array $options
    ): void {
        if ($exception === null) {
            return;
        }

        $context = [
            'requestId' => $this->getRequestId(),
            'code' => $exception->getCode(),
            'message' => $exception->getMessage(),
            'line' => $exception->getLine(),
            'file' => $exception->getFile(),
        ];

        $level = $this->logLevelStrategy->getLevel($exception, $options);
        $logger->log($level, 'Exception', $context);
    }

    private function logStats(
        LoggerInterface $logger,
        TransferStats $stats,
        array $options
    ): void {
        $level = $this->logLevelStrategy->getLevel($stats, $options);
        $logger->log($level, 'Statistics', [
            'requestId' => $this->getRequestId(),
            'time' => $stats->getTransferTime(),
            'uri' => $stats->getEffectiveUri(),
        ]);
    }

    /**
     * @return string|array
     */
    private function formatBody(MessageInterface $message, array $options)
    {
        $stream = $message->getBody();
        if ($stream->isSeekable() === false || $stream->isReadable() === false) {
            return 'Body stream is not seekable/readable.';
        }

        if (isset($options['log']['sensitive']) && $options['log']['sensitive'] === true) {
            return 'Body contains sensitive information therefore it is not included.';
        }

        if ($this->truncateSize !== -1 && $stream->getSize() >= $this->truncateSize) {
            $summary = $stream->read($this->summarySize) . ' (truncated...)';
            $stream->rewind();
            return $summary;
        }

        $body = $stream->__toString();

        $encodeConverter = new Converter();
        $body = $encodeConverter->execute(
            $body,
            $encodeConverter->getCharset($message)
        );

        $contentType = $message->getHeader('Content-Type');

        $isJson = preg_grep('/application\/[\w\.\+]*(json)/', $contentType);
        if (!empty($isJson)) {
            $result = json_decode($body, true);
            $stream->rewind();
            return $result;
        }

        $isForm = preg_grep('/application\/x-www-form-urlencoded/', $contentType);
        if (!empty($isForm)) {
            $result = Query::parse($body);
            $stream->rewind();
            return $result;
        }

        $stream->rewind();
        return $body;
    }

    private function generateRequestId(): void
    {
        $this->requestId = 'X-' . time();
    }

    private function getRequestId(): string
    {
        return $this->requestId;
    }
}
