<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */

declare(strict_types=1);

namespace Silksoftwarecorp\Inventory\Plugin\InventoryApi\SourceRepository;

use Magento\InventoryApi\Api\Data\SourceInterface;
use Magento\InventoryApi\Api\SourceRepositoryInterface;
use Silksoftwarecorp\Inventory\Model\Source\InitSourceExtensionAttributes;

/**
 * Populate source extension attributes when loading single source.
 */
class LoadCustomFieldsOnGetPlugin
{
    /**
     * @var InitSourceExtensionAttributes
     */
    private $initSourceExtensionAttributes;

    /**
     * @param InitSourceExtensionAttributes $initSourceExtensionAttributes
     */
    public function __construct(
        InitSourceExtensionAttributes $initSourceExtensionAttributes
    ) {
        $this->initSourceExtensionAttributes = $initSourceExtensionAttributes;
    }

    /**
     * Enrich the given Source Object with custom extension attributes
     *
     * @param SourceRepositoryInterface $subject
     * @param SourceInterface $source
     *
     * @return SourceInterface
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function afterGet(
        SourceRepositoryInterface $subject,
        SourceInterface $source
    ): SourceInterface {
        $this->initSourceExtensionAttributes->execute($source);

        return $source;
    }
}
