<?php

namespace Silksoftwarecorp\EDI\Helper;

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\Intl\DateTimeFactory;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Magento\Store\Model\ScopeInterface;

class FrequentlyOrderedProducts extends AbstractHelper
{
    const XML_PATH_ENABLED_FREQUENTLY_ORDERED_PRODUCTS = 'edi/frequently_ordered_products/enabled';

    const XML_PATH_REQUEST_TYPE = 'edi/frequently_ordered_products/request_type';

    const XML_PATH_FREQUENCY_DAYS = 'edi/frequently_ordered_products/frequency_days';

    /**
     * @var TimezoneInterface
     */
    protected $timezone;

    /**
     * @var DateTimeFactory
     */
    protected $dateTimeFactory;

    public function __construct(
        Context $context,
        TimezoneInterface $timezone,
        DateTimeFactory $dateTimeFactory,
    ) {
        parent::__construct($context);

        $this->timezone = $timezone;
        $this->dateTimeFactory = $dateTimeFactory;
    }

    public function isEnabled($storeId = null): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_ENABLED_FREQUENTLY_ORDERED_PRODUCTS,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getRequestType($storeId = null)
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_REQUEST_TYPE,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getFrequencyDays($storeId = null)
    {
        $frequencyDays = $this->scopeConfig->getValue(
            self::XML_PATH_FREQUENCY_DAYS,
            ScopeInterface::SCOPE_STORE,
            $storeId
        ) ?: 180;

        return (int)$frequencyDays;
    }

    public function getStartDate($storeId = null): string
    {
        $timezone = new \DateTimeZone($this->timezone->getConfigTimezone());
        $dateTime = $this->dateTimeFactory->create(timezone: $timezone);
        $dateTime->sub(new \DateInterval(sprintf('P%uD', $this->getFrequencyDays($storeId))));

        return $dateTime->format('Y-m-d');
    }
}
