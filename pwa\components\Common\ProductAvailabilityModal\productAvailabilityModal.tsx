import { Button } from 'antd'
import { useDispatch, useSelector } from 'react-redux'
import { memo, useCallback, useState } from 'react'

import { useMutation } from '@apollo/client'
import { actions } from '@/store/cart'
import { useFetchCart } from '@/hooks/FetchCart'
import CommonLoading from '@/components/Common/CommonLoading'
import { POST_REMOVE_CART } from '@/apis/mutations/removeItemFromCart'

import { StyledRemovalModal } from './styled'

const ProductAvailabilityModal = memo(({ items = [], onCancel, onProceed }: any) => {
  const dispatch = useDispatch()
  const { fetchCart } = useFetchCart()
  const [removeItemFromCartMutation] = useMutation(POST_REMOVE_CART)

  const cartId = useSelector((state: Store) => state.cart.cartId)

  const [loading, setLoading] = useState(false)

  const handleProceed = useCallback(async () => {
    try {
      setLoading(true)
      const handleRemove = async (id) => {
        const { data } = await removeItemFromCartMutation({
          variables: {
            cartId,
            cartItemId: id
          }
        })
        return data
      }
      const res = await Promise.all(
        items.map((item) => {
          return handleRemove(item.id)
        })
      )
      if (items.length > 1) {
        await fetchCart()
      } else {
        dispatch(actions.setCartDetail(res?.[0]?.removeCart?.cart ?? {}))
      }
      onProceed()
    } catch (e) {
      console.error(e)
    } finally {
      setLoading(false)
    }
  }, [items, onProceed, removeItemFromCartMutation, cartId, fetchCart, dispatch])

  return (
    <StyledRemovalModal>
      <CommonLoading spinning={loading}>
        <svg width="40px" height="40px" fill="currentColor" aria-hidden="true" focusable="false">
          <use xlinkHref="#icon-modal-warning-icon" />
        </svg>
        <div>
          Changing the address will result in the removal of the following item:
          {items.map((item) => {
            return (
              <b key={item?.id}>
                {item?.product?.name ?? ''}: {item?.product?.sku ?? ''}
              </b>
            )
          })}
        </div>
        <div className="removal-action">
          <Button type="primary" onClick={handleProceed}>
            PROCEED
          </Button>
          <Button className="cancel-btn" onClick={onCancel}>
            CANCEL
          </Button>
        </div>
      </CommonLoading>
    </StyledRemovalModal>
  )
})

export default ProductAvailabilityModal
