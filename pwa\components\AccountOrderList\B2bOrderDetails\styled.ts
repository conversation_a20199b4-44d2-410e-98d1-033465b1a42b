import styled from '@emotion/styled'

export const StyledOrderDate = styled.div`
  margin-bottom: 16px;
  font-weight: 400;
  font-size: 16px;
  line-height: 22px;
  letter-spacing: 0;
  color: var(--color-black);
`

export const StyledOrderDetaildTable = styled.div`
  margin-bottom: 15px;
  margin-right: 36px;
  font-weight: 700;
  font-size: 24px;
  line-height: 30px;
  letter-spacing: 0;
  color: #231f20;

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    margin-bottom: 10px;
  }
`

export const StyledTotalBox = styled.div`
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;

  & > ul {
    padding: 12px 0;
    background: #f5f5f5;
    border-radius: 4px;

    li {
      display: flex;
      justify-content: space-between;
      align-items: center;

      & > div,
      & > span {
        display: inline-grid;
        grid-template-columns: 2fr;
        padding: 5px 25px;
        font-weight: 400;
        font-size: 15px;
        line-height: 21px;
        letter-spacing: 0.01em;

        @media screen and (max-width: ${({ theme }) => theme.breakPoint.m}px) {
          padding: 0.5rem 0.2rem;
        }
      }

      .price {
        font-weight: 400;
        font-size: 15px;
        line-height: 21px;
        letter-spacing: 0.01em;
        color: var(--color-font);
      }

      &.order-total {
        font-weight: 700;
        font-size: 18px;
        line-height: 24px;

        .price {
          font-weight: 700;
          font-size: 18px;
          line-height: 24px;
        }
      }
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    margin-top: 40px;

    & > ul {
      width: 100%;
      padding: 12px 25px;

      li {
        display: flex;
        justify-content: space-between;
        padding: 0;
      }
    }
  }
`

export const StyledAddressPayment = styled.div`
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 24px;
  margin: 32px 0 64px;

  & > div {
    padding: 24px;
    height: 100%;
    border: 1px solid #d9d9d9;

    h3 {
      display: block;
      margin-bottom: 8px;
      font-weight: 700;
      font-size: 17px;
      line-height: 25px;
      letter-spacing: 0.01em;
      color: var(--color-black);
    }

    p {
      font-weight: 400;
      font-size: 15px;
      line-height: 23px;
      letter-spacing: 0.01em;
      color: #101010;
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.m}px) {
    grid-template-columns: 1fr;
    margin: 0 0 40px;
  }
`

export const StyledDetailsWrapper = styled.div`
  .order-collapse-icon.is-active {
    transform: rotateX(180deg);
  }

  .${({ theme }) => theme.namespace}-collapse {
    &-header {
      display: none !important;
    }
    &-content-box {
      padding: 0 !important;
    }
  }

  .common-table-component {
    thead tr th:first-of-type,
    tbody tr td:first-of-type {
      padding-left: 0;
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    margin-top: 40px;
    border-bottom: 1px solid #d9d9d9;

    .${({ theme }) => theme.namespace}-collapse {
      &-header {
        padding: 22px 0 12px !important;
        display: flex !important;
      }
      &-item {
        border-top: 1px solid #d9d9d9;
      }
    }
  }
`
export const StyledOrderDetailsPrint = styled.div`
  .print__order-title {
    display: none;
  }

  @media print {
    padding: 24px;
    .print__order-title {
      display: block;
      margin-bottom: 24px;
      font-weight: 700;
      font-size: 40px;
      line-height: 48px;
      letter-spacing: 0;
      color: var(--color-black);
    }

    .order-panel {
      display: none;
    }

    .print__reorder-btn {
      display: none;
    }

    .print__details-wrapper {
      margin-top: 0;
      border-bottom: none;
    }

    .print__address-payment {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      grid-gap: 24px;
    }

    .print__total {
      & > ul {
        width: auto;
      }
    }

    .${({ theme }) => theme.namespace}-collapse {
      &-item {
        border-top: none;
      }
    }
  }
`
export const StyledOrderPanel = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;

  .order-panel-action {
    display: grid;
    grid-template-columns: auto 1fr;
    grid-column-gap: 32px;
    align-items: center;
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    display: block;

    .print-box {
      margin-top: 24px;
    }
  }
`
