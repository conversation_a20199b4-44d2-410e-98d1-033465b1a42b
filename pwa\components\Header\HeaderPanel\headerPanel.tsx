import { clsx } from 'clsx'
import Link from 'next/link'
import { memo } from 'react'
import dynamic from 'next/dynamic'

import { LineContainer } from '@ranger-theme/ui'
import { MediaLayout } from '@/ui'
import CmsBlock from '@/components/CmsBlock'
import { useHeaderPanel } from '@/hooks/HeaderPanel'

import RestrictedModal from './RestrictedModal'
import { StyledHeaderPanel } from './styled'
import FinStorePencilBanner from '../FinStorePencilBanner'

const ShipTo = dynamic(() => import('./ShipTo'))

const HeaderPanel = () => {
  const { b2bPanelVisible, isRestrictedUser } = useHeaderPanel()

  return b2bPanelVisible ? (
    <StyledHeaderPanel className={clsx({ 'is-restricted': isRestrictedUser })}>
      {isRestrictedUser ? (
        <div className="restricted-content">
          <div className="icon">
            <svg width="3.5px" height="12.5px" fill="currentColor" focusable="false">
              <use xlinkHref="#icon-restricted-icon" />
            </svg>
          </div>
          <div>Lorem ipsum dolor sit amet consectetur adipiscing elit sed do eiusmod</div>
          <Link href="/">Make a Payment</Link>
        </div>
      ) : (
        <LineContainer>
          <ShipTo />
          <MediaLayout>
            <div className="panel__links">
              <CmsBlock identifiers={['pencil_banner_links']} />
            </div>
          </MediaLayout>
        </LineContainer>
      )}
      {isRestrictedUser && <RestrictedModal />}
    </StyledHeaderPanel>
  ) : (
    <FinStorePencilBanner />
  )
}

export default memo(HeaderPanel)
