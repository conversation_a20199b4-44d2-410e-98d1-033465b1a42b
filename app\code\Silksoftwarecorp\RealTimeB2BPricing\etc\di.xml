<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Silksoftwarecorp\RealTimeB2BPricing\Api\PriceStorageInterface" type="Silksoftwarecorp\RealTimeB2BPricing\Storage\Cache"/>
    <preference for="Silksoftwarecorp\RealTimeB2BPricing\Api\B2BPriceItemResultInterface" type="Silksoftwarecorp\RealTimeB2BPricing\Model\B2BPriceItemResult"/>
    <preference for="Silksoftwarecorp\RealTimeB2BPricing\Api\RealtimeB2BPriceServiceInterface" type="Silksoftwarecorp\RealTimeB2BPricing\Service\RealtimeB2BPriceService"/>
    <preference for="Silksoftwarecorp\RealTimeB2BPricing\Api\Data\B2BPriceItemInterface" type="Silksoftwarecorp\RealTimeB2BPricing\Model\B2BPriceItem"/>
    <virtualType name="Silksoftwarecorp\RealTimeB2BPricing\Logger\CustomLogger" type="Magento\Framework\Logger\Monolog">
        <arguments>
            <argument name="name" xsi:type="string">realtime_b2b_price</argument>
            <argument name="handlers" xsi:type="array">
                <item name="debug" xsi:type="object">Silksoftwarecorp\RealTimeB2BPricing\Logger\Handler\Debug</item>
                <item name="system" xsi:type="object">Silksoftwarecorp\RealTimeB2BPricing\Logger\Handler\Info</item>
            </argument>
        </arguments>
    </virtualType>
    <preference for="Silksoftwarecorp\RealTimeB2BPricing\Logger\LoggerInterface" type="Silksoftwarecorp\RealTimeB2BPricing\Logger\Logger"/>
    <type name="Silksoftwarecorp\RealTimeB2BPricing\Logger\Logger">
        <arguments>
            <argument name="logger" xsi:type="object">Silksoftwarecorp\RealTimeB2BPricing\Logger\CustomLogger</argument>
        </arguments>
    </type>
    <preference for="Silksoftwarecorp\RealTimeB2BPricing\Model\API\Http\ClientInterface" type="Silksoftwarecorp\RealTimeB2BPricing\Model\API\Http\Client"/>
    <type name="Silksoftwarecorp\RealTimeB2BPricing\Model\API\Http\Client">
        <arguments>
            <argument name="serializer" xsi:type="object">Magento\Framework\Serialize\Serializer\Json</argument>
        </arguments>
    </type>
    <type name="Magento\Quote\Model\Quote\Item\Processor">
        <plugin name="setRealtimeB2bPrice" type="Silksoftwarecorp\RealTimeB2BPricing\Plugin\Model\Quote\Item\ProcessorPlugin"/>
    </type>
    <type name="Magento\Quote\Model\Cart\AddProductsToCart">
        <plugin name="preloadRealtimeB2BPrices" type="Silksoftwarecorp\RealTimeB2BPricing\Plugin\Model\Cart\AddProductsToCartPlugin"/>
    </type>
    <type name="Magento\Quote\Model\Cart\CustomerCartResolver">
        <plugin name="updateExpiredPriceTrigger" type="Silksoftwarecorp\RealTimeB2BPricing\Plugin\Model\Cart\CustomerCartResolverPlugin"/>
    </type>
    <preference for="Silksoftwarecorp\RealTimeB2BPricing\Model\Quote\QuoteUpdateExpiredPriceInterface" type="Silksoftwarecorp\RealTimeB2BPricing\Model\Quote\QuoteUpdateExpiredPrice"/>
</config>
