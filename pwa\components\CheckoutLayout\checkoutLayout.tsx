import { LineContainer } from '@ranger-theme/ui'
import { clsx } from 'clsx'

import { useCheckoutLayout } from '@/hooks/CheckoutLayout'
import CommonLoading from '@/components/Common/CommonLoading'

import ShippingView from './ShippingView'
import PaymentView from './PaymentView'
import CheckoutSummary from './CheckoutSummary'
import { StyledSteps, StyledCheckout, StyledEmpty } from './styled'

const CheckoutLayout = () => {
  const {
    cartQty,
    pageLoading,
    stepIndex,
    isFulfillmentStep,
    isPaymentReviewStep,
    backShippingStep,
    pageVisible
  } = useCheckoutLayout()

  return (
    <LineContainer>
      <CommonLoading spinning={pageLoading}>
        {pageVisible ? (
          <>
            {cartQty > 0 ? (
              <>
                <StyledSteps>
                  <div
                    className={clsx(['item', isFulfillmentStep && 'active'])}
                    aria-hidden="true"
                    onClick={backShippingStep}>
                    <div className="line">
                      <span className="circle">
                        <svg
                          width="17px"
                          height="13px"
                          fill="currentColor"
                          aria-hidden="true"
                          focusable="false">
                          <use xlinkHref="#icon-check" />
                        </svg>
                      </span>
                    </div>
                    <span className="text">Fulfillment</span>
                  </div>
                  <div className={clsx(['item', isPaymentReviewStep && 'active'])}>
                    <div className="line">
                      <span className="circle">
                        {isPaymentReviewStep ? (
                          <svg
                            width="17px"
                            height="13px"
                            fill="currentColor"
                            aria-hidden="true"
                            focusable="false">
                            <use xlinkHref="#icon-check" />
                          </svg>
                        ) : (
                          <span>2</span>
                        )}
                      </span>
                    </div>
                    <span className="text">Payment & Review</span>
                  </div>
                </StyledSteps>

                <StyledCheckout id="checkout">
                  <div className="sidebar">
                    {stepIndex > 0 ? <PaymentView /> : <ShippingView />}
                  </div>
                  <CheckoutSummary />
                </StyledCheckout>
              </>
            ) : (
              <StyledEmpty>
                <p>The cart is empty.</p>
              </StyledEmpty>
            )}
          </>
        ) : (
          <CommonLoading spinning />
        )}
      </CommonLoading>
    </LineContainer>
  )
}

export default CheckoutLayout
