import Link from 'next/link'
import { Breadcrumb as AntdBreadcrumb } from 'antd'
import { LineContainer } from '@ranger-theme/ui'
import { MediaLayout } from '@/ui'

import { StyledBreadcrumb } from './styled'

interface BreadcrumbProps {
  rootName?: string
  rootUrl?: string
  items?: Array<{ name: string; url?: string }>
}

const Breadcrumb: React.FC<BreadcrumbProps> = ({
  rootName = 'Home',
  rootUrl = '/',
  items = []
}) => {
  const titleList: any[] = items.map((item) => {
    return {
      title: item?.url ? (
        <Link href={item.url}>
          <span dangerouslySetInnerHTML={{ __html: item.name }} />
        </Link>
      ) : (
        <span dangerouslySetInnerHTML={{ __html: item.name }} />
      )
    }
  })
  const itemList: any[] = [
    {
      title: (
        <Link href={rootUrl}>
          <MediaLayout type="mobile">
            <svg
              className="back-icon"
              width="10px"
              height="10px"
              fill="currentColor"
              aria-hidden="true"
              focusable="false">
              <use xlinkHref="#icon-back" />
            </svg>
          </MediaLayout>
          {rootName}
        </Link>
      )
    },
    ...titleList
  ]

  return (
    <StyledBreadcrumb>
      <LineContainer>
        <AntdBreadcrumb items={itemList} />
      </LineContainer>
    </StyledBreadcrumb>
  )
}

export default Breadcrumb
