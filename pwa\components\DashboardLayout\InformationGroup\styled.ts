import styled from '@emotion/styled'

export const StyledInformationGroup = styled.ul`
  display: flex;
  margin-top: 32px;
  margin-bottom: 96px;

  li {
    flex: 1;
    padding: 16px 16px 24px;
    min-height: 287px;
    background: #f5f5f5;

    &:not(:first-of-type) {
      margin-left: 24px;
    }

    h2 {
      padding-bottom: 8px;
      margin-bottom: 24px;
      font-weight: 700;
      font-size: 18px;
      line-height: 25px;
      letter-spacing: 0;
      color: var(--color-black);
      border-bottom: 1px solid #d9d9d9;
    }

    h3 {
      margin-bottom: 4px;
      font-weight: 700;
      font-size: 15px;
      line-height: 23px;
      letter-spacing: 0;
      color: var(--color-black);
    }

    p,
    a,
    .price {
      font-weight: 400;
      font-size: 15px;
      line-height: 23px;
      letter-spacing: 0;
    }

    a {
      font-weight: 700;
      color: var(--color-primary) !important;
    }

    .customer-email {
      text-decoration: underline;
    }

    .information-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-gap: 24px 6px;

      .full-item {
        grid-column: 1 / -1;
      }

      .reward-points {
        font-weight: 700;
        line-height: 21px;
        letter-spacing: 0.01em;
        color: var(--color-primary) !important;
        text-decoration: underline;
        svg {
          margin-left: 2px;
        }
      }
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    display: block;
    margin-bottom: 56px;

    li {
      min-height: initial;

      &:not(:first-of-type) {
        margin-left: 0;
        margin-top: 24px;
      }
    }
  }
`
