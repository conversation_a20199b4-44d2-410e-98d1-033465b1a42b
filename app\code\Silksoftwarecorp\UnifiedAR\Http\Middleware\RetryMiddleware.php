<?php

namespace Silksoftwarecorp\UnifiedAR\Http\Middleware;

use GuzzleHttp\Exception\ConnectException;
use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\ResponseInterface;
use Psr\Log\LoggerInterface;

class RetryMiddleware
{
    protected static array $errorCodes = [249, 429, 500, 502, 503, 504];

    protected static int $maxRetries = 3;

    public static function decider(LoggerInterface $logger = null): \Closure
    {
        return function (
            $retries,
            RequestInterface $request,
            ?ResponseInterface $response,
            ?\RuntimeException $e
        ) use ($logger) {
            if ($retries >= static::$maxRetries) {
                return false;
            }

            if ($e instanceof ConnectException) {
                $logger?->critical(__(
                    'Unable to connect to %1. Retrying (%2/%3)...',
                    $request->getUri(),
                    $retries + 1,
                    static::$maxRetries
                ));

                return true;
            }

            if ($response && in_array($response->getStatusCode(), static::$errorCodes, true)) {
                $logger?->critical(__(
                    'Something went wrong on the server. Uri: %1, Retrying (%2/%3)...',
                    $request->getUri(),
                    $retries + 1,
                    static::$maxRetries
                ));

                return true;
            }

            return false;
        };
    }

    public static function delay(): \Closure
    {
        return function ($retries) {
            /**
             * Exponential Backoff: Wait longer with each retry
             */
            return 1000 * $retries;
        };
    }
}
