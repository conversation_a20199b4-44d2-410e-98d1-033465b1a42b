<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\StoreGraphQl\Model\Resolver\Store\StoreConfigDataProvider">
        <arguments>
            <argument name="extendedConfigData" xsi:type="array">
                <item name="web_cookie_cookie_lifetime" xsi:type="string">web/cookie/cookie_lifetime</item>
                <item name="web_per_page_values" xsi:type="string">general_config/settings/list_per_page_values</item>
                <item name="web_per_page_default_value" xsi:type="string">general_config/settings/list_per_page</item>
            </argument>
        </arguments>
    </type>
</config>
