<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Cms\Model\Template\FilterProvider">
        <arguments>
            <argument name="pageFilter" xsi:type="string">Magento\Widget\Model\Template\FilterEmulate</argument>
            <argument name="blockFilter" xsi:type="string">Magento\Widget\Model\Template\FilterEmulate</argument>
        </arguments>
    </type>
</config>
