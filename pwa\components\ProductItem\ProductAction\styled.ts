import styled from '@emotion/styled'

export const StyledProductAction = styled.div`
  margin-top: 24px;

  .produc-action-simple {
    display: grid;
    grid-template-columns: auto 1fr;
    grid-column-gap: 8px;

    .product-cart-btn {
      height: 49px;
      border-radius: 3px;
      font-weight: 700;
      font-size: 16px;
      line-height: 21px;
      letter-spacing: 0.03em;

      &.${({ theme }) => theme.namespace}-btn-loading {
        background: var(--color-primary);
        color: var(--color-white);
      }
    }
  }
`
