import { gql } from '@apollo/client'

import { amBlogCategoriesItem } from '../fragment/amBlogCategoriesItem'

export const GET_AM_BLOG_POSTS_BY_CATEGORY_ID = gql`
  query getAmBlogPostsByCategoryId($categoryId: Int) {
    blogPosts: amBlogPostsByCategoryId(categoryId: $categoryId) {
      all_post_size
      items {
        ...amBlogCategoriesItem
        __typename
      }
    }
  }
  ${amBlogCategoriesItem}
`
