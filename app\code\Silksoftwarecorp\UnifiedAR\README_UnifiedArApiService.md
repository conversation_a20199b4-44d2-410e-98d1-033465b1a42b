# UnifiedAR ApiService Usage Guide

## Overview

`UnifiedArApiService` is a unified API service class for handling all UnifiedAR third-party API calls. It encapsulates HTTP client configuration, error handling, and generic request methods to avoid code duplication.

## Key Features

- **Unified Configuration**: Automatically configures API URL, authentication headers, timeouts, etc.
- **Error Handling**: Unified exception handling and logging
- **Retry Mechanism**: Automatic retry for failed requests
- **Logging**: Complete request/response logging with performance metrics
- **Type Safety**: Strong-typed parameters and return values
- **Enhanced Monitoring**: Request duration tracking and detailed error context

## Basic Usage

### 1. Generic HTTP Methods

```php
// Inject UnifiedArApiService
public function __construct(UnifiedArApiService $unifiedArApiService) {
    $this->unifiedArApiService = $unifiedArApiService;
}

// GET request
$data = $this->unifiedArApiService->get('endpoint', ['param1' => 'value1']);

// POST request
$data = $this->unifiedArApiService->post('endpoint', ['data' => 'value']);

// PUT request
$data = $this->unifiedArApiService->put('endpoint', ['data' => 'value']);

// DELETE request
$data = $this->unifiedArApiService->delete('endpoint');
```

### 2. Existing Specialized Methods

```php
// Fetch invoices list
$invoices = $this->unifiedArApiService->fetchInvoices('17640');

// Fetch financial information
$financialInfo = $this->unifiedArApiService->fetchFinancialInformation('17640');
```

## Adding New API Interfaces

### Method 1: Add specialized methods directly in UnifiedArApiService

```php
// Add new method in UnifiedArApiService class
public function fetchCustomerProfile(string $customerId): array
{
    $endpoint = sprintf('Customer/%s/profile', $customerId);
    return $this->get($endpoint);
}
```

### Method 2: Create dedicated API class

```php
// Create new API class
class CustomerApi
{
    protected $unifiedArApiService;
    
    public function __construct(UnifiedArApiService $unifiedArApiService) {
        $this->unifiedArApiService = $unifiedArApiService;
    }
    
    public function fetchProfile(string $customerId): array {
        return $this->unifiedArApiService->fetchCustomerProfile($customerId);
    }
}
```

### Method 3: Use generic methods directly

```php
// Use directly in Service class
public function getCustomerOrders(string $customerId): array
{
    return $this->unifiedArApiService->get('Orders', [
        'customerNumber' => $customerId,
        'status' => 'active'
    ]);
}
```

## Configuration

UnifiedArApiService uses `API\Config` class for configuration:

- API URL (production/sandbox)
- API Key
- Merchant Key
- Timeout settings
- Retry settings

## Error Handling

All API calls have unified error handling with enhanced logging:

- **Connection errors**: Automatic retry with detailed logging
- **HTTP errors**: Return empty array with status code logging
- **Parse errors**: Return empty array with error context
- **Performance monitoring**: Request duration tracking
- **Comprehensive logging**: All errors are logged with context

## Dependency Injection Configuration

Configure in `di.xml`:

```xml
<type name="YourService">
    <arguments>
        <argument name="unifiedArApiService" xsi:type="object">Silksoftwarecorp\UnifiedAR\Model\UnifiedArApiService</argument>
    </arguments>
</type>
```

## Best Practices

1. **Use specialized methods**: Create dedicated methods for common APIs to improve code readability
2. **Error handling**: Always check if returned array is empty
3. **Parameter validation**: Validate required parameters before API calls
4. **Caching**: Consider adding cache for data that doesn't change frequently
5. **Logging**: Utilize built-in logging for debugging and monitoring
6. **Performance monitoring**: Review logs for slow API calls and optimize accordingly

## Example: Complete Service Implementation

```php
class OrderService
{
    protected $unifiedArApiService;
    protected $orderMapper;
    
    public function __construct(
        UnifiedArApiService $unifiedArApiService,
        OrderMapper $orderMapper
    ) {
        $this->unifiedArApiService = $unifiedArApiService;
        $this->orderMapper = $orderMapper;
    }
    
    public function getCustomerOrders(string $customerId): array
    {
        if (empty($customerId)) {
            return [];
        }
        
        $orders = $this->unifiedArApiService->get('Orders', [
            'customerNumber' => $customerId
        ]);
        
        if (empty($orders)) {
            return [];
        }
        
        $result = [];
        foreach ($orders as $order) {
            $result[] = $this->orderMapper->execute($order);
        }
        
        return $result;
    }
}
```

## Logging Output Examples

The enhanced error handling provides detailed logging:

### Successful Request
```
[DEBUG] UnifiedAR API Request: {"method":"GET","endpoint":"Invoice","params":{"customerNumber":"17640"}}
[DEBUG] UnifiedAR API Response Success: {"method":"GET","endpoint":"Invoice","status_code":200,"duration_ms":245.67,"response_size":15}
```

### Failed Request
```
[WARNING] UnifiedAR API Response Failed: {"method":"GET","endpoint":"Invoice","status_code":404,"duration_ms":123.45,"response_body":"Customer not found"}
```

### Connection Error
```
[ERROR] UnifiedAR API Connection Error: {"method":"GET","endpoint":"Invoice","error":"Connection timeout","duration_ms":5000.00}
``` 