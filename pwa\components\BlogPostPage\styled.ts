import styled from '@emotion/styled'

export const StyledBlogPostPage = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  grid-gap: 24px;
  padding: 40px 0;
  min-height: 300px;

  div,
  p,
  ul,
  ol,
  li {
    font-weight: 400;
    font-size: 15px;
    line-height: 21px;
    letter-spacing: 0.01em;
    color: var(--color-text);
  }

  p {
    margin-top: 8px;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    margin-bottom: 8px;
    font-weight: 700;
    letter-spacing: 0;
    color: var(--color-font);
  }

  h1 {
    margin-top: 8px;
    font-size: 40px;
    line-height: 48px;
  }

  h2 {
    font-size: 32px;
    line-height: 38px;
  }

  h3 {
    font-size: 26px;
    line-height: 34px;
  }

  h4 {
    font-size: 24px;
    line-height: 30px;
  }

  h5 {
    font-size: 22px;
    line-height: 28px;
  }

  h6 {
    font-size: 18px;
    line-height: 24px;
    letter-spacing: 0.01em;
  }

  a {
    font-weight: 700;
    font-size: 15px;
    line-height: 21px;
    letter-spacing: 0.01em;
    text-decoration: underline !important;
    color: var(--color-primary) !important;
  }

  ul {
    padding-left: 20px;

    li {
      position: relative;
      line-height: 25px;
      //list-style: initial;

      &::marker {
        //font-size: 14px;
        //color: red;
      }

      &::before {
        content: '•';
        position: absolute;
        left: -15px;
        font-size: 14px;
        color: #333;
      }
    }
  }

  ol {
    padding-left: 20px;

    li {
      list-style: auto;
      line-height: 25px;
    }
  }

  button {
    height: 49px;
    padding: 0 32px;
    font-weight: 700;
    font-size: 16px;
    line-height: 21px;
    letter-spacing: 0.03em;
    color: var(--color-white);
    background: var(--color-primary);
    border: none;
    border-radius: 3px;
    cursor: pointer;
  }

  .btn-dark button {
    background: var(--color-bg-base);
  }

  .blog-post-page__tags-items {
    display: grid;
    grid-template-columns: auto 1fr;
    grid-gap: 10px;

    .tags-title {
      display: inline-flex;
      align-items: center;
      height: 33px;
      font-weight: 700;
      font-size: 15px;
      line-height: 21px;
      letter-spacing: 0.01em;
      color: var(--color-font);
    }

    .tags-list {
      span {
        display: inline-flex;
        justify-content: center;
        align-items: center;
        padding: 0 12px;
        height: 33px;
        background: #0038651f;
        border-radius: 5px;
        font-weight: 700;
        font-size: 13px;
        line-height: 21px;
        letter-spacing: 0.01em;
        color: var(--color-font);

        &:not(:first-of-type) {
          margin-left: 8px;
        }
      }
    }
  }

  .blog-post-page-columns__text-product {
    .pagebuilder-column:first-of-type {
      padding-right: 64px;
    }
  }

  .blog-post-page__desktop-visible {
    display: none;
  }

  .blog-post-page__text-columns-2 {
    .pagebuilder-column-line {
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-gap: 24px;

      & > div {
        width: 100% !important;

        .pagebuilder__text {
          p:not(:first-of-type) {
            margin-top: 20px;
          }
        }
      }
    }
  }

  .blog-post-page-columns__text-product {
    .pagebuilder-column {
      &:first-of-type {
        padding-right: 0;
      }

      &:not(:first-of-type) {
        margin-top: 24px;
      }
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    padding: 0;

    & > div {
      padding: 0 16px;
    }

    h1 {
      font-size: 30px;
      line-height: 38px;
    }

    h2,
    h3 {
      font-size: 26px;
      line-height: 34px;
    }

    h4 {
      font-size: 24px;
      line-height: 30px;
    }

    .blog-post-page__text-columns-2 {
      .pagebuilder-column-line {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-gap: 6px;

        & > div {
          width: 100% !important;
        }
      }
    }
  }
`
export const StyledPostTags = styled.div`
  display: grid;
  grid-template-columns: auto 1fr;
  grid-gap: 10px;
  padding-bottom: 36px;

  p {
    margin-top: 6px;
    font-weight: 700;
    font-size: 15px;
    line-height: 21px;
    letter-spacing: 0.01em;
    color: var(--color-font);
  }
  .tag-list div {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    padding: 0 12px;
    margin-right: 8px;
    height: 33px;
    background: #0038651f;
    border-radius: 5px;
    font-weight: 700;
    font-size: 13px;
    line-height: 21px;
    letter-spacing: 0.01em;
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    padding: 0 16px 40px;
  }
`
