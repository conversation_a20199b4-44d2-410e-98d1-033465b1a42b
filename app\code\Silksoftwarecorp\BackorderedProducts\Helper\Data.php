<?php
namespace Silksoftwarecorp\BackorderedProducts\Helper;

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Store\Model\ScopeInterface;

class Data extends AbstractHelper
{
    public function getEnvironment($storeId = null)
    {
        return $this->scopeConfig->getValue('edi/api/environment', ScopeInterface::SCOPE_STORE, $storeId);
    }

    public function getApiUrl($storeId = null)
    {
        $env = $this->getEnvironment($storeId);
        if ($env === 'sandbox') {
            return $this->scopeConfig->getValue('edi/api/sandbox_url', ScopeInterface::SCOPE_STORE, $storeId);
        }
        return $this->scopeConfig->getValue('edi/api/url', ScopeInterface::SCOPE_STORE, $storeId);
    }

    public function getUsername($storeId = null)
    {
        $env = $this->getEnvironment($storeId);
        if ($env === 'sandbox') {
            return $this->scopeConfig->getValue('edi/api/sandbox_username', ScopeInterface::SCOPE_STORE, $storeId);
        }
        return $this->scopeConfig->getValue('edi/api/username', ScopeInterface::SCOPE_STORE, $storeId);
    }

    public function getPassword($storeId = null)
    {
        $env = $this->getEnvironment($storeId);
        if ($env === 'sandbox') {
            return $this->scopeConfig->getValue('edi/api/sandbox_password', ScopeInterface::SCOPE_STORE, $storeId);
        }
        return $this->scopeConfig->getValue('edi/api/password', ScopeInterface::SCOPE_STORE, $storeId);
    }

    public function getDaysBack($storeId = null)
    {
        // Default to 180 if not set
        return (int)$this->scopeConfig->getValue('edi/backordered/days_back', ScopeInterface::SCOPE_STORE, $storeId) ?: 180;
    }
} 