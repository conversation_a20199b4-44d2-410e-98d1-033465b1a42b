import styled from '@emotion/styled'

export const StyledPartStorePartnerLocator = styled.div`
  display: grid;
  grid-template-columns: 478px 1fr;
  grid-gap: 24px;
  padding: 64px 0 92px;

  .map {
    width: 100%;
    border: 1px solid #d9d9d9;
    border-radius: 5px;
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    grid-template-columns: 1fr;
    grid-gap: 40px;
    padding: 40px 0 0;
    margin-top: 180px;

    .map {
      border-radius: 0;
    }
  }
`
