import { memo, useMemo, useCallback } from 'react'
import { clsx } from 'clsx'

import { StyledCommonPagination } from './styled'

const CommonPagination = ({ current = 1, pageSize = 10, total = 50, onChange = () => {} }) => {
  const totalCount = useMemo(() => {
    return Math.ceil(total / pageSize)
  }, [pageSize, total])

  const prevMoreVisible = useMemo(() => {
    return current > 3 && totalCount > 4
  }, [current, totalCount])

  const nextMoreVisible = useMemo(() => {
    return totalCount > 4 && totalCount - current > 2
  }, [current, totalCount])

  const pageList = useMemo(() => {
    const count = totalCount > 4 ? 4 : totalCount
    let list = Array.from({ length: count }, (_, index) => index + 1)

    if (current > 3) {
      list = [current - 1, current, current + 1]
      if (!nextMoreVisible) {
        // 取最后4位
        list = Array.from({ length: 4 }, (_, i) => totalCount - 3 + i)
      }
    }
    return list
  }, [totalCount, current, nextMoreVisible])

  const prevVisible = useMemo(() => {
    return current !== 1
  }, [current])

  const nextVisible = useMemo(() => {
    return current !== totalCount
  }, [current, totalCount])

  const handlePageChange = useCallback(
    (page: number) => {
      if (page !== current) {
        onChange(page, pageSize)
      }
    },
    [current, onChange, pageSize]
  )

  const handlePrev = useCallback(() => {
    onChange(current - 1)
  }, [onChange, current])

  const handleNext = useCallback(() => {
    onChange(current + 1)
  }, [onChange, current])

  const handlePrevMore = useCallback(() => {
    const listPrevPage = pageList[0] - 1
    onChange(listPrevPage, pageSize)
  }, [onChange, pageList, pageSize])

  const handleNextMore = useCallback(() => {
    const listNextPage = pageList[pageList.length - 1] + 1
    onChange(listNextPage, pageSize)
  }, [onChange, pageList, pageSize])

  return (
    <StyledCommonPagination className="common-pagination">
      {prevVisible && (
        <div className="common-pagination__prev" aria-hidden onClick={handlePrev}>
          <svg width="7px" height="13px" fill="currentColor" focusable="false">
            <use xlinkHref="#icon-arrow-right-black" />
          </svg>
        </div>
      )}
      {prevMoreVisible && (
        <div className="common-pagination__more" aria-hidden onClick={handlePrevMore}>
          <span />
          <span />
          <span />
        </div>
      )}
      {pageList.map((page) => (
        <div
          key={page}
          className={clsx({ active: current === page })}
          aria-hidden
          onClick={() => {
            handlePageChange(page)
          }}>
          {page}
        </div>
      ))}
      {nextMoreVisible && (
        <div className="common-pagination__more" aria-hidden onClick={handleNextMore}>
          <span />
          <span />
          <span />
        </div>
      )}
      {nextVisible && (
        <div className="common-pagination__next" aria-hidden onClick={handleNext}>
          <svg width="7px" height="13px" fill="currentColor" focusable="false">
            <use xlinkHref="#icon-arrow-right-black" />
          </svg>
        </div>
      )}
    </StyledCommonPagination>
  )
}

export default memo(CommonPagination)
