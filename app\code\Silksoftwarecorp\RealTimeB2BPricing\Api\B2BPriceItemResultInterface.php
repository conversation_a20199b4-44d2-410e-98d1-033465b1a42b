<?php

namespace Silksoftwarecorp\RealTimeB2BPricing\Api;

use Silksoftwarecorp\RealTimeB2BPricing\Api\Data\B2BPriceItemInterface;
use Silksoftwarecorp\RealTimeB2BPricing\Model\B2BPriceItemResult;

interface B2BPriceItemResultInterface
{
    public function addItem(B2BPriceItemInterface $item): static;

    public function addItems(B2BPriceItemInterface ...$items): static;

    public function getItem($sku): ?B2BPriceItemInterface;

    public function getItems(): array;

    public function merge(B2BPriceItemResultInterface $priceItemResult, bool $overwrite = true): static;

    public function getSkus(): array;

    public function count(): int;
}
