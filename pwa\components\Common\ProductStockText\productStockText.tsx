import { memo, useMemo } from 'react'
import { clsx } from 'clsx'

import { StyledProductStockText } from './styled'

const ProductStockText = ({ text }) => {
  // 0 in Stock
  const isEmptyStockNumber = useMemo(() => {
    return text === '0 in Stock'
  }, [text])

  // On Order, 0 in Stock
  const isOnOrder = useMemo(() => {
    return text?.indexOf('On Order') > -1
  }, [text])

  return (
    <StyledProductStockText
      className={clsx({
        'is-empty-stock': isEmptyStockNumber,
        'on-order': isOnOrder
      })}>
      {text}
    </StyledProductStockText>
  )
}

export default memo(ProductStockText)
