<?php

declare(strict_types=1);

namespace Silksoftwarecorp\CatalogGraphQl\Model\Resolver\Product;

use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Catalog\Model\Product;
use Magento\Catalog\Helper\Output as OutputHelper;

class AttributeWarrantyInformation implements ResolverInterface
{
    /**
     * @var OutputHelper
     */
    private $outputHelper;

    private $productRepository;

    /**
     * @param OutputHelper $outputHelper
     */
    public function __construct(
        OutputHelper $outputHelper,
        \Magento\Catalog\Api\ProductRepositoryInterface $productRepository,
    ) {
        $this->outputHelper = $outputHelper;
        $this->productRepository = $productRepository;
    }

    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
        if (!array_key_exists('model', $value) || !$value['model'] instanceof ProductInterface) {
            throw new LocalizedException(__('"model" value should be specified'));
        }

        /* @var $product Product */
        $product = $value['model'];
        $sku = $product->getSku();
        $productNew = $this->productRepository->get($sku);
        $fieldName = "ble_warranty_information";
        $renderedValue = $this->outputHelper->productAttribute($productNew, $productNew->getData($fieldName), $fieldName);

        return ['html' => $renderedValue ?? ''];
    }

}
