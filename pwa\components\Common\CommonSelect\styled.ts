import { Select } from 'antd'
import styled from '@emotion/styled'

export const StyledCommonSelect = styled(Select)`
  height: 44px !important;
  width: 100% !important;

  .${({ theme }) => theme.namespace} {
    &-select-selector {
      padding-left: 11px !important;
      border-radius: 3px !important;
    }

    &-select-selection-search {
      input {
        height: 44px !important;
      }
    }
  }
`
