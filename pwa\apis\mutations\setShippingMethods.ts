import { gql, DocumentNode } from '@apollo/client'

import { cart_prices } from '../fragment/cartPrices'

export const POST_SHIPPING_METHODS: DocumentNode = gql`
  mutation setShippingMethods($cartId: String!, $method: [ShippingMethodInput]!) {
    shippingMethods: setShippingMethodsOnCart(
      input: { cart_id: $cartId, shipping_methods: $method }
    ) {
      cart {
        shipping_addresses {
          city
          company
          firstname
          lastname
          postcode
          street
          telephone
          country {
            code
            label
          }
          region {
            region_id
            label
          }
          selected_shipping_method {
            amount {
              value
            }
            carrier_code
            carrier_title
            method_code
            method_title
          }
          available_shipping_methods {
            amount {
              value
            }
            carrier_code
            carrier_title
            method_code
            method_title
          }
        }
        prices {
          ...cart_prices
          __typename
        }
      }
    }
  }
  ${cart_prices}
`
