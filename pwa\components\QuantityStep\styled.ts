import styled from '@emotion/styled'

interface StyledQuantityProps {
  enabled: boolean
}

export const StyledQuantity = styled.div<StyledQuantityProps>`
  display: grid;
  grid-template-columns: ${(props: any) => (props.enabled ? '32px auto 32px' : 'unset')};

  .${({ theme }) => theme.namespace} {
    &-btn {
      height: 32px;
      padding: 0;
      background-color: transparent !important;

      &:first-of-type {
        border-right: unset;
        border-radius: 4px 0 0 4px;
      }

      &:last-of-type {
        border-left: unset;
        border-radius: 0 4px 4px 0;
      }

      &:hover {
        color: #6b6b6b;
        border-color: #d9d9d9;
      }

      &:disabled {
        svg {
          opacity: 0.3;
        }
      }
    }

    &-input {
      height: 50px;
      text-align: center;
      border-radius: unset;
      background-color: #fff;

      &-disabled {
        border-color: rgba(118, 118, 118, 0.16);
      }
    }
  }
`
