<?php

namespace Silksoftwarecorp\ERPCompany\Model\Company\SaveHandler;

use Magento\Company\Api\Data\CompanyInterface;
use Silksoftwarecorp\ERPCompany\Model\Company\ERPRepository;

class ERPDataAssociation implements \Magento\Company\Model\SaveHandlerInterface
{
    /**
     * Erp Company settings field.
     *
     * @var array
     */
    private $erpCompanySettings = [
        'erp_customer_id',
        'erp_sales_rep_id',
        'erp_sales_rep_name',
        'erp_credit_status'
    ];

    /**
     * @param ERPRepository $erpRepository
     */
    public function __construct(
        private readonly ERPRepository $erpRepository
    ) {

    }

    /**
     * @param CompanyInterface $company
     * @param CompanyInterface $initialCompany
     * @return void
     *
     * @throws \Exception
     */
    public function execute(CompanyInterface $company, CompanyInterface $initialCompany): void
    {
        $needSave = false;
        $extensionAttributes = $company->getExtensionAttributes();
        $initialExtensionAttributes = $initialCompany->getExtensionAttributes();

        foreach ($this->erpCompanySettings as $erpCompanySetting) {
            $method = 'get'
                . \Magento\Framework\Api\SimpleDataObjectConverter::snakeCaseToUpperCamelCase($erpCompanySetting);
            $result = $extensionAttributes->$method();
            $initialResult = $initialExtensionAttributes->$method();

            if (is_array($result)) {
                $result = implode(',', $result);
            }

            if (strlen((string)$result) > 0 && $result !== $initialResult) {
                $needSave = true;
                break;
            }
        }

        if ($needSave) {
            $this->saveErpCompanySettings($company);
        }

        $company->setExtensionAttributes($this->eraseErpCompanySettingsData($extensionAttributes));
    }

    /**
     * Save erp company settings.
     *
     * @param \Magento\Company\Api\Data\CompanyInterface $company
     * @throws \Exception
     * @return void
     */
    private function saveErpCompanySettings(\Magento\Company\Api\Data\CompanyInterface $company): void
    {
        $erpCompanySettings = $this->erpRepository->get((int)$company->getId());
        /**@var \Magento\Company\Api\Data\CompanyExtensionInterface $extensionAttributes*/
        $extensionAttributes = $company->getExtensionAttributes();

        if (!$erpCompanySettings?->getId()) {
            $erpCompanySettings->setCompanyId($company->getId());
        }

        $erpCustomerId = $extensionAttributes->getErpCustomerId();
        $erpSalesRepId = $extensionAttributes->getErpSalesRepId();
        $erpSalesRepName = $extensionAttributes->getErpSalesRepName();
        $erpCreditStatus = $extensionAttributes->getErpCreditStatus();
        $erpCompanySettings->setErpCustomerId($erpCustomerId);
        $erpCompanySettings->setErpSalesRepId($erpSalesRepId);
        $erpCompanySettings->setErpSalesRepName($erpSalesRepName);
        $erpCompanySettings->setErpCreditStatus($erpCreditStatus);

        $this->erpRepository->save($erpCompanySettings);
    }

    /**
     * Erase saved attributes to prevent breaking of populateWithArray.
     *
     * @param \Magento\Company\Api\Data\CompanyExtensionInterface $extensionAttributes
     * @return \Magento\Company\Api\Data\CompanyExtensionInterface
     */
    private function eraseErpCompanySettingsData(\Magento\Company\Api\Data\CompanyExtensionInterface $extensionAttributes)
    {
        foreach ($this->erpCompanySettings as $companySetting) {
            $method = 'set'
                . \Magento\Framework\Api\SimpleDataObjectConverter::snakeCaseToUpperCamelCase($companySetting);
            $extensionAttributes->$method(null);
        }

        return $extensionAttributes;
    }
}
