import styled from '@emotion/styled'

export const StyledCustomFormPage = styled.div`
  margin-top: 32px;

  .${({ theme }) => theme.namespace}-form {
    h5 {
      margin-bottom: 16px;
      font-weight: 700;
      font-size: 22px;
      line-height: 28px;
      letter-spacing: 0;

      &.is-small {
        margin-top: -8px;
        font-weight: 400;
        font-style: Italic;
        font-size: 15px;
        line-height: 21px;
        letter-spacing: 0.01em;
        color: #535353;
      }
    }

    .submit-btn {
      display: flex;
      justify-content: center;
      align-items: center;
      padding-top: 16px;

      button {
        display: flex;
        align-items: center;
        padding: 0 32px;
        height: 49px;
        border-radius: 3px;
        background: var(--color-primary) !important;
        border: none !important;

        &:not(:first-of-type) {
          margin-left: 16px;
        }

        span {
          font-weight: 700;
          font-size: 16px;
          line-height: 21px;
          letter-spacing: 0.03em;
          text-transform: uppercase;
          color: #fff !important;
        }

        &[disabled] {
          background: #939392 !important;
        }

        &.back-btn {
          background: #003865 !important;
        }
      }
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    margin-top: 24px;

    .submit-btn {
      button {
        width: 100% !important;
      }
    }
  }
`
