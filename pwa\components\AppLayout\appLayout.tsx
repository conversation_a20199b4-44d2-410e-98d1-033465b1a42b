import { App } from 'antd'
import type { FC, PropsWithChildren } from 'react'

import { useAppLayout } from '@/hooks/AppLayout'
import Header from '@/components/Header'
import Subscribe from '@/components/Subscribe'
import Footer from '@/components/Footer'
import AppLoading from '@/components/AppLoading'
import AddedModal from '@/components/AddedModal'
import GlobalMsg from '@/components/GlobalMsg'

import { StyledMain } from './styled'
import PageScripts from './PageScripts'

const AppLayout: FC<PropsWithChildren> = ({ children }) => {
  const { isCheckout, loading } = useAppLayout()

  return (
    <App>
      <Header />
      <StyledMain className="main">{children}</StyledMain>
      {!isCheckout && (
        <>
          {/* TODO: Remove code */}
          {/*<AddedModal />*/}
          {/*<Subscribe />*/}
          <Footer />
        </>
      )}
      {loading && <AppLoading />}
      <GlobalMsg />
      <PageScripts />
    </App>
  )
}

export default AppLayout
