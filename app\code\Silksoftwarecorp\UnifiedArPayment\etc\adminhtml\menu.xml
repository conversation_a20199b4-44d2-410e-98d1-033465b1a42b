<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Backend:etc/menu.xsd">
    <menu>
        <add id="Silksoftwarecorp_UnifiedArPayment::unified_ar_payment"
             title="Unified A/R Payment"
             module="Silksoftwarecorp_UnifiedArPayment"
             sortOrder="100"
             parent="Magento_Backend::stores"
             resource="Silksoftwarecorp_UnifiedArPayment::unified_ar_payment"/>
        
        <add id="Silksoftwarecorp_UnifiedArPayment::config"
             title="Configuration"
             module="Silksoftwarecorp_UnifiedArPayment"
             sortOrder="10"
             parent="Silksoftwarecorp_UnifiedArPayment::unified_ar_payment"
             action="admin/system_config/edit/section/payment"
             resource="Silksoftwarecorp_UnifiedArPayment::config"/>
        
        <!-- <add id="Silksoftwarecorp_UnifiedArPayment::return_data"
             title="Return Data"
             module="Silksoftwarecorp_UnifiedArPayment"
             sortOrder="20"
             parent="Silksoftwarecorp_UnifiedArPayment::unified_ar_payment"
             resource="Silksoftwarecorp_UnifiedArPayment::unified_ar_return_data"/> -->
    </menu>
</config> 