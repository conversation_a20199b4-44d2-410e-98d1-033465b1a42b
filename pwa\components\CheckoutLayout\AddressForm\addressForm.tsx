import { memo, useEffect } from 'react'
import { Form } from 'antd'
import { nanoid } from 'nanoid'
import { isEmpty } from 'lodash-es'
import type { FC } from 'react'

import { TextField, TextSelect } from '@/ui'
import { useCountries } from '@/hooks/Countries'
import { StyledAddressForm, StyledFormItem, StyledFormLine } from './styled'

interface AddressFormProps {
  address?: any
  onChange?: () => void
}

const AddressForm: FC<AddressFormProps> = ({ address = {}, onChange = () => {} }) => {
  const form = Form.useFormInstance()
  const { countries, defaultCountry, regions, handleCountryChange } = useCountries(form)

  const handleFieldChange = () => {
    onChange?.()
  }

  useEffect(() => {
    if (!isEmpty(address)) {
      const { country, street, region, ...rest } = address
      const variables = {
        ...rest,
        country: country.code,
        street: street?.[0] ?? '',
        building: street?.[1] ?? ''
      }
      form.setFieldsValue({
        ...variables
      })
      handleCountryChange(country.code).finally(() => {
        form.setFieldsValue({
          region: region?.region_id > 0 ? region?.region_id : region?.label
        })
      })
    } else {
      // eslint-disable-next-line no-lonely-if
      if (countries.length > 0) {
        form.setFieldsValue({
          country: defaultCountry
        })
        handleCountryChange(defaultCountry)
      }
    }
  }, [address, countries, defaultCountry, form, handleCountryChange])

  return (
    <StyledAddressForm>
      <StyledFormItem>
        <Form.Item name="firstname" rules={[{ required: true }]}>
          <TextField label="First Name" />
        </Form.Item>
        <Form.Item name="lastname" rules={[{ required: true }]}>
          <TextField label="Last Name" />
        </Form.Item>
      </StyledFormItem>
      <Form.Item name="company">
        <TextField label="Company (Optional)" />
      </Form.Item>
      <Form.Item name="street" rules={[{ required: true }]}>
        <TextField label="Street Address" />
      </Form.Item>
      <Form.Item name="building">
        <TextField label="Building, Apartment, Suite etc. (Optional)" />
      </Form.Item>
      <StyledFormLine>
        <Form.Item name="city" rules={[{ required: true }]}>
          <TextField label="City" />
        </Form.Item>
        {regions.length > 0 ? (
          <Form.Item name="region" rules={[{ required: true }]}>
            <TextSelect label="State" elementId={1} onChange={handleFieldChange}>
              {regions.map((region: any) => {
                return (
                  <TextSelect.Option key={nanoid()} value={region.id} param={region.id}>
                    <span dangerouslySetInnerHTML={{ __html: region.name }} />
                  </TextSelect.Option>
                )
              })}
            </TextSelect>
          </Form.Item>
        ) : (
          <Form.Item name="region" rules={[{ required: true }]}>
            <TextField label="State" onBlur={handleFieldChange} />
          </Form.Item>
        )}
        <Form.Item name="postcode" rules={[{ required: true }]}>
          <TextField label="Zip Code" onBlur={handleFieldChange} />
        </Form.Item>
      </StyledFormLine>
      <Form.Item name="country" rules={[{ required: true }]}>
        <TextSelect label="Country" onChange={handleCountryChange}>
          {countries.map((country: any) => {
            return (
              <TextSelect.Option key={country.id} value={country.id} param={country.name}>
                <span dangerouslySetInnerHTML={{ __html: country.name }} />
              </TextSelect.Option>
            )
          })}
        </TextSelect>
      </Form.Item>
      <Form.Item name="telephone" rules={[{ required: true }]}>
        <TextField label="Phone Number" />
      </Form.Item>
    </StyledAddressForm>
  )
}

export default memo(AddressForm)
