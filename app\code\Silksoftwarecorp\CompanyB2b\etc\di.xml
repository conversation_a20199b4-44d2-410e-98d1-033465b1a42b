<?xml version="1.0"?>
<!--
/**
 *
 * @package     Silksoftwarecorp_CompanyB2b
 * @copyright   Copyright © 2021 Silk Software Corp (https://www.silksoftware.com)
 * <AUTHOR> <EMAIL>
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Silksoftwarecorp\CompanyB2b\Api\Data\CompanyCustomAttributesInterface" type="Silksoftwarecorp\CompanyB2b\Model\CompanyCustomAttributes" />
    <preference for="Silksoftwarecorp\CompanyB2b\Api\ErpShipToRepositoryInterface" type="Silksoftwarecorp\CompanyB2b\Model\ErpShipToRepository" />
    <preference for="Silksoftwarecorp\CompanyB2b\Api\Data\ShipToSearchResultsInterface" type="Magento\Framework\Api\SearchResults"/>
    <preference for="Silksoftwarecorp\CompanyB2b\Api\Data\ErpCompanyShipToInterface" type="Silksoftwarecorp\CompanyB2b\Model\ErpCompanyShipTo"/>

    <type name="Magento\Company\Model\SaveHandlerPool">
        <arguments>
            <argument name="handlers" xsi:type="array">
                <item name="erpCustomAttributes" xsi:type="object">\Silksoftwarecorp\CompanyB2b\Model\SaveHandler\ErpCompanyAttributes</item>
            </argument>
        </arguments>
    </type>
    <!--Grid collection-->
    <virtualType name="Silksoftwarecorp\CompanyB2b\Model\ResourceModel\ShipTo\Grid\Collection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
        <arguments>
            <argument name="mainTable" xsi:type="string">company_shipto</argument>
            <argument name="resourceModel" xsi:type="string">Silksoftwarecorp\CompanyB2b\Model\ResourceModel\ErpCompanyShipTo</argument>
        </arguments>
    </virtualType>
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="ship_to_listing_data_source" xsi:type="string">Silksoftwarecorp\CompanyB2b\Model\ResourceModel\ShipTo\Grid\Collection</item>
            </argument>
        </arguments>
    </type>
    <!-- get Erp Info -->
    <type name="Magento\Company\Api\CompanyRepositoryInterface">
        <plugin name="silk_erp_company_repository" type="Silksoftwarecorp\CompanyB2b\Plugin\Company\Model\CompanyRepository" />
    </type>
     <type name="Magento\Company\Model\Company\GetList">
         <plugin name="silk_erp_company_list" type="Silksoftwarecorp\CompanyB2b\Plugin\Company\Model\CompanyList" />
     </type>
    <type name="Magento\Company\Model\ResourcePool">
        <plugin name="companyb2b_resourcepool_plugin" type="Silksoftwarecorp\CompanyB2b\Plugin\Company\Model\ResourcePoolPlugin" sortOrder="10" />
    </type>
</config>
