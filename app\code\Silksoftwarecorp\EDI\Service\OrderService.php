<?php

namespace Silksoftwarecorp\EDI\Service;

use Silksoftwarecorp\EDI\Helper\Order as OrderHelper;
use Silksoftwarecorp\EDI\Model\EdiApiManagement;
use Silksoftwarecorp\EDI\Model\Order\Formatter as OrderFormatter;

class OrderService
{
    /**
     * @var OrderHelper
     */
    protected $orderHelper;

    /**
     * @var EdiApiManagement
     */
    protected $ediApiManagement;

    /**
     * @var OrderFormatter
     */
    protected $orderFormatter;

    protected array $_orders = [];

    protected array $_ordersDetails = [];

    /**
     * @param OrderHelper $orderHelper
     * @param EdiApiManagement $ediApiManagement
     * @param OrderFormatter $orderFormatter
     */
    public function __construct(
        OrderHelper $orderHelper,
        EdiApiManagement $ediApiManagement,
        OrderFormatter $orderFormatter
    ) {
        $this->orderHelper = $orderHelper;
        $this->ediApiManagement = $ediApiManagement;
        $this->orderFormatter = $orderFormatter;
    }

    public function getOrders($customerId, ?string $startDate = null): array
    {
        if (!$this->orderHelper->isEnabled()) {
            return [];
        }

        $startDate = $startDate ?: $this->orderHelper->getStartDate();
        $key = sprintf('%s-%s', $customerId, $startDate);
        if (!isset($this->_orders[$key]) || empty($this->_orders[$key])) {
            $orders = $this->ediApiManagement->getOrders(
                $customerId,
                $startDate,
            );

            $result = [];
            foreach ($orders as $order) {
                $result[] = $this->orderFormatter->format($order);
            }

            $this->_orders[$key] = $result;
        }

        return $this->_orders[$key];
    }

    public function getOrderDetails(string $orderNumber): array
    {
        if (!$this->orderHelper->isEnabled()) {
            return [];
        }

        if (!isset($this->_ordersDetails[$orderNumber]) || empty($this->_ordersDetails[$orderNumber])) {
            $order = $this->ediApiManagement->getOrder($orderNumber);

            $this->_ordersDetails[$orderNumber] = $this->orderFormatter->format($order);
        }

        return $this->_ordersDetails[$orderNumber];
    }
}
