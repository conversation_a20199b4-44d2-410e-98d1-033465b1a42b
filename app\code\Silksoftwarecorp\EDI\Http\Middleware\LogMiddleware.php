<?php

namespace Silksoftwarecorp\EDI\Http\Middleware;

use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Promise\Create;
use GuzzleHttp\Promise\PromiseInterface;
use GuzzleHttp\Promise;
use GuzzleHttp\TransferStats;
use Psr\Log\LoggerInterface;
use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\ResponseInterface;
use Silksoftwarecorp\EDI\Http\Middleware\LogMiddleware\Handler\HandlerInterface;
use Silksoftwarecorp\EDI\Http\Middleware\LogMiddleware\Handler\MultiRecordArrayHandler;

class LogMiddleware
{
    /**
     * @var bool
     */
    private $onFailureOnly = true;

    /**
     * Decides if you need to log statistics or not.
     *
     * @var bool
     */
    private $logStatistics;

    /**
     * @var TransferStats|null
     */
    private $stats = null;

    /**
     * @var HandlerInterface
     */
    private $handler;

    protected $logger;

    public function __construct(
        LoggerInterface $logger,
        ?HandlerInterface $handler = null,
    ) {
        $this->logger = $logger;
        $this->handler = $handler === null ? new MultiRecordArrayHandler() : $handler ;
    }

    public function __invoke(callable $handler): callable
    {
        return function (RequestInterface $request, array $options) use ($handler): PromiseInterface {
            $this->setOptions($options);

            $this->logger->critical('LogMiddleware');

            if ($this->logStatistics && !isset($options['on_stats'])) {
                $options['on_stats'] = function (TransferStats $stats) {
                    $this->stats = $stats;
                };
            }

            return $handler($request, $options)
                ->then(
                    $this->handleSuccess($request, $options),
                    $this->handleFailure($request, $options)
                );
        };
    }

    private function handleSuccess(RequestInterface $request, array $options): callable
    {
        return function (ResponseInterface $response) use ($request, $options) {
            // if onFailureOnly is true then it must not log the response since it was successful.
            if ($this->onFailureOnly === false) {
                $this->handler->log($this->logger, $request, $response, null, $this->stats, $options);
            }

            return $response;
        };
    }

    /**
     * Returns a function which is handled when a request was rejected.
     */
    private function handleFailure(RequestInterface $request, array $options): callable
    {
        return function (\Exception $reason) use ($request, $options) {
            if ($reason instanceof RequestException && $reason->hasResponse() === true) {
                $this->handler->log($this->logger, $request, $reason->getResponse(), $reason, $this->stats, $options);
                return Create::rejectionFor($reason);
            }

            $this->handler->log($this->logger, $request, null, $reason, $this->stats, $options);
            return Create::rejectionFor($reason);
        };
    }

    private function setOptions(array $options): void
    {
        if (!isset($options['log'])) {
            return;
        }

        $options = $options['log'];

        $options = array_merge([
            'on_exception_only' => $this->onFailureOnly,
            'statistics' => $this->logStatistics,
        ], $options);

        $this->stats = null;
        $this->onFailureOnly = $options['on_exception_only'];
        $this->logStatistics = $options['statistics'];
    }
}
