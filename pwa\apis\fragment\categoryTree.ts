import { gql } from '@apollo/client'
import type { DocumentNode } from '@apollo/client'

export const categoryTree: DocumentNode = gql`
  fragment categoryTree on CategoryTree {
    id
    name
    url_path
    display_mode
    categoryDescription: description
    image
    meta_title
    meta_keywords
    meta_description
    breadcrumbs {
      category_id
      category_name
      category_url_path
    }
    children {
      name
      url_path
    }
  }
`
