<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Config/etc/system_file.xsd">
    <system>
        <tab id="silksoftwarecorp" translate="label" sortOrder="300">
            <label>Silk Commerce</label>
        </tab>
        <section id="bel_ship_to" translate="label" type="text" sortOrder="900" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>Ship To</label>
            <tab>silksoftwarecorp</tab>
            <resource>Silksoftwarecorp_ShipToGraphQl::config</resource>
            <group id="request_shipto" translate="label" type="text" sortOrder="1" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Request Ship To</label>
                <field id="email_enable" translate="label" type="select" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="email_receiver" translate="label" type="text" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Email Receiver</label>
                    <validate>validate-emails</validate>
                    <depends>
                        <field id="email_enable">1</field>
                    </depends>
                </field>
                <field id="email_template" translate="label comment" type="select" sortOrder="90" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Email Template</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                    <depends>
                        <field id="email_enable">1</field>
                    </depends>
                </field>
                <field id="email_identity" translate="label" type="select" sortOrder="99" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Email Sender</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Identity</source_model>
                    <depends>
                        <field id="email_enable">1</field>
                    </depends>
                </field>
            </group>
        </section>
    </system>
</config>
