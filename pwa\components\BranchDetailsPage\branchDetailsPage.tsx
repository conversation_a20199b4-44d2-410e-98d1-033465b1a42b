import React, { useCallback, useMemo, useEffect, useState } from 'react'
import { useRouter } from 'next/compat/router'
import Link from 'next/link'
import Image from 'next/image'
import { isEmpty } from 'lodash-es'

import { LineContainer } from '@ranger-theme/ui'
import { useAwaitQuery } from '@ranger-theme/apollo'
import { GET_BRANCH_SOURCE } from '@/apis/queries/getBranchSource'
import CommonLoading from '@/components/Common/CommonLoading'
import GoogleMap from '@/components/GoogleMap'
import { useAppMediaQuery } from '@/packages/hooks'

import { StyledBranchDetailsPage } from './styled'

const BranchDetailsPage = () => {
  const router = useRouter()
  const getBranchSourceQuery = useAwaitQuery(GET_BRANCH_SOURCE)
  const { isMobile } = useAppMediaQuery()

  const [loading, setLoading] = useState<boolean>(false)
  const [details, setDetails] = useState<any>({})

  const code = useMemo(() => router?.query?.code, [router])

  const office = useMemo(() => {
    return details?.schedules?.office || {}
  }, [details])

  const store = useMemo(() => {
    return details?.schedules?.store || {}
  }, [details])

  const location = useMemo(() => {
    return isEmpty(details) ? null : { lat: details?.lat, lng: details?.lng }
  }, [details])

  const fetchBranchSource = useCallback(
    async (val) => {
      try {
        setLoading(true)
        const { data } = await getBranchSourceQuery({
          variables: {
            code: val
          }
        })
        setDetails(data?.getBranchSource || {})
      } catch (e) {
        console.error(e)
      } finally {
        setLoading(false)
      }
    },
    [getBranchSourceQuery]
  )

  useEffect(() => {
    if (code) {
      fetchBranchSource(code)
    }
  }, [code, fetchBranchSource])

  return (
    <LineContainer>
      <CommonLoading spinning={loading}>
        {!isEmpty(details) && (
          <StyledBranchDetailsPage>
            <h1>{details?.name ?? ''}</h1>
            <div className="details-grid">
              <div className="details-list">
                <div>
                  <h5>{details?.name ?? ''}</h5>
                  <p>
                    {details?.address?.street?.[0] ?? ''}
                    {details?.address?.street?.[1] && `, ${details?.address?.street?.[1] ?? ''}`}
                  </p>
                  <p>
                    {details?.address?.city ?? ''}, {details?.address?.region?.region_code ?? ''}{' '}
                    {details?.address?.postcode ?? ''}
                  </p>
                  <p className="mt-item">
                    <b>Phone: </b>
                    <Link href={`tel:${details?.phone ?? ''}`}>{details?.phone ?? ''}</Link>
                  </p>
                  <p>
                    <b>Text: </b>
                    <Link href={`tel:${details?.text_number ?? ''}`}>
                      {details?.text_number ?? ''}
                    </Link>
                  </p>
                  <p>
                    <b>Fax: </b>
                    {details?.fax ?? ''}
                  </p>
                  <p>
                    <b>Email: </b>
                    <Link href={`mailto:${details?.email ?? ''}`}>{details?.email ?? ''}</Link>
                  </p>
                </div>
                <div>
                  <h5>Hours</h5>
                  <p>
                    <b>Office</b>
                  </p>
                  <p>
                    Monday through Friday: {office?.monday?.from ?? ''} AM -{' '}
                    {office?.friday?.to ?? ''} PM
                  </p>
                  <p>Saturday: {office?.saturday?.status ? 'Open' : 'Closed'}</p>
                  <p>Sunday: {office?.sunday?.status ? 'Open' : 'Closed'}</p>
                  <p className="mt-item">
                    <b>Branch</b>
                  </p>
                  <p>
                    Monday through Friday: {store?.monday?.from ?? ''} AM -{' '}
                    {store?.friday?.to ?? ''} PM
                  </p>
                  <p>Saturday: {store?.saturday?.status ? 'Open' : 'Closed'}</p>
                  <p>Sunday: {store?.sunday?.status ? 'Open' : 'Closed'}</p>
                </div>
                <div className="manager-item">
                  <h5>General Manager</h5>
                  <p>{details?.general_manager?.region ?? ''}</p>
                  <div className="general-manager">
                    <Image
                      src={details?.general_manager?.image || ''}
                      width={124}
                      height={124}
                      alt=""
                    />
                    <b>{details?.general_manager?.name ?? ''}</b>
                  </div>
                </div>
                <div className="manager-item">
                  <h5>Territory Managers</h5>
                  {details?.territory_managers?.map((item, index) => {
                    const key = `key_${index}`
                    return (
                      <div key={key}>
                        <p className={index === 0 ? '' : 'mt-item'}>
                          <b>{item?.name ?? ''}</b>
                        </p>
                        <p>{item?.region ?? ''}</p>
                        <p>
                          Phone:{` `}
                          <Link href={`tel:${item?.phone ?? ''}`}>{item?.phone ?? ''}</Link>
                        </p>
                      </div>
                    )
                  })}
                </div>
              </div>
              <div className="details-map">
                {location && (
                  <GoogleMap
                    defaultLocation={location}
                    width={isMobile ? '100%' : '678px'}
                    height={isMobile ? '496px' : '100%'}
                    icon={{
                      url: '/images/map-icon.png'
                      // scaledSize: new google.maps.Size(40, 40)
                      // anchor: new google.maps.Point(20, 40)
                    }}
                  />
                )}
              </div>
            </div>
          </StyledBranchDetailsPage>
        )}
      </CommonLoading>
    </LineContainer>
  )
}

export default BranchDetailsPage
