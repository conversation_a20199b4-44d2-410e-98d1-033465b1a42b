<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Store:etc/config.xsd">
    <default>
        <tax>
            <onesource>
                <active>0</active>
                <mode>sandbox</mode>
                <sandbox>
                    <token_url>https://api-uat.onesourcetax.com/oauth2/v1/token</token_url>
                    <calculate_url>https://api-uat.onesourcetax.com/indirect-tax-determination/taxes/v1/calculate</calculate_url>
                </sandbox>
                <live>
                    <token_url>https://api.onesourcetax.com/oauth2/v1/token</token_url>
                    <calculate_url>https://api.onesourcetax.com/indirect-tax-determination/taxes/v1/calculate</calculate_url>
                </live>
                <client_id></client_id>
                <client_secret></client_secret>
                <scopes></scopes>
                <company_role>S</company_role>
                <external_company_id></external_company_id>
                <processing_options>
                    <charge_included_in_amounts>1</charge_included_in_amounts>
                    <charge_response>SeparateAuthority</charge_response>
                    <response_summary>SummaryByErpCode</response_summary>
                    <document_amount_type>GrossAmount</document_amount_type>
                </processing_options>
            </onesource>
        </tax>
    </default>
</config>