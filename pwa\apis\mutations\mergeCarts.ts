import { gql } from '@apollo/client'
import type { DocumentNode } from '@apollo/client'

import { cartItems } from '../fragment/cartItems'
import { cart_prices } from '../fragment/cartPrices'

export const POST_MERGE_CARTS: DocumentNode = gql`
  mutation mergeCarts($sourcCartId: String!, $destinationCartId: String!) {
    cart: mergeCarts(source_cart_id: $sourcCartId, destination_cart_id: $destinationCartId) {
      id
      prices {
        ...cart_prices
      }
      quantity: total_quantity
      applied_coupons {
        code
      }
      ...cartItems
      __typename
    }
  }
  ${cartItems}
  ${cart_prices}
`
