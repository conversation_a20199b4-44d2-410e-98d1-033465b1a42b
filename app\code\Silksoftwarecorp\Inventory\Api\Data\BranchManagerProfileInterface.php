<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */
declare(strict_types=1);

namespace Silksoftwarecorp\Inventory\Api\Data;

/**
 * Branch Manager Profile Interface
 */
interface BranchManagerProfileInterface
{
    const ID = 'id';
    const SOURCE_CODE = 'source_code';
    const IS_GENERAL = 'is_general';
    const IMAGE = 'image';
    const NAME = 'name';
    const PHONE = 'phone';
    const REGION = 'region';

    /**
     * Get ID
     *
     * @return int|null
     */
    public function getId();

    /**
     * Set ID
     *
     * @param int $id
     * @return $this
     */
    public function setId($id);

    /**
     * Get Source Code
     *
     * @return string
     */
    public function getSourceCode();

    /**
     * Set Source Code
     *
     * @param string $sourceCode
     * @return $this
     */
    public function setSourceCode(string $sourceCode);

    /**
     * Get Is General
     *
     * @return int
     */
    public function getIsGeneral();

    /**
     * Set Is General
     *
     * @param bool $isGeneral
     * @return $this
     */
    public function setIsGeneral(bool $isGeneral);

    /**
     * Get Image
     *
     * @return string|null
     */
    public function getImage();

    /**
     * Set Image
     *
     * @param string $image
     * @return $this
     */
    public function setImage(string $image);

    /**
     * Get Name
     *
     * @return string|null
     */
    public function getName();

    /**
     * Set Name
     *
     * @param string $name
     * @return $this
     */
    public function setName(string $name);

    /**
     * Get Phone
     *
     * @return string|null
     */
    public function getPhone();

    /**
     * Set Phone
     *
     * @param string $phone
     * @return $this
     */
    public function setPhone(string $phone);

    /**
     * Get Region
     *
     * @return string|null
     */
    public function getRegion();

    /**
     * Set Region
     *
     * @param string $region
     * @return $this
     */
    public function setRegion(string $region);

    /**
     * Get Image URL
     *
     * @return string|null
     */
    public function getImageUrl();
}
