import { memo } from 'react'
import { isEmpty } from 'lodash-es'
import { Button, Checkbox, Form, Spin, Modal } from 'antd'
import { FormattedMessage } from 'react-intl'
import { nanoid } from 'nanoid'

import { TextField, TextSelect } from '@/ui'
import { useAddressModal } from '@/hooks/CheckoutLayout'
import StreetFields from '@/components/StreetFields'
import { StyledFormItem, StyledFormLine, StyledActions } from './styled'

const AddressModal = ({ address = {}, isLoading = false, handleFormSubmit, ...props }) => {
  const { visible, ...rest } = props
  const [form] = Form.useForm()
  const { countries, regions, handleCountryChange, handleAddressSave } = useAddressModal({
    address,
    form,
    handleFormSubmit
  })
  // Edit or add address title
  const title = `account.${isEmpty(address) ? 'addNewAddress' : 'eidtAddress'}`

  return (
    <Modal
      className="modal-addresss"
      footer={null}
      width={750}
      open={visible}
      getContainer={() => document.getElementById('checkout')}
      title={<FormattedMessage id={title} />}
      {...rest}>
      <Spin spinning={isLoading}>
        <Form
          form={form}
          initialValues={{
            street: ['', ''],
            default_billing: false,
            default_shipping: false
          }}
          onFinish={handleAddressSave}>
          <StyledFormItem>
            <Form.Item name="firstname" rules={[{ required: true }]}>
              <TextField label="First Name" />
            </Form.Item>
            <Form.Item name="lastname" rules={[{ required: true }]}>
              <TextField label="Last Name" />
            </Form.Item>
          </StyledFormItem>
          <Form.Item name="company">
            <TextField label="Company (Optional)" />
          </Form.Item>
          <StreetFields />
          <StyledFormLine>
            <Form.Item name="city" rules={[{ required: true }]}>
              <TextField label="City" />
            </Form.Item>
            {regions.length > 0 ? (
              <Form.Item name="region" rules={[{ required: true }]}>
                <TextSelect label="State" elementId={1}>
                  {regions.map((region: any) => {
                    return (
                      <TextSelect.Option key={nanoid()} value={region.id} param={region.id}>
                        <span dangerouslySetInnerHTML={{ __html: region.name }} />
                      </TextSelect.Option>
                    )
                  })}
                </TextSelect>
              </Form.Item>
            ) : (
              <Form.Item name="region" rules={[{ required: true }]}>
                <TextField label="State" />
              </Form.Item>
            )}
            <Form.Item name="postcode" rules={[{ required: true }]}>
              <TextField label="Zip Code" />
            </Form.Item>
          </StyledFormLine>
          <Form.Item name="country" rules={[{ required: true }]}>
            <TextSelect label="Country" onChange={handleCountryChange}>
              {countries.map((country: any) => {
                return (
                  <TextSelect.Option key={country.id} value={country.id} param={country.name}>
                    <span dangerouslySetInnerHTML={{ __html: country.name }} />
                  </TextSelect.Option>
                )
              })}
            </TextSelect>
          </Form.Item>
          <Form.Item name="telephone" rules={[{ required: true }]}>
            <TextField label="Phone Number" />
          </Form.Item>
          <Form.Item name="default_billing" valuePropName="checked">
            <Checkbox>
              <FormattedMessage id="account.checkBilling" />
            </Checkbox>
          </Form.Item>
          <Form.Item name="default_shipping" valuePropName="checked">
            <Checkbox>
              <FormattedMessage id="account.checkShipping" />
            </Checkbox>
          </Form.Item>
          <StyledActions>
            <Button type="primary" htmlType="submit" className="addressModal__checkButton">
              <span>Save Address</span>
            </Button>
          </StyledActions>
        </Form>
      </Spin>
    </Modal>
  )
}

export default memo(AddressModal)
