<?php

namespace Silksoftwarecorp\Blog\Model\Config\Source;

use Magento\Cms\Model\ResourceModel\Block\CollectionFactory;

class Block
{
    /**
     * @var array
     */
    private $options;

    /**
     * @var \Magento\Cms\Model\ResourceModel\Block\CollectionFactory
     */
    private $collectionFactory;

    /**
     * @param \Magento\Cms\Model\ResourceModel\Block\CollectionFactory $collectionFactory
     */
    public function __construct(
        CollectionFactory $collectionFactory
    ) {
        $this->collectionFactory = $collectionFactory;
    }

    /**
     * {@inheritdoc}
     */
    public function toOptionArray()
    {
        if (!$this->options) {
            $options = [
                [
                    'value' => '',
                    'label' => __('-- Please Select --')
                ]
            ];

            $options = array_merge($options, $this->toOptionIdArray());

            $this->options = $options;
        }

        return $this->options;
    }

    protected function toOptionIdArray(): array
    {
        $res = [];
        $collection = $this->collectionFactory->create();
        foreach ($collection as $block) {
            $data['value'] = $block->getId();
            $data['label'] = $block->getData('title') . "(ID: {$block->getId()})";

            $res[] = $data;
        }

        return $res;
    }
}
