<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Api/etc/extension_attributes.xsd">
    <extension_attributes for="Magento\Company\Api\Data\CompanyInterface">
        <attribute code="erp_customer_id" type="string">
            <join reference_table="company_erp" reference_field="company_id" join_on_field="entity_id">
                <field>erp_customer_id</field>
            </join>
        </attribute>
        <attribute code="erp_sales_rep_id" type="string">
            <join reference_table="company_erp" reference_field="company_id" join_on_field="entity_id">
                <field>erp_sales_rep_id</field>
            </join>
        </attribute>
        <attribute code="erp_sales_rep_name" type="string">
            <join reference_table="company_erp" reference_field="company_id" join_on_field="entity_id">
                <field>erp_sales_rep_name</field>
            </join>
        </attribute>
        <attribute code="erp_credit_status" type="string">
            <join reference_table="company_erp" reference_field="company_id" join_on_field="entity_id">
                <field>erp_credit_status</field>
            </join>
        </attribute>
    </extension_attributes>
</config>
