import Link from 'next/link'
import { clsx } from 'clsx'

import { useNavigation } from '@/hooks/Navigation'

import { StyledNav, StyledMenuItem } from './styled'

const Navigation = () => {
  const { categories, submenuActive, showSubmenu, closeSubmenu, suffix } = useNavigation()

  return (
    <StyledNav>
      <div className="nav-container" onMouseEnter={showSubmenu} onMouseLeave={closeSubmenu}>
        {categories.map((menu) => {
          const { uid, url_path, name, label_text_color } = menu
          const submenus: any[] = menu?.children ?? []
          const hasSubmenus = submenus.length > 0
          const isAllProductsItem = uid === 'all-products'
          return (
            <StyledMenuItem
              titleColor={label_text_color}
              key={uid}
              className={clsx({
                'has-submenus': hasSubmenus,
                'is-all-products': isAllProductsItem
              })}>
              {submenuActive && (
                <div aria-hidden="true" className="nav-mask" onClick={closeSubmenu} />
              )}
              {url_path ? (
                <Link href={`/${url_path}${suffix}`} title={name}>
                  <span dangerouslySetInnerHTML={{ __html: name }} />
                </Link>
              ) : (
                <div className="simplae-menu-item" dangerouslySetInnerHTML={{ __html: name }} />
              )}
              {hasSubmenus && submenuActive && (
                <div className="submenu">
                  {submenus.map((submenu) => {
                    const isNew = submenu.name === 'NEW Products'
                    const submenuChildren = submenu.children ?? []
                    const hasSubmenuChildren = submenuChildren.length > 0
                    const titleColor = isAllProductsItem
                      ? (submenu?.label_text_color ?? '#191919')
                      : '#191919'
                    return (
                      <div
                        key={submenu.uid}
                        className={clsx('submenu-item', { 'has-child-list': hasSubmenuChildren })}>
                        <Link
                          href={`/${submenu.url_path}${suffix}`}
                          title={submenu.name}
                          className={clsx('submenu-item-title', { 'is-new': isNew })}
                          onClick={closeSubmenu}>
                          <span style={{ color: titleColor }}>{submenu.name}</span>
                          {hasSubmenuChildren && (
                            <>
                              <svg
                                className="menu-arrow"
                                width="1em"
                                height="1em"
                                fill="currentColor"
                                focusable="false">
                                <use xlinkHref="#icon-next" />
                              </svg>
                              <svg
                                className="menu-arrow-light"
                                width="1em"
                                height="1em"
                                fill="currentColor"
                                focusable="false">
                                <use xlinkHref="#icon-next-light" />
                              </svg>
                            </>
                          )}
                        </Link>
                        {hasSubmenuChildren && (
                          <div className="submenu-children">
                            {submenuChildren.map((child) => {
                              return (
                                <div key={child.uid} className="submenu-children-item">
                                  <Link
                                    href={`/${child.url_path}${suffix}`}
                                    title={child.name}
                                    onClick={closeSubmenu}>
                                    <span dangerouslySetInnerHTML={{ __html: child.name }} />
                                  </Link>
                                </div>
                              )
                            })}
                          </div>
                        )}
                      </div>
                    )
                  })}
                </div>
              )}
            </StyledMenuItem>
          )
        })}
      </div>
    </StyledNav>
  )
}

export default Navigation
