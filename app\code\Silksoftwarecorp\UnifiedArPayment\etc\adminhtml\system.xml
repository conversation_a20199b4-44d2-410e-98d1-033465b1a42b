<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="payment" translate="label" type="text" sortOrder="500" showInDefault="1" showInWebsite="1" showInStore="1">
            <group id="unifiedarpayment" translate="label" type="text" sortOrder="999" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Unified A/R Payment</label>
                <field id="active" translate="label" type="select" sortOrder="1" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Unified A/R Payment</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="title" translate="label" type="text" sortOrder="2" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Title</label>
                </field>
                <field id="order_status" translate="label" type="select" sortOrder="90" showInDefault="1" showInWebsite="1" showInStore="0" >
                    <label>New Order Status</label>
                    <source_model>Magento\Sales\Model\Config\Source\Order\Status\Processing</source_model>
                </field>
                <field id="payment_action" translate="label" type="select" sortOrder="110" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Payment Action</label>
                    <source_model>Silksoftwarecorp\UnifiedArPayment\Model\Config\Source\PaymentAction</source_model>
                </field>
                <field id="return_url_title" translate="label" type="text" sortOrder="3" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Return URL Title</label>
                </field>
                <field id="welcome_message" translate="label" type="text" sortOrder="4" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Welcome Message</label>
                </field>
                <field id="company_name" translate="label" type="text" sortOrder="5" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Company Name</label>
                </field>
                <field id="custom_css" translate="label" type="text" sortOrder="5.5" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Custom CSS</label>
                    <comment>Custom CSS to inject into the payment iframe page</comment>
                </field>
                <field id="return_url" translate="label" type="text" sortOrder="7" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Return URL</label>
                    <comment>URL to redirect after payment completion</comment>
                </field>
                <field id="ui_iframe_page_endpoint" translate="label" type="text" sortOrder="8" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>UI Iframe Page Endpoint</label>
                    <comment>Base URL for the iframe payment interface</comment>
                </field>
                <field id="enable_logs" translate="label" type="select" sortOrder="9" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Logs</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="environment" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Environment</label>
                    <source_model>Silksoftwarecorp\UnifiedArPayment\Model\Config\Source\Environment</source_model>
                </field>
                <!-- Sandbox Fields -->
                <field id="sandbox_create_transaction_setup_endpoint" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Sandbox Create Transaction Setup Endpoint</label>
                    <depends><field id="environment">sandbox</field></depends>
                </field>
                <field id="sandbox_payment_account_query_endpoint" translate="label" type="text" sortOrder="21" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Sandbox PaymentAccount Query Endpoint</label>
                    <depends><field id="environment">sandbox</field></depends>
                </field>
                <field id="sandbox_surcharge_query_endpoint" translate="label" type="text" sortOrder="22" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Sandbox Surcharge Query Endpoint</label>
                    <depends><field id="environment">sandbox</field></depends>
                </field>
                <field id="sandbox_account_id" translate="label" type="text" sortOrder="23" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Sandbox AccountID</label>
                    <depends><field id="environment">sandbox</field></depends>
                </field>
                <field id="sandbox_account_token" translate="label" type="text" sortOrder="24" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Sandbox AccountToken</label>
                    <depends><field id="environment">sandbox</field></depends>
                </field>
                <field id="sandbox_acceptor_id" translate="label" type="text" sortOrder="25" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Sandbox AcceptorID</label>
                    <depends><field id="environment">sandbox</field></depends>
                </field>
                <field id="sandbox_application_id" translate="label" type="text" sortOrder="26" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Sandbox ApplicationID</label>
                    <depends><field id="environment">sandbox</field></depends>
                </field>
                <field id="sandbox_application_name" translate="label" type="text" sortOrder="27" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Sandbox ApplicationName</label>
                    <depends><field id="environment">sandbox</field></depends>
                </field>
                <field id="sandbox_application_version" translate="label" type="text" sortOrder="28" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Sandbox ApplicationVersion</label>
                    <depends><field id="environment">sandbox</field></depends>
                </field>
                <!-- Production Fields -->
                <field id="production_create_transaction_setup_endpoint" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Production Create Transaction Setup Endpoint</label>
                    <depends><field id="environment">production</field></depends>
                </field>
                <field id="production_payment_account_query_endpoint" translate="label" type="text" sortOrder="31" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Production PaymentAccount Query Endpoint</label>
                    <depends><field id="environment">production</field></depends>
                </field>
                <field id="production_surcharge_query_endpoint" translate="label" type="text" sortOrder="32" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Production Surcharge Query Endpoint</label>
                    <depends><field id="environment">production</field></depends>
                </field>
                <field id="production_account_id" translate="label" type="text" sortOrder="33" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Production AccountID</label>
                    <depends><field id="environment">production</field></depends>
                </field>
                <field id="production_account_token" translate="label" type="text" sortOrder="34" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Production AccountToken</label>
                    <depends><field id="environment">production</field></depends>
                </field>
                <field id="production_acceptor_id" translate="label" type="text" sortOrder="35" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Production AcceptorID</label>
                    <depends><field id="environment">production</field></depends>
                </field>
                <field id="production_application_id" translate="label" type="text" sortOrder="36" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Production ApplicationID</label>
                    <depends><field id="environment">production</field></depends>
                </field>
                <field id="production_application_name" translate="label" type="text" sortOrder="37" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Production ApplicationName</label>
                    <depends><field id="environment">production</field></depends>
                </field>
                <field id="production_application_version" translate="label" type="text" sortOrder="38" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Production ApplicationVersion</label>
                    <depends><field id="environment">production</field></depends>
                </field>
                <field id="market_code" translate="label" type="select" sortOrder="39" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>MarketCode</label>
                    <comment>A numeric enumeration indicating the market code of the merchant.</comment>
                    <source_model>Silksoftwarecorp\UnifiedArPayment\Model\Config\Source\MarketCode</source_model>
                </field>
            </group>
        </section>
    </system>
</config>
