<?xml version="1.0"?>
<!--
/**
 *
 * @package     Silksoftwarecorp_CompanyB2b
 * @copyright   Copyright © 2021 Silk Software Corp (https://www.silksoftware.com)
 * <AUTHOR> <EMAIL>
 */
-->
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">ship_to_listing.ship_to_listing_data_source</item>
        </item>
    </argument>
    <settings>
        <spinner>shipTo_columns</spinner>
        <deps>
            <dep>ship_to_listing.ship_to_listing_data_source</dep>
        </deps>
    </settings>
    <dataSource name="ship_to_listing_data_source" component="Magento_Ui/js/grid/provider">
        <settings>
            <storageConfig>
                <param name="indexField" xsi:type="string">entity_id</param>
            </storageConfig>
            <updateUrl path="mui/index/render"/>
        </settings>
        <aclResource>Silksoftwarecorp_CompanyB2b::index</aclResource>
        <dataProvider class="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider" name="ship_to_listing_data_source">
            <settings>
                <requestFieldName>id</requestFieldName>
                <primaryFieldName>entity_id</primaryFieldName>
            </settings>
        </dataProvider>
    </dataSource>
    <listingToolbar name="listing_top">
        <settings>
            <sticky>true</sticky>
        </settings>
        <bookmark name="bookmarks"/>
        <columnsControls name="columns_controls"/>
        <filters name="listing_filters"/>
        <massaction name="listing_massaction">
            <action name="delete">
                <settings>
                    <confirm>
                        <message translate="true">Are you sure you want to delete selected items?</message>
                        <title translate="true">Delete items</title>
                    </confirm>
                    <url path="erp/shipto/massDelete"/>
                    <type>delete</type>
                    <label translate="true">Delete</label>
                </settings>
            </action>
        </massaction>
        <paging name="listing_paging"/>
    </listingToolbar>
    <columns name="shipTo_columns">
        <settings>
            <childDefaults>
                <param name="fieldAction" xsi:type="array">
                    <item name="provider" xsi:type="string">ship_to_listing.ship_to_listing.shipTo_columns.actions</item>
                    <item name="target" xsi:type="string">applyAction</item>
                    <item name="params" xsi:type="array">
                        <item name="0" xsi:type="string">view</item>
                        <item name="1" xsi:type="string">${ $.$data.rowIndex }</item>
                    </item>
                </param>
            </childDefaults>
        </settings>
        <selectionsColumn name="ids" sortOrder="0">
            <settings>
                <indexField>entity_id</indexField>
            </settings>
        </selectionsColumn>
        <column name="entity_id" sortOrder="20">
            <settings>
                <filter>textRange</filter>
                <label translate="true">ID</label>
                <sorting>asc</sorting>
            </settings>
        </column>
        <column name="erp_cust_num" sortOrder="30">
            <settings>
                <filter>text</filter>
                <editor>
                    <editorType>text</editorType>
                </editor>
                <label translate="true">Cust Num</label>
            </settings>
        </column>
        <column name="ship_to_num" sortOrder="30">
            <settings>
                <filter>text</filter>
                <editor>
                    <editorType>text</editorType>
                </editor>
                <label translate="true">Ship To Num</label>
            </settings>
        </column>
        <column name="ship_to_name" sortOrder="40">
            <settings>
                <filter>text</filter>
                <editor>
                    <editorType>text</editorType>
                </editor>
                <label translate="true">Ship To Name</label>
            </settings>
        </column>
        <column name="email" sortOrder="60">
            <settings>
                <filter>text</filter>
                <editor>
                    <editorType>text</editorType>
                </editor>
                <label translate="true">Email Address</label>
            </settings>
        </column>
        <column name="region" class="Magento\InventoryAdminUi\Ui\Component\Listing\Column\Region" sortOrder="90">
            <settings>
                <label translate="true">State/Province</label>
                <visible>false</visible>
                <sortable>false</sortable>
            </settings>
        </column>
        <column name="company_id" sortOrder="90">
            <settings>
                <filter>text</filter>
                <label translate="true">Company</label>
                <visible>false</visible>
            </settings>
        </column>
        <column name="location_id" sortOrder="95">
            <settings>
                <filter>text</filter>
                <label translate="true">Location ID</label>
            </settings>
        </column>
        <column name="telephone" sortOrder="100">
            <settings>
                <filter>text</filter>
                <editor>
                    <editorType>text</editorType>
                </editor>
                <label translate="true">Telephone</label>
            </settings>
        </column>
        <column name="country_id" class="Magento\Company\Ui\Component\Listing\Column\Country" sortOrder="110">
            <settings>
                <filter>select</filter>
                <options class="Magento\Directory\Model\ResourceModel\Country\Collection"/>
                <dataType>select</dataType>
                <label translate="true">Country</label>
            </settings>
        </column>
        <column name="postcode" sortOrder="130">
            <settings>
                <filter>text</filter>
                <label translate="true">ZIP</label>
                <visible>false</visible>
            </settings>
        </column>
        <column name="city" sortOrder="140">
            <settings>
                <filter>text</filter>
                <label translate="true">City</label>
                <visible>true</visible>
            </settings>
        </column>

        <column name="street" sortOrder="180">
            <settings>
                <filter>text</filter>
                <label translate="true">Street Address</label>
                <visible>false</visible>
            </settings>
        </column>
        <column name="street_line2" sortOrder="181">
            <settings>
                <filter>text</filter>
                <label translate="true">Street Address Line2</label>
                <visible>false</visible>
            </settings>
        </column>
        <column name="street_line3" sortOrder="182">
            <settings>
                <filter>text</filter>
                <label translate="true">Street Address Line3</label>
                <visible>false</visible>
            </settings>
        </column>
        <column name="created_at" class="Magento\Ui\Component\Listing\Columns\Date" component="Magento_Ui/js/grid/columns/date">
            <settings>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <label translate="true">Created Date</label>
                <sorting>asc</sorting>
            </settings>
        </column>
        <column name="updated_at" class="Magento\Ui\Component\Listing\Columns\Date" component="Magento_Ui/js/grid/columns/date">
            <settings>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <label translate="true">Updated Date</label>
                <sorting>asc</sorting>
            </settings>
        </column>
        <actionsColumn name="actions" class="Silksoftwarecorp\CompanyB2b\Ui\Component\Listing\Column\Actions" sortOrder="230">
            <settings>
                <indexField>entity_id</indexField>
            </settings>
        </actionsColumn>
    </columns>
</listing>
