<?php

namespace Silksoftwarecorp\ERPCompany\Plugin\Company\Model\ResourceModel\Company\Grid;

class CollectionPlugin
{
    /**
     * Add customer_group_code column to companies grid collection before loading.
     *
     * @param \Magento\Company\Model\ResourceModel\Company\Grid\Collection $subject
     * @param bool $printQuery [optional]
     * @param bool $logQuery [optional]
     * @return array
     */
    public function beforeLoad(
        \Magento\Company\Model\ResourceModel\Company\Grid\Collection $subject,
        $printQuery = false,
        $logQuery = false
    ) {
        $subject->getSelect()
            ->joinLeft(
                ['company_erp' => $subject->getTable(
                    'company_erp'
                )],
                'main_table.entity_id = company_erp.company_id',
                ['*']
            );

        return [$printQuery, $logQuery];
    }
}
