import styled from '@emotion/styled'

export const StyledAccountOrderList = styled.div`
  .${({ theme }) => theme.namespace}-table-content {
    & > table {
      & > thead th {
        padding: 0 !important;
      }

      & > tbody tr {
        height: 84px !important;

        td {
          padding: 10px 16px !important;
        }
      }
    }
  }

  &.action-inactive {
    .common-table-component {
      .common-table-sort {
        display: none;
      }
      table thead th {
        p {
          cursor: initial;
        }
        .item-search {
          display: none;
        }
      }
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    .common-table-sort {
      display: none;
    }
  }
`

export const StyledOrderStatus = styled.div`
  span {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    padding: 0 14px;
    height: 29px;
    font-weight: 400;
    font-size: 15px;
    line-height: 21px;
    letter-spacing: 0.01em;
    border-radius: 14px;
  }
`
