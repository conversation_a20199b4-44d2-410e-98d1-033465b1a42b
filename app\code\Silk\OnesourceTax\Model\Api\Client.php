<?php
namespace Silk\OnesourceTax\Model\Api;

use Magento\Framework\HTTP\Client\Curl;
use Magento\Framework\HTTP\Client\CurlFactory;
use Magento\Framework\Serialize\Serializer\Json;
use Silk\OnesourceTax\Model\Config;

class Client
{
    protected $curlFactory;
    protected $config;
    protected $json;
    protected $client;

    public function __construct(
        CurlFactory $curlFactory,
        Config $config,
        Json $json
    ) {
        $this->curlFactory = $curlFactory;
        $this->config = $config;
        $this->json = $json;
    }

    protected function getClient()
    {
        if (!$this->client) {
            $this->client = $this->curlFactory->create();
            $this->client->setOption(CURLOPT_RETURNTRANSFER, true);
            $this->client->setOption(CURLOPT_TIMEOUT, 30);
        }
        return $this->client;
    }

    public function post($url, $data, $headers = [])
    {
        $client = $this->getClient();
        $client->setHeaders($headers);
        $client->post($url, $data);
        return $this->json->unserialize($client->getBody());
    }
}