input UnifiedArAddressInput {
    billing_address1: String!
    billing_address2: String
    billing_city: String!
    billing_state: String!
    billing_zipcode: String!
}

type PaymentAccountItem {
    payment_account_id: String
    payment_account_type: String
    truncated_card_number: String
    expiration_month: String
    expiration_year: String
    payment_account_reference_number: String
    transaction_setup_id: String
    payment_brand: String
    pass_updater_batch_status: String
    pass_updater_status: String
    token_provider_id: String
}

type CreateTransactionSetupOutput {
    success: Boolean!
    message: String
    transaction_setup_url: String
    iframe_url: String
    error_code: String
}

type PaymentAccountQueryOutput {
    success: Boolean!
    message: String
    items: [PaymentAccountItem]
    error_code: String
}

type SurchargeQueryOutput {
    success: Boolean!
    message: String
    surcharge_allowed: Boolean
    surcharge_amount: Float
    surcharge_percent: Float
    error_code: String
}

extend type CustomerOrder {
    unified_ar_surcharge_amount: Float @resolver(class: "Silksoftwarecorp\\UnifiedArPaymentGraphQl\\Model\\Resolver\\OrderSurchargeAmount")
}

type Mutation {
    createTransactionSetup(
        address: UnifiedArAddressInput!
    ): CreateTransactionSetupOutput @resolver(class: "Silksoftwarecorp\\UnifiedArPaymentGraphQl\\Model\\Resolver\\CreateTransactionSetup")

    paymentAccountQuery(
        transaction_setup_id: String!
    ): PaymentAccountQueryOutput @resolver(class: "Silksoftwarecorp\\UnifiedArPaymentGraphQl\\Model\\Resolver\\PaymentAccountQuery")

    surchargeQuery(
        payment_account_id: String!
        billing_state: String!
        amount: Float!
        country: String!
    ): SurchargeQueryOutput @resolver(class: "Silksoftwarecorp\\UnifiedArPaymentGraphQl\\Model\\Resolver\\SurchargeQuery")
}

extend input SetPaymentMethodAndPlaceOrderInput {
    unified_ar_return_data: String
    surcharge_amount: Float
}
