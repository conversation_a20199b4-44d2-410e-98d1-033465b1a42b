type Query {
    customerInvoices(company_id: String!): [CustomerInvoice] @resolver(class: "Silksoftwarecorp\\UnifiedArGraphQl\\Model\\Resolver\\CustomerInvoices") @cache(cacheable: false)
    financialInformation(company_id: String!): FinancialInformation @resolver(class: "Silksoftwarecorp\\UnifiedArGraphQl\\Model\\Resolver\\FinancialInformation") @cache(cacheable: false)
    payNowLink(company_id: String!, invoice_numbers: [String]!): PayNowLink @resolver(class: "Silksoftwarecorp\\UnifiedArGraphQl\\Model\\Resolver\\PayNowLink") @cache(cacheable: false)
}

type CustomerInvoice {
    invoice_number: String
    order_number: String
    order_date: String
    total_amount: Float
    po_number: String
    paid_in_full: Boolean
    pdf_url: String
    html_url: String
}

type FinancialInformation {
    merchantCustomerNbr: String
    customerId: String
    customerName: String
    paymentTermsCode: String
    paymentTermsDescription: String
    creditLimit: Float
    availableCredit: Float
    balance: Float
    pastDueBalance: Float
    pastDueInvoiceCount: Float
    totalOpenInvoiceCount: Float
    lastPaymentAmount: Float
    lastPaymentDate: String
    lastPaymentDetail: String
    isOnCreditHold: Boolean
    salespersonCode: String
    salespersonName: String
    isTaxExempt: Boolean
    sicCode: String
    isSurchargeExempt: String
    creditLimitPerOrder: Float
    creditLimitUsed: Float
    creditStatusCode: String
    creditStatusDescription: String
    averageInvoiceAgeInDays: Float
}

type PayNowLink {
    pay_now_url: String
    success: Boolean
}
