<?php

namespace Silksoftwarecorp\UnifiedAR\Api\Data;

/**
 * Generic Data Mapper Interface
 */
interface MapperInterface
{
    /**
     * Transform raw data to structured format
     * 
     * @param array $data Raw data from API
     * @param array $options Additional transformation options
     * @return array Transformed data
     */
    public function map(array $data, array $options = []): array;

    /**
     * Transform multiple items
     * 
     * @param array $items Array of raw data items
     * @param array $options Additional transformation options
     * @return array Array of transformed items
     */
    public function mapCollection(array $items, array $options = []): array;

    /**
     * Validate data before mapping
     * 
     * @param array $data Raw data to validate
     * @return bool
     */
    public function validate(array $data): bool;

    /**
     * Get mapping configuration
     * 
     * @return array
     */
    public function getMappingConfig(): array;
} 