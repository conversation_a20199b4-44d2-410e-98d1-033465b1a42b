import { gql } from '@apollo/client'

export const GET_CUSTOMER_ORDERS = gql`
  query getCustomerOrders(
    $filter: CustomerOrdersFilterInput
    $currentPage: Int
    $pageSize: Int
    $sort: CustomerOrderSortInput
  ) {
    customer {
      orders(filter: $filter, currentPage: $currentPage, pageSize: $pageSize, sort: $sort) {
        items {
          key: id
          number
          status
          shipping_address {
            firstname
            lastname
          }
          total {
            grand_total {
              value
            }
          }
          order_date
        }
        page_info {
          current_page
          page_size
          total_pages
        }
        total_count
      }
    }
  }
`
