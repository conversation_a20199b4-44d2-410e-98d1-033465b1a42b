<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */

declare(strict_types=1);

namespace Silksoftwarecorp\Inventory\Model\Source;

use Magento\Framework\Api\ExtensionAttributesFactory;
use Magento\Framework\DataObject;
use Magento\InventoryApi\Api\Data\SourceInterface;

/**
 * Set custom source extension attributes
 */
class InitSourceExtensionAttributes
{
    /**
     * @var ExtensionAttributesFactory
     */
    private $extensionAttributesFactory;

    /**
     * @param ExtensionAttributesFactory $extensionAttributesFactory
     */
    public function __construct(
        ExtensionAttributesFactory $extensionAttributesFactory
    ) {
        $this->extensionAttributesFactory = $extensionAttributesFactory;
    }

    /**
     * Set custom source extension attributes.
     *
     * @param SourceInterface $source
     */
    public function execute(SourceInterface $source): void
    {
        if (!$source instanceof DataObject) {
            return;
        }

        $installRequestForm = $source->getData('install_request_form');
        $installApp = $source->getData('install_app');
        $textNumber = $source->getData('text_number');
        $street1 = $source->getData('street1');
        $schedule = $source->getData('schedule');
        $holidayClosures = $source->getData('holiday_closures');

        $extensionAttributes = $source->getExtensionAttributes();

        /**
         * @var \Magento\InventoryApi\Api\Data\SourceExtensionInterface $extensionAttributes
         */
        if ($extensionAttributes === null) {
            $extensionAttributes = $this->extensionAttributesFactory->create(SourceInterface::class);
            /** @noinspection PhpParamsInspection */
            $source->setExtensionAttributes($extensionAttributes);
        }

        $extensionAttributes
            ->setInstallRequestForm((bool)$installRequestForm)
            ->setInstallApp((bool)$installApp)
            ->setTextNumber($textNumber)
            ->setStreet1($street1)
            ->setSchedule($schedule)
            ->setHolidayClosures($holidayClosures);
    }
}
