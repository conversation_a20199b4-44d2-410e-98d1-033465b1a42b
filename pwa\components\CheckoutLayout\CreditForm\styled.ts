import styled from '@emotion/styled'

export const StyledCreditForm = styled.div`
  h3 {
    margin-top: 24px;
    font-weight: 400;
    font-size: 15px;
    line-height: 21px;
    letter-spacing: 0.01em;
  }

  .${({ theme }) => theme.namespace}-form-item {
    margin-bottom: 0;

    input {
      height: 44px;
      border-radius: 3px;
      border: 1px solid #d9d9d9 !important;

      &.card-number-input {
        max-width: 346px;
      }

      &.small-input {
        width: 73px;
      }

      &::placeholder {
        font-weight: 400;
        font-size: 15px;
        line-height: 25px;
        letter-spacing: 0.02em;
        color: #777;
      }
    }
  }

  .date-group {
    display: flex;

    .${({ theme }) => theme.namespace}-form-item {
      .${({ theme }) => theme.namespace}-select {
        height: 44px;
        width: 73px;

        &-selector {
          height: 44px;
          width: 73px;
          padding: 0 11px !important;
          border-radius: 4px !important;

          input {
            height: 44px !important;
          }
        }

        &-selection-item {
          padding-inline-end: 0;
        }

        &-selection-placeholder {
          font-weight: 400;
          font-size: 15px;
          line-height: 25px;
          letter-spacing: 0.02em;
          color: #777;
        }

        &-arrow {
          display: none;
        }
      }
    }

    .date-separator {
      margin: 10px 12px 0;
      // TODO: font
      //font-family: Reddit Sans Condensed;
      font-weight: 400;
      font-size: 16px;
      line-height: 26px;
      letter-spacing: 0.02em;
      color: #354347;
    }
  }
`
