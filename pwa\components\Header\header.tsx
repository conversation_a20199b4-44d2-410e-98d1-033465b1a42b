import { memo, useState, useCallback, useEffect } from 'react'
import dynamic from 'next/dynamic'
import { useSelector } from 'react-redux'
import { useRouter } from 'next/compat/router'
import { LineContainer } from '@ranger-theme/ui'
import { Affix } from 'antd'
import { clsx } from 'clsx'

import { MediaLayout } from '@/ui'
import MiniCart from '@/components/MiniCart'
import Navigation from '@/components/Navigation'
import MiniSearch from '@/components/MiniSearch'
import MiniAccount from '@/components/MiniAccount'
import NavigationMobile from '@/components/NavigationMobile'

import HeaderPanel from './HeaderPanel'
import FinStorePencilBanner from './FinStorePencilBanner'
import { StyledHeader, StyledSearchBar, StyledTools } from './styled'

const Logo = dynamic(() => import('@/components/Logo'), { ssr: false })

const Header = () => {
  const router = useRouter()
  const isCheckout: boolean = router?.route === '/checkout'
  const isLogin = useSelector((state: Store) => state.user.isLogin)
  const headerPanelVisible = !isCheckout

  const [isCollapse, setIsCollapse] = useState(false)

  // const handleWheel = useCallback((event) => {
  //   if (event.deltaY > 0) {
  //     setIsCollapse(true)
  //   } else if (event.deltaY < 0) {
  //     setIsCollapse(false)
  //   }
  // }, [])
  //
  // useEffect(() => {
  //   window.addEventListener('wheel', handleWheel)
  //   return () => {
  //     window.removeEventListener('wheel', handleWheel)
  //   }
  // }, [handleWheel])

  return (
    <Affix offsetTop={0}>
      <StyledHeader>
        <div className={`${headerPanelVisible ? '' : 'panel-hide'}`}>
          {isLogin ? <HeaderPanel /> : <FinStorePencilBanner />}
        </div>
        <StyledSearchBar className={clsx({ 'is-collapse': isCollapse })}>
          <div className="collapse-wrapper">
            <LineContainer>
              {!isCollapse && <Logo />}
              {!isCheckout && (
                <>
                  {!isCollapse && (
                    <MediaLayout>
                      <MiniSearch />
                    </MediaLayout>
                  )}
                  <StyledTools>
                    <MiniAccount />
                    <MiniCart />
                    <MediaLayout type="mobile">
                      <NavigationMobile />
                    </MediaLayout>
                  </StyledTools>
                </>
              )}
            </LineContainer>
            {!isCheckout && (
              <MediaLayout>
                <Navigation />
              </MediaLayout>
            )}
            {!isCheckout && (
              <MediaLayout type="mobile">
                <MiniSearch />
              </MediaLayout>
            )}
          </div>
        </StyledSearchBar>
      </StyledHeader>
    </Affix>
  )
}

export default memo(Header)
