import { gql } from '@apollo/client'
import type { DocumentNode } from '@apollo/client'

export const B2B_GET_SHIP_TO_LIST_ALL: DocumentNode = gql`
  query getShipToListAll($company_id: String!) {
    getShipToListAll(company_id: $company_id) {
      company_id
      items {
        city
        company_id
        country_id
        email
        entity_id
        erp_cust_num
        location_id
        postcode
        region_code
        ship_to_name
        ship_to_num
        street
        street_line2
        street_line3
        telephone
        is_default
      }
    }
  }
`
