<?php

namespace Silksoftwarecorp\UnifiedArPayment\Model;

use Magento\Framework\HTTP\Client\Curl;
use Silksoftwarecorp\UnifiedArPayment\Helper\Config;
use Silksoftwarecorp\UnifiedArPayment\Helper\Logger;

class PaymentAccountQuery
{
    /**
     * @var Curl
     */
    private $curl;

    /**
     * @var Config
     */
    private $configHelper;

    /**
     * @var Logger
     */
    private $logger;

    /**
     * @param Curl $curl
     * @param Config $configHelper
     * @param Logger $logger
     */
    public function __construct(
        Curl $curl,
        Config $configHelper,
        Logger $logger
    ) {
        $this->curl = $curl;
        $this->configHelper = $configHelper;
        $this->logger = $logger;
    }

    /**
     * Query payment account
     *
     * @param string $transactionSetupId
     * @return array
     */
    public function queryPaymentAccount($transactionSetupId)
    {
        try {
            // Get configuration
            $envConfig = $this->configHelper->getEnvironmentConfig();

            // Build XML request
            $xml = $this->buildXmlRequest($envConfig, $transactionSetupId);

            // Make API call
            $response = $this->makeApiCall($envConfig['payment_account_query_endpoint'], $xml, 'PaymentAccountQuery');

            return $response;

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'items' => [],
                'error_code' => 'EXCEPTION'
            ];
        }
    }

    /**
     * Build XML request
     *
     * @param array $envConfig
     * @param string $transactionSetupId
     * @return string
     */
    private function buildXmlRequest($envConfig, $transactionSetupId)
    {
        $xml = '<PaymentAccountQuery xmlns="https://services.elementexpress.com">';
        $xml .= '<Credentials>';
        $xml .= '<AccountID>' . htmlspecialchars($envConfig['account_id']) . '</AccountID>';
        $xml .= '<AccountToken>' . htmlspecialchars($envConfig['account_token']) . '</AccountToken>';
        $xml .= '<AcceptorID>' . htmlspecialchars($envConfig['acceptor_id']) . '</AcceptorID>';
        $xml .= '</Credentials>';
        $xml .= '<Application>';
        $xml .= '<ApplicationID>' . htmlspecialchars($envConfig['application_id']) . '</ApplicationID>';
        $xml .= '<ApplicationName>' . htmlspecialchars($envConfig['application_name']) . '</ApplicationName>';
        $xml .= '<ApplicationVersion>' . htmlspecialchars($envConfig['application_version']) . '</ApplicationVersion>';
        $xml .= '</Application>';
        $xml .= '<PaymentAccountParameters>';
        $xml .= '<TransactionSetupID>' . htmlspecialchars($transactionSetupId) . '</TransactionSetupID>';
        $xml .= '</PaymentAccountParameters>';
        $xml .= '</PaymentAccountQuery>';
        return $xml;
    }

    /**
     * Make API call
     *
     * @param string $endpoint
     * @param string $xml
     * @param string $operation
     * @return array
     */
    private function makeApiCall($endpoint, $xml, $operation = 'API Call')
    {
        // Log request
        $this->logger->logRequest($endpoint, $xml, $operation);

        $this->curl->setOption(CURLOPT_URL, $endpoint);
        $this->curl->setOption(CURLOPT_POST, true);
        $this->curl->setOption(CURLOPT_POSTFIELDS, $xml);
        $this->curl->setOption(CURLOPT_HTTPHEADER, [
            'Content-Type: text/xml'
        ]);
        $this->curl->setOption(CURLOPT_RETURNTRANSFER, true);
        $this->curl->setOption(CURLOPT_TIMEOUT, 30);

        $this->curl->post($endpoint, $xml);
        
        $response = $this->curl->getBody();
        $httpCode = $this->curl->getStatus();

        // Log response
        $this->logger->logResponse($response, $httpCode, $operation);

        // Parse XML response
        $xmlResponse = simplexml_load_string($response);
        
        if ($xmlResponse === false) {
            $this->logger->logError('Invalid XML response from API', ['response' => $response], $operation);
            return [
                'success' => false,
                'message' => 'Invalid XML response from API',
                'items' => [],
                'error_code' => 'INVALID_RESPONSE'
            ];
        }

        // Check for errors in response
        if (isset($xmlResponse->Response)) {
            $responseCode = (string)$xmlResponse->Response->ExpressResponseCode;
            $responseMessage = (string)$xmlResponse->Response->ExpressResponseMessage;
            
            if ($responseCode !== '0') {
                $this->logger->logError('API Error: ' . $responseMessage, [
                    'response_code' => $responseCode,
                    'response_message' => $responseMessage
                ], $operation);
                return [
                    'success' => false,
                    'message' => $responseMessage,
                    'items' => [],
                    'error_code' => $responseCode
                ];
            }
        }

        // Extract items from response
        $items = [];
        if (isset($xmlResponse->QueryData->Items->Item)) {
            foreach ($xmlResponse->QueryData->Items->Item as $item) {
                $items[] = [
                    'payment_account_id' => (string)$item->PaymentAccountID,
                    'payment_account_type' => (string)$item->PaymentAccountType,
                    'truncated_card_number' => (string)$item->TruncatedCardNumber,
                    'expiration_month' => (string)$item->ExpirationMonth,
                    'expiration_year' => (string)$item->ExpirationYear,
                    'payment_account_reference_number' => (string)$item->PaymentAccountReferenceNumber,
                    'transaction_setup_id' => (string)$item->TransactionSetupID,
                    'payment_brand' => (string)$item->PaymentBrand,
                    'pass_updater_batch_status' => (string)$item->PASSUpdaterBatchStatus,
                    'pass_updater_status' => (string)$item->PASSUpdaterStatus,
                    'token_provider_id' => (string)$item->TokenProviderID,
                ];
            }
        }

        return [
            'success' => true,
            'message' => 'Payment account query successful',
            'items' => $items
        ];
    }
} 