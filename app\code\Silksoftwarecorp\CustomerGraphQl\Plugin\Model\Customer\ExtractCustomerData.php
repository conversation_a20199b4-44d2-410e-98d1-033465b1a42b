<?php
namespace Silksoftwarecorp\CustomerGraphQl\Plugin\Model\Customer;

use Magento\Customer\Api\Data\CustomerInterface;
use Magento\CustomerGraphQl\Model\Customer\ExtractCustomerData as Subject;

/**
 * Class ExtractCustomerData
 */
class ExtractCustomerData
{
    public function afterExecute(
        Subject $subject,
        array $customerData,
        CustomerInterface $customer
    ): array {
        if (empty($customerData['id'])) {
            $customerData['id'] = $customer->getId();
        }

        return $customerData;
    }
}
