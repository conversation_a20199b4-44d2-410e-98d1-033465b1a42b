import { isEmpty } from 'lodash-es'
import { FormattedMessage } from 'react-intl'
import dayjs from 'dayjs'
import { Collapse } from 'antd'
import { useCallback } from 'react'
import { clsx } from 'clsx'

import { MediaLayout } from '@/ui'
import { useB2bOrderDetails } from '@/hooks/AccountOrderList'
import BasePrice from '@/components/BasePrice'
import CommonAccountPageLayout from '@/components/Common/CommonAccountPageLayout'
import CommonButton from '@/components/Common/CommonButton'
import CommonTable from '@/components/Common/CommonTable'
import CommonLink from '@/components/Common/CommonLink'
import CommonLoading from '@/components/Common/CommonLoading'
import ProductTools from '@/components/CatalogLayout/ProductTools'
import { orderStatusOptions } from '@/utils/constant'

import {
  StyledTotalBox,
  StyledOrderDetaildTable,
  StyledOrderDate,
  StyledDetailsWrapper,
  StyledAddressPayment,
  StyledOrderDetailsPrint,
  StyledOrderPanel
} from './styled'

const B2bOrderDetails = ({ number }) => {
  const {
    items,
    total,
    isLoading,
    orderDetails,
    paymentMethod,
    poNumber,
    billingAddress,
    shippingMethod,
    shippingAddress,
    suffix,
    handleReorder,
    isMobile,
    taxValue,
    shippingFreight,
    orderTitle,
    detailsCollapseActiveKey,
    onDetailsCollapseChange,
    contentRef,
    handlePrint,
    subtotal,
    orderProductsItems
  } = useB2bOrderDetails(number)

  const columns = [
    {
      title: <FormattedMessage id="global.productName" />,
      dataIndex: 'product_name',
      key: 'product_name'
    },
    {
      title: 'Item ID',
      dataIndex: 'product_sku',
      key: 'product_sku',
      width: 200,
      render: (param) => {
        return (
          <CommonLink underline href={`/${param}${suffix}`}>
            {param}
          </CommonLink>
        )
      }
    },
    {
      title: <FormattedMessage id="global.price" />,
      dataIndex: 'product_sale_price',
      key: 'product_sale_price',
      width: 160,
      render: (param) => <BasePrice value={param.value} unit />
    },
    {
      title: 'Quantity',
      dataIndex: 'quantity_ordered',
      key: 'quantity_ordered',
      width: 120,
      align: 'center'
    },
    {
      title: <FormattedMessage id="global.subtotal" />,
      dataIndex: 'product_sale_price',
      key: 'subtotal',
      align: 'right',
      width: 160,
      render: (param, record) => <BasePrice value={param.value * record.quantity_ordered} />
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      align: 'center',
      render: (param) => {
        const statusItem = orderStatusOptions.find((item) => item.value === param)
        return statusItem?.label ?? ''
      }
    }
  ]

  const expandIconRender = useCallback(
    (panelProps) => (
      <svg
        className={clsx('order-collapse-icon', { 'is-active': panelProps.isActive })}
        width="18px"
        height="11px"
        fill="currentColor"
        focusable="false">
        <use xlinkHref="#icon-collapse-icon" />
      </svg>
    ),
    []
  )

  return (
    <CommonLoading spinning={isLoading}>
      {!isEmpty(orderDetails) && (
        <CommonAccountPageLayout
          title={orderTitle}
          breadcrumbItems={[
            { url: '/account', name: 'Account' },
            { url: '/account/orders', name: 'Order History' },
            { name: orderTitle }
          ]}
          breadcrumbProps={
            isMobile ? { items: [], rootName: 'Order History', rootUrl: '/account/orders' } : {}
          }>
          <StyledOrderDetailsPrint ref={contentRef}>
            <div className="print__order-title">{orderTitle}</div>
            {orderDetails?.order_date && (
              <StyledOrderDate>
                Order Date: {dayjs(orderDetails.order_date).format('MM/DD/YYYY')}
              </StyledOrderDate>
            )}
            <StyledOrderPanel className="order-panel">
              <div className="order-panel-action">
                <CommonButton height={44} onClick={handleReorder} className="print__reorder-btn">
                  Reorder
                </CommonButton>
                <div>
                  <ProductTools isSimple productItems={orderProductsItems} />
                </div>
              </div>
              <div className="print-box">
                <CommonButton type="text" fontSize={15} uppercase={false} onClick={handlePrint}>
                  <svg
                    className="print-icon"
                    width="24px"
                    height="24px"
                    fill="currentColor"
                    aria-hidden="true"
                    focusable="false">
                    <use xlinkHref="#icon-print-page" />
                  </svg>
                  Save as PDF
                </CommonButton>
              </div>
            </StyledOrderPanel>
            <StyledDetailsWrapper className="print__details-wrapper">
              <Collapse
                ghost
                expandIconPosition="end"
                activeKey={detailsCollapseActiveKey}
                onChange={onDetailsCollapseChange}
                expandIcon={expandIconRender}
                items={[
                  {
                    key: 'address-payment',
                    label: <StyledOrderDetaildTable>Address & Payment</StyledOrderDetaildTable>,
                    children: (
                      <StyledAddressPayment className="print__address-payment">
                        <div>
                          <h3>Billing Address</h3>
                          <p>{billingAddress?.name ?? ''}</p>
                          <p>
                            {billingAddress?.street?.map((add, index) => {
                              return add ? `${index > 0 ? ', ' : ''}${add}` : ''
                            })}
                          </p>
                          <p>
                            {billingAddress?.city ?? ''}
                            {billingAddress?.state ? `, ${billingAddress?.state}` : ''}
                            {billingAddress?.zip_code ? `, ${billingAddress?.zip_code}` : ''}
                          </p>
                          <p>{billingAddress?.country ?? ''}</p>
                          <p>{billingAddress?.phone ?? ''}</p>
                        </div>
                        <div>
                          <h3>Fulfillment Method</h3>
                          <p>
                            <b>{shippingMethod.title}</b>
                          </p>
                          <p>{shippingMethod.description}</p>
                        </div>
                        <div>
                          <h3>Fulfillment Location</h3>
                          <p>{shippingAddress?.name ?? ''}</p>
                          <p>
                            {shippingAddress?.street?.map((add, index) => {
                              return add ? `${index > 0 ? ', ' : ''}${add}` : ''
                            })}
                          </p>
                          <p>
                            {shippingAddress?.city ?? ''}
                            {shippingAddress?.state ? `, ${shippingAddress?.state}` : ''}
                            {shippingAddress?.zip_code ? `, ${shippingAddress?.zip_code}` : ''}
                          </p>
                          <p>{shippingAddress?.country ?? ''}</p>
                          <p>{shippingAddress?.phone ?? ''}</p>
                        </div>
                        <div>
                          <h3>Payment Method</h3>
                          <p>{paymentMethod}</p>
                          {poNumber && <p>{`PO#: ${poNumber}`}</p>}
                        </div>
                      </StyledAddressPayment>
                    )
                  },
                  {
                    key: 'table',
                    label: <StyledOrderDetaildTable>Items Ordered</StyledOrderDetaildTable>,
                    children: (
                      <div>
                        <MediaLayout>
                          <StyledOrderDetaildTable>Items Ordered</StyledOrderDetaildTable>
                        </MediaLayout>
                        <CommonTable
                          lineStyle
                          rowKey={(record) => record.item_id}
                          columns={columns}
                          dataSource={items}
                          pagination={false}
                        />
                      </div>
                    )
                  }
                ]}
              />
            </StyledDetailsWrapper>

            <StyledTotalBox className="print__total">
              <ul>
                <li>
                  <div>Subtotal</div>
                  <BasePrice value={subtotal} />
                </li>
                <li>
                  <div>Tax</div>
                  <BasePrice value={taxValue} />
                </li>
                {/*<li>
                  <div>Delivery Charge</div>
                  <BasePrice value={shippingFreight} />
                </li>*/}
                <li className="order-total">
                  <div>Order Total</div>
                  <BasePrice value={total} />
                </li>
              </ul>
            </StyledTotalBox>
          </StyledOrderDetailsPrint>
        </CommonAccountPageLayout>
      )}
    </CommonLoading>
  )
}

export default B2bOrderDetails
