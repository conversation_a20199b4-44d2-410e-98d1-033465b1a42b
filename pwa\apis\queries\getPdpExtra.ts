import { gql } from '@apollo/client'
import type { DocumentNode } from '@apollo/client'

export const GET_PRODUCT_EXTRA: DocumentNode = gql`
  query getPdpExtra($filter: ProductAttributeFilterInput) {
    products(filter: $filter) {
      items {
        main_description: description {
          html
        }
        media_gallery {
          disabled
          label
          position
          url
          ... on ProductVideo {
            video_content {
              media_type
              video_provider
              video_url
              video_title
              video_description
              video_metadata
            }
          }
          __typename
        }
        videos
        specifications
        resources
      }
    }
  }
`
