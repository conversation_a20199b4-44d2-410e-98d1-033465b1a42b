<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */

namespace Silksoftwarecorp\AkeneoConnector\Plugin\Helper\Import;

use <PERSON>keneo\Connector\Helper\Import\Entities;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Eav\Api\Data\AttributeInterface;
use Magento\Eav\Model\Config as EavConfig;
use Magento\Eav\Model\Entity\Attribute\AbstractAttribute;
use Magento\Framework\DB\Adapter\AdapterInterface;
use Magento\Framework\DB\Select;
use Magento\Framework\Exception\LocalizedException;
use Psr\Log\LoggerInterface;
use Zend_Db_Expr as Expr;
use Zend_Db_Select_Exception;
use Silksoftwarecorp\AkeneoConnector\Helper\Data as ConfigHelper;

class EntitiesPlugin
{
    /**
     * @var ConfigHelper
     */
    private $configHelper;

    /**
     * This variable contains an EavAttribute
     *
     * @var  EavConfig $eavConfig
     */
    protected $eavAttribute;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @param ConfigHelper $configHelper
     * @param EavConfig $eavConfig
     * @param LoggerInterface $logger
     */
    public function __construct(
        ConfigHelper $configHelper,
        EavConfig $eavConfig,
        LoggerInterface $logger
    ) {
        $this->configHelper = $configHelper;
        $this->eavAttribute = $eavConfig;
        $this->logger = $logger;
    }

    /**
     * Filter empty product names from values before setting them
     *
     * @param Entities $subject
     * @param string $import
     * @param string $entityTable
     * @param array $values
     * @return array|null
     * @throws LocalizedException
     * @throws \Zend_Db_Statement_Exception
     */
    public function beforeSetValues(
        Entities $subject,
        string $import,
        string $entityTable,
        array $values
    ): ?array {
        // Only process product imports
        if ($import !== 'product' || $entityTable !== 'catalog_product_entity') {
            return null;
        }

        $this->logger->debug(__('$import: %1', $import));
        $this->logger->debug(__('$entityTable: %1', $entityTable));
        $this->logger->debug(__('$values: %1', json_encode($values)));
        $this->logger->debug(__('shouldClearProductNameWhenMissing: %1', $this->configHelper->shouldClearProductNameWhenMissing()));

        // If configuration is set to clear product name when missing (Yes), don't filter
        if (!$this->configHelper->shouldClearProductNameWhenMissing()) {
            $this->useOriginalProductName($subject, $import, $values);
        }

        return null;
    }

    public function aroundSetValues(
        Entities $subject,
        \Closure $proceed,
        string $import,
        string $entityTable,
        array $values,
        $entityTypeId,
        $storeId,
        $mode = AdapterInterface::INSERT_ON_DUPLICATE
    ) {
        if ($import !== 'product' || $entityTable !== 'catalog_product_entity') {
            return $proceed($import, $entityTable, $values, $entityTypeId, $storeId, $mode);
        }

        if (!$this->configHelper->shouldClearProductNameWhenMissing()) {
            /** @var AdapterInterface $connection */
            $connection = $subject->getConnection();
            /** @var string $tableName */
            $tableName = $subject->getTableName($import);

            $hasProcessed = false;

            /**
             * @var string $code
             * @var string $value
             */
            foreach ($values as $code => $value) {
                if ($code === ProductInterface::NAME) {
                    /** @var array|bool $attribute */
                    $attribute = $subject->getAttribute($code, $entityTypeId);
                    if (empty($attribute)) {
                        continue;
                    }

                    if (!isset($attribute[AttributeInterface::BACKEND_TYPE])) {
                        continue;
                    }

                    if ($attribute[AttributeInterface::BACKEND_TYPE] === 'static') {
                        continue;
                    }

                    if (!$connection->tableColumnExists($tableName, $value)) {
                        continue;
                    }

                    /** @var string $backendType */
                    $backendType = $attribute[AttributeInterface::BACKEND_TYPE];
                    /** @var string $table */
                    $table = $subject->getTable($entityTable . '_' . $backendType);
                    /** @var string $identifier */
                    $identifier = $subject->getColumnIdentifier($table);
                    /** @var bool $rowIdExists */
                    $rowIdExists = $subject->rowIdColumnExists($table);
                    if ($rowIdExists) {
                        /** @var Select $select */
                        $select = $connection->select()->from(
                            $tableName,
                            [
                                'attribute_id' => new Expr($attribute[AttributeInterface::ATTRIBUTE_ID]),
                                'store_id'     => new Expr($storeId),
                                'value'        => $value,
                            ]
                        );
                        $subject->addJoinForContentStaging($select, [$identifier => 'row_id']);

                        $select->where("`{$value}` is not null");
                    } else {
                        /** @var Select $select */
                        $select = $connection->select()->from(
                            $tableName,
                            [
                                'attribute_id' => new Expr($attribute[AttributeInterface::ATTRIBUTE_ID]),
                                'store_id'     => new Expr($storeId),
                                'value'        => $value,
                                $identifier    => '_entity_id',
                            ]
                        )->where("`{$value}` is not null");
                    }

                    /** @var string $insert */
                    $insert = $connection->insertFromSelect(
                        $select,
                        $subject->getTable($entityTable . '_' . $backendType),
                        ['attribute_id', 'store_id', 'value', $identifier],
                        $mode
                    );

                    $connection->query($insert);

                    $hasProcessed = true;
                    break;
                }
            }

            $this->logger->debug(__('shouldClearProductNameWhenMissing: %1', $this->configHelper->shouldClearProductNameWhenMissing()));
            if ($hasProcessed) {
                unset($values[ProductInterface::NAME]);
                $this->logger->debug(__('Ignore empty product name.'));
            }
        }

        return $proceed($import, $entityTable, $values, $entityTypeId, $storeId, $mode);
    }

    /**
     * @param Entities $subject
     * @param string $import
     * @param array $values
     * @return void
     * @throws LocalizedException
     * @throws \Zend_Db_Statement_Exception
     */
    protected function useOriginalProductName(Entities $subject, string $import, array $values): void
    {
        foreach ($values as $code => $value) {
            if ($code === ProductInterface::NAME) {
                $connection = $subject->getConnection();
                $tmpTable = $subject->getTableName($import);
                if (!$connection->tableColumnExists($tmpTable, $value)) {
                    continue;
                }

                /** @var AbstractAttribute $nameAttribute */
                $nameAttribute = $this->eavAttribute->getAttribute('catalog_product', ProductInterface::NAME);
                $identifierColumn = $subject->getColumnIdentifier(
                    $subject->getTable('catalog_product_entity_' . $nameAttribute->getBackendType())
                );
                $productTable = $subject->getTable('catalog_product_entity');

                /** @var string[] $pKeyColumn */
                $pKeyColumn = 'a._entity_id';
                /** @var string[] $columnsForName */
                $columnsForName = ['entity_id' => $pKeyColumn, '_entity_id', '_is_new' => 'a._is_new'];

                /** @var bool $rowIdExists */
                $rowIdExists = $subject->rowIdColumnExists($productTable);
                if ($rowIdExists) {
                    $pKeyColumn = 'p.row_id';
                    $columnsForName['entity_id'] = $pKeyColumn;
                }

                /** @var Select $select */
                $select = $connection->select()->from(['a' => $tmpTable], $columnsForName);
                if ($rowIdExists) {
                    $subject->addJoinForContentStaging($select, []);
                }

                $select->joinInner(
                    ['b' => $subject->getTable('catalog_product_entity_' . $nameAttribute->getBackendType())],
                    $pKeyColumn . ' = b.' . $identifierColumn
                )
                    ->where('a._is_new = ?', 0)
                    ->where("a.`{$value}` is null or a.`{$value}` = ?", '')
                    ->where('b.attribute_id = ?', $nameAttribute->getId())
                    ->where('b.store_id = ?', 0);

                $this->logger->debug(__('Insert original product name to template table.'));

                /** @var Zend_Db_Statement_Pdo $oldStatus */
                $oldName = $connection->query($select);

                while (($row = $oldName->fetch())) {
                    $valuesToInsert = [$value => $row['value']];

                    $connection->update($tmpTable, $valuesToInsert, ['_entity_id = ?' => $row['_entity_id']]);
                }

                break;
            }
        }
    }
}
