import { memo } from 'react'
import Link from 'next/link'

import BasePrice from '@/components/BasePrice'
import CommonLoading from '@/components/Common/CommonLoading'
import { useInformationGroup } from '@/hooks/AccountLayout'

import { StyledInformationGroup } from './styled'

const InformationGroup = () => {
  const { userDetail, financialLoading, financialData, financialVisible } = useInformationGroup()

  return (
    <StyledInformationGroup>
      <li>
        <h2>Account Information</h2>
        <h3>Contact</h3>
        <p>{`${userDetail?.firstname ?? ''} ${userDetail?.lastname ?? ''}`}</p>
        <Link href={`mailto:${userDetail?.email ?? ''}`} title="Email" className="customer-email">
          {userDetail?.email ?? ''}
        </Link>
      </li>
      <li>
        <h2>Company Information</h2>
        {/* TODO: data */}
        <div className="information-grid">
          <div>
            <h3>Customer ID</h3>
            <p>{userDetail?.id ?? ''}</p>
          </div>
          <div>
            {/* TODO: link */}
            <h3>Use VIP+ Rewards</h3>
            <p className="reward-points">
              {/*5,400
              <svg width="16px" height="16px" fill="currentColor" focusable="false">
                <use xlinkHref="#icon-vip-reward-points" />
              </svg>*/}
            </p>
          </div>
          <div className="full-item">
            <h3>Company XYZ</h3>
            <p>1234 Address Street</p>
            <p>City, State, Zip Code</p>
            <p>Country</p>
            <p>(123) 456-7890</p>
          </div>
        </div>
      </li>
      {financialVisible && (
        <li>
          <CommonLoading spinning={financialLoading}>
            <h2>Financial Information</h2>
            <div className="information-grid">
              <div>
                <h3>Payment Terms</h3>
                <p>{financialData?.paymentTermsDescription ?? ''}</p>
              </div>
              <div>
                <h3>Outstanding Balance</h3>
                <p>
                  <BasePrice value={financialData?.balance ?? 0} />
                </p>
              </div>
              <div>
                <h3>Credit Limit</h3>
                <p>
                  <BasePrice value={financialData?.creditLimitUsed ?? 0} />
                </p>
              </div>
              <div>
                <h3>Last Payment Date</h3>
                {/* TODO: Format: <p>02/01/2025</p> */}
                <p>{financialData?.lastPaymentDate ?? ''}</p>
              </div>
              <div>
                <h3>Available Credit</h3>
                <p>
                  <BasePrice value={financialData?.availableCredit ?? 0} />
                </p>
              </div>
              <div>
                <h3>Last Payment Amount</h3>
                <p>
                  <BasePrice value={financialData?.lastPaymentAmount ?? 0} />
                </p>
              </div>
            </div>
          </CommonLoading>
        </li>
      )}
    </StyledInformationGroup>
  )
}

export default memo(InformationGroup)
