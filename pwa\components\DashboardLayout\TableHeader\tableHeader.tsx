import Link from 'next/link'
import { memo } from 'react'

import { StyledTableHeader } from './styled'

type TTableHeader = {
  title: string
  subTitle?: string | null
  viewAllUrl: string
}

const TableHeader = ({ title, subTitle = null, viewAllUrl }: TTableHeader) => {
  return (
    <StyledTableHeader>
      <h2>
        {title}
        {subTitle && <span className="sub-title"> {subTitle}</span>}
      </h2>
      <Link href={viewAllUrl} title="view-all" className="view-all-btn">
        View All
        <svg width="13px" height="14px" fill="currentColor" focusable="false">
          <use xlinkHref="#icon-view-all-arrow" />
        </svg>
      </Link>
    </StyledTableHeader>
  )
}

export default memo(TableHeader)
