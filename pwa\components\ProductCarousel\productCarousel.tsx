import { Carousel } from 'antd'
import { memo, useCallback, useMemo, useRef, useState } from 'react'

import { LineContainer } from '@ranger-theme/ui'
import BannerLine from '@/components/BannerLine'
import ProductItem from '@/components/ProductItem'
import { useAppMediaQuery } from '@/packages/hooks'

import Arrow from './arrow'
import { StyledProductCarousel } from './styled'

const ProductCarousel = ({ products }) => {
  const bannerRef = useRef(null)
  const { isMobile } = useAppMediaQuery()

  const [bannerIndex, setBannerIndex] = useState(0)

  const carouselLeng = useMemo(() => {
    return isMobile ? 1 : 4
  }, [isMobile])

  const totalCount = useMemo(() => {
    return products.length
  }, [products])

  const carouselVisible = useMemo(() => {
    return totalCount > carouselLeng
  }, [carouselLeng, totalCount])

  const handlePrev = useCallback(() => {
    if (bannerRef?.current?.prev) {
      bannerRef.current.prev()
    }
  }, [bannerRef])

  const handleNext = useCallback(() => {
    if (bannerRef?.current?.next) {
      bannerRef.current.next()
    }
  }, [bannerRef])

  const beforeChange = useCallback((current: number, next: number) => {
    setBannerIndex(next)
  }, [])

  return (
    <StyledProductCarousel className="product-carousel-component">
      <LineContainer>
        <h2>Continue Shopping</h2>
        {carouselVisible ? (
          <div className="is-carousel">
            <Carousel
              centerMode={isMobile}
              arrows={!isMobile}
              centerPadding="100px"
              dots={!isMobile}
              slidesToShow={carouselLeng}
              ref={bannerRef}
              beforeChange={beforeChange}
              nextArrow={
                <Arrow
                  icon={
                    <svg
                      className="right"
                      width="1em"
                      height="1em"
                      fill="currentColor"
                      aria-hidden="true"
                      focusable="false">
                      <use xlinkHref="#icon-right" />
                    </svg>
                  }
                />
              }
              prevArrow={
                <Arrow
                  icon={
                    <svg
                      className="left"
                      width="1em"
                      height="1em"
                      fill="currentColor"
                      aria-hidden="true"
                      focusable="false">
                      <use xlinkHref="#icon-left" />
                    </svg>
                  }
                />
              }>
              {products.map((product) => {
                return <ProductItem key={product.sku} product={product} />
              })}
            </Carousel>
          </div>
        ) : (
          <div className="is-simple-list">
            {products.map((product) => {
              return <ProductItem key={product.sku} product={product} />
            })}
          </div>
        )}
      </LineContainer>
      {isMobile && carouselVisible && (
        <BannerLine
          curIndex={bannerIndex}
          totalCount={totalCount}
          handlePrev={handlePrev}
          handleNext={handleNext}
        />
      )}
    </StyledProductCarousel>
  )
}

export default memo(ProductCarousel)
