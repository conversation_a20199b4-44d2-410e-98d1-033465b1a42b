import { memo, useEffect, useState } from 'react'
import { clsx } from 'clsx'

import { StyledCommonTableSort } from './styled'

const CommonTableSort = ({ sortOrder }) => {
  const [isRendered, setIsRendered] = useState<boolean>(false)

  useEffect(() => {
    setIsRendered(true)
  }, [])

  return isRendered ? (
    <StyledCommonTableSort
      className={clsx('common-table-sort', {
        'is-asc': sortOrder === 'ascend' || sortOrder === 'ASC',
        'is-desc': sortOrder === 'descend' || sortOrder === 'DESC'
      })}>
      <svg
        className="asc-icon"
        width="8"
        height="6"
        viewBox="0 0 8 6"
        fill="none"
        xmlns="http://www.w3.org/2000/svg">
        <path
          d="M3.39514 0.82502C3.6948 0.416284 4.3052 0.416285 4.60486 0.825021L7.45516 4.7128C7.81836 5.20821 7.46458 5.90625 6.8503 5.90625H1.1497C0.535418 5.90625 0.181638 5.20821 0.544841 4.7128L3.39514 0.82502Z"
          fill="#191919"
        />
      </svg>
      <svg
        className="desc-icon"
        width="8"
        height="6"
        viewBox="0 0 8 6"
        fill="none"
        xmlns="http://www.w3.org/2000/svg">
        <path
          d="M3.39514 0.82502C3.6948 0.416284 4.3052 0.416285 4.60486 0.825021L7.45516 4.7128C7.81836 5.20821 7.46458 5.90625 6.8503 5.90625H1.1497C0.535418 5.90625 0.181638 5.20821 0.544841 4.7128L3.39514 0.82502Z"
          fill="#191919"
        />
      </svg>
    </StyledCommonTableSort>
  ) : null
}

export default memo(CommonTableSort)
