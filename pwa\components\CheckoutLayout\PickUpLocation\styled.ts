import styled from '@emotion/styled'

export const StyledPickUpLocation = styled.div`
  h3 {
    font-weight: 700;
    font-size: 24px;
    line-height: 30px;
    letter-spacing: 0;
  }

  .location-content {
    display: grid;
    grid-template-columns: auto 366px;
    grid-gap: 60px;
    padding: 15px 15px 15px 40px;
    width: 100%;
    min-height: 502px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;

    .map-icon {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 25px;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background-color: var(--color-bg-base);
    }

    h4,
    p {
      font-weight: 400;
      font-size: 15px;
      line-height: 21px;
      letter-spacing: 0.01em;

      a {
        margin-left: 5px;
        font-weight: 700;
        text-decoration: underline;
        color: var(--color-primary) !important;
      }
    }

    h4 {
      margin-top: 22px;
      margin-bottom: 0;
      font-weight: 700;
    }
  }

  &.is-order-confirmation-page {
    h2 {
      margin-top: 16px;
      margin-bottom: -10px;
      font-weight: 700;
      font-size: 20px;
      line-height: 26px;
      letter-spacing: 0;
    }

    .location-content {
      grid-template-columns: 1fr 787px;
      margin-top: 38px;
      padding: 14px 14px 14px 38px;
      min-height: 541px;
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    .location-content,
    &.is-order-confirmation-page .location-content {
      grid-template-columns: 1fr;
      grid-gap: 24px;
      padding: 0;
      border: none;

      .location-information {
        padding: 15px 30px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
      }
    }
  }
`
