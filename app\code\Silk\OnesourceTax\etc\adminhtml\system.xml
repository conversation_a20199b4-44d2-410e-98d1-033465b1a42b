<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="tax">
            <group id="onesource" translate="label" type="text" sortOrder="2" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>OneSource Tax</label>
                <field id="active" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="mode" translate="label" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Mode</label>
                    <source_model>Silk\OnesourceTax\Model\Config\Source\Mode</source_model>
                </field>
                <field id="client_id" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Client ID</label>
                    <validate>required-entry</validate>
                </field>
                <field id="client_secret" translate="label" type="obscure" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Client Secret</label>
                    <backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
                    <validate>required-entry</validate>
                </field>
                <field id="scopes" translate="label" type="text" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Scopes</label>
                    <validate>required-entry</validate>
                </field>
                <field id="company_role" translate="label" type="select" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Company Role</label>
                    <source_model>Silk\OnesourceTax\Model\Config\Source\CompanyRole</source_model>
                    <validate>required-entry</validate>
                </field>
                <field id="external_company_id" translate="label" type="text" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>External Company ID</label>
                    <validate>required-entry</validate>
                </field>
            </group>
        </section>
    </system>
</config>