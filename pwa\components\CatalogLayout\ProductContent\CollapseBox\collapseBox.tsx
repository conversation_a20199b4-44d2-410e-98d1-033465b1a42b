import { Collapse } from 'antd'
import dynamic from 'next/dynamic'
import { useCallback } from 'react'

import { StyledCollapseBox } from './styled'

const PageBuilder = dynamic(() => import('@/packages/pagebuilder'), {
  ssr: false
})

const CollapseBox = ({ className = '', title = '', contentHtml, contentChildren }: any) => {
  const expandIcon = useCallback(
    (panelProps) => (
      <svg width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false">
        <use xlinkHref={panelProps.isActive ? '#icon-collapse-hide' : '#icon-collapse'} />
      </svg>
    ),
    []
  )

  return (
    <StyledCollapseBox className={className}>
      <Collapse
        ghost
        // defaultActiveKey={[defaultActiveKey]}
        expandIconPosition="end"
        expandIcon={expandIcon}
        items={[
          {
            key: 'expand-item',
            label: title,
            children: contentChildren || <PageBuilder html={contentHtml} />
          }
        ]}
      />
    </StyledCollapseBox>
  )
}

export default CollapseBox
