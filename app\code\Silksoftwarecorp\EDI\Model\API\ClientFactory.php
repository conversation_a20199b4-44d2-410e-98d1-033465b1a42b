<?php

namespace Silksoftwarecorp\EDI\Model\API;

use Silksoftwarecorp\EDI\Http\Client;
use Silksoftwarecorp\EDI\Http\ClientInterface;
use Silksoftwarecorp\EDI\Http\ClientInterfaceFactory;
use Silksoftwarecorp\EDI\Model\API\Config as APIConfig;

class ClientFactory
{
    /**
     * @var ClientInterfaceFactory
     */
    protected $httpClientFactory;

    /**
     * @var APIConfig
     */
    protected $apiConfig;

    /**
     * @param ClientInterfaceFactory $httpClientFactory
     * @param Config $apiConfig
     */
    public function __construct(
        ClientInterfaceFactory $httpClientFactory,
        Config $apiConfig
    ) {
        $this->httpClientFactory = $httpClientFactory;
        $this->apiConfig = $apiConfig;
    }

    public function create(): ClientInterface
    {
        /**@var $httpClient Client*/
        $httpClient = $this->httpClientFactory->create();
        $httpClient->withBaseUri(
            $this->apiConfig->getApiUrl()
        )->withHeaders([
            'UserName' => $this->apiConfig->getUserName(),
            'Password' => $this->apiConfig->getPassword()
        ])->timeout(
            $this->apiConfig->getTimeout()
        )->withRetry()->withLog();

        return $httpClient;
    }
}
