import { memo } from 'react'
import { But<PERSON> } from 'antd'
import type { FC } from 'react'
import { isEmpty } from 'lodash-es'
import { FormattedMessage } from 'react-intl'

import { MediaLayout } from '@/ui'
import { useFilters } from '@/hooks/CategoryGrid'

import CurrentFilters from './CurrentFilters'
import FilterItem from './FilterItem'
import { StyledFilters, StyledFilterChoose, StyledMobileFilterHeader } from './styled'

interface FiltersProps {
  id?: string
  search?: string
  closeMobileDrawer?: () => void
  updateFilterCount?: (val: number) => void
  mobileDrawerOpen?: boolean
}

const Filters: FC<FiltersProps> = ({
  id,
  search,
  closeMobileDrawer = () => {},
  updateFilterCount = () => {},
  mobileDrawerOpen = false
}) => {
  const {
    filterApi,
    filtersData,
    filterState,
    setIsApplying,
    handleClearAll,
    hasFilters,
    filtersVisible,
    applyFilter,
    handleCloseMobileDrawer,
    handleClearMobileDrawerAll
  } = useFilters({ id, search, mobileDrawerOpen, closeMobileDrawer, updateFilterCount })

  return (
    <StyledFilters>
      {filtersVisible && (
        <>
          <MediaLayout type="mobile">
            <StyledMobileFilterHeader>
              <h3>Filters</h3>
              <div>
                {!isEmpty(filterState) && (
                  <Button className="clear-btn" type="link" onClick={handleClearMobileDrawerAll}>
                    <FormattedMessage id="plp.clearAll" />
                  </Button>
                )}
              </div>
              <div className="close-btn" aria-hidden="true" onClick={handleCloseMobileDrawer}>
                <svg width="16px" height="16px" fill="currentColor" focusable="false">
                  <use xlinkHref="#icon-close-white" />
                </svg>
              </div>
            </StyledMobileFilterHeader>
          </MediaLayout>
          {hasFilters && (
            <>
              <MediaLayout>
                <CurrentFilters
                  filterState={filterState}
                  filterApi={filterApi}
                  filtersData={filtersData}
                  setIsApplying={setIsApplying}
                  handleClearAll={handleClearAll}
                />
                <h2 className="filter-by-title">Filter by</h2>
              </MediaLayout>
              <StyledFilterChoose>
                {filtersData.map((filter) => {
                  return (
                    <FilterItem
                      key={filter.attribute_code}
                      filterState={filterState.get(filter.attribute_code)}
                      items={filter}
                      filterApi={filterApi}
                      setIsApplying={setIsApplying}
                    />
                  )
                })}
              </StyledFilterChoose>
              <MediaLayout type="mobile">
                <Button type="primary" className="apply-filter" onClick={applyFilter}>
                  APPLY FILTER
                </Button>
              </MediaLayout>
            </>
          )}
        </>
      )}
    </StyledFilters>
  )
}

export default memo(Filters)
