<?php

namespace Silksoftwarecorp\UnifiedAR\Model\Data;

use Magento\Framework\Exception\LocalizedException;
use Psr\Log\LoggerInterface;
use Silksoftwarecorp\UnifiedAR\Api\Data\MapperInterface;

/**
 * Abstract Data Mapper
 * 
 * Provides common functionality for data mapping
 */
abstract class AbstractMapper implements MapperInterface
{
    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * @var array
     */
    protected $mappingConfig = [];

    /**
     * @var array
     */
    protected $validationRules = [];

    /**
     * @var array
     */
    protected $transformers = [];

    /**
     * Constructor
     * 
     * @param LoggerInterface $logger
     * @param array $mappingConfig
     * @param array $validationRules
     * @param array $transformers
     */
    public function __construct(
        LoggerInterface $logger,
        array $mappingConfig = [],
        array $validationRules = [],
        array $transformers = []
    ) {
        $this->logger = $logger;
        $this->mappingConfig = array_merge($this->getDefaultMappingConfig(), $mappingConfig);
        $this->validationRules = array_merge($this->getDefaultValidationRules(), $validationRules);
        $this->transformers = array_merge($this->getDefaultTransformers(), $transformers);
    }

    /**
     * Transform raw data to structured format
     * 
     * @param array $data Raw data from API
     * @param array $options Additional transformation options
     * @return array Transformed data
     */
    public function map(array $data, array $options = []): array
    {
        try {
            // Validate input data
            if (!$this->validate($data)) {
                $this->logger->warning('Data validation failed', [
                    'data' => $data,
                    'mapper' => static::class
                ]);
                return [];
            }

            $result = [];
            $config = $this->getMappingConfig();

            foreach ($config as $targetField => $sourceConfig) {
                $result[$targetField] = $this->mapField($data, $sourceConfig, $options);
            }

            // Apply post-processing
            $result = $this->postProcess($result, $data, $options);

            $this->logger->debug('Data mapping completed', [
                'mapper' => static::class,
                'input_fields' => array_keys($data),
                'output_fields' => array_keys($result)
            ]);

            return $result;

        } catch (\Exception $e) {
            $this->logger->error('Error in data mapping', [
                'mapper' => static::class,
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            throw new LocalizedException(__('Data mapping failed: %1', $e->getMessage()), $e);
        }
    }

    /**
     * Transform multiple items
     * 
     * @param array $items Array of raw data items
     * @param array $options Additional transformation options
     * @return array Array of transformed items
     */
    public function mapCollection(array $items, array $options = []): array
    {
        $result = [];
        
        foreach ($items as $index => $item) {
            try {
                $mappedItem = $this->map($item, $options);
                if (!empty($mappedItem)) {
                    $result[] = $mappedItem;
                }
            } catch (\Exception $e) {
                $this->logger->warning('Failed to map item in collection', [
                    'index' => $index,
                    'error' => $e->getMessage(),
                    'item' => $item
                ]);
                // Continue with other items
            }
        }

        return $result;
    }

    /**
     * Validate data before mapping
     * 
     * @param array $data Raw data to validate
     * @return bool
     */
    public function validate(array $data): bool
    {
        $rules = $this->validationRules;
        
        foreach ($rules as $field => $rule) {
            if (!$this->validateField($data, $field, $rule)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get mapping configuration
     * 
     * @return array
     */
    public function getMappingConfig(): array
    {
        return $this->mappingConfig;
    }

    /**
     * Map individual field
     * 
     * @param array $data Source data
     * @param array|string $config Field mapping configuration
     * @param array $options Mapping options
     * @return mixed
     */
    protected function mapField(array $data, $config, array $options = [])
    {
        // Simple string mapping
        if (is_string($config)) {
            return $this->getValue($data, $config);
        }

        // Complex mapping configuration
        if (is_array($config)) {
            $sourceField = $config['source'] ?? null;
            $transformer = $config['transformer'] ?? null;
            $default = $config['default'] ?? null;

            $value = $sourceField ? $this->getValue($data, $sourceField, $default) : $default;

            // Apply transformer if specified
            if ($transformer && isset($this->transformers[$transformer])) {
                $value = $this->transformers[$transformer]($value, $data, $options);
            }

            return $value;
        }

        return null;
    }

    /**
     * Get value from nested array using dot notation
     * 
     * @param array $data Source data
     * @param string $path Field path (e.g., 'user.profile.name')
     * @param mixed $default Default value
     * @return mixed
     */
    protected function getValue(array $data, string $path, $default = null)
    {
        $keys = explode('.', $path);
        $current = $data;

        foreach ($keys as $key) {
            if (!is_array($current) || !array_key_exists($key, $current)) {
                return $default;
            }
            $current = $current[$key];
        }

        return $current;
    }

    /**
     * Validate individual field
     * 
     * @param array $data Source data
     * @param string $field Field name
     * @param array $rule Validation rule
     * @return bool
     */
    protected function validateField(array $data, string $field, array $rule): bool
    {
        $value = $this->getValue($data, $field);
        
        // Required field validation
        if (isset($rule['required']) && $rule['required'] && empty($value)) {
            return false;
        }

        // Type validation
        if (isset($rule['type']) && !empty($value)) {
            switch ($rule['type']) {
                case 'string':
                    return is_string($value);
                case 'int':
                case 'integer':
                    return is_int($value) || ctype_digit($value);
                case 'float':
                case 'double':
                    return is_numeric($value);
                case 'bool':
                case 'boolean':
                    return is_bool($value) || in_array($value, [0, 1, '0', '1', 'true', 'false']);
                case 'array':
                    return is_array($value);
            }
        }

        return true;
    }

    /**
     * Post-process mapped data
     * 
     * @param array $result Mapped data
     * @param array $originalData Original source data
     * @param array $options Mapping options
     * @return array
     */
    protected function postProcess(array $result, array $originalData, array $options = []): array
    {
        // Override in child classes for custom post-processing
        return $result;
    }

    /**
     * Get default mapping configuration
     * 
     * @return array
     */
    abstract protected function getDefaultMappingConfig(): array;

    /**
     * Get default validation rules
     * 
     * @return array
     */
    protected function getDefaultValidationRules(): array
    {
        return [];
    }

    /**
     * Get default transformers
     * 
     * @return array
     */
    protected function getDefaultTransformers(): array
    {
        return [
            'float' => function ($value) {
                return (float)($value ?? 0);
            },
            'int' => function ($value) {
                return (int)($value ?? 0);
            },
            'bool' => function ($value) {
                return (bool)$value;
            },
            'string' => function ($value) {
                return (string)($value ?? '');
            },
            'date' => function ($value) {
                return $value ? date('Y-m-d', strtotime($value)) : null;
            },
            'datetime' => function ($value) {
                return $value ? date('Y-m-d H:i:s', strtotime($value)) : null;
            },
            'url' => function ($value) {
                return filter_var($value, FILTER_VALIDATE_URL) ? $value : null;
            },
            'email' => function ($value) {
                return filter_var($value, FILTER_VALIDATE_EMAIL) ? $value : null;
            }
        ];
    }
} 