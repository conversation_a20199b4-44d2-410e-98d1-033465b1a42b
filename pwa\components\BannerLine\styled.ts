import styled from '@emotion/styled'

export const StyledBannerLine = styled.div`
  display: grid;
  grid-template-columns: 1fr 76px;
  grid-column-gap: 1rem;
  align-items: center;
  margin: 24px auto 0;
  max-width: 1180px;

  .banner-controller-line {
    width: 100%;
    height: 6px;
    background: #d9d9d9;

    span {
      display: block;
      height: 6px;
      background: var(--color-primary);
      transition: all 0.3s;
    }
  }

  .banner-controller-arrows {
    display: grid;
    grid-template-columns: auto 1fr auto;
    align-items: center;
    text-align: center;

    .controller-arrow {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 22px;
      width: 22px;
      cursor: pointer;

      &.prev {
        transform: rotate(180deg);
      }
    }

    .line-separator {
      margin: 0 2px;
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    grid-template-columns: 1fr;
    align-items: center;
    padding: 0 16px;

    .banner-controller-arrows {
      margin: 26px auto 0;
      width: 88px;
    }
  }
`
