<?php

namespace Silksoftwarecorp\UnifiedAR\Service;

use Silksoftwarecorp\UnifiedAR\Model\FinancialInformation\Mapper as FinancialInformationMapper;
use Silksoftwarecorp\UnifiedAR\Model\UnifiedArApiService;

/**
 * Financial Information Service
 */
class FinancialInformationService
{
    /**
     * @var UnifiedArApiService
     */
    protected $unifiedArApiService;

    /**
     * @var FinancialInformationMapper
     */
    protected $financialInformationMapper;

    /**
     * Constructor
     * @param UnifiedArApiService $unifiedArApiService
     * @param FinancialInformationMapper $financialInformationMapper
     */
    public function __construct(
        UnifiedArApiService $unifiedArApiService,
        FinancialInformationMapper $financialInformationMapper
    ) {
        $this->unifiedArApiService = $unifiedArApiService;
        $this->financialInformationMapper = $financialInformationMapper;
    }

    /**
     * Get financial snapshot information
     * @param string $customerId
     * @return array
     */
    public function getFinancialInformation(string $customerId): array
    {
        if (empty($customerId)) {
            return [];
        }

        $financialInfo = $this->unifiedArApiService->fetchFinancialInformation($customerId);

        if (empty($financialInfo)) {
            return [];
        }

        return $this->financialInformationMapper->execute($financialInfo);
    }
} 