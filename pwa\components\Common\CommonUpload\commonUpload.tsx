import { memo, useState, useCallback } from 'react'

import { StyledCommonUpload } from './styled'

const CommonUpload = ({ onUploadDone }: any) => {
  const [files, setFiles] = useState<any[]>([])

  const beforeUpload = useCallback((file: File) => {
    const isLt10M = file.size / 1024 / 1024 < 10 // 10MB
    if (!isLt10M) {
      console.error('File must be smaller than 10MB!')
    }
    return isLt10M
  }, [])

  const onChange = useCallback(
    async ({ fileList }) => {
      setFiles(fileList)
      if (fileList.length === 0) {
        onUploadDone('')
        return
      }

      if (fileList?.[0]?.status === 'done') {
        const fileToBase64 = (file: File): Promise<string> => {
          return new Promise((resolve, reject) => {
            const reader = new FileReader()
            reader.readAsDataURL(file)
            reader.onload = () => resolve(reader.result as string)
            reader.onerror = (error) => reject(error)
          })
        }
        const file = fileList[0].originFileObj as File
        const base64Data = await fileToBase64(file)
        // const jsonData = {
        //   fileName: file.name,
        //   fileType: file.type,
        //   fileSize: file.size,
        //   fileContent: base64Data // Base64
        // }
        if (onUploadDone) {
          onUploadDone(base64Data)
        }
      }
    },
    [onUploadDone]
  )

  return (
    <StyledCommonUpload
      fileList={files}
      beforeUpload={beforeUpload}
      onChange={onChange}
      maxCount={1}>
      <div className="choose-btn">
        <svg width="20px" height="20px" fill="currentColor" focusable="false">
          <use xlinkHref="#icon-upload-file" />
        </svg>
        Choose file
      </div>
    </StyledCommonUpload>
  )
}

export default memo(CommonUpload)
