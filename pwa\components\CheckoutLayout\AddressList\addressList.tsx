// import dynamic from 'next/dynamic'
import Link from 'next/link'
import { Button } from 'antd'
import { isEmpty } from 'lodash-es'

import { useAddressList } from '@/hooks/CheckoutLayout'
import UnifiedARForm from '@/components/CheckoutLayout/UnifiedARForm/unifiedARForm'

// import ChangeModal from '../ChangeModal'
import { StyledAddressContainer, StyledActiveAddress, StyledNoAddress } from './styled'

// const AddressModal = dynamic(() => import('../AddressModal'), {
//   ssr: false
// })

const AddressList = ({
  type,
  isUnifiedAR = false,
  handleUpdateUnifiedARData
}: {
  type: string
  isUnifiedAR: boolean
  handleUpdateUnifiedARData?: any
}) => {
  const {
    activeAddress: address,
    handleChangeOpen,
    unifiedAddress,
    getRegionNameByCode
  } = useAddressList({ type })

  return (
    <div>
      {!isEmpty(address) ? (
        <StyledAddressContainer>
          {!isEmpty(address) && (
            <>
              {type === 'shipping' && <h3>Shipping Address</h3>}
              <StyledActiveAddress>
                <div className="address">
                  {/* TODO: company name */}
                  <div className="address-company">{address?.company ?? ''}</div>
                  <div>{address?.ship_to_name ?? ''}</div>
                  <div>{`${address?.street ?? ''} ${address?.street_line2 ? `, ${address?.street_line2}` : ''}`}</div>

                  <div>
                    <span>{`${address.city}, `}</span>
                    <span>{`${getRegionNameByCode(address?.country_id, address?.region_code)}, `}</span>
                    <span>{address.postcode}</span>
                  </div>
                  <div>
                    <Link className="address-telephone" href={`tel:${address.telephone}`}>
                      {address.telephone}
                    </Link>
                  </div>
                </div>
                <Button className="change-btn" type="link" onClick={handleChangeOpen}>
                  Change
                </Button>
              </StyledActiveAddress>
            </>
          )}
        </StyledAddressContainer>
      ) : (
        <StyledNoAddress>
          <p>There are currently no address available here.</p>
          {/* <Button type="primary" onClick={handleModalOpen}>
            <FormattedMessage id="account.addNewAddress" />
          </Button> */}
        </StyledNoAddress>
      )}
      {/*{modalVisibility && (
        <AddressModal
          address={editAddress}
          isLoading={isLoading}
          visible={modalVisibility}
          onCancel={handleModalCancel}
          handleFormSubmit={handleFormSubmit}
        />
      )}*/}
      {/*{changeVisibility && (
        <ChangeModal
          activeId={activeId}
          addressList={addressList}
          visible={changeVisibility}
          handleChooseAddress={handleChooseAddress}
          onCancel={handleChangeCancel}
        />
      )}*/}

      {isUnifiedAR && (
        <UnifiedARForm
          address={unifiedAddress}
          handleUpdateUnifiedARData={handleUpdateUnifiedARData}
        />
      )}
    </div>
  )
}

export default AddressList
