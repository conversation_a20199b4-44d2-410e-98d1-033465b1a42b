<?xml version="1.0"?>
<!--
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="general_config" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>General</label>
            <tab>silksoftwarecorp</tab>
            <resource>Silksoftwarecorp_StoreConfig::config</resource>
            <group id="settings" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Settings</label>
                <field id="list_per_page_values" translate="label comment" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Items per Page on List Allowed Values</label>
                    <comment>Comma-separated.</comment>
                    <validate>required-entry</validate>
                </field>
                <field id="list_per_page" translate="label comment" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Items per Page on List Default Value</label>
                    <comment>Must be in the allowed values list.</comment>
                    <validate>required-entry</validate>
                </field>
            </group>
        </section>
    </system>
</config>
