import dynamic from 'next/dynamic'
import { memo } from 'react'
import { Dropdown } from 'antd'
import type { MenuProps } from 'antd'

import { useProductRequisition } from '@/hooks/ProductRequisition'

import { StyledRequisition, StyledOverlay, StyledCreated } from './styled'

const RequisitionModal = dynamic(() => import('../RequisitionModal'), {
  ssr: false
})

const ProductRequisition = ({ children, form, product }) => {
  const { itemList, loading, visible, handleAddToRequisition, handleModalOpen, handleModalCancel } =
    useProductRequisition({
      product,
      form
    })

  const items: MenuProps['items'] = [
    {
      key: '1',
      label: (
        <StyledOverlay>
          {itemList.map((item) => {
            const { uid, name } = item
            return (
              <li
                key={uid}
                aria-hidden="true"
                onClick={() => {
                  handleAddToRequisition(uid)
                }}
              >
                <span dangerouslySetInnerHTML={{ __html: name }} />
              </li>
            )
          })}
          <li>
            <StyledCreated aria-hidden="true" onClick={handleModalOpen}>
              <span>Create New Requisition List</span>
            </StyledCreated>
            {visible && <RequisitionModal visible={visible} onCancel={handleModalCancel} />}
          </li>
        </StyledOverlay>
      )
    }
  ]

  return (
    <Dropdown menu={{ items }} arrow>
      <StyledRequisition allowed={loading}>{children}</StyledRequisition>
    </Dropdown>
  )
}

export default memo(ProductRequisition)
