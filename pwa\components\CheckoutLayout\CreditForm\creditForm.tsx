import { Form, Input, Select } from 'antd'

import { StyledCreditForm } from './styled'

const CreditForm = () => {
  // const cartTypes: any[] = [
  //   {
  //     name: 'Visa',
  //     value: 'VI'
  //   },
  //   {
  //     name: 'MasterC<PERSON>',
  //     value: 'MC'
  //   },
  //   {
  //     name: 'American Express',
  //     value: 'AE'
  //   },
  //   {
  //     name: 'Discover',
  //     value: 'DI'
  //   }
  // ]
  const currentYear = new Date().getFullYear()
  const months = new Array(12).fill(null).map((_, index) => ({
    name: (index + 1).toString().padStart(2, '0'),
    value: index + 1
  }))
  const years = Array.from({ length: 11 }, (_, index) => ({
    name: (currentYear + index).toString(),
    value: currentYear + index
  }))

  return (
    <StyledCreditForm>
      {/* <Form.Item name="credit_type" rules={[{ required: true }]}>
        <TextSelect label="Credit Card Type" elementId={0}>
          {cartTypes.map((cartType: any) => {
            return (
              <TextSelect.Option key={cartType.value} value={cartType.value}>
                <span dangerouslySetInnerHTML={{ __html: cartType.name }} />
              </TextSelect.Option>
            )
          })}
        </TextSelect>
      </Form.Item> */}
      <h3>Credit Card Number*</h3>
      <Form.Item name="credit_number" rules={[{ required: true }]}>
        <Input className="card-number-input" />
      </Form.Item>
      <h3>Expiration Date*</h3>
      <div className="date-group">
        <Form.Item name="credit_month" rules={[{ required: true }]}>
          <Select placeholder="MM">
            {months.map((month) => (
              <Select.Option key={month.value} value={month.value}>
                {month.label}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <span className="date-separator">/</span>
        <Form.Item name="credit_year" rules={[{ required: true }]}>
          <Select placeholder="YY">
            {years.map((year) => (
              <Select.Option key={year.value} value={year.value}>
                {year.label}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
      </div>
      <h3>Card Verification Number</h3>
      <Form.Item name="credit_cvc" rules={[{ required: true }]}>
        <Input type="number" className="small-input" />
      </Form.Item>
    </StyledCreditForm>
  )
}

export default CreditForm
