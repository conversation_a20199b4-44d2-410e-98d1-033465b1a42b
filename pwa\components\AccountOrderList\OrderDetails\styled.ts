import styled from '@emotion/styled'

export const StyledOrderDate = styled.div`
  margin-bottom: 16px;
  font-weight: 400;
  font-size: 16px;
  line-height: 22px;
  letter-spacing: 0;
  color: var(--color-black);
`

export const DetailsTableDescriptionWarp = styled.div`
  margin-bottom: 15px;

  div {
    display: inline-block;
    margin-right: 36px;
    font-weight: 700;
    font-size: 24px;
    line-height: 30px;
    letter-spacing: 0;
    color: #231f20;
    opacity: 0.5;
    cursor: pointer;

    &.active {
      opacity: 1;
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    margin-bottom: 10px;

    div {
      margin-right: 16px;
      font-size: 15px;
    }
  }

  @media print {
    .orderDetails__invoices {
      display: none;
    }

    .orderDetails__Shipments {
      display: none;
    }
  }
`

export const FooterWrap = styled.div`
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;

  & > div {
    padding: 12px 0;
    background: #f5f5f5;
    border-radius: 4px;
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    & > div {
      width: 100%;
      padding: 12px 25px;
    }
  }
`

export const DetailsFooterWrap = styled.ul`
  display: flex;
  justify-content: space-between;
  align-items: center;

  &.order-total {
    li {
      font-weight: 700;
      font-size: 18px;
      line-height: 24px;
    }

    .price {
      font-weight: 700;
      font-size: 18px;
      line-height: 24px;
    }
  }

  .price {
    font-weight: 400;
    font-size: 15px;
    line-height: 21px;
    letter-spacing: 0.01em;
    color: var(--color-font);
  }

  @media screen and (max-width: ${({ theme }) => theme.breakPoint.m}px) {
    display: flex;
    justify-content: space-between;
    padding: 0;
  }
`

export const DetailsFooterItem = styled.li`
  display: inline-grid;
  grid-template-columns: 2fr;
  padding: 5px 25px;
  font-weight: 400;
  font-size: 15px;
  line-height: 21px;
  letter-spacing: 0.01em;

  @media screen and (max-width: ${({ theme }) => theme.breakPoint.m}px) {
    padding: 0.5rem 0.2rem;
  }
`

export const DetailsAddress = styled.div`
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 24px;
  margin: 32px 0 64px;

  & > div {
    padding: 24px;
    height: 100%;
    border: 1px solid #d9d9d9;
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.m}px) {
    display: block;
  }
`

export const DetailsProductName = styled.div`
  .orderDetails__label {
    font-weight: 600;
  }
`

export const ModelLayoutWarp = styled.div`
  strong {
    display: block;
    margin-bottom: 8px;
    font-weight: 700;
    font-size: 17px;
    line-height: 25px;
    letter-spacing: 0.01em;
    color: var(--color-black);
  }

  p {
    font-weight: 400;
    font-size: 15px;
    line-height: 23px;
    letter-spacing: 0.01em;
    color: var(--color-black);
  }

  @media screen and (max-width: ${({ theme }) => theme.breakPoint.m}px) {
    margin: 1rem 0;
  }
`

export const OrderQty = styled.span`
  display: inline-block;
  width: 60%;
  text-align: center;

  @media screen and (max-width: ${({ theme }) => theme.breakPoint.m}px) {
    text-align: left;
  }
`

export const OrderDetailsqty = styled.span`
  display: block;
`

export const OrderDescription = styled.div`
  border: 1px solid #d9d9d9;
  margin-bottom: 24px;

  .details__printAll {
    display: block;
    color: ${({ theme }) => theme.colors.primary};
  }

  .detailsDescription__title {
    display: inline-block;
    padding-left: 0.75rem;
  }

  .details__print {
    display: inline-block;
    color: ${({ theme }) => theme.colors.primary};
  }

  @media print {
    .details__print {
      display: none;
    }

    .details__printAll {
      display: none;
    }
  }
`

export const StyledTagItem = styled.div`
  span {
    padding: 3px 16px 0;
    height: 29px;
    border-radius: 15px;
  }
`
