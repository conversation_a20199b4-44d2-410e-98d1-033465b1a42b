<?php

namespace Silksoftwarecorp\QuoteGraphQl\Observer;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Quote\Api\Data\AddressInterface;

class AddressDataBeforeSave implements ObserverInterface
{
    public function execute(Observer $observer): void
    {
        $address = $observer->getEvent()->getData('quote_address');
        if ($address instanceof AddressInterface) {
            $address->collectShippingRates();
        }
    }
}
