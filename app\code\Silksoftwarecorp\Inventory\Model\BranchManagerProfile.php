<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */
declare(strict_types=1);

namespace Silksoftwarecorp\Inventory\Model;

use Magento\Framework\App\ObjectManager;
use Magento\Framework\Model\AbstractModel;
use Silksoftwarecorp\Inventory\Api\Data\BranchManagerProfileInterface;
use Silksoftwarecorp\Inventory\Model\ManagerProfileImageUrlService;

/**
 * Branch Manager Profile Model
 */
class BranchManagerProfile extends AbstractModel implements BranchManagerProfileInterface
{
    /**
     * @var ManagerProfileImageUrlService
     */
    private $imageUrlService;

    /**
     * @param \Magento\Framework\Model\Context $context
     * @param \Magento\Framework\Registry $registry
     * @param \Magento\Framework\Model\ResourceModel\AbstractResource $resource
     * @param \Magento\Framework\Data\Collection\AbstractDb $resourceCollection
     * @param ManagerProfileImageUrlService $imageUrlService
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\Model\Context $context,
        \Magento\Framework\Registry $registry,
        \Magento\Framework\Model\ResourceModel\AbstractResource $resource = null,
        \Magento\Framework\Data\Collection\AbstractDb $resourceCollection = null,
        ManagerProfileImageUrlService $imageUrlService = null,
        array $data = []
    ) {
        $this->imageUrlService = $imageUrlService ?: ObjectManager::getInstance()->get(ManagerProfileImageUrlService::class);
        parent::__construct($context, $registry, $resource, $resourceCollection, $data);
    }

    /**
     * Initialize resource model
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init(\Silksoftwarecorp\Inventory\Model\ResourceModel\BranchManagerProfile::class);
    }

    /**
     * @inheritdoc
     */
    public function getSourceCode(): string
    {
        return $this->getData(self::SOURCE_CODE);
    }

    /**
     * @inheritdoc
     */
    public function setSourceCode(string $sourceCode): BranchManagerProfileInterface
    {
        return $this->setData(self::SOURCE_CODE, $sourceCode);
    }

    /**
     * @inheritdoc
     */
    public function getIsGeneral(): int
    {
        return (int)$this->getData(self::IS_GENERAL);
    }

    /**
     * @inheritdoc
     */
    public function setIsGeneral(bool $isGeneral): BranchManagerProfileInterface
    {
        return $this->setData(self::IS_GENERAL, $isGeneral);
    }

    /**
     * @inheritdoc
     */
    public function getImage(): ?string
    {
        return $this->getData(self::IMAGE);
    }

    /**
     * @inheritdoc
     */
    public function setImage(string $image): BranchManagerProfileInterface
    {
        return $this->setData(self::IMAGE, $image);
    }

    /**
     * @inheritdoc
     */
    public function getName(): ?string
    {
        return $this->getData(self::NAME);
    }

    /**
     * @inheritdoc
     */
    public function setName(string $name): BranchManagerProfileInterface
    {
        return $this->setData(self::NAME, $name);
    }

    /**
     * @inheritdoc
     */
    public function getPhone(): ?string
    {
        return $this->getData(self::PHONE);
    }

    /**
     * @inheritdoc
     */
    public function setPhone(string $phone): BranchManagerProfileInterface
    {
        return $this->setData(self::PHONE, $phone);
    }

    /**
     * @inheritdoc
     */
    public function getRegion(): ?string
    {
        return $this->getData(self::REGION);
    }

    /**
     * @inheritdoc
     */
    public function setRegion(string $region): BranchManagerProfileInterface
    {
        return $this->setData(self::REGION, $region);
    }

    /**
     * Get image URL
     *
     * @return string|null
     */
    public function getImageUrl(): ?string
    {
        // Priority to use image_url set by Resource Model
        $imageUrl = $this->getData('image_url');
        if ($imageUrl !== null) {
            return $imageUrl;
        }

        // If image_url is not set, generate using service class
        $image = $this->getImage();
        if (!$image || !$this->getId()) {
            return null;
        }

        return $this->imageUrlService?->getFullImageUrl((int)$this->getId(), $image);
    }
}
