import { gql } from '@apollo/client'
import type { DocumentNode } from '@apollo/client'

export const PAYMENT_ACCOUNT_QUERY: DocumentNode = gql`
  mutation paymentAccountQuery($transaction_setup_id: String!) {
    paymentAccountQuery(transaction_setup_id: $transaction_setup_id) {
      error_code
      items {
        expiration_month
        expiration_year
        pass_updater_batch_status
        pass_updater_status
        payment_account_id
        payment_account_reference_number
        payment_account_type
        payment_brand
        token_provider_id
        transaction_setup_id
        truncated_card_number
      }
      message
      success
    }
  }
`
