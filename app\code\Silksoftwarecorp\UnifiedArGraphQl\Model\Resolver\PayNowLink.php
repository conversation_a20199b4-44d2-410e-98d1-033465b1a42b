<?php

namespace Silksoftwarecorp\UnifiedArGraphQl\Model\Resolver;

use Magento\CustomerGraphQl\Model\Customer\GetCustomer;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlAuthenticationException;
use Magento\Framework\GraphQl\Exception\GraphQlAuthorizationException;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Exception\GraphQlNoSuchEntityException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Query\Uid;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Silksoftwarecorp\ERPCompany\Model\Company\ErpResolverInterface;
use Silksoftwarecorp\UnifiedAR\Service\PayNowService;

/**
 * Pay Now Link GraphQL Resolver
 */
class PayNowLink implements ResolverInterface
{
    /**
     * @var PayNowService
     */
    protected $payNowService;

    /**
     * @var GetCustomer
     */
    private $getCustomer;

    /**
     * @var ErpResolverInterface
     */
    private $companyErpResolver;

    /**
     * @var Uid
     */
    private $idEncoder;

    /**
     * Constructor
     * @param PayNowService $payNowService
     * @param GetCustomer $getCustomer
     * @param Uid $idEncoder
     * @param ErpResolverInterface $companyErpResolver
     */
    public function __construct(
        PayNowService $payNowService,
        GetCustomer $getCustomer,
        Uid $idEncoder,
        ErpResolverInterface $companyErpResolver
    ) {
        $this->payNowService = $payNowService;
        $this->getCustomer = $getCustomer;
        $this->idEncoder = $idEncoder;
        $this->companyErpResolver = $companyErpResolver;
    }

    /**
     * Resolve pay now link GraphQL query
     * @param Field $field
     * @param $context
     * @param ResolveInfo $info
     * @param array|null $value
     * @param array|null $args
     * @return array
     * @throws GraphQlAuthorizationException
     * @throws GraphQlInputException
     * @throws GraphQlAuthenticationException
     * @throws GraphQlNoSuchEntityException
     */
    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        if (false === $context->getExtensionAttributes()->getIsCustomer()) {
            throw new GraphQlAuthorizationException(__('The current customer isn\'t authorized.'));
        }

        if (empty($args['company_id'])) {
            throw new GraphQlInputException(__('Please specify the `company_id`.'));
        }

        if (empty($args['invoice_numbers']) || !is_array($args['invoice_numbers'])) {
            throw new GraphQlInputException(__('Please specify the `invoice_numbers` as an array.'));
        }

        $this->getCustomer->execute($context);

        try {
            $companyId = (int) $this->idEncoder->decode($args['company_id']);

            // Get ERP Customer ID using the resolver
            $erpCustomerId = $this->companyErpResolver->getErpCustomerId($companyId);

            $payNowLink = $this->payNowService->generatePayNowLink($erpCustomerId, $args['invoice_numbers']);

            return [
                'pay_now_url' => $payNowLink,
                'success' => !empty($payNowLink)
            ];
        } catch (\Exception $e) {
            throw new GraphQlInputException(__($e->getMessage()), $e);
        }
    }
}

