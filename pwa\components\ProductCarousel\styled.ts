import styled from '@emotion/styled'

export const StyledProductCarousel = styled.div`
  padding: 70px 0;

  h2 {
    font-weight: 700;
    font-size: 32px;
    line-height: 38px;
    letter-spacing: 0;
  }

  .is-carousel {
    width: calc(100% + 24px);
    margin-left: -12px;
  }

  .slick-product {
    padding: 8px 12px;

    & > div {
      background: #fff;
      padding: 15px;
      border: none;

      .image img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }

      .product-name {
        margin-top: 16px;
        min-height: 36px;
        font-weight: 700;
        font-size: 18px;
        line-height: 26px;
        letter-spacing: 0;
        text-align: center;
      }

      .product-sku,
      .product-content-grid {
        display: none;
      }
    }
  }

  .slick-slider {
    .slick-arrow {
      top: 45%;
      transform: translateY(-50%);
      .arrow {
        svg {
          fill: #fff !important;
        }
      }
    }

    .slick-dots {
      position: static !important;
      margin-top: 20px;

      li {
        button {
          background-color: #d9d9d9;
          opacity: 1 !important;

          &:hover,
          &:focus {
            opacity: 1 !important;
          }
        }

        &.slick-active button {
          background-color: #191919 !important;
        }
      }
    }
  }

  .is-simple-list {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    width: calc(100% + 24px);
    margin-left: -12px;
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    padding: 60px 0 55px;

    h2 {
      padding: 0 16px;
      margin-bottom: 0;
    }

    .is-carousel {
      width: 100%;
      margin-left: 0;
      padding: 16px;
    }

    .slick-product {
      padding: 8px;

      & > div {
        .product-image img {
          margin: 0 auto;
        }
      }
    }

    .slick-slider {
      margin-left: -110px;
    }

    .is-simple-list {
      display: grid;
      grid-template-columns: 1fr;
      padding: 0 8px;
      margin-top: 16px;
      margin-left: 0;
      width: 100%;
    }
  }
`
