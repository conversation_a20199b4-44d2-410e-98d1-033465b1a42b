import dynamic from 'next/dynamic'
import { Button } from 'antd'

import { MediaLayout } from '@/ui'
import { useAccountAddressContainer } from '@/hooks/AccountAddressPage'
import DefaultAddress from '@/components/DefaultAddress'
import CommonAccountPageLayout from '@/components/Common/CommonAccountPageLayout'
import CommonCheckbox from '@/components/Common/CommonCheckbox'
import CommonLoading from '@/components/Common/CommonLoading'
import CommonLink from '@/components/Common/CommonLink'

import ShipToAddress from './ShipToAddress'
import AdditionalAddress from './AdditionalAddress'
import { StyledAddressLayout } from './styled'

const AddressModal = dynamic(() => import('../AddressModal'), {
  ssr: false
})

const AddressLayout = () => {
  const {
    addressList,
    addressModal,
    pageLoading,
    billToAddress,
    isB2bUser,
    contentVisible,
    defaultShipToAddress,
    ...rest
  } = useAccountAddressContainer()

  return (
    <CommonAccountPageLayout title="Addresses">
      <StyledAddressLayout>
        <CommonLoading spinning={pageLoading}>
          {contentVisible && (
            <>
              <div className="address-grid">
                <div className="address-grid-item">
                  {billToAddress && (
                    <>
                      <DefaultAddress address={billToAddress} titleText="Bill To Address" />
                      <CommonLink href="/new-bill-to-address-request" title="Request New Bill To">
                        Request New Bill To
                      </CommonLink>
                    </>
                  )}
                </div>
                <div className="address-grid-item">
                  {defaultShipToAddress && (
                    <DefaultAddress
                      address={defaultShipToAddress}
                      titleText="Default Ship To Address"
                    />
                  )}
                </div>
              </div>
              <div className="address-table-panel">
                <h2>Ship To Addresses</h2>
                <CommonLink
                  href="/new-ship-to-address-request"
                  title="REQUEST NEW SHIP TO ADDRESS"
                  type="button"
                  fontSize={14}>
                  REQUEST NEW SHIP TO ADDRESS
                </CommonLink>
              </div>
              {isB2bUser ? (
                // B2B user
                <ShipToAddress />
              ) : (
                // User
                <div>
                  <MediaLayout>
                    <AdditionalAddress loading={pageLoading} addressList={addressList} {...rest} />
                  </MediaLayout>
                  <MediaLayout type="mobile">
                    <ul className="mobile-address-list">
                      {addressList.map((item) => {
                        return (
                          <li key={item.key}>
                            <div className="mobile-address-item-header">
                              <CommonCheckbox checked={item.default_billing} />
                              <b>Make Default Address</b>
                              <Button
                                type="link"
                                onClick={() => {
                                  rest?.showEditAddressModal?.(item)
                                }}>
                                Edit
                              </Button>
                            </div>
                            <div>
                              <b>Company</b>
                              <span>
                                {item.company}
                                {` ${item.firstname}  ${item.lastname}`}
                              </span>
                            </div>
                            <div>
                              <b>Address</b>
                              <div>
                                {item.street.map((add, index) => {
                                  return (
                                    <span key={add}>
                                      {index > add.length - 1 && add !== '' ? `${add}, ` : add}
                                    </span>
                                  )
                                })}
                              </div>
                            </div>
                            <div>
                              <b>City</b>
                              <span>{item.city}</span>
                            </div>
                            <div>
                              <b>State</b>
                              <span>{item.region.region}</span>
                            </div>
                            <div>
                              <b>Zip</b>
                              <span>{item.postcode}</span>
                            </div>
                            <div>
                              <b>Phone</b>
                              <span>{item.telephone}</span>
                            </div>
                          </li>
                        )
                      })}
                    </ul>
                  </MediaLayout>
                </div>
              )}

              {/* New Address Modal */}
              {addressModal.visible && (
                <AddressModal addressData={addressModal} loading={pageLoading} {...rest} />
              )}

              {/* Edit Address Modal */}
              {rest?.editAddressModal?.visible && (
                <AddressModal
                  addressData={rest?.editAddressModal}
                  loading={pageLoading}
                  {...rest}
                />
              )}
            </>
          )}
        </CommonLoading>
      </StyledAddressLayout>
    </CommonAccountPageLayout>
  )
}

export default AddressLayout
