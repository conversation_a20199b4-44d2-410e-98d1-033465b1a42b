<?php
namespace Silksoftwarecorp\RealTimeB2BPricing\Controller\Index;
use GuzzleHttp\ClientFactory;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\App\ObjectManager;
use Silksoftwarecorp\RealTimeB2BPricing\Service\RealtimeB2BPriceApiService;
use Silksoftwarecorp\RealTimeB2BPricing\Service\RealtimeB2BPriceService;
use Silksoftwarecorp\RealTimeB2BPricing\Api\RealtimeB2BPriceServiceInterface;

class Index extends \Magento\Framework\App\Action\Action
{
    protected $resultPageFactory;

    public function __construct(
        \Magento\Framework\App\Action\Context $context,
        \Magento\Framework\View\Result\PageFactory $resultPageFactory)
    {
        $this->resultPageFactory = $resultPageFactory;
        parent::__construct($context);
    }

    public function execute()
    {
        $clientFactory = \Magento\Framework\App\ObjectManager::getInstance()->get(ClientFactory::class);
        $client = $clientFactory->create([
            'data' => [
                'base_uri'        => 'https://edi.blevinsinc.com/',
                'timeout'         => 10,
                'allow_redirects' => false,
                'proxy'           => '127.0.0.1:8888',
                'Verify'=>false,
                'headers' => [
                    'CustomerID' => '36787',
                    'ItemID' => '0100235,0100360',
                    'Location' => '100',
                    'Password' => 'kjG52q-avjhs62JwP-&163Rw!',
                    'RequestType' => 'ItemInfo',
                    'UserName' => 'Bl3y6Wo9'
                ]
            ]
        ]);
////
//        $response = $client->get('https://edi.blevinsinc.com/api', [
//            'proxy' => '127.0.0.1:8888',
//            'timeout' => 10,
//            'verify' => false,
//            'headers' => [
//                'CustomerID' => '36787',
//                'ItemID' => '0100235,0100360',
//                'Location' => '100',
//                'Password' => 'kjG52q-avjhs62JwP-&163Rw!',
//                'RequestType' => 'ItemInfo',
//                'UserName' => 'Bl3y6Wo9'
//            ]
//        ]);
//        $body = $response->getBody()->getContents();
//
//        print_r($body);

        $customerId = $this->getRequest()->getParam('id', 74984);
        $skus = ['0120049', '0100235', '0100360', '0100361', '0100498', '0100500', '0100503', '0010'];
        $skus = ['0100001','0100002','0100102','0100235','0100360','0100361','0100363','0100364','0100498','0100500','0100501','0100503','0100504','0100505','0100507','0100509','0100510','0100511','0100519','0100520','0100521','0100525','0100526','0100531','0100532','0100535','0100540','0100541','0100546','0100547','0100548','0100549','0100550','0100551','0100552','0100553','0100554','0100555','0100556','0100557','0100558','0100559','0100560','0100561','0100562','0100563','0100564','0100580','0100597','0100598','0100601','0100603','0100605','0100607','0100624','0100625','0100628','0100629','0100631','0100632','0100634','0100635','0100636','0100637','0100638','0100647','0100648','0100649','0100653','0100663','0100667','0100676','0100680','0100693','0100694','0100696','0100908','0100909','0100913','0100914','0100915','0100916','0100917','0100918','0100919','0100920','0100925','0100926','0101038','0101039','0101041','0101042','0101043','0101044','0101045','0101087','0101088','0101089','0101090','0101092','0101094','0101095','0101096','0101100','0101106','0101107','0101108','0101125','0101202','0101203','0101204','0101205','0101206','0101208','0101209','0101212','0101213','0101221','0101222','0101223','0101224','0101225','0101226','0101227','0101238','0101240','0101241','0101241USS','0101242','0101243','0101244','0101245','0101245USS','0101246','0101247','0101248','0101249','0101254','0101255','0101261'];
        $skus = ['0100001','0100002','0100102','0100235','0100360','0100361','0100363','0100364','0100498','0100500','0100501','0100503','0100504','0100505','0100507','0100509','0100510','0100511','0100519','0100520','0100521','0100525','0100526','0100531','0100532','0100535','0100540','0100541','0100546','0100547','0100548','0100549','0100550','0100551','0100552','0100553','0100554','0100555','0100556','0100557','0100558','0100559','0100560','0100561','0100562','0100563','0100564','0100580','0100597','0100598'];
        $b2bPricingService = ObjectManager::getInstance()->get(RealtimeB2BPriceServiceInterface::class);
        $priceItemResult = $b2bPricingService->getPriceItems($customerId, '100', $skus);

        $data = [];
        foreach ($priceItemResult->getItems() as $priceItem) {
            $data[] = $priceItem->toArray();
        }

        print_r(json_encode($data));

//        $customerRepository = \Magento\Framework\App\ObjectManager::getInstance()->get(CustomerRepositoryInterface::class);
//        $customer = $customerRepository->getById(18);
    }
}
