<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */

namespace Silksoftwarecorp\AkeneoConnector\Helper;

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Store\Model\ScopeInterface;

class Data extends AbstractHelper
{
    /**
     * Configuration path for clear product name when missing
     */
    private const XML_PATH_CLEAR_PRODUCT_NAME_WHEN_MISSING = 'akeneo_connector/product/clear_product_name_when_missing';

    /**
     * Check if product name should be cleared when missing
     *
     * @return bool
     */
    public function shouldClearProductNameWhenMissing(): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_CLEAR_PRODUCT_NAME_WHEN_MISSING,
            ScopeInterface::SCOPE_STORE
        );
    }
}
