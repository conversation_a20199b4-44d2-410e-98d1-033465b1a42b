<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */
declare(strict_types=1);

namespace Silksoftwarecorp\ConditionalPaymentGraphQl\Model\Payment;

use Magento\Payment\Model\Method\AbstractMethod;

/**
 * Cash on Delivery payment method
 */
class CashOnDelivery extends AbstractMethod
{
    /**
     * Payment method code
     *
     * @var string
     */
    protected $_code = 'cashondelivery';

    /**
     * Payment method title
     *
     * @var string
     */
    protected $_title = 'Cash on Delivery';

    /**
     * Availability option
     *
     * @var bool
     */
    protected $_isOffline = true;

    /**
     * Can use for checkout
     *
     * @var bool
     */
    protected $_canUseCheckout = true;

    /**
     * Can use for multishipping
     *
     * @var bool
     */
    protected $_canUseForMultishipping = true;

    /**
     * Check if payment method is available for the cart
     *
     * @param \Magento\Quote\Api\Data\CartInterface|null $quote
     * @return bool
     */
    public function isAvailable(\Magento\Quote\Api\Data\CartInterface $quote = null)
    {
        if (!parent::isAvailable($quote)) {
            return false;
        }

        if (!$quote) {
            return false;
        }

        /** @var \Magento\Quote\Model\Quote $quote */
        // Check if cart is virtual (no shipping needed)
        if ($quote->isVirtual()) {
            return false;
        }
        
        // Check if the selected shipping method is Blevins Delivery (flatrate)
        $shippingAddress = $quote->getShippingAddress();
        if (!$shippingAddress) {
            return false;
        }
        $selectedShippingMethod = $shippingAddress->getShippingMethod();

        if (!$selectedShippingMethod) {
            return false;
        }

        $shippingMethodParts = explode('_', $selectedShippingMethod);
        $carrierCode = $shippingMethodParts[0] ?? '';

        // Only available for flatrate (Blevins Delivery)
        return $carrierCode === 'flatrate';
    }
}
