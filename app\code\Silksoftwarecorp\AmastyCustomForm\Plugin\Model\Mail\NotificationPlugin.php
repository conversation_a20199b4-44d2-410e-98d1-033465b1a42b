<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */

namespace Silksoftwarecorp\AmastyCustomForm\Plugin\Model\Mail;

use Amasty\Customform\Model\Mail\Notification;
use Amasty\Customform\Model\Form;
use Amasty\Customform\Model\Answer;
use Magento\Framework\App\RequestInterface;
use Psr\Log\LoggerInterface;
use Silksoftwarecorp\AmastyCustomForm\Model\Form\AdminNotification\FormEmailResolver;
use Silksoftwarecorp\AmastyCustomForm\Helper\Data as Helper;

class NotificationPlugin
{
    /**
     * @var RequestInterface
     */
    private $request;

    /**
     * @var Helper
     */
    private $helper;

    /**
     * @var FormEmailResolver
     */
    private $formEmailResolver;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var array
     */
    private $originalSendToValues = [];

    /**
     * @param RequestInterface $request
     * @param Helper $helper
     * @param FormEmailResolver $formEmailResolver
     * @param LoggerInterface $logger
     */
    public function __construct(
        RequestInterface $request,
        Helper $helper,
        FormEmailResolver $formEmailResolver,
        LoggerInterface $logger
    ) {
        $this->request = $request;
        $this->helper = $helper;
        $this->formEmailResolver = $formEmailResolver;
        $this->logger = $logger;
    }

    /**
     * Before sendNotifications - modify form model's sendTo based on our configuration
     *
     * @param Notification $subject
     * @param Form $formModel
     * @param Answer $model
     * @return array
     */
    public function beforeSendNotifications(
        Notification $subject,
        Form $formModel,
        Answer $model
    ): array {
        if ($this->helper->enableFormCustomEmailRecipient()) {
            $locationId = $this->helper->getLocationId();
            $formId = (string)$formModel->getFormId();

            if ($locationId && $formId) {
                // Get matched email and update if found
                $matchedEmail = $this->formEmailResolver->resolve(
                    $locationId,
                    $formId
                );

                if ($matchedEmail) {
                    // Store original value for restoration
                    $this->originalSendToValues[$formId] = $formModel->getSendTo();

                    $formModel->setSendTo($matchedEmail);
                    $this->logger->info('[Forms: Email Recipient]: Email recipient changed', [
                        'form_id' => $formId,
                        'location_id' => $locationId,
                        'new_email' => $matchedEmail
                    ]);
                }
            }
        }

        return [$formModel, $model];
    }

    /**
     * After sendNotifications - restore original sendTo value
     *
     * @param Notification $subject
     * @param mixed $result
     * @param Form $formModel
     * @param Answer $model
     * @return void
     */
    public function afterSendNotifications(
        Notification $subject,
        $result,
        Form $formModel,
        Answer $model
    ): void {
        $formId = $formModel->getFormId();

        if (isset($this->originalSendToValues[$formId])) {
            $originalSendTo = $this->originalSendToValues[$formId];
            $formModel->setSendTo($originalSendTo);

            // Clean up stored value
            unset($this->originalSendToValues[$formId]);

            $this->logger->debug('[Forms: Email Recipient]: SendTo restored', [
                'form_id' => $formId,
                'current_send_to' => $formModel->getSendTo(),
                'original_send_to' => $originalSendTo
            ]);
        }
    }
}
