import { Upload } from 'antd'
import styled from '@emotion/styled'

export const StyledCommonUpload = styled(Upload)`
  display: flex;
  height: 44px;
  border: 1px solid #d9d9d9;
  border-radius: 3px;

  .choose-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 12px;
    width: 128px;
    height: 100%;
    background: #e3e2e2;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    letter-spacing: 0.01em;
    color: #191919;
    cursor: pointer;

    svg {
      margin-right: 4px;
    }
  }

  .${({ theme }) => theme.namespace}-upload-list {
    padding-top: 3px;
  }
`
