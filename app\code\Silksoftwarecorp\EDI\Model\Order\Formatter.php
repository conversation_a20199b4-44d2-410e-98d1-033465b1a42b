<?php

namespace Silksoftwarecorp\EDI\Model\Order;

use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\DataObject;
use Silksoftwarecorp\EDI\Model\Order\ShippingMethodResolver;

class Formatter
{
    /**
     * @var ProductRepositoryInterface
     */
    private $productRepository;

    /**
     * @var SearchCriteriaBuilder
     */
    private $searchCriteriaBuilder;

    /**
     * @var ShippingMethodResolver
     */
    private $shippingMethodResolver;

    protected array $_productList = [];

    /**
     * @param ProductRepositoryInterface $productRepository
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param ShippingMethodResolver $shippingMethodResolver
     */
    public function __construct(
        ProductRepositoryInterface $productRepository,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        ShippingMethodResolver $shippingMethodResolver
    ) {
        $this->productRepository = $productRepository;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->shippingMethodResolver = $shippingMethodResolver;
    }

    public function format(array $data): array
    {
        if (empty($data)) {
            return [];
        }

        $order = new DataObject($data);

        $data = [
            'order_number' => $order->getData('OrderNo'),
            'po_number' => $order->getData('PONo'),
            'order_date' => $order->getData('OrderDate'),
            'customer_id' => $order->getData('CustomerId'),
            'completed' => $order->getData('Completed'),
            'canceled' => $order->getData('Canceled'),
            'carrier' => $order->getData('Carrier'),
            'shipping_method' => $this->formatShippingMethod($order),
            'tracking_number' => $order->getData('TrackingNo'),
            'freight' => (float)$order->getData('Freight'),
            'sales_tax' => (float)$order->getData('SalesTax'),
            'payment_method' => $order->getData('PaymentMethod'),
            'total_amount' => (float)$order->getData('TotalAmount'),
            'status' =>  $order->getData('OrderStatus'),
            'billing_address' => $this->formatBillTo($order->getData('BillTo')),
            'shipping_address' => $this->formatShipTo($order->getData('ShipTo')),
            'ship_from' => $this->formatShipFrom($order->getData('ShipFrom')),
            'ship_to' => [
                'id' => $order->getData('ShipToID'),
                'name' => $order->getData('ShipToName'),
            ],
            'items' => $this->formatOrderLines($order->getData('OrderLines')),
        ];

        $subTotal = 0;
        foreach ($data['items'] as $item) {
            $subTotal += (float)$item['row_total'] ?? 0;
        }

        $data['subtotal'] = $subTotal;

        return $data;
    }

    protected function formatShippingMethod(DataObject $order): ?array
    {
        $carrier = $order->getData('Carrier');
        if ($carrier) {
            return $this->shippingMethodResolver->resolve((string)$carrier);
        }

        return null;
    }

    protected function formatBillTo(?array $data): ?array
    {
        if (!$data) {
            return null;
        }

        $billTo = new DataObject($data);
        $address = [];
        foreach(['BillToAddress1', 'BillToAddress2'] as $key) {
            if ($billTo->hasData($key) && !empty($billTo->getData($key))) {
                $address[] = $billTo->getData($key);
            }
        }

        return [
            'id' => $billTo->getData('CustomerID'),
            'name' => $billTo->getData('BillToName'),
            'address' => $address,
            'city' => $billTo->getData('BillToCity'),
            'state' => $billTo->getData('BillToState'),
            'country' => $billTo->getData('BillToCountry'),
            'phone' => $billTo->getData('BillToPhone'),
            'email' => $billTo->getData('BillToEmail'),
            'zip_code' => $billTo->getData('BillToZipcode'),
        ];
    }

    protected function formatShipTo(?array $data): ?array
    {
        if (!$data) {
            return null;
        }

        $shipTo = new DataObject($data);
        $address = [];
        foreach(['ShipToAddress1', 'ShipToAddress2', 'ShipToAddress3'] as $key) {
            if ($shipTo->hasData($key) && !empty($shipTo->getData($key))) {
                $address[] = $shipTo->getData($key);
            }
        }

        return [
            'id' => $shipTo->getData('ShipToID'),
            'name' => $shipTo->getData('ShipToName'),
            'address' => $address,
            'city' => $shipTo->getData('ShipToCity'),
            'state' => $shipTo->getData('ShipToState'),
            'zip_code' => $shipTo->getData('ShipToZipcode'),
            'country' => $shipTo->getData('ShipToCountry'),
            'phone' => $shipTo->getData('ShipToPhone'),
            'email' => $shipTo->getData('ShipToEmail'),
        ];
    }

    protected function formatShipFrom(?array $data): ?array
    {
        if (!$data) {
            return null;
        }

        $ShipFrom = new DataObject($data);
        $address = [];
        foreach(['ShipFromAddress1', 'ShipFromAddress2'] as $key) {
            if ($ShipFrom->hasData($key) && !empty($ShipFrom->getData($key))) {
                $address[] = $ShipFrom->getData($key);
            }
        }

        return [
            'name' => $ShipFrom->getData('ShipFromName'),
            'address' => $address,
            'city' => $ShipFrom->getData('ShipFromCity'),
            'state' => $ShipFrom->getData('ShipFromState'),
            'zip_code' => $ShipFrom->getData('ShipFromZipcode'),
            'country' => $ShipFrom->getData('ShipFromCountry'),
        ];
    }

    protected function formatOrderLines(?array $orderLines): array
    {
        if (!$orderLines) {
            return [];
        }

        $this->fetchProducts($orderLines);

        $items = [];
        foreach ($orderLines as $orderLine) {
            $item = $this->formatOrderLine($orderLine);
            if ($item) {
                $items[] = $item;
            }
        }

        return $items;
    }

    protected function formatOrderLine(?array $data): ?array
    {
        if (!$data) {
            return null;
        }

        $orderLine = new DataObject($data);
        return [
            'item_id' => $orderLine->getData('LineNo'),
            'product_sku' => $orderLine->getData('ItemID'),
            'product_name' => $orderLine->getData('ItemDesc'),
            'bin_id' => $orderLine->getData('BinID'),
            'quantity_ordered' => (int)$orderLine->getData('QtyOrdered'),
            'status' => $orderLine->getData('LineStatus'),
            'um' => $orderLine->getData('UM'),
            'product_sale_price' => [
                'value' => (float)$orderLine->getData('UnitPrice'),
                'currency' => 'USD',
            ],
            'row_total' => (float)$orderLine->getData('ExtendedPrice'),
            'description' => $orderLine->getData('ExtendedDesc'),
            'tracking_number' => $orderLine->getData('TrackingNo'),
            'associatedProduct' => $this->_productList[$orderLine->getData('ItemID')] ?? '',
        ];
    }

    /**
     * Fetch associated products for order Lines
     *
     * @param array $orderLines
     * @return array
     */
    private function fetchProducts(array $orderLines): array
    {
        $skus = array_map(
            function ($orderLine) {
                return $orderLine['ItemID'] ?? '';
            },
            $orderLines
        );

        $searchCriteria = $this->searchCriteriaBuilder
            ->addFilter('sku', array_filter($skus), 'in')
            ->create();
        $products = $this->productRepository->getList($searchCriteria)->getItems();
        foreach ($products as $product) {
            $this->_productList[$product->getSku()] = $product;
        }

        return $this->_productList;
    }
}
