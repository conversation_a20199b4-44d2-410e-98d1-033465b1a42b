import Link from 'next/link'
import { nanoid } from 'nanoid'
import { <PERSON><PERSON><PERSON>, Drawer } from 'antd'
import { useSelector } from 'react-redux'
import { events } from '@ranger-theme/utils'
import { useCallback, useEffect, useMemo, useState } from 'react'

import { StyledSubMenu } from './styled'

const SubMenu = ({
  title,
  menus,
  menuOpen
}: {
  title: string
  menus: any[]
  menuOpen: boolean
}) => {
  const storeConfig = useSelector((state: any) => state.app.storeConfig)

  const [visible, setVisible] = useState<boolean>(false)

  const suffix = useMemo(() => {
    return storeConfig?.category_url ?? ''
  }, [storeConfig])

  const onClose = useCallback(() => {
    setVisible(false)
  }, [])

  const onOpen = useCallback(() => {
    setVisible(true)
  }, [])

  const handleHideNav = useCallback(() => {
    setVisible(false)
    events.emit('hideNav')
  }, [])

  const expandIcon = useCallback(
    (panelProps) => (
      <svg width="9px" height="9px" fill="currentColor" aria-hidden="true" focusable="false">
        <use xlinkHref={panelProps.isActive ? '#icon-collapse-hide-dark' : '#icon-collapse-dark'} />
      </svg>
    ),
    []
  )

  useEffect(() => {
    if (!menuOpen) {
      setVisible(false)
    }
  }, [menuOpen])

  return (
    <>
      <div className="arrow-wrapper" aria-hidden="true" onClick={onOpen}>
        <svg className="arrow" width="8px" height="12px" fill="currentColor" focusable="false">
          <use xlinkHref="#icon-next" />
        </svg>
      </div>
      <Drawer
        rootClassName="mb-navigation"
        placement="left"
        closable={false}
        open={menuOpen && visible}
        zIndex={menuOpen ? 998 : 1000}
        onClose={onClose}
        style={{ marginTop: 172, height: 'calc(100vh - 172px)', width: '100vw' }}>
        <StyledSubMenu>
          {title && (
            <>
              <p className="back" aria-hidden onClick={onClose}>
                <svg width="10px" height="10px" aria-hidden="true" focusable="false">
                  <use xlinkHref="#icon-back" />
                </svg>
                <span>Back to Main Menu</span>
              </p>
              <h3>
                <span dangerouslySetInnerHTML={{ __html: title }} />
              </h3>
            </>
          )}
          <div className="menus">
            <Collapse expandIconPosition="end" expandIcon={expandIcon}>
              {menus.map((submenu) => {
                const { uid, url_path, name } = submenu
                const submenuHref = `/${url_path}${suffix}`
                const submenuChildren = submenu?.children ?? []
                const hasChildren = submenuChildren.length > 0

                return hasChildren ? (
                  <Collapse.Panel
                    key={submenu.uid}
                    className="lowmenu-item"
                    header={
                      <>
                        <Link className="title" href={submenuHref} title={name}>
                          <span dangerouslySetInnerHTML={{ __html: name }} />
                        </Link>
                        {/* {submenu.type === 'wrapper' && <h5 className="title" dangerouslySetInnerHTML={{__html: name}}/>} */}
                      </>
                    }>
                    <div className="content">
                      {submenuChildren.map((lowmenu) => {
                        return (
                          <div key={nanoid()} className="submenu-item">
                            {/* {['custom_url', 'product', 'category'].includes(lowmenu.type) && (
                                <Link
                                  href={`/${lowmenu.url_path}${suffix}`}
                                  title={lowmenu.name}
                                  onClick={handleHideNav}>
                                  <span dangerouslySetInnerHTML={{ __html: lowmenu.name }} />
                                </Link>
                              )} */}
                            <Link
                              href={`/${lowmenu.url_path}${suffix}`}
                              title={lowmenu.name}
                              onClick={handleHideNav}>
                              <span dangerouslySetInnerHTML={{ __html: lowmenu.name }} />
                            </Link>
                          </div>
                        )
                      })}
                      {submenuChildren.map((lowmenu) => {
                        return (
                          <div key={nanoid()} className="submenu-item">
                            {/* {['custom_url', 'product', 'category'].includes(lowmenu.type) && (
                                <Link
                                  href={`/${lowmenu.url_path}${suffix}`}
                                  title={lowmenu.name}
                                  onClick={handleHideNav}>
                                  <span dangerouslySetInnerHTML={{ __html: lowmenu.name }} />
                                </Link>
                              )} */}
                            <Link
                              href={`/${lowmenu.url_path}${suffix}`}
                              title={lowmenu.name}
                              onClick={handleHideNav}>
                              <span dangerouslySetInnerHTML={{ __html: lowmenu.name }} />
                            </Link>
                          </div>
                        )
                      })}
                    </div>
                  </Collapse.Panel>
                ) : (
                  <div className="lowmenu-item lowmenu-item-only" key={uid}>
                    <Link className="title" href={submenuHref} title={name} onClick={handleHideNav}>
                      <span dangerouslySetInnerHTML={{ __html: name }} />
                    </Link>
                  </div>
                )
              })}
            </Collapse>
          </div>
        </StyledSubMenu>
      </Drawer>
    </>
  )
}

export default SubMenu
