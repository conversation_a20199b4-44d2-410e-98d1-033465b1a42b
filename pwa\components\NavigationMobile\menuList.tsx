import Link from 'next/link'
import { Drawer } from 'antd'
import { useCallback, memo, useState } from 'react'
import { events } from '@ranger-theme/utils'

import { useNavigation } from '@/hooks/Navigation'
import CommonLoading from '@/components/Common/CommonLoading'

import SubMenu from './subMenu'
import { StyledMbNavigation } from './styled'

const MenuList = ({ menuOpen }: any) => {
  const { categories, suffix, loading } = useNavigation()

  const [open, setOpen] = useState<boolean>(false)

  const handleHideNav = useCallback(() => {
    events.emit('hideNav')
  }, [])

  const openAllProductsDrawer = () => {
    setOpen(true)
  }

  const onCloseAllProductsDrawer = () => {
    setOpen(false)
  }

  return (
    <StyledMbNavigation>
      <div className="mb-navigation-header">
        <div>
          <svg width="32px" height="32px" fill="currentColor" focusable="false">
            <use xlinkHref="#icon-menu-install" />
          </svg>
          Install
        </div>
        <div>
          <svg width="32px" height="32px" fill="currentColor" focusable="false">
            <use xlinkHref="#icon-menu-payment" />
          </svg>
          Make A Payment
        </div>
        <div>
          <svg width="32px" height="32px" fill="currentColor" focusable="false">
            <use xlinkHref="#icon-menu-resources" />
          </svg>
          Resources
        </div>
        <div>
          <svg width="32px" height="32px" fill="currentColor" focusable="false">
            <use xlinkHref="#icon-menu-order" />
          </svg>
          Order Pad
        </div>
      </div>

      {loading && <CommonLoading spinning />}

      {categories?.map((menu: any) => {
        const { uid, url_path, name } = menu
        const submenus: any[] = menu?.children ?? []
        const hasSubmenus = submenus.length > 0
        const titleColor = menu?.label_text_color ?? '#191919'
        const isAllProducts = uid === 'all-products'

        return (
          <div className="item" key={uid}>
            {url_path ? (
              <Link
                className="first-menu"
                href={`/${url_path}${suffix}`}
                title={name}
                onClick={handleHideNav}>
                <span dangerouslySetInnerHTML={{ __html: name }} style={{ color: titleColor }} />
              </Link>
            ) : (
              <div
                className="first-menu"
                dangerouslySetInnerHTML={{ __html: name }}
                style={{ color: titleColor }}
              />
            )}
            {hasSubmenus && (
              <>
                {/* All Products Drawer */}
                {isAllProducts ? (
                  <div id="all-products-content">
                    <div
                      aria-hidden="true"
                      className="arrow-wrapper"
                      onClick={openAllProductsDrawer}>
                      <svg
                        className="arrow"
                        width="8px"
                        height="12px"
                        fill="currentColor"
                        focusable="false">
                        <use xlinkHref="#icon-next" />
                      </svg>
                    </div>
                    <Drawer
                      rootClassName="mb-navigation"
                      placement="left"
                      open={open}
                      closable={false}
                      width="100%"
                      zIndex={open ? 998 : 1000}
                      onClose={onCloseAllProductsDrawer}
                      style={{ marginTop: 172, height: 'calc(100vh - 172px)', width: '100vw' }}>
                      <StyledMbNavigation>
                        <div className="all-products-drawer-header">
                          <p className="back" aria-hidden onClick={onCloseAllProductsDrawer}>
                            <svg width="10px" height="10px" aria-hidden="true" focusable="false">
                              <use xlinkHref="#icon-back" />
                            </svg>
                            <span>Back to Main Menu</span>
                          </p>
                          <h3>
                            <span dangerouslySetInnerHTML={{ __html: name }} />
                          </h3>
                        </div>
                        <div>
                          {submenus.map((subMenu) => {
                            const children: any[] = subMenu?.children ?? []
                            const hasChildren = children.length > 0
                            const nameColor = subMenu?.label_text_color ?? '#191919'

                            return (
                              <div className="item" key={subMenu.uid}>
                                {subMenu.url_path ? (
                                  <Link
                                    className="first-menu"
                                    href={`/${subMenu.url_path}${suffix}`}
                                    title={subMenu.name}
                                    onClick={handleHideNav}>
                                    <span
                                      dangerouslySetInnerHTML={{ __html: subMenu.name }}
                                      style={{ color: nameColor }}
                                    />
                                  </Link>
                                ) : (
                                  <div
                                    className="first-menu"
                                    dangerouslySetInnerHTML={{ __html: subMenu.name }}
                                    style={{ color: nameColor }}
                                  />
                                )}
                                {hasChildren && (
                                  <SubMenu
                                    title={subMenu.name}
                                    menus={children}
                                    menuOpen={menuOpen}
                                  />
                                )}
                              </div>
                            )
                          })}
                        </div>
                      </StyledMbNavigation>
                    </Drawer>
                  </div>
                ) : (
                  <SubMenu title={name} menus={submenus} menuOpen={menuOpen} />
                )}
              </>
            )}
          </div>
        )
      })}
    </StyledMbNavigation>
  )
}

export default memo(MenuList)
