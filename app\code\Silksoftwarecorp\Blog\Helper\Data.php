<?php

namespace Silksoftwarecorp\Blog\Helper;

use Exception;
use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Store\Model\ScopeInterface;
use Magento\Cms\Api\BlockRepositoryInterface;
use Magento\Cms\Model\Template\FilterProvider;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Psr\Log\LoggerInterface;

class Data extends AbstractHelper
{
    const XML_PATH_BLOG_HOME_PAGE_DESCRIPTION = 'amblog/search_engine/home_page_description';

    /**
     * @var BlockRepositoryInterface
     */
    private $blockRepository;

    /**
     * @var FilterProvider
     */
    private $filterProvider;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @param Context $context
     * @param BlockRepositoryInterface $blockRepository
     * @param FilterProvider $filterProvider
     * @param LoggerInterface $logger
     */
    public function __construct(
        Context $context,
        BlockRepositoryInterface $blockRepository,
        FilterProvider $filterProvider,
        LoggerInterface $logger
    ) {
        parent::__construct($context);
        $this->blockRepository = $blockRepository;
        $this->filterProvider = $filterProvider;
        $this->logger = $logger;
    }

    /**
     * Get Blog Home Page Description Block Id
     *
     * @param int|null $storeId
     * @return string|null
     */
    public function getBlogHomePageDescriptionBlockId($storeId = null): ?string
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_BLOG_HOME_PAGE_DESCRIPTION,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Get Blog Home Page Description HTML Content
     *
     * @param null $storeId
     * @return string|null
     * @throws Exception
     */
    public function getBlogHomePageDescription($storeId = null): ?string
    {
        $blockId = $this->getBlogHomePageDescriptionBlockId($storeId);
        if (!$blockId) {
            return null;
        }

        try {
            $block = $this->blockRepository->getById($blockId);

            if (!$block->isActive()) {
                return null;
            }

            return $this->filterProvider->getBlockFilter()->filter($block->getContent());
        } catch (NoSuchEntityException $e) {
            $this->logger->warning(__('CMS Block not found: block_id = %1', $blockId));
            return null;
        } catch (LocalizedException $e) {
            $this->logger->error(__(
                'Error loading CMS Block: block_id = %1, Error: %2',
                $blockId,
                $e->getMessage()
            ));

            return null;
        }
    }
}
