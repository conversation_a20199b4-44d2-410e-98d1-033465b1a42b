<?php

declare(strict_types=1);

namespace Silksoftwarecorp\UnifiedArPayment\Model\ResourceModel;

use Magento\Framework\Model\ResourceModel\Db\AbstractDb;
use Magento\Framework\Model\ResourceModel\Db\Context;

class BlevinsOrderPayment extends AbstractDb
{
    public const MAIN_TABLE = 'blevins_order_payment';

    public function __construct(
        Context $context,
                $connectionName = null
    ) {
        parent::__construct($context, $connectionName);
    }

    protected function _construct()
    {
        $this->_init(self::MAIN_TABLE, 'id');
    }

    public function getBlePaymentDataByQuoteId($quoteId)
    {
        $select = $this->getConnection()->select()
            ->from($this->getMainTable() . ' AS main_table')
            ->where('quote_id=?', $quoteId);

        return (array)$this->getConnection()->fetchRow($select);
    }

    public function updateBlePaymentData($rowId, $paymentAccountId, $allowed, $surchargeAmount)
    {
        $table = $this->getMainTable();
        $bindData = [
            "payment_account_id" => $paymentAccountId,
            "unified_ar_surcharge_allowed" => $allowed,
            "unified_ar_surcharge_amount" => $surchargeAmount
        ];
        $where = 'id = ' . $rowId;
        $this->getConnection()->update($table, $bindData, $where);
    }

    public function updateBlePaymentAuthorizationData($quoteId, $authorizationData)
    {
        $table = $this->getMainTable();
        $bindData = [
            "unified_ar_authorization_data" => $authorizationData
        ];
        $where = 'quote_id = ' . $quoteId;
        $this->getConnection()->update($table, $bindData, $where);
    }

    public function updateBlePaymentReturnData($quoteId, $returnData)
    {
        $table = $this->getMainTable();
        $bindData = [
            "unified_ar_return_data" => $returnData
        ];
        $where = 'quote_id = ' . $quoteId;
        $this->getConnection()->update($table, $bindData, $where);
    }

    public function getBlePaymentByOrderId($orderId)
    {
        $select = $this->getConnection()->select()
            ->from($this->getMainTable() . ' AS main_table')
            ->where('order_id=?', $orderId);

        return (array)$this->getConnection()->fetchRow($select);
    }

    public function updateBlePaymentDataByQuoteId($quoteId, $orderId)
    {
        $table = $this->getMainTable();
        $bindData = [
            "order_id" => $orderId,
        ];
        $where = 'quote_id = ' . $quoteId;
        $this->getConnection()->update($table, $bindData, $where);
    }
}
