<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */

namespace Silksoftwarecorp\AmastyCustomForm\Block\Adminhtml\Form\Field;

use Magento\Backend\Block\Template\Context;
use Magento\Config\Block\System\Config\Form\Field\FieldArray\AbstractFieldArray;
use Magento\Framework\App\ObjectManager;
use Magento\Framework\DataObject;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\View\Helper\SecureHtmlRenderer;
use Silksoftwarecorp\AmastyCustomForm\Block\Adminhtml\Form\Field\Renderer\Branch as BranchRenderer;
use Silksoftwarecorp\AmastyCustomForm\Block\Adminhtml\Form\Field\Renderer\Form as FormRenderer;

class EmailRecipients extends AbstractFieldArray
{
    /**
     * @var BranchRenderer
     */
    protected $_branchRenderer;

    /**
     * @var FormRenderer
     */
    protected $_formRenderer;

    /**
     * Prepare to render
     *
     * @return void
     * @throws LocalizedException
     */
    protected function _prepareToRender()
    {
        $this->addColumn(
            'branch',
            [
                'label' => __('Branch'),
                'renderer' => $this->_getBranchRenderer(),
                'class' => 'required-entry',
            ]
        );

        $this->addColumn(
            'form',
            [
                'label' => __('Form'),
                'renderer' => $this->_getFormRenderer(),
                'class' => 'required-entry',
            ]
        );

        $this->addColumn(
            'email_recipients',
            [
                'label' => __('Email Recipients'),
                'class' => 'required-entry validate-multiple-emails',
            ]
        );

        $this->_addAfter = false;
        $this->_addButtonLabel = __('Add');
    }

    /**
     * Get branch renderer
     *
     * @return BranchRenderer
     * @throws LocalizedException
     */
    protected function _getBranchRenderer(): BranchRenderer
    {
        if (!$this->_branchRenderer) {
            $this->_branchRenderer = $this->getLayout()->createBlock(
                BranchRenderer::class,
                '',
                ['data' => ['is_render_to_js_template' => true]]
            );

            // Enable multiselect for branch column
            $this->_branchRenderer->setIsMultipleSelect(true);
            $this->_branchRenderer->setClass('branch_select required-entry');
        }

        return $this->_branchRenderer;
    }

    /**
     * Get form renderer
     *
     * @return FormRenderer
     * @throws LocalizedException
     */
    protected function _getFormRenderer(): FormRenderer
    {
        if (!$this->_formRenderer) {
            $this->_formRenderer = $this->getLayout()->createBlock(
                FormRenderer::class,
                '',
                ['data' => ['is_render_to_js_template' => true]]
            );

            $this->_formRenderer->setClass('form_select required-entry');
        }

        return $this->_formRenderer;
    }

    /**
     * Prepare existing row data object
     *
     * @param DataObject $row
     * @return void
     * @throws LocalizedException
     */
    protected function _prepareArrayRow(DataObject $row): void
    {
        parent::_prepareArrayRow($row);

        $optionExtraAttr = $row->getData('option_extra_attrs') ?: [];

        // Handle multi-select branch values (array or comma-separated string)
        $branchValues = $row->getData('branch');
        if (is_string($branchValues)) {
            $branchValues = array_filter(array_map('trim', explode(',', $branchValues)));
        } elseif (!is_array($branchValues)) {
            $branchValues = [];
        }

        foreach ($branchValues as $branchValue) {
            $optionExtraAttr['option_' . $this->_calcBranchHash($branchValue)] = 'selected="selected"';
        }

        // Form remains single-select
        $optionExtraAttr['option_' . $this->_calcFormHash($row->getData('form'))] = 'selected="selected"';

        $row->setData('option_extra_attrs', $optionExtraAttr);
    }

    /**
     * Calculate branch hash
     *
     * @param string $branchCode
     * @return string
     * @throws LocalizedException
     */
    protected function _calcBranchHash($branchCode): string
    {
        return $this->_getBranchRenderer()->calcOptionHash($branchCode);
    }

    /**
     * Calculate form hash
     *
     * @param string $formId
     * @return string
     * @throws LocalizedException
     */
    protected function _calcFormHash($formId): string
    {
        return $this->_getFormRenderer()->calcOptionHash($formId);
    }

    protected function _toHtml()
    {
        $html = parent::_toHtml();
        $script = <<<JS
    require(['jquery'], function($) {
        $(document).ready(function() {
            setTimeout(function() {
                  $('select.multiselect').each(function() {
                    const select = $(this);
                    const selectedOption = select.find('option:selected').first();

                    if (selectedOption.length) {
                        const optionHeight = select.find('option').first().outerHeight() || 29;
                        const containerHeight = select.innerHeight();
                        const scrollPosition = selectedOption.index() * optionHeight - (containerHeight / 2);

                        select.scrollTop(scrollPosition);
                        setTimeout(() => {
                            console.log('Actual scrollTop:', select.scrollTop());
                        }, 100);
                    }
                });
            }, 1000);
        });
    });
JS;

        $secureRenderer = ObjectManager::getInstance()->get(SecureHtmlRenderer::class);

        $js = $secureRenderer->renderTag(
            'script',
            [],
            $script,
            false
        );

        return $html . $js;
    }
}
