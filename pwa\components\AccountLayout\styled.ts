import styled from '@emotion/styled'

export const StyledAccountLayout = styled.div`
  display: grid;
  margin-top: 56px;
  margin-bottom: 96px;
  grid-template-columns: 292px 1fr;
  grid-column-gap: 129px;
  justify-content: space-between;
  align-items: flex-start;

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    grid-template-columns: 1fr;
    grid-gap: 24px;
    padding: 16px;
    margin-top: 0;
  }
`

export const StyledMenus = styled.div`
  padding-bottom: 8px;
  background-color: #f2f2f2;

  h4 {
    margin: 24px 24px 0;
    padding-bottom: 16px;
    font-size: 22px;
    font-weight: 500;
    line-height: 31px;
    border-bottom: 1px solid #d9d9d9;
  }

  > ul {
    margin: 16px 24px;
    border-bottom: 1px solid #d9d9d9;

    > li {
      position: relative;
      margin-bottom: 16px;

      > a {
        color: var(--color-font);
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
      }

      &.active {
        > a {
          font-weight: 700;
        }

        &::after {
          position: absolute;
          top: 0;
          left: -24px;
          width: 4px;
          height: 24px;
          display: block;
          content: '';
          z-index: 20;
          background-color: #cf102d;
        }
      }
    }

    &:last-of-type {
      border-bottom: 0;
    }
  }
`
