#!/bin/bash

echo "\n================================== Install and configure node - Start =================================="
echo "\n================================== Install and configure PWA - Upgrade node latest version - Start =================================="
unset NPM_CONFIG_PREFIX
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.5/install.sh | bash
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
nvm current
nvm install v18
node -v
echo "\n================================== Install and configure node - Upgrade node latest version - End ==================================\n"
echo "\n================================== Install and configure pnpm - Install pnpm packages - Start  =================================="
npm install pnpm -g
pnpm -v
echo "\n================================== Install and configure pnpm - Install pnpm packages - End  ==================================\n"
echo "\n================================== Install and configure pwa dependencies - Start  =================================="
cd $HOME/pwa/
touch pwa_build.log
touch pwa_install.log
ls -lt
node -v >> pwa_install.log
pnpm install >> pwa_install.log
ls -lt
echo "\n================================== Install and configure pwa dependencies - End  ==================================\n"
echo "\n================================== Build pwa resource - Start  =================================="
pnpm build >> pwa_build.log
echo "\n================================== Build pwa resource - End  ==================================\n"
