<?php

namespace Silksoftwarecorp\ERPCompany\Model\Company;

use Silksoftwarecorp\ERPCompany\Model\ResourceModel\ERPCompany as ERPCompanyResource;
use Silksoftwarecorp\ERPCompany\Model\ERPCompany as ERPCompanyModel;
use Silksoftwarecorp\ERPCompany\Model\ERPCompanyFactory;

class ERPRepository
{
    /**
     * @var array
     */
    private $instances = [];

    public function __construct(
        private readonly ERPCompanyResource $erpCompanyResource,
        private readonly ERPCompanyFactory $erpCompanyFactory
    ) {

    }

    /**
     * Loads ERP company config by company id
     *
     * @param int $companyId
     * @return ERPCompanyModel|null
     */
    public function get(int $companyId): ?ERPCompanyModel
    {
        if (!isset($this->instances[$companyId])) {
            $erpCompany = $this->erpCompanyFactory->create();
            $this->erpCompanyResource->load($erpCompany, $companyId);
            $this->instances[$companyId] = $erpCompany;
        }

        return $this->instances[$companyId] ?? null;
    }

    /**
     * Save ERP Company config
     *
     * @param ERPCompanyModel $erpCompanyModel
     * @return void
     * @throws \Magento\Framework\Exception\AlreadyExistsException
     */
    public function save(ERPCompanyModel $erpCompanyModel): void
    {
        unset($this->instances[$erpCompanyModel->getCompanyId()]);
        $this->erpCompanyResource->save($erpCompanyModel);
    }
}
