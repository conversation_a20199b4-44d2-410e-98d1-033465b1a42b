import { gql } from '@apollo/client'
import type { DocumentNode } from '@apollo/client'

export const GET_CUSTOMER: DocumentNode = gql`
  query getCustomer {
    customer {
      id
      firstname
      lastname
      email
      customer_status
      customer_type
      addresses {
        city
        company
        country_code
        default_billing
        default_shipping
        firstname
        key: id
        lastname
        postcode
        region {
          region
          region_code
          region_id
        }
        region_id
        street
        telephone
      }
    }
  }
`
