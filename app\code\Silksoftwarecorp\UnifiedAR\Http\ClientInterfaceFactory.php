<?php

namespace Silksoftwarecorp\UnifiedAR\Http;

use Magento\Framework\ObjectManagerInterface;

class ClientInterfaceFactory
{
    /**
     * @var ObjectManagerInterface
     */
    protected $objectManager;

    /**
     * @param ObjectManagerInterface $objectManager
     */
    public function __construct(ObjectManagerInterface $objectManager)
    {
        $this->objectManager = $objectManager;
    }

    /**
     * Create HTTP client instance
     * @return ClientInterface
     */
    public function create(): ClientInterface
    {
        return $this->objectManager->create(ClientInterface::class);
    }
}

