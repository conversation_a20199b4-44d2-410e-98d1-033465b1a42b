import styled from '@emotion/styled'

export const StyledBlogs = styled.div`
  > h4 {
    line-height: 31px;
    margin-bottom: 24px;
    color: #1b060f;
    font-family: var(--font-poppins-medium);
    font-size: 22px;
    font-weight: 500;
    text-align: center;
  }
`

export const StyledBlogList = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-column-gap: 24px;
  justify-content: space-between;
  align-items: flex-start;

  .image {
    position: relative;
    height: 164px;
    background-color: #d9d9d9;
  }

  h6 {
    line-height: 24px;
    margin-top: 16px;
    margin-bottom: 8px;
    font-family: var(--font-poppins-bold);
    font-size: 16px;
    font-weight: 600;
  }

  .label {
    position: absolute;
    top: 8px;
    left: 8px;
    width: 78px;
    height: 26px;
    display: flex;
    padding: 4px 8px;
    color: #667071;
    font-family: var(--font-montserrat-bold);
    font-size: 14px;
    font-weight: 700;
    z-index: 20;
    background-color: #fff;
    justify-content: center;
    align-items: center;
  }

  .description {
    line-height: 24px;
    font-family: var(--font-montserrat);
    font-size: 15px;
    font-weight: 400;
  }
`
