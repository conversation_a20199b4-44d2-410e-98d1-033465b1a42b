import styled from '@emotion/styled'

export const StyledCompany = styled.div`
  display: grid;
  margin-top: 56px;
  margin-bottom: 96px;
  grid-template-columns: 1fr 397px;
  grid-column-gap: 130px;
  justify-content: space-between;
  align-items: flex-start;

  .placeholder {
    height: 1228px;
    background-image: url('/images/account.png');
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
  }
`

export const StyledLayout = styled.div`
  h1 {
    margin-bottom: 40px;
    font-family: var(--font-poppins-medium);
    font-size: 40px;
    font-weight: 500;
    line-height: 46px;
  }

  h4 {
    margin-bottom: 24px;
    padding-bottom: 16px;
    font-family: var(--font-poppins-medium);
    font-size: 22px;
    font-weight: 500;
    line-height: 31px;
    border-bottom: 1px solid #d9d9d9;
  }

  .personal {
    padding-bottom: 24px;
  }

  .${({ theme }) => theme.namespace} {
    &-form-item {
      margin-bottom: 24px;
    }

    &-btn {
      width: 252px;
      height: 56px;
      font-family: var(--font-poppins-medium);
      font-size: 16px;
      font-weight: 600;
      line-height: 22px;
      text-transform: uppercase;
      border-radius: 50px;
    }
  }

  .checkbox {
    margin-bottom: 16px;

    .${({ theme }) => theme.namespace} {
      &-form-item-control-input {
        min-height: 22px;
      }
    }
  }
`

export const StyledFormLine = styled.div`
  display: grid;
  grid-template-columns: 1fr 186px 186px;
  grid-column-gap: 24px;
  justify-content: space-between;
  align-items: center;
`
