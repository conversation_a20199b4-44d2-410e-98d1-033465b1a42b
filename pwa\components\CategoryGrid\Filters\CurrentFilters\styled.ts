import styled from '@emotion/styled'

export const StyledCurrentFilters = styled.div`
  .current-filters-title {
    display: flex;
    align-items: center;
    padding-left: 16px;
    margin-bottom: 0;
    width: 100%;
    height: 48px;
    background: #003865;
    font-weight: 700;
    font-size: 18px;
    line-height: 24px;
    letter-spacing: 0;
    color: var(--color-white);
  }

  .current-filters-content {
    padding: 14px;
    width: 100%;
    border: 1px solid #d9d9d9;
    border-top: none;
  }

  .clear-all {
    padding: 0;
    font-weight: 700;
    font-size: 15px;
    line-height: 21px;
    letter-spacing: 0.01em;
    text-decoration: underline;
    text-decoration-style: solid;
    text-decoration-offset: Auto;
    text-decoration-thickness: Auto;
    color: var(--color-primary) !important;
  }
`

export const StyledCurrentFilter = styled.div`
  display: inline-flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 6px 0 10px;
  margin: 0 6px 6px 0;
  height: 36px;
  border-radius: 3px;
  background: rgba(150, 140, 131, 0.25);
  font-weight: 700;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0.03em;

  .current-filter-label {
    padding-right: 3px;
  }

  .current-filter-close {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 20px;
    height: 22px;

    &:hover {
      cursor: pointer;
      background: #ddd;
    }
  }
`
