import styled from '@emotion/styled'

export const StyledTableHeader = styled.header`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  h2 {
    margin-bottom: 0;
    font-weight: 700;
    font-size: 24px;
    line-height: 30px;
    letter-spacing: 0;
    color: #231f20;
  }

  .view-all-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 167px;
    height: 53px;
    font-weight: 700;
    font-size: 16px;
    line-height: 21px;
    letter-spacing: 0.03em;
    text-transform: uppercase;
    border-radius: 3px;
    background: var(--color-bg-base) !important;
    color: #fff !important;

    svg {
      margin-top: -2px;
      margin-left: 8px;
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    h2 {
      flex: 1;
      padding-right: 16px;

      .sub-title {
        display: block;
      }
    }

    .view-all-btn {
      width: initial;
      height: initial;
      font-size: 15px;
      letter-spacing: 0;
      text-decoration: underline;
      text-decoration-style: solid;
      text-decoration-thickness: 0;
      text-transform: initial;
      color: var(--color-primary) !important;
      background: none !important;

      svg {
        display: none;
      }
    }
  }
`
