<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Store:etc/config.xsd">
    <default>
        <edi>
            <general>
                <active>1</active>
                <debug>1</debug>
            </general>
            <api>
                <environment>sandbox</environment>
                <enable_ssl_certificate_verify>0</enable_ssl_certificate_verify>
                <timeout>10</timeout>
                <cache_lifetime>600</cache_lifetime>
                <password backend_model="Magento\Config\Model\Config\Backend\Encrypted"/>
                <sandbox_password backend_model="Magento\Config\Model\Config\Backend\Encrypted"/>
            </api>
            <frequently_ordered_products>
                <enabled>1</enabled>
                <request_type>OrderDetails</request_type>
                <frequency_days>180</frequency_days>
            </frequently_ordered_products>
            <order>
                <enabled>1</enabled>
                <frequency_days>365</frequency_days>
            </order>
        </edi>
    </default>
</config>
