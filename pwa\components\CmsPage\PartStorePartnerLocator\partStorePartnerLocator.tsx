import { useState, useEffect, useCallback, useRef } from 'react'
import { LineContainer } from '@ranger-theme/ui'
import { useSelector } from 'react-redux'
import { GoogleApiWrapper } from 'google-maps-react'

import CommonLoading from '@/components/Common/CommonLoading'
import GoogleMapMarker from '@/components/GoogleMapMarker'

import StoreList from './StoreList'
import { StyledPartStorePartnerLocator } from './styled'

const defaulCenter = { lat: 39.8283, lng: -98.5795 } // US center

const PartStorePartnerLocator = ({ google }) => {
  const googleMapRef = useRef<any>(null)

  const [loading, setLoading] = useState(false)
  const [storeItems, setStoreItems] = useState([])
  const [isRendered, setIsRendered] = useState<boolean>(false)
  const [center, setCenter] = useState<any>(null)

  const handleSetLoading = useCallback((isLoading) => {
    setLoading(isLoading)
  }, [])

  const handleSaveStoreItems = useCallback((items) => {
    setStoreItems(items)
  }, [])

  const handleMoveToCenter = useCallback(() => {
    if (googleMapRef?.current?.map?.panTo && center && googleMapRef?.current?.map?.setZoom) {
      googleMapRef.current.map.panTo(center)
      googleMapRef.current.map.setZoom(4)
    }
  }, [center])

  const handleMoveTo = useCallback((val) => {
    if (googleMapRef?.current?.map?.panTo && googleMapRef?.current?.map?.setZoom) {
      googleMapRef.current.map.panTo(val)
      googleMapRef.current.map.setZoom(7)
    }
  }, [])

  useEffect(() => {
    if (isRendered) {
      try {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            if (position?.coords?.latitude && position.coords?.longitude) {
              setCenter({ lat: position.coords.latitude, lng: position.coords.longitude })
            } else {
              console.error('Get location failed, position: ', position)
              setCenter(defaulCenter)
            }
          },
          (error) => {
            console.error('Get location failed:', error.message)
            setCenter(defaulCenter)
          }
        )
      } catch (e) {
        console.error('catch')
        console.error(e)
      }
    }
  }, [isRendered])

  useEffect(() => {
    setIsRendered(true)
  }, [])

  return isRendered ? (
    <LineContainer>
      <CommonLoading spinning={loading}>
        {center && (
          <StyledPartStorePartnerLocator>
            <StoreList
              center={center}
              google={google}
              storeItems={storeItems}
              handleSaveStoreItems={handleSaveStoreItems}
              handleSetLoading={handleSetLoading}
              handleMoveToCenter={handleMoveToCenter}
              handleMoveTo={handleMoveTo}
            />
            <div className="map">
              {center && (
                <GoogleMapMarker
                  google={google}
                  markers={storeItems}
                  googleMapRef={googleMapRef}
                  center={center}
                />
              )}
            </div>
          </StyledPartStorePartnerLocator>
        )}
      </CommonLoading>
    </LineContainer>
  ) : null
}

const PartStorePage = (props) => {
  const [WrappedMap, setWrappedMap] = useState(null)
  const apiKey = useSelector(
    (state: Store) => state.app.storeConfig?.amasty_store_locator_google_api_key
  )

  useEffect(() => {
    if (apiKey) {
      try {
        const wrapped = GoogleApiWrapper({
          apiKey,
          LoadingContainer: 'Loading...'
        })(PartStorePartnerLocator)
        setWrappedMap(() => wrapped) // Save the dynamically created components
      } catch (e) {
        console.error('Create GoogleApiWrapper component error')
        console.error(e)
      }
    }
  }, [apiKey])

  if (!apiKey || !WrappedMap) return null

  return <WrappedMap {...props} />
}

export default PartStorePage
