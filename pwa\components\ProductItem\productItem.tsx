import Image from 'next/image'
import Link from 'next/link'
import { clsx } from 'clsx'
import { memo } from 'react'

import { useProductItem } from '@/hooks/ProductItem'
import ProductPrice from '@/components/ProductPrice'
import ProductStock from '@/components/Common/ProductStock'
import ProductStockText from '@/components/Common/ProductStockText'
import ProductRealTimePrice from '@/components/Common/ProductRealTimePrice'
import CommonPurchased from '@/components/Common/CommonPurchased'

import ProductAction from './ProductAction'
import { StyledProductItem } from './styled'

const ProductItem = ({ product, isHorizontal, infoVisible }: any) => {
  const {
    productUrl,
    image,
    name,
    price_range,
    sku,
    shortDescriptionHtml,
    priceShow,
    actionDisabled,
    isActionVisible,
    imgSize,
    isB2bUser,
    stockAndFormShow,
    stockShow
  } = useProductItem(product, isHorizontal)

  return (
    <div className="slick-product">
      <StyledProductItem className={clsx('product-item', { 'is-horizontal': isHorizontal })}>
        {product?.purchasedDate && (
          <div className="product-item-purchased">
            <CommonPurchased date={product.purchasedDate} />
          </div>
        )}
        <Link className="product-image" href={productUrl} title={image?.label ?? ''}>
          <Image
            src={image?.url ?? '/images/placeholder.jpg'}
            alt={image?.label ?? ''}
            width={imgSize}
            height={imgSize}
          />
        </Link>
        <div className="product-content">
          <div>
            <div className="product-name">
              <Link href={productUrl} title={name} dangerouslySetInnerHTML={{ __html: name }} />
            </div>
            <p className="product-sku">{sku}</p>
            {isHorizontal && shortDescriptionHtml && (
              <div
                className="product-description"
                dangerouslySetInnerHTML={{ __html: `${shortDescriptionHtml}` }}
              />
            )}
          </div>
          {infoVisible && (
            <div className="product-content-grid">
              <div>
                {stockShow && (
                  <div className="product-stock">
                    <ProductStockText text={product?.stockText ?? ''} />
                    {/*<ProductStock
                    product={{
                      ...product,
                      only_x_left_in_stock: isB2bUser
                        ? product?.inventoryQty
                        : product?.only_x_left_in_stock
                    }}
                  />*/}
                  </div>
                )}
                {priceShow && (
                  <>
                    {/*<ProductPrice className="prices" {...price_range} />*/}
                    <ProductRealTimePrice
                      priceRange={price_range}
                      realTimePrice={product?.realTimePrice ?? null}
                    />
                  </>
                )}
              </div>
              {stockAndFormShow && (
                <ProductAction
                  product={product}
                  isActionVisible={isActionVisible}
                  disabled={actionDisabled}
                />
              )}
            </div>
          )}
        </div>
      </StyledProductItem>
    </div>
  )
}

export default memo(ProductItem)
