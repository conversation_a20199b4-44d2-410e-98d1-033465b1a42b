import { gql } from '@apollo/client'

export const B2B_CREATE_COMPANY_ROLE = gql`
  mutation createCompanyRole($input: CompanyRoleCreateInput!) {
    companyRole: createCompanyRole(input: $input) {
      role {
        id
        name
        permissions {
          key: id
          title: text
          children {
            key: id
            title: text
            children {
              key: id
              title: text
              children {
                key: id
                title: text
              }
            }
          }
        }
        users_count
      }
    }
  }
`
