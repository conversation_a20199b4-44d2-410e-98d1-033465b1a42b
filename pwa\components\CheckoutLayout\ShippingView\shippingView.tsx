import { Button, Radio, Space } from 'antd'

import { shippingMethodsDescriptionOptions } from '@/utils/constant'
import { useShippingView } from '@/hooks/CheckoutLayout'
// import BasePrice from '@/components/BasePrice'
// import SignInModal from '@/components/SignInModal'
// import AddressForm from '../AddressForm'
import AddressList from '../AddressList'
import PickUpLocation from '../PickUpLocation'
import { StyledShippingView, StyledMethodList } from './styled'

const ShippingView = () => {
  const {
    handleNext,
    currentShippingMethodCode,
    isInStorePickup,
    handleMethodChange,
    shippingMethods
  } = useShippingView()

  return (
    <StyledShippingView>
      <div>
        <h2 className="title">Fulfillment Method</h2>
        <StyledMethodList id="fulfillment-method-list">
          <Radio.Group value={currentShippingMethodCode} onChange={handleMethodChange}>
            <Space direction="vertical">
              {shippingMethods.map((item) => {
                const methodCode = item?.method_code
                const methodItem = shippingMethodsDescriptionOptions.find(
                  (option) => option.method_code === item?.method_code
                )
                const isDescVisible =
                  currentShippingMethodCode === methodCode && methodItem?.description

                return (
                  <Radio key={methodCode} value={methodCode}>
                    <div>
                      <h3>{item?.carrier_title ?? ''}</h3>
                      {isDescVisible && <p>{methodItem.description}</p>}
                    </div>
                  </Radio>
                )
              })}
            </Space>
          </Radio.Group>
        </StyledMethodList>
        <div style={{ display: isInStorePickup ? 'none' : 'block' }}>
          <AddressList type="shipping" />
        </div>
        {isInStorePickup && <PickUpLocation />}
        {/* {isB2b && (
            <Form.Item name="custom_po_number">
              <TextField label="ATTN To / PO Number (Optional)" />
            </Form.Item>
          )} */}
        {/* <Form.Item name="comments">
            <TextField
              type="texarea"
              label="Order Comments (Optional)"
              height={200}
              borderRadius={20}
            />
          </Form.Item> */}
        {/* <div className="info">
            {isEmpty(shippingMethod) ? (
              <p>
                <FormattedMessage id="checkout.info" />
              </p>
            ) : (
              <p className="shipping">
                <span>Shipping Cost:</span>&nbsp;
                <BasePrice value={shippingMethod.amount.value} />
              </p>
            )}
          </div> */}
        <Button
          className="shipping-next"
          type="primary"
          onClick={handleNext}
          disabled={!currentShippingMethodCode}>
          NEXT
        </Button>
      </div>
      {/* {!isLogin && (
        <Modal ref={modalRef} title={formatMessage({ id: 'global.login' })} width={608}>
          <SignInModal />
        </Modal>
      )} */}
    </StyledShippingView>
  )
}

export default ShippingView
