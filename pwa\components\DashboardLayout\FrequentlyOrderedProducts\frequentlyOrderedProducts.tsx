import { useCallback, memo, useEffect, useState, useMemo } from 'react'
import { useSelector } from 'react-redux'
import { Input } from 'antd'
import { debounce } from 'lodash-es'
import dynamic from 'next/dynamic'
import { clsx } from 'clsx'

import { useAwaitQuery } from '@ranger-theme/apollo'
import CommonTable from '@/components/Common/CommonTable'
import CommonLoading from '@/components/Common/CommonLoading'
import CommonLink from '@/components/Common/CommonLink'
import { GET_FREQUENTLY_ORDERED_PRODUCTS } from '@/apis/queries/getFrequentlyOrderedProducts'
import BasePrice from '@/components/BasePrice/basePrice'
import { defaultCompanyIdSelector } from '@/store/user/slice'
import { useB2bProductData } from '@/hooks/B2bProductData'

import QtyItem from './QtyItem'
import TableHeader from '../TableHeader'
import { StyledFrequentlyOrderedProducts } from './styled'
import { getProductsStockInfo } from '@/utils'
import ProductStockText from '@/components/Common/ProductStockText/productStockText'

const TableHeaderSearchItem = dynamic(() => import('@/components/Common/TableHeaderSearchItem'))
const CommonTableSort = dynamic(() => import('@/components/Common/CommonTableSort'))

const FrequentlyOrderedProducts = ({ isQuickView = false }) => {
  const frequentlyOrderedProductsQuery = useAwaitQuery(GET_FREQUENTLY_ORDERED_PRODUCTS)
  const { fetchProductInventory } = useB2bProductData()

  const defaultCompanyId = useSelector(defaultCompanyIdSelector)
  const locationId = useSelector((state: Store) => state.user.shipTo?.locationId)

  const [dataItems, setDataItems] = useState<any>([])
  const [stockItems, setStockItems] = useState<any>([])
  const [loading, setLoading] = useState(false)
  const [stockLoading, setStockLoading] = useState(false)
  const [filter, setFilter] = useState({})
  const [sort, setSort] = useState<any>({
    code: 'total_qty_ordered', // Default
    direction: 'DESC' // Default
  })

  const pagination = useMemo(() => {
    return {
      current: 1,
      pageSize: isQuickView ? 5 : 25
    }
  }, [isQuickView])

  const products = useMemo(() => {
    // Merge stock
    const items = getProductsStockInfo(dataItems, stockItems)
    // Filter
    const listByFilter = items.filter((item) => {
      return Object.keys(filter).every((itemKey) => {
        return item?.[itemKey]?.toLowerCase()?.indexOf(filter?.[itemKey]?.toLowerCase() ?? '') > -1
      })
    })
    // Sort
    const listBySort = !sort.direction
      ? listByFilter
      : listByFilter.sort((a, b) => {
          const aValue = a[sort.code]
          const bValue = b[sort.code]
          if (sort.code === 'name') {
            return sort.direction === 'ASC'
              ? aValue.localeCompare(bValue, undefined, {
                  numeric: true,
                  sensitivity: 'base'
                })
              : bValue.localeCompare(aValue, undefined, {
                  numeric: true,
                  sensitivity: 'base'
                })
          }
          return sort.direction === 'ASC' ? aValue - bValue : bValue - aValue
        })
    // Pagination
    const startIndex = (pagination.current - 1) * pagination.pageSize
    return listBySort.slice(startIndex, startIndex + pagination.pageSize)
  }, [dataItems, filter, pagination, sort, stockItems])

  const productSkus = useMemo(() => {
    return dataItems.map((item) => item?.sku)
  }, [dataItems])

  const onSortChange = useCallback(
    (code) => {
      if (isQuickView) {
        return
      }
      if (sort.code === code) {
        if (!sort.direction) {
          setSort({
            code,
            direction: 'ASC'
          })
        } else {
          setSort({
            code,
            direction: sort.direction === 'ASC' ? 'DESC' : ''
          })
        }
      } else {
        setSort({
          code,
          direction: 'ASC'
        })
      }
    },
    [sort, isQuickView]
  )

  const onFilterInputChange = debounce(async (e, key) => {
    const param: string = e?.target?.value || ''
    setFilter({
      ...filter,
      [key]: param
    })
  }, 600)

  const fetchData = useCallback(
    async (company_id) => {
      try {
        setLoading(true)
        const { data } = await frequentlyOrderedProductsQuery({
          // fetchPolicy: 'no-cache',
          variables: {
            company_id
          }
        })
        setDataItems(data?.customer?.frequentlyOrderedProducts ?? [])
      } catch (error) {
        console.info(error)
      } finally {
        setLoading(false)
      }
    },
    [frequentlyOrderedProductsQuery]
  )

  const fetchProductStock = useCallback(
    async (skus) => {
      try {
        setStockLoading(true)
        const res = await fetchProductInventory(skus)
        setStockItems(res)
      } catch (e) {
        console.error(e)
      } finally {
        setStockLoading(false)
      }
    },
    [fetchProductInventory]
  )

  useEffect(() => {
    if (defaultCompanyId) {
      fetchData(defaultCompanyId)
    }
  }, [defaultCompanyId, fetchData])

  useEffect(() => {
    if (productSkus.length > 0 && locationId) {
      fetchProductStock(productSkus)
    }
  }, [productSkus, fetchProductStock, locationId])

  const columns = [
    {
      title: (
        <TableHeaderSearchItem>
          <p
            aria-hidden
            onClick={() => {
              onSortChange('sku')
            }}>
            Item ID
            <CommonTableSort sortOrder={sort.code === 'sku' ? sort.direction : ''} />
          </p>
          <div className="item-search">
            <Input
              allowClear
              onChange={(e) => {
                onFilterInputChange(e, 'sku')
              }}
            />
          </div>
        </TableHeaderSearchItem>
      ),
      dataIndex: 'sku',
      key: 'sku',
      className: 'primary-text',
      width: 124,
      render: (param, record) => {
        return record.stock_status === 'IN_STOCK' ? (
          <CommonLink href={`/${param}`} title={param}>
            <span dangerouslySetInnerHTML={{ __html: param }} />
          </CommonLink>
        ) : (
          <span dangerouslySetInnerHTML={{ __html: param }} />
        )
      }
    },
    {
      title: (
        <TableHeaderSearchItem>
          <p
            aria-hidden
            onClick={() => {
              onSortChange('name')
            }}>
            Product Name
            <CommonTableSort sortOrder={sort.code === 'name' ? sort.direction : ''} />
          </p>
          <div className="item-search">
            <Input
              allowClear
              onChange={(e) => {
                onFilterInputChange(e, 'name')
              }}
            />
          </div>
        </TableHeaderSearchItem>
      ),
      dataIndex: 'name',
      key: 'name',
      className: 'primary-text',
      width: 208,
      render: (param, record) => {
        return record.stock_status === 'IN_STOCK' ? (
          <CommonLink href={`/${record.sku}`} title={param}>
            <span dangerouslySetInnerHTML={{ __html: param }} />
          </CommonLink>
        ) : (
          <span dangerouslySetInnerHTML={{ __html: param }} />
        )
      }
    },
    {
      title: (
        <TableHeaderSearchItem>
          <p
            aria-hidden
            onClick={() => {
              onSortChange('total_qty_ordered')
            }}>
            Total Quantity Ordered
            <CommonTableSort sortOrder={sort.code === 'total_qty_ordered' ? sort.direction : ''} />
          </p>
          <div className="item-search" />
        </TableHeaderSearchItem>
      ),
      dataIndex: 'total_qty_ordered',
      key: 'sku',
      align: 'center'
    },
    {
      title: (
        <TableHeaderSearchItem>
          <p
            aria-hidden
            onClick={() => {
              onSortChange('price')
            }}>
            Current Price
            <CommonTableSort sortOrder={sort.code === 'price' ? sort.direction : ''} />
          </p>
          <div className="item-search" />
        </TableHeaderSearchItem>
      ),
      dataIndex: 'price',
      key: 'sku',
      align: 'right',
      render: (param) => {
        return <BasePrice unit value={param} />
      }
    },
    {
      title: (
        <TableHeaderSearchItem>
          <p
            aria-hidden
            onClick={() => {
              onSortChange('stockQty')
            }}>
            Stock
            <CommonTableSort sortOrder={sort.code === 'stockQty' ? sort.direction : ''} />
          </p>
          <div className="item-search" />
        </TableHeaderSearchItem>
      ),
      dataIndex: 'sku',
      key: 'sku',
      width: 180,
      render: (param, record) => <ProductStockText text={record?.stockText ?? ''} />
    },
    {
      title: (
        <TableHeaderSearchItem>
          <p>Quantity</p>
          <div className="item-search" />
        </TableHeaderSearchItem>
      ),
      dataIndex: 'sku',
      key: 'sku',
      width: 312,
      mobileContentFull: true,
      render: (param, record) => {
        return (
          <QtyItem
            product={{
              __typename: 'SimpleProduct',
              sku: param,
              stock_status: record?.stock_status
            }}
          />
        )
      }
    }
  ]

  return loading ? (
    <>{!isQuickView && <CommonLoading />}</>
  ) : (
    <CommonLoading spinning={stockLoading}>
      <StyledFrequentlyOrderedProducts className={clsx({ 'action-inactive': isQuickView })}>
        {isQuickView ? (
          <TableHeader
            title="Frequently Ordered Products"
            subTitle="(past 6 months)"
            viewAllUrl="/account/frequently-ordered-products"
          />
        ) : (
          <div className="table-description">{`My company's 25 most frequently ordered products for the past 6 months`}</div>
        )}

        <CommonTable
          rowKey={(params) => params.sku}
          columns={columns}
          dataSource={products}
          pagination={false}
        />
      </StyledFrequentlyOrderedProducts>
    </CommonLoading>
  )
}

export default memo(FrequentlyOrderedProducts)
