import styled from '@emotion/styled'

export const StyledCommonTable = styled.div`
  &.line-style.common-table-component {
    .common-table {
      border: none;

      table tbody tr {
        td {
          padding-top: 20px;
          padding-bottom: 20px;
          background: #fff !important;
          border-color: #d9d9d9;
        }
      }
    }
  }
  .common-table {
    border: 1px solid #d9d9d9;

    table {
      thead {
        tr {
          th {
            padding-top: 14px;
            padding-bottom: 14px;
            font-weight: 700;
            font-size: 15px;
            line-height: 21px;
            letter-spacing: 0;
            white-space: nowrap;
            color: var(--color-black);
            background: #fff !important;
            border-bottom: 1px solid #d9d9d9;

            &::before {
              display: none !important;
            }

            .${({ theme }) => theme.namespace}-table-column-sorters {
              justify-content: flex-start;
              .${({ theme }) => theme.namespace}-table-column-title {
                flex: initial;
              }
            }
          }
        }
      }
      tbody {
        tr {
          td {
            padding-top: 24px;
            padding-bottom: 24px;
            font-weight: 400;
            font-size: 15px;
            line-height: 21px;
            letter-spacing: 0;
            color: var(--color-font);
            background: #fff !important;

            .price {
              font-weight: 400;
              font-size: 15px;
              line-height: 21px;
              letter-spacing: 0;
              color: var(--color-black);
            }

            &.primary-text {
              font-weight: 700;
              font-size: 15px;
              line-height: 21px;
              letter-spacing: 0;
              text-decoration: underline;
              text-decoration-style: solid;
              text-decoration-thickness: 0;
              color: var(--color-primary);
            }
          }

          &:nth-of-type(2n) {
            td {
              background: #f5f5f5 !important;
            }
          }
        }
      }
    }
  }
`
export const StyledCommonTableMobile = styled.div`
  //padding: 0 16px;
`
