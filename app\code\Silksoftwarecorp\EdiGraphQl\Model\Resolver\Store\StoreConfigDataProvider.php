<?php

namespace Silksoftwarecorp\EdiGraphQl\Model\Resolver\Store;

use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Silksoftwarecorp\EDI\Helper\Order as OrderHelper;

class StoreConfigDataProvider implements ResolverInterface
{
    /**
     * @var OrderHelper
     */
    protected $orderHelper;

    /**
     * @param OrderHelper $orderHelper
     */
    public function __construct(OrderHelper $orderHelper)
    {
        $this->orderHelper = $orderHelper;
    }

    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        $storeId = $context->getExtensionAttributes()->getStore()->getId();

        return $this->orderHelper->getStartDate($storeId);
    }
}
