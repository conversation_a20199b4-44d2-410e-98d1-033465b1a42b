<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */

declare(strict_types=1);

namespace Silksoftwarecorp\Inventory\Plugin\InventoryApi\SourceRepository;

use Magento\Framework\Api\SearchResultsInterface;
use Magento\InventoryApi\Api\SourceRepositoryInterface;
use Silksoftwarecorp\Inventory\Model\Source\InitSourceExtensionAttributes;

/**
 * Populate source extension attributes when loading source list.
 */
class LoadCustomFieldsOnGetListPlugin
{
    /**
     * @var InitSourceExtensionAttributes
     */
    private $initSourceExtensionAttributes;

    /**
     * @param InitSourceExtensionAttributes $initSourceExtensionAttributes
     */
    public function __construct(
        InitSourceExtensionAttributes $initSourceExtensionAttributes
    ) {
        $this->initSourceExtensionAttributes = $initSourceExtensionAttributes;
    }

    /**
     * Enrich the given Source Objects with custom extension attributes
     *
     * @param SourceRepositoryInterface $subject
     * @param SearchResultsInterface $searchResults
     *
     * @return SearchResultsInterface
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function afterGetList(
        SourceRepositoryInterface $subject,
        SearchResultsInterface $searchResults
    ): SearchResultsInterface {
        foreach ($searchResults->getItems() as $source) {
            $this->initSourceExtensionAttributes->execute($source);
        }

        return $searchResults;
    }
}
