<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */

namespace Silksoftwarecorp\AmastyCustomForm\Test\Unit\Helper;

use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Store\Model\ScopeInterface;
use Silksoftwarecorp\AmastyCustomForm\Helper\Data;

class DataTest extends TestCase
{
    /**
     * @var Data
     */
    private $helper;

    /**
     * @var Context|MockObject
     */
    private $contextMock;

    /**
     * @var ScopeConfigInterface|MockObject
     */
    private $scopeConfigMock;

    /**
     * @var RequestInterface|MockObject
     */
    private $requestMock;

    protected function setUp(): void
    {
        $this->contextMock = $this->createMock(Context::class);
        $this->scopeConfigMock = $this->createMock(ScopeConfigInterface::class);
        $this->requestMock = $this->createMock(RequestInterface::class);

        $this->contextMock->expects($this->any())
            ->method('getScopeConfig')
            ->willReturn($this->scopeConfigMock);

        $this->helper = new Data($this->contextMock);
        
        // Use reflection to set the private request property
        $reflection = new \ReflectionClass($this->helper);
        $requestProperty = $reflection->getProperty('_request');
        $requestProperty->setAccessible(true);
        $requestProperty->setValue($this->helper, $this->requestMock);
    }

    public function testEnableFormCustomEmailRecipientReturnsTrue()
    {
        $this->scopeConfigMock->expects($this->once())
            ->method('isSetFlag')
            ->with(
                'custom_forms/email/enable_email_recipient',
                ScopeInterface::SCOPE_STORE
            )
            ->willReturn(true);

        $result = $this->helper->enableFormCustomEmailRecipient();
        $this->assertTrue($result);
    }

    public function testEnableFormCustomEmailRecipientReturnsFalse()
    {
        $this->scopeConfigMock->expects($this->once())
            ->method('isSetFlag')
            ->with(
                'custom_forms/email/enable_email_recipient',
                ScopeInterface::SCOPE_STORE
            )
            ->willReturn(false);

        $result = $this->helper->enableFormCustomEmailRecipient();
        $this->assertFalse($result);
    }

    public function testGetFormCustomEmailRecipients()
    {
        $expectedValue = '{"test":"value"}';
        
        $this->scopeConfigMock->expects($this->once())
            ->method('getValue')
            ->with(
                'custom_forms/email/email_recipient',
                ScopeInterface::SCOPE_STORE
            )
            ->willReturn($expectedValue);

        $result = $this->helper->getFormCustomEmailRecipients();
        $this->assertEquals($expectedValue, $result);
    }

    public function testGetLocationId()
    {
        $expectedLocationId = 'location123';
        
        $this->requestMock->expects($this->once())
            ->method('getParam')
            ->with('X-Location-Id')
            ->willReturn($expectedLocationId);

        $result = $this->helper->getLocationId();
        $this->assertEquals($expectedLocationId, $result);
    }

    public function testGetLocationIdReturnsEmptyStringWhenNull()
    {
        $this->requestMock->expects($this->once())
            ->method('getParam')
            ->with('X-Location-Id')
            ->willReturn(null);

        $result = $this->helper->getLocationId();
        $this->assertEquals('', $result);
    }
}