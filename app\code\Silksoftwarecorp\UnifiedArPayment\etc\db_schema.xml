<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="sales_order" resource="default" engine="innodb" comment="Sales Flat Order">
        <column xsi:type="text" name="unified_ar_return_data" nullable="true" comment="Unified A/R Return Data"/>
        <column xsi:type="decimal" name="unified_ar_surcharge_amount" scale="4" precision="12" nullable="true" comment="Unified A/R Surcharge Amount"/>
    </table>
    <table name="blevins_order_payment" resource="default" engine="innodb" comment="">
        <column xsi:type="int" name="id" padding="11" unsigned="true" nullable="false" identity="true" />
        <column xsi:type="int" name="quote_id"  unsigned="true" nullable="false" identity="false"
                default="0" comment="Quote ID" />
        <column xsi:type="int" name="order_id"  unsigned="true" nullable="false" identity="false"
                default="0" comment="Order ID" />
        <column xsi:type="int" name="customer_id"  unsigned="true" nullable="false" identity="false"
                default="0" comment="Customer ID" />
        <column xsi:type="varchar" name="payment_account_id" nullable="true"/>
        <column xsi:type="tinyint" name="unified_ar_surcharge_allowed"  unsigned="true" nullable="false" identity="false"
                default="0" comment="unified_ar_surcharge_allowed ID" />
        <column xsi:type="decimal" name="unified_ar_surcharge_amount" scale="4" precision="12" nullable="true" comment="Unified A/R Surcharge Amount"/>
        <column xsi:type="mediumtext" name="unified_ar_return_data" nullable="true" comment="Unified A/R Return Data"/>
        <column xsi:type="mediumtext" name="unified_ar_authorization_data" nullable="true"/>
        <column xsi:type="timestamp" name="created_at" on_update="false" nullable="false" default="CURRENT_TIMESTAMP"
                comment="Created At"/>
        <column xsi:type="timestamp" name="updated_at" on_update="true" nullable="false" default="CURRENT_TIMESTAMP"
                comment="Updated At"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="id"/>
        </constraint>
    </table>
</schema>
