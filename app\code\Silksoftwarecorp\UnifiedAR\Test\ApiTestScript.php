<?php

/**
 * UnifiedAR API Test Script
 * 
 * This script can be used to test the UnifiedAR API calls directly
 * Run from Magento root: php app/code/Silksoftwarecorp/UnifiedAR/Test/ApiTestScript.php
 */

use Magento\Framework\App\Bootstrap;

require __DIR__ . '/../../../../app/bootstrap.php';

$bootstrap = Bootstrap::create(BP, $_SERVER);
$objectManager = $bootstrap->getObjectManager();

// Get the UnifiedArApiService
$apiService = $objectManager->get(\Silksoftwarecorp\UnifiedAR\Model\UnifiedArApiService::class);

// Test parameters
$customerId = '17640';

echo "=== UnifiedAR API Test Script ===\n";
echo "Testing Financial Information API\n";
echo "Customer ID: $customerId\n\n";

try {
    echo "1. Testing fetchFinancialInformation method...\n";
    $result = $apiService->fetchFinancialInformation($customerId);
    
    if (empty($result)) {
        echo "❌ No data returned from API\n";
        echo "Check the logs for detailed error information\n";
    } else {
        echo "✅ Successfully fetched financial information\n";
        echo "Response data:\n";
        print_r($result);
    }
    
} catch (\Exception $e) {
    echo "❌ Exception occurred: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}

echo "\n2. Testing direct API configuration...\n";

try {
    $apiConfig = $objectManager->get(\Silksoftwarecorp\UnifiedAR\Model\API\Config::class);
    
    echo "API URL: " . $apiConfig->getApiUrl() . "\n";
    echo "API Key: " . substr($apiConfig->getApiKey(), 0, 10) . "...\n";
    echo "Merchant Key: " . $apiConfig->getMerchantKey() . "\n";
    echo "Timeout: " . $apiConfig->getTimeout() . "s\n";
    
} catch (\Exception $e) {
    echo "❌ Configuration error: " . $e->getMessage() . "\n";
}

echo "\n3. Manual curl comparison...\n";
echo "Your working curl command:\n";
echo "curl --request GET \\\n";
echo "  --url 'https://coreapi-dev-eus.gounified-nonprod.com/api/v2/Customer/lookupBy/customerNumber/$customerId/financialInformation?merchantKey=blevinsinc' \\\n";
echo "  --header 'ApiKey: 3T11VALHEAGA6LHX10LOORWKBXO1BF' \\\n";
echo "  --header 'merchantKey: blevinsinc'\n\n";

echo "=== Test Complete ===\n";
echo "Check var/log/unified_ar.log for detailed API logs\n"; 