
type RequisitionList {
    list_id: Int! @doc(description: "The unique ID of a `RequisitionList` object.") @resolver(class: "Silksoftwarecorp\\RequistionGraphQl\\Model\\Resolver\\List\\ListId")
}


type RequisitionLists @doc(description: "Defines customer requisition lists.")  {
    total_count: Int @doc(description: "The number of returned requisition lists.") @resolver(class: "Silksoftwarecorp\\RequistionGraphQl\\Model\\Resolver\\List\\TotalCount")
}
