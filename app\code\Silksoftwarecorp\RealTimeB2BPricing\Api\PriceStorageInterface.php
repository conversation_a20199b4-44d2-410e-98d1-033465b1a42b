<?php

namespace Silksoftwarecorp\RealTimeB2BPricing\Api;

use Silksoftwarecorp\RealTimeB2BPricing\Api\Data\B2BPriceItemInterface;

interface PriceStorageInterface
{
    /**
     * Save the price to storage
     *
     * @param string $customerId
     * @param string $locationId
     * @param string $sku
     * @param B2BPriceItemInterface $priceItem
     * @param int|null $ttl 过期时间(秒)
     *
     * @return bool
     */
    public function save(
        string $customerId,
        string $locationId,
        string $sku,
        B2BPriceItemInterface $priceItem,
        ?int $ttl = null
    ): bool;

    /**
     * Get the price from storage
     *
     * @param string $customerId
     * @param string $locationId
     * @param string $sku
     * @return B2BPriceItemInterface|null
     */
    public function get(
        string $customerId,
        string $locationId,
        string $sku
    ): ?B2BPriceItemInterface;

    /**
     * Delete the price from storage
     *
     * @param string $customerId
     * @param string $sku
     * @param string $locationId
     *
     * @return bool
     */
    public function delete(string $customerId, string $locationId, string $sku): bool;

    /**
     * Check whether the price exists in the storage
     *
     * @param string $customerId
     * @param string $sku
     * @param string $locationId
     *
     * @return bool
     */
    public function has(string $customerId, string $locationId, string $sku): bool;

    /**
     * Clear all prices from stored
     *
     * @return bool
     */
    public function clear(): bool;
}
