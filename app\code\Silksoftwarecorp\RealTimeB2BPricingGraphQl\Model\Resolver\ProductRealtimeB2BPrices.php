<?php

namespace Silksoftwarecorp\RealTimeB2BPricingGraphQl\Model\Resolver;

use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlAuthorizationException;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\GraphQl\Model\Query\ContextInterface;
use Silksoftwarecorp\RealTimeB2BPricing\Api\Data\B2BPriceItemInterface;
use Silksoftwarecorp\RealTimeB2BPricing\Logger\LoggerInterface;
use Silksoftwarecorp\RealTimeB2BPricing\Api\RealtimeB2BPriceServiceInterface;

class ProductRealtimeB2BPrices implements ResolverInterface
{
    /**
     * @var RealtimeB2BPriceServiceInterface
     */
    protected $realtimeB2BPriceService;

    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * @param RealtimeB2BPriceServiceInterface $realtimeB2BPriceService
     * @param LoggerInterface $logger
     */
    public function __construct(
        RealtimeB2BPriceServiceInterface $realtimeB2BPriceService,
        LoggerInterface $logger
    ) {
        $this->realtimeB2BPriceService = $realtimeB2BPriceService;
        $this->logger = $logger;
    }

    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        if (!isset($args['input'])) {
            throw new GraphQlInputException(__('"input" is required'));
        }

        $input =  $args['input'];
        $this->_validateInput($input);

        /**
         * @var ContextInterface $context
         */
        if (false === $context->getExtensionAttributes()->getIsCustomer()) {
            throw new GraphQlAuthorizationException(__('The request is allowed for logged in customer.'));
        }

        $currentUserId = $context->getUserId();

        try {
            $priceItemResult = $this->realtimeB2BPriceService->getPriceItems(
                $currentUserId,
                $input['location_id'],
                $input['skus']
            );

            $items = [];
            foreach ($priceItemResult->getItems() as $price) {
                $items[] = $this->formatPriceItem($price);
            }

            return [
                'items' => $items,
            ];
        } catch (LocalizedException $e) {
            $this->logger->critical(__($e->getMessage()));

            throw new GraphQlInputException(__($e->getMessage()));
        } catch (\Exception $e) {
            $this->logger->critical(__($e->getMessage()));

            throw new GraphQlInputException(__('The products price search failed.'));
        }
    }

    private function formatPriceItem(B2BPriceItemInterface $priceItem): array
    {
        return [
            'sku' => $priceItem->getSku(),
            'price' => $priceItem->getPrice(),
            'last_invoice_date' => $priceItem->getLastInvoiceDate()
        ];
    }

    /**
     * @param array $input
     * @return void
     *
     * @throws GraphQlInputException
     */
    private function _validateInput(array $input): void
    {
        if (empty($input['location_id'])) {
            throw new GraphQlInputException(__('Please provide a location id.'));
        }

        if (empty($input['skus'])) {
            throw new GraphQlInputException(__('Please provide a skus.'));
        }
    }
}
