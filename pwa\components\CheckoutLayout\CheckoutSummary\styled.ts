import styled from '@emotion/styled'

export const StyledCheckoutSummary = styled.div`
  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    padding-bottom: 86px;
    height: 85vh;
    overflow: auto;
  }
`

export const StyledOrderSummary = styled.div`
  padding: 24px 24px 18px;
  background-color: #f5f5f5;
  border-radius: 4px;

  .title {
    padding-bottom: 3px;
    font-weight: 700;
    font-size: 24px;
    line-height: 30px;
    letter-spacing: 0;
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    padding: 0;
    background-color: var(--color-white);

    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      button {
        padding: 0;
        background: none !important;

        span {
          font-weight: 700;
          font-size: 15px;
          line-height: 21px;
          letter-spacing: 0.02em;
          text-decoration: underline;
          text-decoration-style: solid;
          text-decoration-thickness: 0;
          color: var(--color-primary);
        }
      }
    }
  }
`
export const StyledMobileOrderSummary = styled.div`
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 2;
  padding: 13px 16px;
  width: 100%;
  min-height: 89px;
  box-shadow: 0 0 15px 0 #0000001f;
  background-color: var(--color-white);

  .order-summary-title {
    display: flex;
    justify-content: space-between;
    align-items: center;

    div {
      font-weight: 700;
      font-size: 20px;
      line-height: 26px;
      letter-spacing: 0;
    }

    button {
      padding: 0;
      background: none !important;

      span {
        font-weight: 700;
        font-size: 15px;
        line-height: 21px;
        letter-spacing: 0.02em;
        text-decoration: underline;
        text-decoration-style: solid;
        text-decoration-thickness: 0;
        color: var(--color-primary);
      }
    }
  }

  .order-summary-total {
    margin-top: 8px;

    span {
      font-weight: 700;
      font-size: 14px;
      line-height: 20px;
      letter-spacing: 0.01em;
    }
  }
`
