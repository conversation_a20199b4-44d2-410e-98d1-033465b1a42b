import { gql } from '@apollo/client'
import type { DocumentNode } from '@apollo/client'

import { categoryTree } from '../fragment/categoryTree'
import { cmsPage } from '../fragment/cmsPage'
import { simpleProduct } from '../fragment/simpleProduct'
// TODO
// import { configurableProduct } from '../fragment/configurableProduct'
// import { blogPost } from '../fragment/blogPost'
// import { blogCategory } from '../fragment/blogCategory'

export const GET_ROUTE: DocumentNode = gql`
  query getRoute($url: String!) {
    route(url: $url) {
      type
      ...categoryTree
      ...cmsPage
      ...simpleProduct
      __typename
    }
  }
  ${categoryTree}
  ${cmsPage}
  ${simpleProduct}
`
