import styled from '@emotion/styled'

export const StyledBlogPage = styled.div`
  padding: 72px 0 96px;

  .blog-page-title {
    text-align: center;

    div {
      font-weight: 700;
      font-size: 14px;
      line-height: 21px;
      letter-spacing: 0.04em;
      color: var(--color-text);
    }

    h2 {
      margin-top: 8px;
      margin-bottom: 40px;
      font-weight: 700;
      font-size: 32px;
      line-height: 38px;
      letter-spacing: 0;
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    padding: 64px 0;

    .blog-page-title {
      padding: 0 16px;
    }
  }
`

export const StyledBlogDescription = styled.div`
  margin: 0 auto;
  max-width: ${({ theme }) => theme.breakPoint.xl}px;

  .contained {
    margin-top: auto;
    margin-bottom: auto;
    max-width: 1180px;

    .pagebuilder-column-group {
      max-width: 558px;
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    padding-bottom: 215px;

    .pagebuilder__row.fullBleed {
      min-height: 250px !important;
      height: 250px !important;

      .contained {
        margin-top: 180px;
        padding: 0 16px;

        .pagebuilder-column {
          background: #fff !important;
          box-shadow: 0 0 15px 0 #0000001a;
        }
      }
    }
  }
`

export const StyledBlogPanel = styled.div`
  display: flex;
  justify-content: space-between;

  .tag-list {
    button {
      display: inline-block;
      margin-right: 16px;
      border-radius: 5px;
      background: rgba(150, 140, 131, 0.1) !important;

      span {
        color: var(--color-font);
      }

      &.active {
        background: var(--color-bg-base) !important;

        span {
          color: var(--color-white);
        }
      }
    }
  }

  .blog-search {
    .${({ theme }) => theme.namespace}-input-outlined {
      padding-right: 6px;
      width: 400px;
      height: 53px;
      border-radius: 4px;
      border: 1px solid #d9d9d9;

      input {
        &::placeholder {
          font-weight: 400;
          font-size: 16px;
          line-height: 24px;
          letter-spacing: 0.02em;
          color: var(--color-font);
        }
      }
    }
  }

  .blog-search-icon {
    padding: 8px 10px;
    cursor: pointer;
    &:hover {
      background: #f5f5f5;
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    flex-direction: column-reverse;

    .blog-search {
      padding: 0 16px;

      .${({ theme }) => theme.namespace}-input-outlined {
        width: 100%;
      }
    }

    .tag-list {
      margin-top: 16px;
      margin-left: 16px;
      overflow: auto;

      .tag-list-inner {
        display: flex;
        flex-wrap: nowrap;
      }
    }
  }
`

export const StyledBlogList = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 48px 24px;
  margin-top: 40px;
  padding-bottom: 40px;

  .item-img {
    display: block;
    width: 100%;
    height: 200px;
    overflow: hidden;
    border-radius: 5px;

    img {
      width: 100%;
      object-fit: contain !important;
    }
  }

  .item-tags {
    margin-top: 16px;

    div {
      display: inline-flex;
      justify-content: center;
      align-items: center;
      padding: 0 12px;
      margin-right: 8px;
      height: 33px;
      background: #0038651f;
      border-radius: 5px;
      font-weight: 700;
      font-size: 13px;
      line-height: 21px;
      letter-spacing: 0.01em;
    }
  }

  .item-date {
    margin-top: 16px;
    font-weight: 400;
    font-size: 14px;
    line-height: 21px;
    letter-spacing: 0.01em;
  }

  .item-title {
    margin-top: 8px;
    font-weight: 700;
    font-size: 22px;
    line-height: 28px;
    letter-spacing: 0;
    color: #000 !important;
    display: -webkit-box;
    overflow: hidden;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  .item-desc {
    margin-top: 8px;
    font-weight: 400;
    font-size: 15px;
    line-height: 21px;
    letter-spacing: 0.01em;
    display: -webkit-box;
    overflow: hidden;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  .item-read-more {
    display: inline-block;
    margin-top: 8px;
    font-weight: 700;
    font-size: 16px;
    line-height: 21px;
    letter-spacing: 0.01em;
    text-decoration: underline !important;
    color: var(--color-primary) !important;
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    grid-template-columns: 1fr;
    padding: 0 16px 40px;
    margin-top: 32px;
  }
`

export const StyledBlogListEmpty = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  font-weight: 400;
  font-size: 15px;
  line-height: 21px;
  letter-spacing: 0.01em;
  color: var(--color-text);
`
