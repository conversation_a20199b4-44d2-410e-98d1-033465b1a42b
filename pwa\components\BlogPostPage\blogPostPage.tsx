import dynamic from 'next/dynamic'

import { LineContainer } from '@ranger-theme/ui'
import Breadcrumb from '@/components/Breadcrumb'
import CommonLoading from '@/components/Common/CommonLoading'
import { useBlogPostPage } from '@/hooks/BlogPostPage'

import { StyledBlogPostPage, StyledPostTags } from './styled'

const PageBuilder = dynamic(() => import('@ranger-theme/pagebuilder'), {
  ssr: false
})

const BlogPostPage = () => {
  const { loading, postContent, postTags, postMetaTitle } = useBlogPostPage()

  return (
    <>
      <Breadcrumb items={[{ name: 'Blog', url: '/blog' }, { name: postMetaTitle }]} />
      <LineContainer>
        <CommonLoading spinning={loading}>
          <>
            <StyledBlogPostPage>
              {postContent && <PageBuilder html={postContent} />}
            </StyledBlogPostPage>

            {/* Tags */}
            {postTags.length > 0 && (
              <StyledPostTags>
                <p>Tags:</p>
                <div className="tag-list">
                  {postTags.map((tag) => (
                    <div key={tag?.tag_id}>{tag?.name ?? ''}</div>
                  ))}
                </div>
              </StyledPostTags>
            )}
          </>
        </CommonLoading>
      </LineContainer>
    </>
  )
}

export default BlogPostPage
