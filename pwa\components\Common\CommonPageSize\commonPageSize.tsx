import { memo, useMemo } from 'react'
import { Select } from 'antd'
import { useSelector } from 'react-redux'

import { StyledCommonPageSize } from './styled'

const CommonPageSize = ({ pageSize = 10, onChange }) => {
  const storeConfig = useSelector((state: Store) => state.app.storeConfig)

  const options = useMemo(() => {
    if (storeConfig?.web_per_page_values) {
      return storeConfig.web_per_page_values?.split(',').map((item) => {
        return {
          label: Number(item),
          value: Number(item)
        }
      })
    }
    return [
      {
        value: 10,
        label: 10
      },
      {
        value: 20,
        label: 20
      },
      {
        value: 30,
        label: 30
      }
    ]
  }, [storeConfig])

  return (
    <StyledCommonPageSize className="common-page-size">
      <p>Show</p>
      <Select
        value={pageSize}
        onChange={onChange}
        options={options}
        suffixIcon={
          <svg
            className="down"
            width="1em"
            height="1em"
            fill="currentColor"
            aria-hidden="true"
            focusable="false">
            <use xlinkHref="#icon-down" />
          </svg>
        }
      />
      <p>per page</p>
    </StyledCommonPageSize>
  )
}

export default memo(CommonPageSize)
