import styled from '@emotion/styled'

export const StyledQtyItem = styled.div`
  form {
    display: grid;
    grid-template-columns: 1fr 84px;
    grid-gap: 16px;
  }

  .${({ theme }) => theme.namespace} {
    &-form-item {
      margin-bottom: 0;
    }

    &-input {
      padding: 0 6px;
      height: 50px;
      border-radius: 3px;
      font-weight: 400;
      font-size: 16px;
      line-height: 24px;
      letter-spacing: 0.02em;
    }
  }

  .btn-prev,
  .btn-next {
    position: absolute;
    top: 50%;
    left: 12px;
    width: 32px;
    height: 32px;
    border-width: 0;
    background: transparent;
    z-index: 8;
    transform: translateY(-50%);
  }

  .btn-next {
    left: unset;
    right: 12px;
  }

  .quantity {
    display: block;
  }

  .cart-btn {
    width: 84px;
    height: 50px;
    border-radius: 3px;

    &[disabled] {
      background: var(--color-primary);
      opacity: 0.6;
    }
  }

  .contact-btn button {
    width: 100%;
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    .${({ theme }) => theme.namespace} {
      &-form-item {
        //margin-bottom: 0;
        flex: 1;
        margin-right: 8px;
      }

      &-input {
        //padding: 0 6px;
        //height: 50px;
        //border-radius: 3px;
        //font-weight: 400;
        //font-size: 16px;
        //line-height: 24px;
        //letter-spacing: 0.02em;
      }
    }
  }
`
