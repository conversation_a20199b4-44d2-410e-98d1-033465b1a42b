type Query {
    getShipToListAll(company_id: String!): ShipToListAll @resolver(class: "Silksoftwarecorp\\ShipToGraphQl\\Model\\Resolver\\ShipToList")
}

type ShipToListAll {
    company_id: Int!
    items: [ShipToItems]
}

type ShipToItems {
    entity_id: Int
    erp_cust_num: String
    ship_to_num: String
    ship_to_name: String
    email: String
    region_code: String
    street: String
    street_line2: String
    street_line3: String
    company_id: Int
    telephone: String
    postcode: String
    city: String
    country_id: String
    location_id: String
    is_default: Boolean
}

interface ProductInterface {
    product_inventory_source: [InventorySourceItem] @resolver(class: "\\Silksoftwarecorp\\ShipToGraphQl\\Model\\Resolver\\Product\\ProductInventorySource")
}

type InventorySourceItem {
    source_code: String
    quantity: Float
    status: Int
}

input RequestNewShipToInput {
    business_name: String!
    business_address1: String!
    business_address2: String
    city: String!
    state: String!
    zip_code: String!
    phone_number: String!
    email_address: String!
    confirm_email_address: String!
    address_types: [String!]!
    delivery_instructions: String
}

type RequestNewShipToOutput {
    success: Boolean!
    message: String
}

input SetDefaultShipToInput {
    entity_id: Int!
}

type SetDefaultShipToOutput {
    success: Boolean!
    message: String
}

type Mutation {
    requestNewShipTo(
        input: RequestNewShipToInput!
    ): RequestNewShipToOutput @resolver(class: "Silksoftwarecorp\\ShipToGraphQl\\Model\\Resolver\\RequestNewShipTo")
    setDefaultShipTo(
        input: SetDefaultShipToInput!
    ): SetDefaultShipToOutput @resolver(class: "Silksoftwarecorp\\ShipToGraphQl\\Model\\Resolver\\SetDefaultShipTo")
}
