import styled from '@emotion/styled'

export const StyledBanner = styled.div`
  margin: 0 auto;
  background-color: #f2f2f2;
  max-width: ${({ theme }) => theme.breakPoint.xl}px;
  min-height: 341px;
`

export const StyledBannerItem = styled.div`
  position: relative;

  .image {
    width: 100%;
    height: 341px;
  }

  .caption {
    margin: 0 auto;
    max-width: 1180px;
    min-height: 115px;
    position: absolute;
    bottom: 38px;
    right: 0;
    left: 0;
    z-index: 20;

    .content {
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding-left: 35px;
      max-width: 580px;
      min-height: 135px;
      background: #003865;
    }

    p {
      margin-bottom: 0;
      width: 100%;
      font-size: 30px;
      font-weight: 700;
      line-height: 38px;
      text-align: left;
      color: ${({ theme }) => theme.colors.white};
    }

    a {
      font-size: 15px;
      font-weight: 700;
      line-height: 21px;
      letter-spacing: 0.01em;
      text-align: left;
      text-decoration-line: underline;
      text-decoration-style: solid;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      color: ${({ theme }) => theme.colors.white};
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    .image {
      height: 273px;
      object-fit: cover;
    }

    .caption {
      position: relative;
      bottom: unset;
      right: unset;
      left: unset;
      height: auto;

      .content {
        padding: 16px 24px 24px;
        height: auto;
      }

      p {
        font-weight: 700;
        font-size: 23px;
        line-height: 33px;
        letter-spacing: 0;
        text-align: center;
      }

      a {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 18px auto 0;
        width: 143px;
        height: 44px;
        border-radius: 3px;
        background: var(--color-primary);
        font-weight: 700;
        font-size: 14px;
        line-height: 20px;
        letter-spacing: 0.01em;
        text-transform: uppercase;
        text-decoration: initial;
      }
    }
  }
`
