import styled from '@emotion/styled'

export const StyledFormItem = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-column-gap: 24px;
  justify-content: space-between;
  align-items: center;
`

export const StyledFormLine = styled.div`
  display: grid;
  grid-template-columns: 1fr 186px 186px;
  grid-column-gap: 24px;
  justify-content: space-between;
  align-items: center;
`

export const StyledActions = styled.div`
  .${({ theme }) => theme.namespace} {
    &-btn {
      width: 202px;
      height: 46px;
      font-family: var(--font-poppins-medium);
      font-size: 15px;
      font-weight: 600;
      text-transform: uppercase;
      border-radius: 50px;
    }
  }
`
