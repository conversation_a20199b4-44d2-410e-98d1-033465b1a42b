import { memo, useState, useCallback } from 'react'
import { DatePicker, Checkbox } from 'antd'
import dayjs from 'dayjs'

import { StyledMixedItemPropertyLocator } from './styled'

const MixedItemPropertyLocator = ({ handleSaveVal }: any) => {
  const [checkedVal, setCheckedVal] = useState<any>('')
  const [dateVal, setDateVal] = useState<any>(null)

  const onCheckChange = useCallback(
    (val) => {
      if (checkedVal !== val) {
        setCheckedVal(val)
        setDateVal(null)
        handleSaveVal(val === 'sent' ? 'sent' : '')
      }
    },
    [checkedVal, handleSaveVal]
  )

  return (
    <StyledMixedItemPropertyLocator>
      <div className="mixed-item-box">
        <div>
          <Checkbox
            checked={checkedVal === 'sent'}
            onChange={() => {
              onCheckChange('sent')
            }}>
            Sent
          </Checkbox>
        </div>
        <div className="mixed-item-flex">
          <Checkbox
            checked={checkedVal === 'will'}
            onChange={() => {
              onCheckChange('will')
            }}>
            Will be sent on:
          </Checkbox>
          <DatePicker
            value={dateVal}
            className="mixed-item-date-picker"
            placeholder=""
            format="MM/DD/YYYY"
            showNow={false}
            suffixIcon={null}
            onChange={(v) => {
              setDateVal(v)
              handleSaveVal(dayjs(v).format('MM/DD/YYYY'))
              if (checkedVal !== 'will') {
                setCheckedVal('will')
              }
            }}
          />
        </div>
      </div>
    </StyledMixedItemPropertyLocator>
  )
}

export default memo(MixedItemPropertyLocator)
