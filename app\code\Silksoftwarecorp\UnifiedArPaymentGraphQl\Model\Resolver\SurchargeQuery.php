<?php

namespace Silksoftwarecorp\UnifiedArPaymentGraphQl\Model\Resolver;

use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlAuthorizationException;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\GraphQl\Model\Query\ContextInterface;
use Silksoftwarecorp\UnifiedArPayment\Model\SurchargeQuery as SurchargeQueryModel;
use Magento\QuoteGraphQl\Model\Cart\GetCartForUser;
use Silksoftwarecorp\UnifiedArPayment\Model\ResourceModel\BlevinsOrderPayment as BlevinsOrderPaymentResource;
use Magento\Quote\Model\Cart\CustomerCartResolver;

class SurchargeQuery implements ResolverInterface
{
    /**
     * @var SurchargeQueryModel
     */
    private $surchargeQuery;

    /**
     * @var GetCartForUser
     */
    private $getCartForUser;

    private $blevinsOrderPaymentResource;

    private $_blevinsOrderPaymentFactory;

    /**
     * @var CustomerCartResolver
     */
    private $customerCartResolver;

    /**
     * @param SurchargeQueryModel $surchargeQuery
     */
    public function __construct(
        SurchargeQueryModel $surchargeQuery,
        GetCartForUser $getCartForUser,
        BlevinsOrderPaymentResource $BlevinsOrderPayment,
        \Silksoftwarecorp\UnifiedArPayment\Model\BlevinsOrderPaymentFactory $blevinsOrderPaymentFactory,
        CustomerCartResolver $customerCartResolver
    ) {
        $this->surchargeQuery = $surchargeQuery;
        $this->getCartForUser = $getCartForUser;
        $this->blevinsOrderPaymentResource = $BlevinsOrderPayment;
        $this->_blevinsOrderPaymentFactory = $blevinsOrderPaymentFactory;
        $this->customerCartResolver = $customerCartResolver;
    }

    /**
     * @inheritdoc
     */
    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
        $currentUserId = $context->getUserId();

        /**
         * @var ContextInterface $context
         */
        if (false === $context->getExtensionAttributes()->getIsCustomer()) {
            throw new GraphQlAuthorizationException(__('The request is allowed for logged in customer'));
        }

        try {
            $paymentAccountId = $args['payment_account_id'] ?? null;
            $billingState = $args['billing_state'] ?? null;
            $amount = $args['amount'] ?? null;
            $country = $args['country'] ?? null;

            if (!$paymentAccountId) {
                throw new GraphQlInputException(__('Payment Account ID is required'));
            }

            if (!$billingState) {
                throw new GraphQlInputException(__('Billing State is required'));
            }

            if (!$amount) {
                throw new GraphQlInputException(__('Amount is required'));
            }

            if (!$country) {
                throw new GraphQlInputException(__('Country is required'));
            }

            // Get store ID from context
            $storeId = $context->getExtensionAttributes()->getStore()->getId();

            // Call the surcharge query model
            $response = $this->surchargeQuery->querySurcharge($paymentAccountId, $billingState, $amount, $country, $storeId);
            $this->saveToPaymentData($currentUserId, $paymentAccountId, $amount, $response);

            $percent = $response['surcharge_percent'] ?? 0;
            $surchargeAmount = (float)$amount * (float)$percent;
            $surchargeAmount = round($surchargeAmount, 2);

            return [
                'success' => $response['success'],
                'message' => $response['message'],
                'surcharge_allowed' => $response['surcharge_allowed'] ?? false,
                'surcharge_percent' => $response['surcharge_percent'] ?? 0,
                'surcharge_amount' => $surchargeAmount,
                'error_code' => $response['error_code'] ?? null
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'surcharge_allowed' => false,
                'surcharge_percent' => 0,
                'surcharge_amount' => 0,
                'error_code' => 'EXCEPTION'
            ];
        }
    }

    protected function saveToPaymentData($currentUserId, $paymentAccountId, $amount, $response)
    {
        $cart = $this->customerCartResolver->resolve($currentUserId);
        $quoteId = $cart->getId();

        $allowed = $response['surcharge_allowed'] ?? false;
        $percent = $response['surcharge_percent'] ?? 0;
        $surchargeAmount = (float)$amount * (float)$percent;
        $surchargeAmount = round($surchargeAmount, 2);

        $rowData = $this->blevinsOrderPaymentResource->getBlePaymentDataByQuoteId($quoteId);
        if ($rowData) {
            $rowId = $rowData['id'] ?? false;
            if ($rowId) {
                $this->blevinsOrderPaymentResource->updateBlePaymentData($rowId, $paymentAccountId, $allowed, $surchargeAmount);
            } else {
                $this->createNewBlePaymentData($quoteId, $currentUserId, $paymentAccountId, $allowed, $surchargeAmount);
            }
        } else {
            $this->createNewBlePaymentData($quoteId, $currentUserId, $paymentAccountId, $allowed, $surchargeAmount);
        }
    }

    protected function createNewBlePaymentData($quoteId, $cartCustomerId, $paymentAccountId, $allowed, $surchargeAmount)
    {
        $waybill = $this->_blevinsOrderPaymentFactory->create();
        $data = [
            "quote_id" => $quoteId,
            "customer_id" => $cartCustomerId,
            "payment_account_id" => $paymentAccountId,
            "unified_ar_surcharge_allowed" => $allowed,
            "unified_ar_surcharge_amount" => $surchargeAmount
        ];
        $waybill->setData($data)->save();
    }
}
