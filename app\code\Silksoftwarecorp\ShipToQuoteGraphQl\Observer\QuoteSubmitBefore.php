<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */
declare(strict_types=1);

namespace Silksoftwarecorp\ShipToQuoteGraphQl\Observer;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;

/**
 * Observer to convert quote fields to order during checkout
 */
class QuoteSubmitBefore implements ObserverInterface
{
    /**
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer)
    {
        $quote = $observer->getQuote();
        $order = $observer->getOrder();

        // Transfer ship_to_id from quote to order
        if ($quote->getData('ship_to_id')) {
            $order->setData('ship_to_id', $quote->getData('ship_to_id'));
        }

        // Transfer customer_contact_id from quote to order
        if ($quote->getData('customer_contact_id')) {
            $order->setData('customer_contact_id', $quote->getData('customer_contact_id'));
        }
    }
}



