type Query {
    b2bOrdersSummary(
        company_id: String!,
        start_date: String
    ): B2bOrdersSummary @doc(description: "An object that contains a list of orders.") @resolver(class:"Silksoftwarecorp\\EdiGraphQl\\Model\\Resolver\\Customer\\Orders")
    b2bOrderDetails(order_number: String!): B2BOrder @doc(description: "An object that contains order details.") @resolver(class:"Silksoftwarecorp\\EdiGraphQl\\Model\\Resolver\\Customer\\OrderDetails")
}

type Customer {
    frequentlyOrderedProducts(company_id: String!): [FrequentlyOrderedProduct] @doc(description: "An object that contains a list of Top Ordered Product is assigned to.") @resolver(class:"Silksoftwarecorp\\EdiGraphQl\\Model\\Resolver\\Customer\\FrequentlyOrderedProducts")
}

type FrequentlyOrderedProduct {
    sku: String
    name: String
    price: Float
    stock_status: String
    total_qty_ordered: Int
    only_x_left_in_stock: Float @doc(description: "Product stock only x left count")
}


type B2bOrdersSummary @doc(description: "The collection of orders.") {
    items: [B2BOrderSummary]
}

type B2BOrderSummary {
    order_number: String
    po_number: String
    order_date: String
    carrier: String
    tracking_number: String
    freight: Float
    sales_tax: Float
    payment_method: String
    shipping_method: B2BOrderShippingMethod
    total_amount: Float
    status: String
    ship_to: B2BOrderAddress
}

type B2BOrder {
    order_number: String
    po_number: String
    order_date: String
    carrier: String
    tracking_number: String
    freight: Float
    sales_tax: Float
    payment_method: String
    shipping_method: B2BOrderShippingMethod
    total_amount: Float
    subtotal: Float
    status: String
    billing_address: B2BOrderAddress
    shipping_address: B2BOrderAddress
    ship_from: B2BOrderAddress
    items: [B2BOrderItem]
}

type B2BOrderAddress {
    id: String @doc(description: "The id of the person associated with the shipping/billing address.")
    name: String @doc(description: "The name of the person associated with the shipping/billing address.")
    address: [String] @doc(description: "An array of strings that define the address.")
    city: String @doc(description: "The city or town.")
    state: String @doc(description: "The state name.")
    country: String @doc(description: "The customer's country.")
    phone: String @doc(description: "The telephone number.")
    email: String @doc(description: "The email.")
    zip_code: String @doc(description: "The zip code.")
}

type B2BOrderItem {
    item_id: String
    product_sku: String
    product_name: String
    quantity_ordered: Int
    status: String
    product_sale_price: Money
    row_total: Float
    description: String
    tracking_number: String
    product: ProductInterface @doc(description: "The ProductInterface object, which contains details about the base product") @resolver(class: "Silksoftwarecorp\\EdiGraphQl\\Model\\Resolver\\ProductResolver")
}

type B2BOrderShippingMethod @doc(description: "Contains details about the possible shipping methods and carriers.") {
    carrier_code: String! @doc(description: "A string that identifies a commercial carrier or an offline shipping method.")
    carrier_title: String! @doc(description: "The label for the carrier code.")
    method_code: String @doc(description: "A shipping method code associated with a carrier.")
    method_title: String @doc(description: "The label for the shipping method code.")
}

type StoreConfig {
    p21_order_default_start_date: String @resolver(class:"Silksoftwarecorp\\EdiGraphQl\\Model\\Resolver\\Store\\StoreConfigDataProvider")
}
