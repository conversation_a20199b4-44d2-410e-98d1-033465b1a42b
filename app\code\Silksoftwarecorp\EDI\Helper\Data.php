<?php

namespace Silksoftwarecorp\EDI\Helper;

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Store\Model\ScopeInterface;

class Data extends AbstractHelper
{
    const XML_PATH_GENERAL_ACTIVE = 'edi/general/active';

    const XML_PATH_GENERAL_DEBUG = 'edi/general/debug';

    const XML_PATH_API_ENVIRONMENT = 'edi/api/environment';

    const XML_PATH_API_URL ='edi/api/url';

    const XML_PATH_API_USERNAME ='edi/api/username';

    const XML_PATH_API_PASSWORD ='edi/api/password';

    const XML_PATH_API_SANDBOX_URL = 'edi/api/sandbox_url';

    const XML_PATH_API_SANDBOX_USERNAME ='edi/api/sandbox_username';

    const XML_PATH_API_SANDBOX_PASSWORD ='edi/api/sandbox_password';

    const XML_PATH_API_ENABLE_SSL_CERTIFICATE_VERIFY = 'edi/api/enable_ssl_certificate_verify';

    const XML_PATH_API_TIMEOUT = 'edi/api/timeout';

    public function isActive($storeId = null): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_GENERAL_ACTIVE,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function isDebugged($storeId = null): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_GENERAL_DEBUG,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getApiEnvironment($storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_API_ENVIRONMENT,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getApiUrl($storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_API_URL,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getApiUsername($storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_API_USERNAME,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getApiPassword($storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_API_PASSWORD,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getSandboxApiUrl($storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_API_SANDBOX_URL,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getSandboxApiUsername($storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_API_SANDBOX_USERNAME,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getSandboxApiPassword($storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_API_SANDBOX_PASSWORD,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function enableSSLCertificateVerify($storeId = null): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_API_ENABLE_SSL_CERTIFICATE_VERIFY,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getApiTimeout($storeId = null): int
    {
        return (int)$this->scopeConfig->getValue(
            self::XML_PATH_API_TIMEOUT,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }
}
