<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="quote" resource="default">
        <column xsi:type="varchar" name="ship_to_id" nullable="true" length="255" comment="Ship To ID"/>
        <column xsi:type="varchar" name="customer_contact_id" nullable="true" length="255" comment="Customer Contact ID"/>
    </table>
    <table name="sales_order" resource="default">
        <column xsi:type="varchar" name="ship_to_id" nullable="true" length="255" comment="Ship To ID"/>
        <column xsi:type="varchar" name="customer_contact_id" nullable="true" length="255" comment="Customer Contact ID"/>
    </table>
</schema>



