import styled from '@emotion/styled'

export const StyledAddUser = styled.div`
  margin-top: 1rem;

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    margin-top: 0;
    margin-bottom: 16px;

    button {
      width: 100%;
    }
  }
`

export const StyledUsersContainer = styled.div`
  margin-top: 30px;

  .item-action {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .item-action-line {
      padding: 0 10px;
    }
  }

  table {
    thead tr th:first-of-type,
    tbody tr td:first-of-type {
      padding-left: 0;
    }

    tbody tr td:last-of-type {
      padding-right: 0;
    }
  }

  .all-users-items {
    margin-top: 16px;
    display: flex;
    align-items: center;
    height: 41px;
    font-weight: 400;
    font-size: 15px;
    line-height: 21px;
    letter-spacing: 0;
    color: var(--color-black);
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    margin-top: 0;

    .tools.tools-mb {
      display: flex;
      align-items: flex-start;
      padding-bottom: 12px;

      button {
        margin-top: 8px;
        margin-left: 8px;
      }
    }

    .mobile-content-full {
      display: inline-block !important;

      .item-action-line {
        padding: 0 20px;
      }
    }
  }
`

export const StyledRemoveAction = styled.div`
  > button {
    color: blue;

    &:hover {
      color: ${({ theme }) => theme.colors.primary};
    }
  }
`
