import Link from 'next/link'
import { useCallback, useEffect, useState, useMemo, memo } from 'react'
import { useSelector } from 'react-redux'

import { useAwaitQuery } from '@ranger-theme/apollo'
import { GET_RELATED_DOCUMENTS } from '@/apis/queries/getRelatedDocuments'

import CollapseBox from '../CollapseBox'
import { StyledRelatedDocuments } from './styled'

const RelatedDocuments = ({ sku }: any) => {
  const getRelatedDocumentsQuery = useAwaitQuery(GET_RELATED_DOCUMENTS)
  const storeConfig = useSelector((state: Store) => state.app.storeConfig)

  const [relatedDocuments, setRelatedDocuments] = useState<any[]>([])

  const mediaUrl = useMemo(() => {
    return storeConfig?.media_url ?? ''
  }, [storeConfig])

  const fetchRelatedDocuments = useCallback(async () => {
    try {
      const { data } = await getRelatedDocumentsQuery({
        variables: {
          filter: {
            sku: { eq: sku }
          }
        }
      })
      const items = data?.products?.items ?? []
      const productItem = items?.find((it) => it?.sku === sku)
      if (productItem?.documents_asset_family_attribute) {
        const res = JSON.parse(productItem.documents_asset_family_attribute)
        const list = Object.keys(res).map((key) => {
          return res[key]
        })
        setRelatedDocuments(list)
      }
    } catch (err) {
      console.error('Fetch error:', err)
    }
  }, [getRelatedDocumentsQuery, sku])

  useEffect(() => {
    fetchRelatedDocuments()
  }, [fetchRelatedDocuments])

  return relatedDocuments.length > 0 ? (
    <CollapseBox
      title="Downloads"
      contentChildren={
        <StyledRelatedDocuments>
          {relatedDocuments.map((item) => {
            const href = `${mediaUrl}${item?.media ?? ''}`
            return (
              <div key={item.item_id}>
                <span>{item.type}</span>
                <Link href={href} target="_blank">
                  Download
                </Link>
              </div>
            )
          })}
        </StyledRelatedDocuments>
      }
    />
  ) : null
}

export default memo(RelatedDocuments)
