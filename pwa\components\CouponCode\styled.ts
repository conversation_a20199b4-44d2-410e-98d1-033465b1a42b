import styled from '@emotion/styled'

export const StyledCouponCode = styled.div`
  .coupon-title {
    margin-bottom: 20px;
    font-weight: 700;
    font-size: 24px;
    line-height: 30px;
    letter-spacing: 0;

    span {
      font-weight: 400;
      font-size: 13px;
      line-height: 19px;
      letter-spacing: 0;
    }
  }

  form {
    display: grid;
    grid-template-columns: 1fr 189px;
    grid-column-gap: 8px;
    align-items: center;

    .${({ theme }) => theme.namespace} {
      &-form-item {
        margin-bottom: 0;
      }

      &-input {
        height: 44px;
        padding: 8px 14px;
        border-radius: 3px;
        border: 1px solid #d9d9d9 !important;
        box-shadow: none !important;
        font-weight: 400;
        font-size: 16px;
        line-height: 24px;
        letter-spacing: 0.02em;
        color: var(--color-font);

        &::placeholder {
          font-weight: 400;
          font-size: 16px;
          line-height: 24px;
          letter-spacing: 0.02em;
          color: #777;
        }
      }

      &-btn {
        width: 189px;
        height: 44px;
        border: 2px solid #003865;
        border-radius: 3px;

        span {
          font-weight: 700;
          font-size: 14px;
          line-height: 20px;
          letter-spacing: 0.01em;
          color: var(--color-font);
        }
      }
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    form {
      grid-template-columns: 1fr auto;

      .${({ theme }) => theme.namespace} {
        &-input {
          padding: 8px 12px;
        }

        &-btn {
          width: unset;
        }
      }
    }
  }
`

export const StyledCouponCollapse = styled.div`
  border: 1px solid #d9d9d9;
  border-radius: 3px;

  .${({ theme }) => theme.namespace}-collapse {
    &-header {
      align-items: center !important;
      padding: 0 16px !important;
      height: 58px;

      &-text {
        font-weight: 700;
        font-size: 20px;
        line-height: 26px;
        letter-spacing: 0;

        span {
          font-weight: 400;
          font-size: 13px;
          line-height: 19px;
          letter-spacing: 0;
        }
      }

      .expand-icon {
        transform: rotate(90deg);

        &.is-active {
          transform: rotate(-90deg);
        }
      }
    }

    &-content-box {
      padding-top: 0 !important;
    }
  }
`
