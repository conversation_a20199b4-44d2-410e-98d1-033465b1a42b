# Silksoftwarecorp Blog 模块

这个模块扩展了Amasty Blog的功能，添加了Blog Home Page Description配置功能。

## 功能: Blog Home Page Description配置

### 实现的文件：
- `etc/adminhtml/system.xml` - 添加系统配置字段
- `Model/Config/Source/Block.php` - 自定义CMS Block选项源，添加空选项和详细信息
- `Helper/Data.php` - 配置值获取Helper，处理CMS Block内容

### 配置位置：
**Admin Panel > Stores > Configuration > Blog Pro > Search Engine Optimization > Blog Home Page Description (CMS Block)**

### 功能说明：
- 在Amasty Blog的SEO配置组中添加了"Blog Home Page Description (CMS Block)"字段
- 自定义CMS Block选择器，提供详细的Block信息
- 下拉框选择器，显示格式：`Block Title (ID: 123, Identifier: block-identifier)`
- 包含"-- Please Select --"空白选项
- 支持store级别的配置
- 如果CMS Block被禁用或不存在，Helper返回null

### CMS Block处理特性：
- 自定义CMS Block选择器，提供最佳用户体验
- 显示Block ID和Identifier，便于管理员区分不同的Block
- 添加"-- Please Select --"空白选项，提供更好的用户体验
- 支持CMS Block identifier（处理重复标识符的情况）
- 自动处理CMS Block的激活状态检查
- 完整的错误处理和日志记录
- 支持所有CMS过滤器功能（变量、小部件、PageBuilder等）

### 选项格式示例：
```
-- Please Select --
About Us (ID: 1, Identifier: about-us)
Contact Info (ID: 3, Identifier: contact-info)
Footer Links (ID: 5, Identifier: footer-links)
```

### Helper方法：
```php
// 获取配置的CMS Block identifier
$blockIdentifier = $this->blogHelper->getBlogHomePageDescriptionBlockIdentifier($storeId);

// 获取CMS Block的HTML内容
$content = $this->blogHelper->getBlogHomePageDescription($storeId);
```

## 安装说明：

1. 确保模块文件已正确放置
2. 运行以下命令：
```bash
php bin/magento setup:upgrade
php bin/magento setup:di:compile
php bin/magento cache:clean
```

## 依赖模块：
- Amasty_Blog
- Magento_Cms

## 注意事项：
- 自定义CMS Block选择器，显示详细的Block信息便于管理
- 提供空白选项，允许用户不选择任何CMS Block
- 如果选择的CMS Block不存在或被禁用，Helper返回null
- 建议在CMS Block中使用PageBuilder创建丰富的内容
- 配置值存储的是CMS Block的identifier，不是ID
- CMS Block内容经过完整的CMS过滤器处理，包含所有动态内容
- 选项按Block标题字母顺序排序，便于查找 