<?php

namespace Silksoftwarecorp\RealTimeB2BPricing\Storage;

use Magento\Framework\App\CacheInterface;
use Magento\Framework\Serialize\Serializer\Json as JsonSerializer;
use Silksoftwarecorp\RealTimeB2BPricing\Api\Data\B2BPriceItemInterface;
use Silksoftwarecorp\RealTimeB2BPricing\Api\Data\B2BPriceItemInterfaceFactory;
use Silksoftwarecorp\RealTimeB2BPricing\Helper\Data as DataHelper;
use Silksoftwarecorp\RealTimeB2BPricing\Api\PriceStorageInterface;
use Silksoftwarecorp\RealTimeB2BPricing\Cache\Type as CacheType;

class Cache implements PriceStorageInterface
{
    /**
     * @var CacheInterface
     */
    protected $cache;

    /**
     * @var DataHelper
     */
    protected $helper;

    /**
     * @var StorageKeyGenerator
     */
    protected $keyGenerator;

    /**
     * @var B2BPriceItemInterfaceFactory
     */
    protected $priceItemFactory;

    /**
     * @var JsonSerializer
     */
    protected $serializer;

    /**
     * @param CacheInterface $cache
     * @param DataHelper $helper
     * @param StorageKeyGenerator $keyGenerator
     * @param B2BPriceItemInterfaceFactory $priceItemFactory
     * @param JsonSerializer $serializer
     */
    public function __construct(
        CacheInterface $cache,
        DataHelper $helper,
        StorageKeyGenerator $keyGenerator,
        B2BPriceItemInterfaceFactory $priceItemFactory,
        JsonSerializer $serializer
    ) {
        $this->cache = $cache;
        $this->helper = $helper;
        $this->keyGenerator = $keyGenerator;
        $this->priceItemFactory = $priceItemFactory;
        $this->serializer = $serializer;
    }

    public function save(
        string $customerId,
        string $locationId,
        string $sku,
        B2BPriceItemInterface $priceItem,
        ?int $ttl = null
    ): bool {
        $key = $this->keyGenerator->generate($customerId, $locationId, $sku);
        $this->cache->save(
            $this->serializer->serialize($priceItem->toArray()),
            $key,
            [CacheType::CACHE_TAG],
            $ttl ?: $this->helper->getCacheLifetime()
        );

        return true;
    }

    public function get(
        string $customerId,
        string $locationId,
        string $sku
    ): ?B2BPriceItemInterface {
        $key = $this->keyGenerator->generate($customerId, $locationId, $sku);
        $data = $this->cache->load($key);
        if ($data) {
            try {
                $data = $this->serializer->unserialize($data);

                return $this->priceItemFactory->create([
                    'data' => $data,
                ]);
            } catch (\Exception $e) {}
        }

        return null;
    }

    public function delete(string $customerId, string $locationId, string $sku): bool
    {
        $key = $this->keyGenerator->generate($customerId, $locationId, $sku);
        $this->cache->remove($key);

        return true;
    }

    public function has(string $customerId, string $locationId, string $sku): bool
    {
        $key = $this->keyGenerator->generate($customerId, $locationId, $sku);
        $data = $this->cache->load($key);

        return $data !== null;
    }

    public function clear(): bool
    {
        $this->cache->clean([CacheType::CACHE_TAG]);

        return true;
    }
}
