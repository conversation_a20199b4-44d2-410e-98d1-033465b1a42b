import { clsx } from 'clsx'
import Image from 'next/image'
import { Carousel } from 'antd'
import { nanoid } from 'nanoid'
import { useRef, useState, useMemo, useCallback } from 'react'

import { MediaLayout } from '@/ui'
import { useProductContext } from '@/route/ProductProvider'
import BannerLine from '@/components/BannerLine'

import Arrow from './arrow'
import { StyledBase, StyledMainImg, StyledImage } from './styled'

const BaseImages = () => {
  const { product } = useProductContext()
  const sliderRef = useRef<any>(null)

  const [activeIndex, setActiveIndex] = useState<number>(0)

  const mediaList = useMemo(() => {
    return product?.media_gallery ?? []
  }, [product])

  const slidesToShow = useMemo(() => {
    return 4
  }, [])

  const arrowVisible = useMemo(() => {
    return mediaList.length > slidesToShow
  }, [mediaList, slidesToShow])

  const bannerTotal = useMemo(() => {
    return mediaList.length
  }, [mediaList])

  const handleSlide = useCallback((index: number) => {
    setActiveIndex(index)
    sliderRef?.current?.goTo(index)
  }, [])

  const handleOnChange = useCallback((index: number) => {
    setActiveIndex(index)
  }, [])

  const handlePrev = useCallback(() => {
    if (sliderRef?.current?.prev) {
      sliderRef.current.prev()
    }
  }, [sliderRef])

  const handleNext = useCallback(() => {
    if (sliderRef?.current?.next) {
      sliderRef.current.next()
    }
  }, [sliderRef])

  return (
    <StyledBase>
      <StyledMainImg>
        <Carousel
          ref={sliderRef}
          slidesPerRow={1}
          slidesToShow={1}
          dots={false}
          arrows={false}
          draggable
          infinite
          beforeChange={handleOnChange}>
          {mediaList.map((media: any) => {
            return (
              <div key={nanoid()} className="main-item">
                <Image
                  className="main-item-img"
                  src={media.url}
                  alt={media.label}
                  width={578}
                  height={578}
                  aria-hidden
                />
              </div>
            )
          })}
        </Carousel>
      </StyledMainImg>
      {/* Desktop Sliders Controller */}
      <MediaLayout>
        <div className="carousel">
          <Carousel
            slidesPerRow={1}
            slidesToShow={slidesToShow}
            slickGoTo={1}
            dots={false}
            infinite={false}
            // draggable
            arrows={arrowVisible}
            nextArrow={
              <Arrow
                icon={
                  <svg
                    className="right"
                    width="1em"
                    height="1em"
                    fill="currentColor"
                    aria-hidden="true"
                    focusable="false">
                    <use xlinkHref="#icon-right" />
                  </svg>
                }
              />
            }
            prevArrow={
              <Arrow
                icon={
                  <svg
                    className="left"
                    width="1em"
                    height="1em"
                    fill="currentColor"
                    aria-hidden="true"
                    focusable="false">
                    <use xlinkHref="#icon-left" />
                  </svg>
                }
              />
            }>
            {mediaList.map((media: any, index: number) => {
              return (
                <div key={nanoid()}>
                  <StyledImage
                    className={clsx({ active: activeIndex === index })}
                    src={media.url}
                    alt={media.label}
                    aria-hidden
                    onClick={() => {
                      handleSlide(index)
                    }}
                  />
                </div>
              )
            })}
          </Carousel>
        </div>
      </MediaLayout>
      {/* Mobile Sliders Controller */}
      <MediaLayout type="mobile">
        <BannerLine
          curIndex={activeIndex}
          totalCount={bannerTotal}
          handlePrev={handlePrev}
          handleNext={handleNext}
        />
      </MediaLayout>
    </StyledBase>
  )
}

export default BaseImages
