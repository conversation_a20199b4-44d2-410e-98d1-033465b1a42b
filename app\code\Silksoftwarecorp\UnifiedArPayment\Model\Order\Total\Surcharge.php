<?php

namespace Silksoftwarecorp\UnifiedArPayment\Model\Order\Total;

use Magento\Sales\Model\Order\Total\AbstractTotal;

class Surcharge extends AbstractTotal
{
    /**
     * @param \Magento\Sales\Model\Order $order
     * @return $this
     */
    public function collect(\Magento\Sales\Model\Order $order)
    {
        $surchargeAmount = $order->getData('unified_ar_surcharge_amount');
        if ($surchargeAmount && $surchargeAmount > 0) {
            $order->setUnifiedArSurchargeAmount($surchargeAmount);
        }
        
        return $this;
    }

    /**
     * @param \Magento\Sales\Model\Order $order
     * @return $this
     */
    public function fetch(\Magento\Sales\Model\Order $order)
    {
        $surchargeAmount = $order->getData('unified_ar_surcharge_amount');
        if ($surchargeAmount && $surchargeAmount > 0) {
            $this->setCode('unified_ar_surcharge');
            $this->setTitle(__('Surcharge'));
            $this->setValue($surchargeAmount);
        }
        
        return $this;
    }
} 