import dynamic from 'next/dynamic'
import { isEmpty } from 'lodash-es'
import { Tag } from 'antd'
import { FormattedMessage } from 'react-intl'
import dayjs from 'dayjs'

import { useOrderDetails } from '@/hooks/AccountOrderList'
import BasePrice from '@/components/BasePrice'
import DefaultAddress from '@/components/DefaultAddress'
import CommonAccountPageLayout from '@/components/Common/CommonAccountPageLayout'
import CommonButton from '@/components/Common/CommonButton'
import CommonTable from '@/components/Common/CommonTable'
import CommonLink from '@/components/Common/CommonLink'
import CommonLoading from '@/components/Common/CommonLoading'

import ModelLayout from './modelLayout'
import {
  DetailsTableDescriptionWarp,
  DetailsFooterWrap,
  DetailsFooterItem,
  DetailsAddress,
  DetailsProductName,
  FooterWrap,
  OrderDetailsqty,
  StyledOrderDate,
  StyledTagItem
} from './styled'

const OrderInvoices = dynamic(() => import('./OrderInvoices'), {
  ssr: false
})
const OrderShipments = dynamic(() => import('./OrderShipments'), {
  ssr: false
})

const OrderDetails = ({ id }) => {
  const {
    items,
    total,
    discount,
    invoices,
    shipments,
    isLoading,
    orderDetails,
    detailsTable,
    paymentMethod,
    billingAddress,
    shippingMethod,
    shippingAddress,
    handleChangeVisible,
    suffix,
    handleReorder,
    isMobile
  } = useOrderDetails(id)
  const orderTitle = `Order #${id}`

  const columns = [
    {
      title: <FormattedMessage id="global.productName" />,
      dataIndex: 'product_name',
      key: 'product_name',
      render: (param, record) => {
        return (
          <DetailsProductName>
            <div>{param}</div>
            {record.selected_options.map((option) => {
              return (
                <div key={`${option.label}_${param}`}>
                  <span className="orderDetails__label">{`${option.label}: `}</span>
                  <span>{option.value}</span>
                </div>
              )
            })}
          </DetailsProductName>
        )
      }
    },
    {
      title: 'Item ID',
      dataIndex: 'product_sku',
      key: 'product_sku',
      width: 200,
      render: (param) => {
        return (
          <CommonLink underline href={`/${param}${suffix}`}>
            {param}
          </CommonLink>
        )
      }
    },
    {
      title: <FormattedMessage id="global.price" />,
      dataIndex: 'product_sale_price',
      key: 'product_sale_price',
      width: 160,
      render: (param) => <BasePrice value={param.value} unit />
    },
    {
      title: 'Quantity',
      dataIndex: 'quantity_ordered',
      key: 'quantity_ordered',
      width: 120,
      align: 'center',
      render: (param, record) => {
        return (
          <div>
            <span>{param}</span>
            {!!record.quantity_shipped && (
              <OrderDetailsqty>
                <FormattedMessage id="order.shipped" />
                {`: ${record.quantity_shipped}`}
              </OrderDetailsqty>
            )}
            {!!record.quantity_canceled && (
              <OrderDetailsqty>
                <FormattedMessage id="order.canceled" />
                {`: ${record.quantity_canceled}`}
              </OrderDetailsqty>
            )}
          </div>
        )
      }
    },
    {
      title: <FormattedMessage id="global.subtotal" />,
      dataIndex: 'product_sale_price',
      key: 'subtotal',
      align: 'right',
      width: 160,
      render: (param, record) => <BasePrice value={param.value * record.quantity_ordered} />
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      align: 'center',
      render: (param) => {
        const colorArray = [
          { key: 'Pending', value: 'default' },
          { key: 'Processing', value: 'processing' },
          { key: 'On Hold', value: 'warning' },
          { key: 'Complete', value: 'success' },
          { key: 'Canceled', value: 'error' },
          { key: 'Available', value: '#87d068' }
        ]

        const color = colorArray.find((item) => item.key === param)

        return (
          <StyledTagItem>
            {color?.value ? <Tag color={color.value}>{param}</Tag> : param}
          </StyledTagItem>
        )
      }
    }
  ]

  return (
    <CommonLoading spinning={isLoading}>
      {!isEmpty(orderDetails) && (
        <CommonAccountPageLayout
          title={orderTitle}
          breadcrumbItems={[
            { url: '/account', name: 'Account' },
            { url: '/account/orders', name: 'Order History' },
            { name: orderTitle }
          ]}
          breadcrumbProps={
            isMobile ? { items: [], rootName: 'Order History', rootUrl: '/account/orders' } : {}
          }>
          {orderDetails?.order_date && (
            <StyledOrderDate>
              Order Date: {dayjs(orderDetails.order_date).format('MM/DD/YYYY')}
            </StyledOrderDate>
          )}
          <div>
            <CommonButton height={44} onClick={handleReorder}>
              Reorder
            </CommonButton>
          </div>
          <DetailsAddress>
            {!isEmpty(billingAddress) && (
              <DefaultAddress
                title="order.billingAddress"
                address={billingAddress}
                isClick={false}
              />
            )}
            {!isEmpty(shippingMethod) && (
              <ModelLayout title="Fulfillment Method" method={shippingMethod} />
            )}
            {!isEmpty(shippingAddress) && (
              <DefaultAddress
                titleText="Fulfillment Location"
                address={shippingAddress}
                isClick={false}
              />
            )}
            <ModelLayout title="Payment Method" method={paymentMethod} />
          </DetailsAddress>
          <DetailsTableDescriptionWarp>
            <div
              className={detailsTable.isItems ? 'active' : ''}
              aria-hidden
              onClick={() => {
                handleChangeVisible('itemsOrdered')
              }}>
              <FormattedMessage id="order.itemsOrdered" />
            </div>
            {!isEmpty(invoices) && (
              <div
                className={detailsTable.isInvoices ? 'active' : ''}
                aria-hidden
                onClick={() => {
                  handleChangeVisible('Invoices')
                }}>
                <FormattedMessage id="order.invoices" />
              </div>
            )}
            {!isEmpty(shipments) && (
              <div
                aria-hidden
                className={detailsTable.isShipments ? 'active' : ''}
                onClick={() => {
                  handleChangeVisible('Shipments')
                }}>
                <FormattedMessage id="order.orderShipments" />
              </div>
            )}
          </DetailsTableDescriptionWarp>

          {detailsTable.isItems && (
            <>
              <CommonTable
                lineStyle
                rowKey={(record) => record.product_sku}
                columns={columns}
                dataSource={items}
                pagination={false}
              />

              <FooterWrap>
                <div>
                  <DetailsFooterWrap>
                    <DetailsFooterItem>
                      <FormattedMessage id="global.subtotal" />
                    </DetailsFooterItem>
                    <DetailsFooterItem>
                      <BasePrice value={total?.subtotal.value} />
                    </DetailsFooterItem>
                  </DetailsFooterWrap>
                  {!!discount && (
                    <DetailsFooterWrap>
                      <DetailsFooterItem>
                        <FormattedMessage id="order.discount" />
                      </DetailsFooterItem>
                      <DetailsFooterItem>
                        <BasePrice value={-discount} />
                      </DetailsFooterItem>
                    </DetailsFooterWrap>
                  )}
                  <DetailsFooterWrap>
                    <DetailsFooterItem>
                      <FormattedMessage id="order.shippingHandling" />
                    </DetailsFooterItem>
                    <DetailsFooterItem>
                      <BasePrice value={total?.total_shipping.value} />
                    </DetailsFooterItem>
                  </DetailsFooterWrap>
                  <DetailsFooterWrap className="order-total">
                    <DetailsFooterItem>Order Total</DetailsFooterItem>
                    <DetailsFooterItem>
                      <BasePrice value={total?.grand_total.value} />
                    </DetailsFooterItem>
                  </DetailsFooterWrap>
                </div>
              </FooterWrap>
            </>
          )}
          {detailsTable.isInvoices && <OrderInvoices invoices={invoices} />}
          {detailsTable.isShipments && <OrderShipments shipments={shipments} />}
        </CommonAccountPageLayout>
      )}
    </CommonLoading>
  )
}

export default OrderDetails
