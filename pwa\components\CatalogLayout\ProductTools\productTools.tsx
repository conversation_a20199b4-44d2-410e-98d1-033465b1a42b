import { clsx } from 'clsx'
import { Select, Spin } from 'antd'

import { useProductTools } from '@/hooks/CatalogLayout'
import RequisitionModal from '@/components/RequisitionModal'
import HowToMeasure from '@/components/HowToMeasure'

import { StyledProductTools } from './styled'

const ProductTools = ({
  sku = '',
  form,
  isSimple = false,
  productItems = [],
  measureValue = ''
}: any) => {
  const {
    curList,
    listOpen,
    handleListDropdownVisibleChange,
    listModalVisible,
    handleModalCancel,
    requisitionList,
    handleToList,
    handleUpdateRequisition,
    listLoading,
    disabled,
    handleCreateNewListCallback,
    hasList
  } = useProductTools({ sku, form, isSimple, productItems })

  return (
    <Spin spinning={listLoading}>
      <StyledProductTools className={clsx({ 'is-desabled': disabled, 'is-simple': isSimple })}>
        <Select
          disabled={disabled}
          value={curList}
          className="list-select"
          placeholder="Add to List"
          open={listOpen}
          onChange={handleToList}
          onDropdownVisibleChange={handleListDropdownVisibleChange}
          suffixIcon={
            <svg
              className="select-icon"
              width="1em"
              height="1em"
              fill="currentColor"
              aria-hidden="true"
              focusable="false">
              <use xlinkHref={isSimple ? '#icon-select-suffix-primary' : '#icon-select-suffix'} />
            </svg>
          }>
          <Select.Option key="1" value="" className="option-title">
            <span className="option-title-text">
              {isSimple && '+ '}
              Add to List
            </span>
          </Select.Option>
          {hasList && (
            <>
              {requisitionList.map((item) => (
                <Select.Option key={item.uid} value={item.uid}>
                  {item.name}
                </Select.Option>
              ))}
              <Select.Option value="create-new-list">+ Create New List</Select.Option>
            </>
          )}
        </Select>

        {measureValue && (
          <div className="btn-measure">
            <HowToMeasure defaultMeasureModalTabKey={measureValue} />
          </div>
        )}

        {/* <Button>
          <svg
            className="contrast"
            width="1em"
            height="1em"
            fill="currentColor"
            aria-hidden="true"
            focusable="false"
          >
            <use xlinkHref="#icon-contrast" />
          </svg>
          <FormattedMessage id="global.addToCompare" />
        </Button>
        <Button>
          <svg
            className="download"
            width="1em"
            height="1em"
            fill="currentColor"
            aria-hidden="true"
            focusable="false"
          >
            <use xlinkHref="#icon-download" />
          </svg>
          <FormattedMessage id="global.download" />
        </Button> */}

        {listModalVisible && (
          <RequisitionModal
            visible={listModalVisible}
            onCancel={handleModalCancel}
            handleUpdateRequisition={handleUpdateRequisition}
            handleCreateNewListCallback={handleCreateNewListCallback}
          />
        )}
      </StyledProductTools>
    </Spin>
  )
}

export default ProductTools
