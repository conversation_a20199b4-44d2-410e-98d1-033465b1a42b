import styled from '@emotion/styled'

export const StyledFooter = styled.footer`
  .copyright {
    margin: 0 auto;
    max-width: ${({ theme }) => theme.breakPoint.xl}px;
    height: 67px;
    display: flex;
    align-items: center;

    p {
      margin: 0 auto;
      width: 100%;
      max-width: 1180px;
      font-size: 13px;
      font-weight: 400;
      line-height: 19px;
      letter-spacing: 0.01em;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      color: var(--color-font);
    }
  }

  &.is-invoice-history-page {
    padding-bottom: 85px;
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    .copyright {
      p {
        text-align: center;
      }
    }

    &.is-invoice-history-page {
      padding-bottom: 69px;
    }
  }
`

export const StyledNewsletterBlock = styled.div`
  margin: 0 auto;
  max-width: ${({ theme }) => theme.breakPoint.xl}px;

  .contained {
    position: relative;
    height: 312px;

    .pagebuilder-column-group {
      position: absolute;
      left: 0;
      bottom: -75px;
      z-index: 1;

      .pagebuilder-column {
        box-sizing: border-box;
        padding: 35px 55px;
        width: 562px !important;
        height: 327px;
        background: #fff;
        border-radius: 4px;
        display: flex;
        flex-direction: column;
        justify-content: space-between !important;

        h3 {
          font-size: 32px;
          font-weight: 700;
          line-height: 38px;
          text-align: left;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          color: var(--color-font);
        }

        p {
          font-size: 15px;
          font-weight: 400;
          line-height: 23px;
          letter-spacing: 0.01em;
          text-align: left;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          color: var(--color-text);
        }

        button {
          height: 49px;
          padding: 0 24px;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 3px;
          text-transform: initial;

          span {
            font-size: 16px;
            font-weight: 700;
            line-height: 21px;
            letter-spacing: 0.03em;
            text-align: left;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;
          }
        }
      }
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    .pagebuilder__row {
      background-size: cover !important;
      background-color: #003865;
      position: relative;

      .contained {
        position: absolute;
        z-index: 2;
        padding: 0 16px;
        height: 480px;

        .pagebuilder-column-group {
          margin: 0 16px;
          left: 0;
          bottom: 0;

          .pagebuilder-column {
            padding: 24px;
            width: 100% !important;
            height: auto;

            h3 {
              margin-bottom: 10px;
              font-weight: 700;
              font-size: 24px;
              line-height: 30px;
              letter-spacing: 0;
              text-align: center;
            }

            p {
              text-align: center;
            }

            .inline div {
              width: 100%;

              button {
                padding: 0;
                margin-top: 20px;
                width: 100%;

                span {
                  text-align: center;
                }
              }
            }
          }
        }
      }
    }
  }
`

export const StyledLinks = styled.div`
  .cms-block__footer-links {
    .contained {
      max-width: ${({ theme }) => theme.breakPoint.xl}px;

      .footer-links-container {
        padding: 122px 0 80px;
        margin: 0 auto;
        max-width: 1440px;
        background: #003865;

        .footer-links-layout {
          max-width: 1180px;
          margin: 0 auto;
          display: grid;
          grid-template-columns: repeat(4, 1fr);

          .footer-links-column {
            h3 {
              font-size: 18px;
              font-weight: 700;
              line-height: 29px;
              letter-spacing: 0.04em;
              text-align: left;
              text-underline-position: from-font;
              text-decoration-skip-ink: none;
              color: #fff;
            }

            .footer-links-groups {
              display: flex;
              flex-direction: column;

              a {
                font-size: 16px;
                font-weight: 400;
                line-height: 30px;
                letter-spacing: 0.04em;
                text-align: left;
                text-underline-position: from-font;
                text-decoration-skip-ink: none;
                color: #fff;
                text-decoration: none;
              }
            }

            .get-in-touch {
              font-size: 16px;
              font-weight: 400;
              line-height: 29px;
              letter-spacing: 0.04em;
              text-align: left;
              text-underline-position: from-font;
              text-decoration-skip-ink: none;
              color: #fff;

              & > div {
                margin-top: 2px;
                color: #fff;
              }

              b {
                text-decoration: underline;
              }

              .sales-email {
                margin-left: 6px;
                font-size: 16px;
                font-weight: bold;
                line-height: 29px;
                letter-spacing: 0.04em;
                text-align: left;
                text-underline-position: from-font;
                text-decoration-skip-ink: none;
                color: #fff;
                text-decoration: underline;
              }

              .contact-us a {
                color: #fff !important;
              }

              .svg-groups {
                margin-top: 12px;

                svg:not(:first-of-type) {
                  margin-left: 10px;
                }
              }
            }
          }
        }
      }
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    .cms-block__footer-links {
      .contained {
        .footer-links-container {
          padding: 200px 0 24px;

          .footer-links-layout {
            grid-template-columns: 1fr;

            .footer-links-column {
              padding: 17px 16px;

              &:not(:last-of-type) {
                border-bottom: 1px solid #fff;
              }

              h3 {
                position: relative;
                margin-bottom: 0;
                padding: 5px 0;

                &::after {
                  content: '';
                  display: block;
                  position: absolute;
                  top: 50%;
                  right: 0;
                  margin-top: -6px;
                  width: 10px;
                  height: 10px;
                  background-image: url('/images/collapse.png');
                  background-position: center;
                  background-repeat: no-repeat;
                }

                &.active {
                  &::after {
                    background-image: url('/images/collapse-hide.png');
                  }

                  & + div {
                    &.footer-links-groups {
                      display: flex;
                    }
                    &.get-in-touch {
                      display: block;
                    }
                  }
                }
              }

              .footer-links-groups {
                display: none;
                align-items: flex-start;
                margin-top: 4px;

                a {
                  margin-top: 5px;
                }
              }

              .get-in-touch {
                display: none;
                padding-bottom: 22px;

                .svg-groups {
                  margin-top: 20px;
                }
              }
            }
          }
        }
      }
    }

    &.is-login {
      .cms-block__footer-links .footer-links-container {
        padding-top: 24px;
      }
    }
  }
`
