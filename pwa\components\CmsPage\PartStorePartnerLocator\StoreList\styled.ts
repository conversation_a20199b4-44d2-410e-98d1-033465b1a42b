import styled from '@emotion/styled'

export const StyledStoreList = styled.div`
  position: relative;
  display: grid;
  grid-auto-flow: column;
  grid-template-rows: auto auto auto 1fr;
  grid-gap: 16px;
  height: 725px;

  h2 {
    margin-bottom: 0;
    font-weight: 700;
    font-size: 22px;
    line-height: 28px;
    letter-spacing: 0;
  }

  .store-filter {
    padding: 0 24px;
    display: flex;
    align-items: center;
    width: 100%;
    height: 72px;
    background: #f5f5f5;
    border-radius: 5px;

    .filter-title {
      font-weight: 700;
      font-size: 18px;
      line-height: 24px;
      letter-spacing: 0.01em;
    }

    .filter-item {
      display: flex;
      align-items: center;
      margin-left: 16px;

      div {
        margin-right: 8px;
      }
    }
  }

  .store-items {
    padding-bottom: 60px;
    overflow: auto;

    .item {
      padding: 22px 24px;
      margin-bottom: 8px;
      border: 1px solid #d9d9d9;
      border-radius: 5px;
    }
  }

  .store-empty {
    margin-top: 100px;
    text-align: center;
  }

  .panel-mask {
    position: absolute;
    right: 0;
    bottom: 0;
    z-index: 9;
    width: 100%;
    height: 50px;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 100%);
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    height: auto;
    padding: 16px 16px 0;

    .store-items {
      height: 600px;

      .item {
        padding: 22px 18px;
      }
    }
  }
`
