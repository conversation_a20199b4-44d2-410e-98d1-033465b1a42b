<?php

namespace Silksoftwarecorp\UnifiedAR\Helper;

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Store\Model\ScopeInterface;

class Data extends AbstractHelper
{
    const XML_PATH_GENERAL_ACTIVE = 'unified_ar/general/active';
    const XML_PATH_GENERAL_DEBUG = 'unified_ar/general/debug';

    const XML_PATH_API_ENVIRONMENT = 'unified_ar/api/environment';

    const XML_PATH_API_URL ='unified_ar/api/url';

    const XML_PATH_API_MERCHANT_KEY ='unified_ar/api/merchant_key';

    const XML_PATH_API_API_KEY ='unified_ar/api/api_key';

    const XML_PATH_API_SANDBOX_URL = 'unified_ar/api/sandbox_url';

    const XML_PATH_API_SANDBOX_MERCHANT_KEY ='unified_ar/api/sandbox_merchant_key';

    const XML_PATH_API_SANDBOX_API_KEY ='unified_ar/api/sandbox_api_key';

    const XML_PATH_API_TIMEOUT = 'unified_ar/api/timeout';

    const XML_PATH_PAY_NOW_KEY = 'unified_ar/invoice/pay_now_key';
    const XML_PATH_PAY_NOW_LINK = 'unified_ar/invoice/pay_now_link';
    const XML_PATH_COMPLETE_URL = 'unified_ar/invoice/complete_url';

    public function isActive($storeId = null): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_GENERAL_ACTIVE,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function isDebugged($storeId = null): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_GENERAL_DEBUG,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getApiEnvironment($storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_API_ENVIRONMENT,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getApiUrl($storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_API_URL,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getApiKey($storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_API_API_KEY,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getMerchantKey($storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_API_MERCHANT_KEY,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getSandboxApiUrl($storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_API_SANDBOX_URL,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getSandboxApiKey($storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_API_SANDBOX_API_KEY,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getSandboxMerchantKey($storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_API_SANDBOX_MERCHANT_KEY,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getApiTimeout($storeId = null): int
    {
        return (int)$this->scopeConfig->getValue(
            self::XML_PATH_API_TIMEOUT,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getPayNowKey($storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_PAY_NOW_KEY,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getPayNowLink($storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_PAY_NOW_LINK,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    public function getCompletedRedirectUrl($storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_COMPLETE_URL,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }
}
