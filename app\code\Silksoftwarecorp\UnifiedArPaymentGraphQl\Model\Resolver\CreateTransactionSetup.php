<?php

namespace Silksoftwarecorp\UnifiedArPaymentGraphQl\Model\Resolver;

use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Silksoftwarecorp\UnifiedArPayment\Model\TransactionSetup;
use Magento\Framework\GraphQl\Exception\GraphQlAuthorizationException;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Silksoftwarecorp\ERPCompany\Model\Company\ERPRepository;

class CreateTransactionSetup implements ResolverInterface
{
    /**
     * @var TransactionSetup
     */
    private $transactionSetup;

    /**
     * @var CustomerRepositoryInterface
     */
    private $customerRepository;

    /**
     * @var ERPRepository
     */
    private $erpRepository;

    /**
     * @param TransactionSetup $transactionSetup
     * @param CustomerRepositoryInterface $customerRepository
     * @param ERPRepository $erpRepository
     */
    public function __construct(
        TransactionSetup $transactionSetup,
        CustomerRepositoryInterface $customerRepository,
        ERPRepository $erpRepository
    ) {
        $this->transactionSetup = $transactionSetup;
        $this->customerRepository = $customerRepository;
        $this->erpRepository = $erpRepository;
    }

    /**
     * @inheritdoc
     */
    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
        /**
         * @var ContextInterface $context
         */
        if (false === $context->getExtensionAttributes()->getIsCustomer()) {
            throw new GraphQlAuthorizationException(__('The request is allowed for logged in customer.'));
        }

        $currentCustomerId = $context->getUserId();

        try {
            // Get customer and check if they have a company
            $customer = $this->customerRepository->getById($currentCustomerId);
            $companyAttributes = $customer->getExtensionAttributes()?->getCompanyAttributes();
            
            if (!$companyAttributes || !$companyAttributes->getCompanyId()) {
                return [
                    'success' => false,
                    'message' => 'Customer does not have an associated company',
                    'transaction_setup_url' => null,
                    'iframe_url' => null,
                    'error_code' => 'NO_COMPANY'
                ];
            }

            // Get the company's ERP customer ID
            $companyId = $companyAttributes->getCompanyId();
            $erpCompany = $this->erpRepository->get($companyId);
            
            if (!$erpCompany || !$erpCompany->getErpCustomerId()) {
                return [
                    'success' => false,
                    'message' => 'Company does not have an ERP customer ID configured',
                    'transaction_setup_url' => null,
                    'iframe_url' => null,
                    'error_code' => 'NO_ERP_CUSTOMER_ID'
                ];
            }

            $address = $args['address'] ?? null;
            
            if (!$address) {
                throw new GraphQlInputException(__('Address is required'));
            }

            // Call the transaction setup model with the ERP customer ID
            $response = $this->transactionSetup->createTransactionSetup($address, $erpCompany->getErpCustomerId());

            return [
                'success' => $response['success'],
                'message' => $response['message'],
                'transaction_setup_url' => $response['transaction_setup_url'] ?? null,
                'iframe_url' => $response['iframe_url'] ?? null,
                'error_code' => $response['error_code'] ?? null
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'transaction_setup_url' => null,
                'iframe_url' => null,
                'error_code' => 'EXCEPTION'
            ];
        }
    }
} 