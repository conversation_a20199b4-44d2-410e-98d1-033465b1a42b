<?php

namespace Silksoftwarecorp\AmastyCustomForm\ViewModel\Answser\Email;

use Amasty\Base\Model\Serializer;
use Amasty\Customform\Api\Answer\AttachedFileDataProviderInterface;
use Amasty\Customform\Api\Data\AnswerInterface;
use Amasty\Customform\Model\Submit;
use Amasty\Customform\ViewModel\Answser\Email\FieldsDataProviderFactory;
use Magento\Framework\DataObject;
use Magento\Framework\DataObjectFactory;
use Magento\Framework\View\Element\BlockFactory;
use Magento\Framework\View\Element\Template;
use Silksoftwarecorp\AmastyCustomForm\Helper\Data as Helper;
use Silksoftwarecorp\AmastyCustomForm\Model\Customer\DataProvider\SalesRepDataProvider;
use Psr\Log\LoggerInterface;

class SubmittedFieldsRenderer extends \Amasty\Customform\ViewModel\Answser\Email\SubmittedFieldsRenderer
{

    /**
     * @var FieldsDataProviderFactory
     */
    private $fieldsDataProviderFactory;

    /**
     * @var BlockFactory
     */
    private $blockFactory;

    /**
     * @var string
     */
    private $emailFieldsTemplate;

    /**
     * @var Serializer
     */
    private $serializer;

    /**
     * @var AttachedFileDataProviderInterface
     */
    private $attachedFileDataProvider;

    /**
     * @var DataObjectFactory
     */
    private $dataObjectFactory;

    /**
     * @var Helper
     */
    private $helper;

    /**
     * @var SalesRepDataProvider
     */
    private $salesRepDataProvider;

    /**
     * @var LoggerInterface
     */
    private $logger;

    public function __construct(
        FieldsDataProviderFactory $fieldsDataProviderFactory,
        BlockFactory $blockFactory,
        Serializer $serializer,
        DataObjectFactory $dataObjectFactory,
        AttachedFileDataProviderInterface $attachedFileDataProvider,
        Helper $helper,
        SalesRepDataProvider $salesRepDataProvider,
        LoggerInterface $logger,
        string $emailFieldsTemplate = self::DEFAULT_TEMPLATE
    ) {
        parent::__construct(
            $fieldsDataProviderFactory,
            $blockFactory,
            $serializer,
            $dataObjectFactory,
            $attachedFileDataProvider,
            $emailFieldsTemplate
        );

        $this->fieldsDataProviderFactory = $fieldsDataProviderFactory;
        $this->blockFactory = $blockFactory;
        $this->emailFieldsTemplate = $emailFieldsTemplate;
        $this->serializer = $serializer;
        $this->attachedFileDataProvider = $attachedFileDataProvider;
        $this->dataObjectFactory = $dataObjectFactory;
        $this->helper = $helper;
        $this->salesRepDataProvider = $salesRepDataProvider;
        $this->logger = $logger;
    }

    public function render(AnswerInterface $answer, array &$attachments = []): string
    {
        try {
            $fields = $this->serializer->unserialize($answer->getResponseJson());
        } catch (\Exception $e) {
            $fields = [];
        }

        $viewModel = $this->fieldsDataProviderFactory->create();
        $fieldsData = $this->getFieldsForRendering($fields, $attachments);
        $fieldsData = array_merge($fieldsData, $this->getFieldsForSalesRep($answer));

        $viewModel->setFieldsData($fieldsData);

        $block = $this->blockFactory->createBlock(
            Template::class,
            [
                'data' => [
                    'view_model' => $viewModel,
                    'template' => $this->emailFieldsTemplate
                ]
            ]
        );

        return $block->toHtml();
    }

    /**
     * @param string[][] $formFields
     * @param array $attachments
     *
     * @return DataObject[]
     */
    private function getFieldsForRendering(array $formFields, array &$attachments): array
    {
        $result = [];

        foreach ($formFields as $field) {
            $value = $field[Submit::VALUE] ?? '';
            $type = $field[Submit::TYPE] ?? '';
            $label = $field[Submit::LABEL] ?? '';

            if ($type === self::TYPE_FILE) {
                if (is_array($value)) {
                    $filteredFiles = array_filter($value);

                    foreach ($filteredFiles as $fileName) {
                        if ($fileName) {
                            $this->addAttachment($attachments, $fileName);
                        }
                    }
                } else {
                    $this->addAttachment($attachments, $value);
                }
            }

            if (is_array($value)) {
                $filteredFiles = array_filter($value);
                $value = implode(', ', $filteredFiles);
            }

            $result[] = $this->dataObjectFactory->create([
                'data' => [
                    'label' => $label,
                    'value' => $value
                ]
            ]);
        }

        return $result;
    }

    private function getFieldsForSalesRep(AnswerInterface $answer): array
    {
        $result = [];

        try {
            $customerId = $answer->getCustomerId();

            // Check if this form should include sales rep information
            if (!$this->helper->shouldIncludeSalesRepInfo((string)$answer->getFormId()) || !$customerId) {
                return $result;
            }

            // Get sales rep data
            $salesRepData = $this->salesRepDataProvider->getSalesRepByCustomerId((int)$customerId);

            if (empty($salesRepData)) {
                return $result;
            }

            if (!empty($salesRepData['id'])) {
                $result[] = $this->dataObjectFactory->create([
                    'data' => [
                        'label' => __('Sales Rep ID'),
                        'value' => $salesRepData['id']
                    ]
                ]);
            }

            if (!empty($salesRepData['name'])) {
                $result[] = $this->dataObjectFactory->create([
                    'data' => [
                        'label' => __('Sales Rep Name'),
                        'value' => $salesRepData['name']
                    ]
                ]);
            }

            $this->logger->info(__('Sales rep information added to email'), [
                'customer_id' => $customerId,
                'form_id' => $answer->getFormId(),
                'sales_rep' => $salesRepData,
            ]);
        } catch (\Exception $e) {
            $this->logger->critical(__('Error getting sales rep fields'), [
                'answer_id' => $answer->getAnswerId() ?? 'unknown',
                'form_id' => $answer->getFormId() ?? 'unknown',
                'customer_id' => $answer->getCustomerId() ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }

        return $result;
    }

    /**
     * @param array $attachments
     * @param string|string[] $value
     */
    private function addAttachment(array &$attachments, $value): void
    {
        try {
            $attachments[$value] = $this->attachedFileDataProvider->getContents($value);
            // phpcs:ignore Magento2.CodeAnalysis.EmptyBlock.DetectedCatch
        } catch (\Throwable $e) {
        }
    }
}
