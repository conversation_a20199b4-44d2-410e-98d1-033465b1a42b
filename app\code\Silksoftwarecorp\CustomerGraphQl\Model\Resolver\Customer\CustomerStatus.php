<?php

namespace Silksoftwarecorp\CustomerGraphQl\Model\Resolver\Customer;

use Magento\Eav\Api\AttributeRepositoryInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;

class CustomerStatus implements ResolverInterface
{
    protected $attributeRepository;

    public function __construct(
        AttributeRepositoryInterface $attributeRepository,
    ) {
        $this->attributeRepository = $attributeRepository;
    }

    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
        if (!isset($value['model'])) {
            throw new LocalizedException(__('"model" value should be specified'));
        }

        /**@var \Magento\Customer\Model\Data\Customer $customer*/
         $customer = $value['model'];

        $customerStatusValue = $customer->getCustomAttribute('customer_status')?->getValue();
        if ($customerStatusValue) {
            $attribute = $this->attributeRepository->get(
                'customer',
                'customer_status'
            );

            $optionId = $customerStatusValue;
            $optionLabel = $attribute->getSource()->getOptionText($optionId);

            return $optionLabel;
        }

        return '';
    }
}
