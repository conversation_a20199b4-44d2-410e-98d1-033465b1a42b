<?php

namespace Silksoftwarecorp\RealTimeB2BPricing\Model\API;

class Request implements RequestInterface
{
    protected string $method = 'GET';

    protected string $endpoint = '';

    protected $location;

    protected array $skus = [];

    protected $customerId;

    protected array $options = [];

    /**
     * @param $customerId
     * @param $location
     * @param array $skus
     * @param string $method
     * @param string $endpoint
     * @param array $options
     */
    public function __construct(
        $customerId,
        $locationId,
        array $skus,
        string $method = 'GET',
        string $endpoint = '',
        array $options = []
    ) {
        $this->location = $locationId;
        $this->skus = $skus;
        $this->customerId = $customerId;
        $this->options = $options;
    }

    public function getMethod(): string
    {
        return $this->method;
    }

    public function getEndpoint(): string
    {
        return $this->endpoint;
    }

    public function setLocation($location): static
    {
        $this->location = $location;

        return $this;
    }

    public function setSkus(array $skus): static
    {
        $this->skus = array_unique($skus);

        return $this;
    }

    public function setCustomerId($customerId): static
    {
        $this->customerId = $customerId;

        return $this;
    }

    public function getHeaders(): array
    {
        return [
            'CustomerID' =>  $this->customerId,
            'Location' =>  $this->location,
            'ItemID' =>  implode(',', $this->skus),
        ];
    }

    public function build(): array
    {
        return array_merge_recursive($this->options, [
            'headers' =>  $this->getHeaders(),
        ]);
    }
}
