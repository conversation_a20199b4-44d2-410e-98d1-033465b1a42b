import { memo } from 'react'
import { isEmpty } from 'lodash-es'
import { Button, Form, Modal, Input, Spin, Tree } from 'antd'

import { useRolePermissions } from '@/hooks/CompanyRolsesPage'
import CommonButton from '@/components/Common/CommonButton'
import CommonLoading from '@/components/Common/CommonLoading'

const RolePermissions = ({ currentRole, handleUpdateRole, ...rest }) => {
  const [form] = Form.useForm()
  const {
    checkedKeys,
    expandedKeys,
    isFetching,
    isLoading,
    permissions,
    handleCollapaseAll,
    handleExpandAll,
    handleFormSubmit,
    handleTreeCheck,
    handleTreeExpand
  } = useRolePermissions({
    currentRole,
    form,
    handleUpdateRole
  })
  const isEdit = !isEmpty(currentRole) ? currentRole.type === 'edit' : false

  return (
    <Modal
      centered
      footer={null}
      width={900}
      title={isEdit ? 'Edit Role' : 'Add New Role'}
      {...rest}>
      {isFetching ? (
        <CommonLoading />
      ) : (
        <>
          {!isEmpty(permissions) && (
            <Spin spinning={isLoading}>
              <Form form={form} layout="vertical" onFinish={handleFormSubmit}>
                <div>
                  <Form.Item>
                    <h3>Role Information</h3>
                  </Form.Item>
                  <Form.Item
                    name="name"
                    label="Role Name"
                    rules={[
                      {
                        required: true
                      }
                    ]}>
                    <Input />
                  </Form.Item>
                </div>
                <Form.Item>
                  <h3>Role Permissions</h3>
                  <CommonButton type="text" uppercase={false} onClick={handleExpandAll}>
                    Expand All
                  </CommonButton>
                  <CommonButton
                    type="text"
                    uppercase={false}
                    style={{ marginLeft: '30px' }}
                    onClick={handleCollapaseAll}>
                    Collapase All
                  </CommonButton>
                </Form.Item>
                <Form.Item>
                  <Tree
                    autoExpandParent
                    blockNode
                    checkable
                    defaultExpandAll
                    checkedKeys={checkedKeys}
                    expandedKeys={expandedKeys}
                    showLine
                    treeData={permissions}
                    onExpand={handleTreeExpand}
                    onCheck={handleTreeCheck}
                  />
                </Form.Item>
                <Form.Item>
                  <Button type="primary" htmlType="submit">
                    Save
                  </Button>
                </Form.Item>
              </Form>
            </Spin>
          )}
        </>
      )}
    </Modal>
  )
}

export default memo(RolePermissions)
