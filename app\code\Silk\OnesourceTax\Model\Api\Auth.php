<?php
namespace Silk\OnesourceTax\Model\Api;

use Silk\OnesourceTax\Model\Config;
use Magento\Framework\App\CacheInterface;
use Magento\Framework\Serialize\SerializerInterface;

class Auth
{
    protected $client;
    protected $config;
    protected $token;
    protected $cache;
    protected $serializer;

    const CACHE_KEY = 'onesource_access_token';

    public function __construct(
        Client $client,
        Config $config,
        CacheInterface $cache,
        SerializerInterface $serializer
    ) {
        $this->client = $client;
        $this->config = $config;
        $this->cache = $cache;
        $this->serializer = $serializer;
    }

    public function getAccessToken()
    {
        
        $cached = $this->cache->load(self::CACHE_KEY);
        if ($cached) {
            $data = $this->serializer->unserialize($cached);
            if (isset($data['expires_at'])  && $data['expires_at'] > time()) {
                return $data['access_token'];
            }
        }

        $url = $this->config->getTokenUrl();
        $data = [
            'grant_type' => 'client_credentials',
            'client_id' => $this->config->getClientId(),
            'client_secret' => $this->config->getClientSecret(),
            'scopes' => $this->config->getScopes()
        ];
        $response = $this->client->post($url, $data);

        if (isset($response['access_token'], $response['expires_in'])) {
            $data = [
                'access_token' => $response['access_token'],
                'expires_at' => time() + $response['expires_in'] - 60
            ];
            $this->cache->save($this->serializer->serialize($data), self::CACHE_KEY, [], $response['expires_in']);

            return $data['access_token'];
        }

        $this->token = $response['access_token'] ?? null;

        return $this->token;
    }
}