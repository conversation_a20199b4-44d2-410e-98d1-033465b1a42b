import styled from '@emotion/styled'

export const StyledCommonPagination = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;

  div {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 41px;
    height: 41px;
    border-radius: 3px;
    background: #968c831a;
    font-weight: 700;
    font-size: 16px;
    line-height: 22px;
    letter-spacing: 0.02em;
    color: var(--color-font);
    cursor: pointer;

    &:not(:first-of-type) {
      margin-left: 10px;
    }

    &.active {
      background: var(--color-bg-base);
      color: var(--color-white);
    }
  }

  .common-pagination__prev {
    svg {
      transform: rotate(180deg);
    }
  }

  .common-pagination__more {
    display: flex;
    justify-content: center;
    align-items: center;
    background: none;

    span {
      display: block;
      width: 5px;
      height: 5px;
      border-radius: 50%;
      background: var(--color-black) !important;

      &:not(:first-of-type) {
        margin-left: 4px;
      }
    }
  }
`
