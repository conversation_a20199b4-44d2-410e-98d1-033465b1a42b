<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */

declare(strict_types=1);

namespace Silksoftwarecorp\InventoryGraphQl\Model\Resolver;

use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\InventoryApi\Api\SourceRepositoryInterface;
use Silksoftwarecorp\InventoryGraphQl\Model\Formatter\Source as SourceFormatter;

/**
 * Resolver for getting a single branch source
 */
class GetBranchSource implements ResolverInterface
{
    /**
     * @var SourceRepositoryInterface
     */
    private $sourceRepository;

    /**
     * @var SourceFormatter
     */
    private $sourceFormatter;

    /**
     * @param SourceRepositoryInterface $sourceRepository
     * @param SourceFormatter $sourceFormatter
     */
    public function __construct(
        SourceRepositoryInterface $sourceRepository,
        SourceFormatter $sourceFormatter
    ) {
        $this->sourceRepository = $sourceRepository;
        $this->sourceFormatter = $sourceFormatter;
    }

    /**
     * @inheritdoc
     */
    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
        if (!isset($args['code']) || empty($args['code'])) {
            throw new GraphQlInputException(__('Source code is required'));
        }

        try {
            $source = $this->sourceRepository->get($args['code']);
            return $this->sourceFormatter->format($source);
        } catch (\Exception $e) {
            throw new GraphQlInputException(__('Source with code "%1" does not exist', $args['code']));
        }
    }
} 