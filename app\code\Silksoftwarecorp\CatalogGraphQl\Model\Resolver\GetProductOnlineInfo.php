<?php

namespace Silksoftwarecorp\CatalogGraphQl\Model\Resolver;

use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\InventoryApi\Api\SourceItemRepositoryInterface;
use Magento\InventoryApi\Api\Data\SourceItemSearchResultsInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Eav\Api\AttributeRepositoryInterface;
use Magento\Catalog\Model\Product;
use Psr\Log\LoggerInterface;

class GetProductOnlineInfo implements ResolverInterface
{
    /**
     * @var ProductRepositoryInterface
     */
    private $productRepository;

    /**
     * @var SourceItemRepositoryInterface
     */
    private $sourceItemRepository;

    /**
     * @var SearchCriteriaBuilder
     */
    private $searchCriteriaBuilder;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var AttributeRepositoryInterface
     */
    private $attributeRepository;

    /**
     * @param ProductRepositoryInterface $productRepository
     * @param SourceItemRepositoryInterface $sourceItemRepository
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param AttributeRepositoryInterface $attributeRepository
     * @param LoggerInterface $logger
     */
    public function __construct(
        ProductRepositoryInterface $productRepository,
        SourceItemRepositoryInterface $sourceItemRepository,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        AttributeRepositoryInterface $attributeRepository,
        LoggerInterface $logger
    ) {
        $this->productRepository = $productRepository;
        $this->sourceItemRepository = $sourceItemRepository;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->attributeRepository = $attributeRepository;
        $this->logger = $logger;
    }

    /**
     * @inheritdoc
     */
    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
        if (empty($args['location_id'])) {
            throw new GraphQlInputException(__('location_id is required.'));
        }

        if (empty($args['sku']) || !is_array($args['sku'])) {
            throw new GraphQlInputException(__('sku must be a non-empty array.'));
        }

        $locationId = $args['location_id'];
        $skus = $args['sku'];

        $items = [];

        foreach ($skus as $sku) {
            $item = $this->processProductSku($sku, $locationId);
            $items[] = $item;
        }

        return [
            'items' => $items
        ];
    }

    /**
     * Process individual product SKU
     *
     * @param string $sku
     * @param string $locationId
     * @return array
     */
    private function processProductSku(string $sku, string $locationId): array
    {
        // Default values
        $result = [
            'sku' => $sku,
            'qty' => 0.0,
            'allow_to_order' => false,
            'allow_to_backorder' => false
        ];

        try {
            // Get product
            $product = $this->productRepository->get($sku);

            // Get inventory quantity for this location
            $qty = $this->getInventoryQuantity($sku, $locationId);
            $result['qty'] = $qty;

            // Get product attributes
            $sellableAttribute = $product->getCustomAttribute('sellable');
            $p21BuyAttribute = $product->getCustomAttribute('p21_buy');
            $p21DiscontinuedAttribute = $product->getCustomAttribute('p21_discontinued');

            // Convert option IDs to option values (location IDs)
            $sellableValues = $this->getAttributeOptionValues('sellable', $sellableAttribute);
            $p21BuyValues = $this->getAttributeOptionValues('p21_buy', $p21BuyAttribute);
            $p21Discontinued = $p21DiscontinuedAttribute ? (bool)$p21DiscontinuedAttribute->getValue() : false;

            // Apply business logic
            $result = $this->applyBusinessLogic(
                $result,
                $locationId,
                $qty,
                $sellableValues,
                $p21BuyValues,
                $p21Discontinued
            );

        } catch (NoSuchEntityException $e) {
            $this->logger->warning("Product with SKU {$sku} not found: " . $e->getMessage());
            // Return default values for non-existent products
        } catch (\Exception $e) {
            $this->logger->error("Error processing SKU {$sku}: " . $e->getMessage());
            // Return default values for any other errors
        }

        return $result;
    }

    /**
     * Get inventory quantity for a specific source
     *
     * @param string $sku
     * @param string $sourceCode
     * @return float
     */
    private function getInventoryQuantity(string $sku, string $sourceCode): float
    {
        try {
            $searchCriteria = $this->searchCriteriaBuilder
                ->addFilter('sku', $sku)
                ->addFilter('source_code', $sourceCode)
                ->create();

            $sourceItems = $this->sourceItemRepository->getList($searchCriteria);

            if ($sourceItems->getTotalCount() > 0) {
                $items = $sourceItems->getItems();
                $sourceItem = reset($items);
                return (float)$sourceItem->getQuantity();
            }
        } catch (\Exception $e) {
            $this->logger->error("Error getting inventory for SKU {$sku} and source {$sourceCode}: " . $e->getMessage());
        }

        return 0.0;
    }

    /**
     * Apply business logic to determine ordering permissions
     *
     * @param array $result
     * @param string $locationId
     * @param float $qty
     * @param array $sellableValues Array of location IDs from sellable attribute
     * @param array $p21BuyValues Array of location IDs from p21_buy attribute
     * @param bool $p21Discontinued
     * @return array
     */
    private function applyBusinessLogic(
        array $result,
        string $locationId,
        float $qty,
        array $sellableValues,
        array $p21BuyValues,
        bool $p21Discontinued
    ): array {
        // Check if location_id is in sellable values
        $isSellable = in_array($locationId, $sellableValues);

        // If not sellable for this location, return defaults (allow_to_order = false)
        if (!$isSellable) {
            return $result;
        }

        // Check if location_id is in p21_buy values
        $isP21Buy = in_array($locationId, $p21BuyValues);

        // Apply the business logic rules
        if ($isP21Buy) {
            // v1 includes location_id and v2 includes location_id
            $result['allow_to_order'] = true;
            $result['allow_to_backorder'] = true;
        } elseif ($p21Discontinued) {
            // v1 includes location_id and v3 is true
            if ($qty > 0) {
                $result['allow_to_order'] = true;
                $result['allow_to_backorder'] = false;
            } else {
                $result['allow_to_order'] = false;
                $result['allow_to_backorder'] = false;
            }
        }
        // If v1 includes location_id but v2 doesn't and v3 is false,
        // keep defaults (allow_to_order = false, allow_to_backorder = false)

        return $result;
    }

    /**
     * Get attribute option values from option IDs
     *
     * @param string $attributeCode
     * @param \Magento\Framework\Api\AttributeInterface|null $attribute
     * @return array
     */
    private function getAttributeOptionValues(string $attributeCode, $attribute): array
    {
        if (!$attribute || !$attribute->getValue()) {
            return [];
        }

        try {
            // Get the attribute model
            $attributeModel = $this->attributeRepository->get(Product::ENTITY, $attributeCode);
            
            // Get option IDs from the attribute value
            $optionIds = explode(',', $attribute->getValue());
            $optionValues = [];

            // Get all options for this attribute
            $options = $attributeModel->getOptions();
            
            foreach ($options as $option) {
                // Check if this option ID is in our selected values
                if (in_array($option->getValue(), $optionIds)) {
                    // Add the option label (which is our location ID) to the result
                    $optionValues[] = $option->getLabel();
                }
            }

            return $optionValues;
        } catch (\Exception $e) {
            $this->logger->error("Error getting attribute option values for {$attributeCode}: " . $e->getMessage());
            return [];
        }
    }
}
