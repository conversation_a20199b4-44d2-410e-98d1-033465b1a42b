import type { FC } from 'react'
import dynamic from 'next/dynamic'
import { <PERSON><PERSON>, Drawer, Affix } from 'antd'
import { useState, useCallback, memo } from 'react'

import { StyledMobileCategoryPanel } from './styled'
import { usePageScroll } from '@/hooks/PageScroll'

interface FiltersProps {
  id?: string
  search?: string
}

const Filters = dynamic(() => import('@/components/CategoryGrid/Filters'), {
  ssr: false
})
const Sorters = dynamic(() => import('@/components/CategoryGrid/Sorters'))

const MobileCategoryPanel: FC<FiltersProps> = ({ id, search }) => {
  const { isScrollDown } = usePageScroll()
  const [filterVisible, setFilterVisible] = useState(false)
  const [sortersVisible, setSortersVisible] = useState(false)
  const [filterCount, setFilterCount] = useState(0)

  const handleToggleFilter = useCallback(() => {
    setFilterVisible((prev: boolean) => !prev)
  }, [])

  const onFilterClose = useCallback(() => {
    setFilterVisible(false)
  }, [])

  const updateFilterCount = useCallback((val) => {
    setFilterCount(val)
  }, [])

  const handleToggleSorters = useCallback(() => {
    setSortersVisible((prev: boolean) => !prev)
  }, [])

  const onSortersClose = useCallback(() => {
    setSortersVisible(false)
  }, [])

  return (
    <>
      <Affix offsetTop={isScrollDown ? 116 : 172} className="mobile-category-panel__affix">
        <StyledMobileCategoryPanel>
          <Button type="primary" onClick={handleToggleFilter}>
            FILTER {filterCount > 0 && `(${filterCount})`}
          </Button>
          <Button type="primary" onClick={handleToggleSorters}>
            SORT
          </Button>
        </StyledMobileCategoryPanel>
      </Affix>

      <Drawer
        rootClassName="mb-navigation"
        forceRender
        placement="left"
        open={filterVisible}
        closable={false}
        zIndex={filterVisible ? 9998 : 1000}
        onClose={onFilterClose}
        style={{ height: '100vh', width: '100vw' }}>
        <Filters
          id={id}
          search={search}
          closeMobileDrawer={handleToggleFilter}
          mobileDrawerOpen={filterVisible}
          updateFilterCount={updateFilterCount}
        />
      </Drawer>
      <Drawer
        rootClassName="mb-navigation"
        forceRender
        placement="left"
        open={sortersVisible}
        closable={false}
        // getContainer={false}
        zIndex={sortersVisible ? 9998 : 1000}
        onClose={onSortersClose}
        style={{ height: '100vh', width: '100vw' }}>
        <Sorters
          search={search}
          categoryId={id}
          mobileDrawerOpen={sortersVisible}
          handleCloseSortDrawer={handleToggleSorters}
        />
      </Drawer>
    </>
  )
}

export default memo(MobileCategoryPanel)
