import { gql } from '@apollo/client'
import type { DocumentNode } from '@apollo/client'

import { price_range } from '../fragment/priceRange'

export const GET_LIVE_SEARCH: DocumentNode = gql`
  query getLiveSearch(
    $context: QueryContextInput
    $search: String!
    $filter: [SearchClauseInput!]
    $pageSize: Int
    $currentPage: Int
    $sort: [ProductSearchSortInput!]
  ) {
    productSearch(
      context: $context
      phrase: $search
      page_size: $pageSize
      current_page: $currentPage
      filter: $filter
      sort: $sort
    ) {
      items {
        product {
          name
          sku
          image {
            label
            url
          }
          small_image {
            label
            url
          }
          price_range {
            ...price_range
            __typename
          }
          __typename
        }
        productView {
          url_key: urlKey
          stock_status: inStock
        }
      }
      total_count
    }
  }
  ${price_range}
`
