import { But<PERSON>, Tag, Spin } from 'antd'
import { FormattedMessage } from 'react-intl'

import { useOrderModal } from '@/hooks/AccountOrderList'
import OrderDetails from '../OrderDetails'
import { ModalTitle, OrderModalWrap, ModalContent } from './styled'

// TODO: Delete
const OrderModal = ({
  orderModal = {
    open: false,
    id: '',
    status: '',
    order_date: ''
  },
  ...props
}) => {
  const {
    id,
    color,
    status,
    isLoading,
    order_date,
    handleReorder,
    handlePrintOrder,
    reorderAvailable
  } = useOrderModal(orderModal)

  const modalTitle = (
    <ModalTitle>
      <div>
        <h2 className="modal__title">
          <FormattedMessage id="order.orderId" />
          {` ${id}`}
        </h2>
        <Tag color={color.value}>{status}</Tag>
      </div>
      <p className="modal__date">{order_date}</p>
      <div className="order__modal--actions">
        {reorderAvailable ? (
          <Button
            type="link"
            className="modal__link"
            disabled={isLoading}
            onClick={() => {
              handleReorder(id)
            }}>
            <FormattedMessage id="order.reorder" />
          </Button>
        ) : (
          <span />
        )}
        <Button
          type="link"
          className="modal__print"
          disabled={isLoading}
          onClick={handlePrintOrder}>
          <FormattedMessage id="order.printOrder" />
        </Button>
      </div>
    </ModalTitle>
  )

  return (
    <ModalContent title={modalTitle} footer={null} width={1000} {...props}>
      <Spin spinning={isLoading}>
        <OrderModalWrap key={id}>
          <OrderDetails id={id} />
        </OrderModalWrap>
      </Spin>
    </ModalContent>
  )
}

export default OrderModal
