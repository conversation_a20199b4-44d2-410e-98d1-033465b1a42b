<?php

namespace Silksoftwarecorp\UnifiedAR\Model;

use Magento\Framework\Exception\LocalizedException;
use Psr\Log\LoggerInterface;
use Silksoftwarecorp\UnifiedAR\Http\Client;
use Silksoftwarecorp\UnifiedAR\Http\ClientInterface;
use Silksoftwarecorp\UnifiedAR\Http\ClientInterfaceFactory;
use Silksoftwarecorp\UnifiedAR\Http\Exception\ConnectionException;
use Silksoftwarecorp\UnifiedAR\Model\API\Config as APIConfig;

class UnifiedArApiService
{
    /**
     * @var ClientInterfaceFactory
     */
    protected $httpClientFactory;

    /**
     * @var APIConfig
     */
    protected $apiConfig;

    /**
     * @var ClientInterface
     */
    protected $httpClient;

    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * @param ClientInterfaceFactory $httpClientFactory
     * @param APIConfig $apiConfig
     * @param LoggerInterface $logger
     */
    public function __construct(
        ClientInterfaceFactory $httpClientFactory,
        APIConfig $apiConfig,
        LoggerInterface $logger
    ) {
        $this->httpClientFactory = $httpClientFactory;
        $this->apiConfig = $apiConfig;
        $this->logger = $logger;
    }

    /**
     * Fetch invoices list with pagination support
     * @param string $customerId
     * @return array
     */
    public function fetchInvoices(string $customerId): array
    {
        $data = [];
        $offset = 0;

        do {
            $response = $this->get('Invoice', [
                'offset' => $offset,
                'limit' => $this->apiConfig->getLimit(),
                'includeDisputed' => 'true',
                'includeCredits' => 'true',
                'includeZeroDue' => 'true',
                'includeUnapproved' => 'true',
                'customerNumber' => $customerId
            ]);

            $count = count($response);
            $data = array_merge($data, $response);
            $offset += $this->apiConfig->getLimit();
        } while ($count === $this->apiConfig->getLimit());

        return $data;
    }

    /**
     * Fetch financial information for a customer
     * @param string $customerId
     * @return array
     * @throws LocalizedException
     */
    public function fetchFinancialInformation(string $customerId): array
    {
        $endpoint = sprintf('Customer/lookupBy/customerNumber/%s/financialInformation', $customerId);

        return $this->get($endpoint, [
            'merchantKey' => $this->apiConfig->getMerchantKey()
        ]);
    }

    /**
     * Generic request method with enhanced error handling
     * @param string $method HTTP method (GET, POST, PUT, DELETE)
     * @param string $endpoint API endpoint
     * @param array $params Request parameters
     * @return array
     */
    protected function request(string $method, string $endpoint, array $params = []): array
    {
        $method = strtoupper($method);
        $startTime = microtime(true);

        try {
            $this->logger->info('UnifiedAR API Request', [
                'method' => $method,
                'endpoint' => $endpoint,
                'params' => $params,
                'base_url' => $this->apiConfig->getApiUrl()
            ]);

            $response = $this->getHttpClient()->{strtolower($method)}($endpoint, $params);

            $duration = round((microtime(true) - $startTime) * 1000, 2);

            if ($response->isSuccess()) {
                $responseData = $response->json();
                $this->logger->info('UnifiedAR API Response Success', [
                    'method' => $method,
                    'endpoint' => $endpoint,
                    'status_code' => $response->status(),
                    'duration_ms' => $duration,
                ]);

                return $responseData['result'] ?? [];
            } else {
                $this->logger->error('UnifiedAR API Response Failed', [
                    'method' => $method,
                    'endpoint' => $endpoint,
                    'status_code' => $response->status(),
                    'duration_ms' => $duration,
                    'response_body' => $response->body()
                ]);
            }
        } catch (ConnectionException $e) {
            $this->logger->error('UnifiedAR API Connection Error', [
                'method' => $method,
                'endpoint' => $endpoint,
                'error' => $e->getMessage(),
                'duration_ms' => round((microtime(true) - $startTime) * 1000, 2)
            ]);
        } catch (LocalizedException $e) {
            $this->logger->error('UnifiedAR API Local Exception', [
                'method' => $method,
                'endpoint' => $endpoint,
                'error' => $e->getMessage(),
                'duration_ms' => round((microtime(true) - $startTime) * 1000, 2)
            ]);
        } catch (\Exception $e) {
            $this->logger->critical('UnifiedAR API Unexpected Error', [
                'method' => $method,
                'endpoint' => $endpoint,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'duration_ms' => round((microtime(true) - $startTime) * 1000, 2)
            ]);
        }

        return [];
    }

    /**
     * Perform GET request
     * @param string $endpoint
     * @param array $params
     * @return array
     */
    public function get(string $endpoint, array $params = []): array
    {
        return $this->request('GET', $endpoint, $params);
    }

    /**
     * Perform POST request
     * @param string $endpoint
     * @param array $params
     * @return array
     */
    public function post(string $endpoint, array $params = []): array
    {
        return $this->request('POST', $endpoint, $params);
    }

    /**
     * Perform PUT request
     * @param string $endpoint
     * @param array $params
     * @return array
     */
    public function put(string $endpoint, array $params = []): array
    {
        return $this->request('PUT', $endpoint, $params);
    }

    /**
     * Perform DELETE request
     * @param string $endpoint
     * @param array $params
     * @return array
     */
    public function delete(string $endpoint, array $params = []): array
    {
        return $this->request('DELETE', $endpoint, $params);
    }

    /**
     * Get configured HTTP client instance
     * @return ClientInterface
     * @throws LocalizedException
     */
    protected function getHttpClient(): ClientInterface
    {
        if (!$this->httpClient) {
            /**@var $httpClient Client*/
            $httpClient = $this->httpClientFactory->create();
            $this->httpClient = $httpClient->withBaseUri(
                $this->apiConfig->getApiUrl()
            )->withHeaders([
                'ApiKey' => $this->apiConfig->getApiKey(),
                'merchantKey' => $this->apiConfig->getMerchantKey()
            ])->timeout(
                $this->apiConfig->getTimeout()
            )->withRetry()->withLog();
        }

        return $this->httpClient;
    }
}
