<?php
namespace Silksoftwarecorp\BackorderedProductsGraphQl\Resolver;

use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlAuthorizationException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Silksoftwarecorp\BackorderedProducts\Api\BackorderRepositoryInterface;
use Magento\GraphQl\Model\Query\ContextInterface;
use Silksoftwarecorp\ERPCompany\Model\Company\ERPRepository;

class BackorderedProducts implements ResolverInterface
{
    protected $customerRepository;
    protected $backorderRepository;

    private $erpRepository;

    public function __construct(
        CustomerRepositoryInterface $customerRepository,
        BackorderRepositoryInterface $backorderRepository,
        ERPRepository $erpRepository
    ) {
        $this->customerRepository = $customerRepository;
        $this->backorderRepository = $backorderRepository;
        $this->erpRepository = $erpRepository;
    }

    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        /** @var ContextInterface $context */
        if (false === $context->getExtensionAttributes()->getIsCustomer()) {
            throw new GraphQlAuthorizationException(__('The request is allowed for logged in customer.'));
        }
        $customerId = $context->getUserId();
        if (!$customerId) {
            return [];
        }
        $customer = $this->customerRepository->getById($customerId);

        $companyAttributes = $customer->getExtensionAttributes()?->getCompanyAttributes();

        if (!$companyAttributes || !$companyAttributes->getCompanyId()) {
            return [];
        }

        // Get the company's ERP customer ID
        $companyId = $companyAttributes->getCompanyId();
        $erpCompany = $this->erpRepository->get($companyId);

        if (!$erpCompany || !$erpCompany->getErpCustomerId()) {
            return [];
        }

        $erpCustomerId = $erpCompany->getErpCustomerId();
        if (empty($erpCustomerId)) {
            return [];
        }

        return $this->backorderRepository->getBackorderedProducts($erpCustomerId);
    }
}
