# UnifiedArPaymentGraphQl

This module provides GraphQL endpoints for the Unified A/R Payment integration in Magento 2. It allows frontend applications to interact with the Unified A/R payment gateway for transaction setup, payment account queries, and surcharge checks.

## Example GraphQL Requests

### 1. Create Transaction Setup
```graphql

mutation {
  createTransactionSetup(
    address: {
      billing_address1: "123 Main Street"
      billing_address2: "Suite 200"
      billing_city: "Nashville"
      billing_state: "TN"
      billing_zipcode: "37209"
    }
  ) {
    error_code
    iframe_url
    message
    success
    transaction_setup_url
  }
}

response:
{
  "data": {
    "createTransactionSetup": {
      "error_code": null,
      "iframe_url": "https://eem-ui.gounified-nonprod.com/iframe?TransactionSetupId=98ca00a9-bc1d-448b-a9fa-cdd257232bff",
      "message": "Transaction setup created successfully",
      "success": true,
      "transaction_setup_url": "98ca00a9-bc1d-448b-a9fa-cdd257232bff"
    }
  }
}
```

### 2. Payment Account Query
```graphql

mutation {
  paymentAccountQuery(transaction_setup_id: "98ca00a9-bc1d-448b-a9fa-cdd257232bff") {
    error_code
    items {
      expiration_month
      expiration_year
      pass_updater_batch_status
      pass_updater_status
      payment_account_id
      payment_account_reference_number
      payment_account_type
      payment_brand
      token_provider_id
      transaction_setup_id
      truncated_card_number
    }
    message
    success
  }
}

response:
{
  "data": {
    "paymentAccountQuery": {
      "error_code": null,
      "items": [
        {
          "expiration_month": "12",
          "expiration_year": "28",
          "pass_updater_batch_status": "0",
          "pass_updater_status": "14",
          "payment_account_id": "NMQXQEB21GFKNHPY10RC3C31FQRZFK",
          "payment_account_reference_number": "100012",
          "payment_account_type": "0",
          "payment_brand": "mastercard",
          "token_provider_id": "0",
          "transaction_setup_id": "98ca00a9-bc1d-448b-a9fa-cdd257232bff",
          "truncated_card_number": "555555******4444"
        }
      ],
      "message": "Payment account query successful",
      "success": true
    }
  }
}
```

### 3. Surcharge Query
```graphql

mutation {
  surchargeQuery(
    payment_account_id: "NMQXQEB21GFKNHPY10RC3C31FQRZFK"
    origin_state: "AR"
    billing_state: "TN"
    amount: 345.67
    country: "US"
  ) {
    error_code
    message
    success
    surcharge_allowed
    surcharge_percent
  }
}
 response:
 {
  "data": {
    "surchargeQuery": {
      "error_code": null,
      "message": "Surcharge query successful",
      "success": true,
      "surcharge_allowed": false,
      "surcharge_percent": 0
    }
  }
}
```

### 4. setpayment and place order
```graphql
mutation {
  setPaymentMethodAndPlaceOrder(
    input: { cart_id: "rTCDpopgBeF1beYbCcjGVAQb73Ci8oDJ", payment_method: { code: "unifiedarpayment" },unified_ar_return_data:"HostedPaymentStatus=Complete&TransactionSetupID=98ca00a9-bc1d-448b-a9fa-cdd257232bff&TransactionID=&ExpressResponseCode=0&ExpressResponseMessage=N&AVSResponseCode=N&LastFour=4444&ValidationCode=RDTTAGJIFJIRPSAKELTR&CardLogo=mastercard&PaymentAccountID=NMQXQEB21GFKNHPY10RC3C31FQRZFK&BillingAddress1=123%20Main%20Street&BillingZipcode=37209&Bin=555555&Entry=Manual&TranDT=2025-07-02%2007%3A20%3A42&ExpirationMonth=12&ExpirationYear=28",surcharge_amount:21.38 }
  ) {
    order {
      order_id
      order_number
    }
    orderV2 {
      id
      increment_id
      order_number
    }
  }
}
```

### 4.customer order surcharge amount
```graphql
{
customerOrders {
items {
order_number
id
created_at
grand_total
status
unified_ar_surcharge_amount
}
}
}
```
---

For more details, see the module code or contact your Magento developer. 
