<?php

namespace Silksoftwarecorp\UnifiedArPayment\Logger\Handler;

use Magento\Framework\Filesystem\Driver\File;
use Magento\Framework\Logger\Handler\Base;
use Monolog\Logger;

class UnifiedArPayment extends Base
{
    /**
     * @var string
     */
    protected $fileName = 'var/log/unifiedarpayment.log';

    /**
     * @var int
     */
    protected $loggerType = Logger::INFO;

    /**
     * @param File $filesystem
     */
    public function __construct(File $filesystem)
    {
        parent::__construct($filesystem);
        $this->setFileName($this->fileName);
    }

    /**
     * Set the log file name
     *
     * @param string $fileName
     * @return void
     */
    protected function setFileName($fileName)
    {
        $this->fileName = $fileName;
    }
} 