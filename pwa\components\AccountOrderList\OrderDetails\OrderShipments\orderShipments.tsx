import { Button } from 'antd'
import { FormattedMessage } from 'react-intl'

import { useOrderShipments } from '@/hooks/AccountOrderList'
import CommonTable from '@/components/Common/CommonTable'

import { DetailsProductName, OrderQty, OrderDescription } from '../styled'

const OrderShipments = ({ shipments = [] }) => {
  const { shipmentsId, shipmentsItems, handlePrintOrder } = useOrderShipments(shipments)

  const columns = [
    {
      title: <FormattedMessage id="global.productName" />,
      dataIndex: 'product_name',
      key: 'product_name',
      render: (param, record) => {
        return (
          <DetailsProductName>
            <div>{param}</div>
            {record.order_item.selected_options.map((option) => {
              return (
                <div key={`${option.label}_${param}`}>
                  <span className="orderDetails__label">{`${option.label}: `}</span>
                  <span>{option.value}</span>
                </div>
              )
            })}
          </DetailsProductName>
        )
      }
    },
    {
      title: <FormattedMessage id="global.sku" />,
      dataIndex: 'product_sku',
      key: 'product_sku'
    },
    {
      title: <FormattedMessage id="order.qtyShipped" />,
      dataIndex: 'quantity_shipped',
      key: 'quantity_shipped',
      render: (param) => <OrderQty>{param}</OrderQty>
    }
  ]

  return (
    <div>
      <OrderDescription>
        <Button
          type="link"
          className="details__printAll"
          onClick={() => {
            handlePrintOrder()
          }}>
          <FormattedMessage id="order.printAllShipments" />
        </Button>
        <h2 className="detailsDescription__title">
          <FormattedMessage id="order.shipment" />
          {` ${shipmentsId}`}
        </h2>
        <Button type="link" className="details__print" onClick={handlePrintOrder}>
          <FormattedMessage id="order.printShipments" />
        </Button>
      </OrderDescription>
      <CommonTable
        lineStyle
        rowKey={(record) => record.product_sku}
        columns={columns}
        dataSource={shipmentsItems}
        pagination={false}
      />
    </div>
  )
}

export default OrderShipments
