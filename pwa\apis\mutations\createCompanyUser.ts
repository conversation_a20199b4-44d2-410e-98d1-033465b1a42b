import { gql } from '@apollo/client'

export const B2B_CREATE_COMPANY_USER = gql`
  mutation createCompanyUser($input: CompanyUserCreateInput!) {
    createCompanyUser(input: $input) {
      user {
        email
        firstname
        gender
        job_title
        lastname
        role {
          id
          name
        }
        status
        team {
          description
          id
          name
        }
        telephone
      }
    }
  }
`
