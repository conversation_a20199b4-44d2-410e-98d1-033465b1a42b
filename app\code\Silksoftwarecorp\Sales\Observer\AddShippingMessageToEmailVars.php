<?php

namespace Silksoftwarecorp\Sales\Observer;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Sales\Model\Order;

class AddShippingMessageToEmailVars implements ObserverInterface
{
    /**
     * Add shipping message to email template variables
     *
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer)
    {
        $transportObject = $observer->getData('transportObject');
        
        /** @var Order $order */
        $order = $transportObject->getData('order');
        
        if (!$order || $order->getIsVirtual()) {
            return;
        }

        $shippingMethod = (string) $order->getShippingMethod();
        $shippingDescription = (string) $order->getShippingDescription();

        $message = '';
        if ($shippingMethod === 'flatrate_flatrate' || stripos($shippingDescription, 'Blevins Delivery') !== false) {
            $message = 'Estimated delivery is in 5 to 7 days, excluding backordered items. The shipping cost will appear on the invoice.';
        } elseif ($shippingMethod === 'freeshipping_freeshipping' || stripos($shippingDescription, 'LTL') !== false) {
            $message = 'Customer Service will contact you with the shipping cost and expected delivery date.';
        } elseif ($shippingMethod === 'instore_pickup' || stripos($shippingDescription, 'In Store Pickup') !== false) {
            $message = 'Your order will be available for pick-up in one to two hours, excluding backordered items. Orders placed after 2PM or during non-business hours will be ready for pick-up the following business day.';
        }

        if ($message !== '') {
            $transportObject->setData('shipping_msg', $message);
        }
    }
}
