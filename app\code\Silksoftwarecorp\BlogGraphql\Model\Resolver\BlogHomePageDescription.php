<?php

namespace Silksoftwarecorp\BlogGraphql\Model\Resolver;

use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Silksoftwarecorp\Blog\Helper\Data as BlogHelper;

class BlogHomePageDescription implements ResolverInterface
{
    /**
     * @var BlogHelper
     */
    private $blogHelper;

    /**
     * @param BlogHelper $blogHelper
     */
    public function __construct(BlogHelper $blogHelper)
    {
        $this->blogHelper = $blogHelper;
    }

    /**
     * @param Field $field
     * @param $context
     * @param ResolveInfo $info
     * @param array|null $value
     * @param array|null $args
     * @return string|null
     * @throws \Exception
     */
    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ): ?string {
        $storeId = $context->getExtensionAttributes()->getStore()->getId();
        return $this->blogHelper->getBlogHomePageDescription($storeId);
    }
}
