import { isEmpty } from 'lodash-es'
import { Spin } from 'antd'

import { MediaLayout } from '@/ui'
import CommonLoading from '@/components/Common/CommonLoading'
import CommonTable from '@/components/Common/CommonTable'
import CommonButton from '@/components/Common/CommonButton'
import ConfirmModal from '@/components/ConfirmModal'
import { useCompanyRolsesPage } from '@/hooks/CompanyRolsesPage'

import RolePermissions from './RolePermissions'
import { StyledActionItem, StyledAddRole } from './styled'

const CompanyRolsesPage = () => {
  const {
    currentRole,
    isLoading,
    roles,
    visible,
    handlePageChange,
    handleModalCancel,
    handleModalOpen,
    handleUpdateRole,
    handleRemoveRole,
    page,
    handlePageSizeChange
  } = useCompanyRolsesPage()
  const userList = roles?.items ?? []
  const totalCount = roles?.total_count ?? 0

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 130
    },
    {
      title: 'Role',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (param) => {
        return <span dangerouslySetInnerHTML={{ __html: param }} />
      }
    },
    {
      title: 'Users',
      dataIndex: 'users_count',
      key: 'users_count'
    },
    {
      title: '',
      dataIndex: 'x',
      key: 'x',
      width: 250,
      mobileContentFull: true,
      render: (param, record) => {
        return (
          <StyledActionItem>
            <CommonButton
              type="text"
              uppercase={false}
              underline
              onClick={() => {
                handleModalOpen({
                  ...record,
                  type: 'duplicate'
                })
              }}>
              Duplicate
            </CommonButton>
            <span className="item-line">|</span>
            <CommonButton
              type="text"
              uppercase={false}
              underline
              onClick={() => {
                handleModalOpen({
                  ...record,
                  type: 'edit'
                })
              }}>
              Edit
            </CommonButton>
            <span className="item-line">|</span>
            <ConfirmModal
              title="Delete This Role?"
              onOk={() => {
                handleRemoveRole(record.id)
              }}
              trigger={
                <CommonButton type="text" uppercase={false} underline>
                  Delete
                </CommonButton>
              }>
              <p>This action cannot be undone. Are you sure you want to delete this role?</p>
            </ConfirmModal>
          </StyledActionItem>
        )
      }
    }
  ]

  return (
    <CommonLoading spinning={isLoading}>
      {!isEmpty(roles) && (
        <>
          <MediaLayout type="mobile">
            <StyledAddRole>
              <CommonButton
                onClick={() => {
                  handleModalOpen(null)
                }}>
                + Add New role
              </CommonButton>
            </StyledAddRole>
          </MediaLayout>
          <div className="tools">
            {userList.length > 0 && (
              <CommonTable
                lineStyle
                rowKey={(params) => params.id}
                columns={columns}
                dataSource={userList}
                total={totalCount}
                current={page.currentPage}
                pageSize={page.pageSize}
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
              />
            )}
          </div>
          <MediaLayout>
            <StyledAddRole>
              <CommonButton
                onClick={() => {
                  handleModalOpen(null)
                }}>
                + Add New role
              </CommonButton>
            </StyledAddRole>
          </MediaLayout>
          {visible && (
            <RolePermissions
              currentRole={currentRole}
              open={visible}
              onCancel={handleModalCancel}
              handleUpdateRole={handleUpdateRole}
            />
          )}
        </>
      )}
    </CommonLoading>
  )
}

export default CompanyRolsesPage
