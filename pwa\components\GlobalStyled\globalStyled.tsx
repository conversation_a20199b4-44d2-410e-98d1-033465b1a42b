import { css, Global } from '@emotion/react'

import { emotionTheme } from '@/config/emotion.config'

const GlobalStyled = () => {
  const globalStyle = css`
    @font-face {
      font-family: 'Poppins';
      font-weight: normal;
      src: url('/fonts/poppins-regular.ttf');
      font-display: swap;
    }

    @font-face {
      font-family: 'Poppins-Bold';
      font-weight: normal;
      src: url('/fonts/poppins-bold.ttf');
      font-display: swap;
    }

    @font-face {
      font-family: 'Poppins-SemiBold';
      font-weight: normal;
      src: url('/fonts/poppins-semiBold.ttf');
      font-display: swap;
    }

    @font-face {
      font-family: 'Poppins-Medium';
      font-weight: normal;
      src: url('/fonts/poppins-medium.ttf');
      font-display: swap;
    }

    @font-face {
      font-family: 'Montserrat';
      font-weight: normal;
      src: url('/fonts/montserrat-regular.ttf');
      font-display: swap;
    }

    @font-face {
      font-family: 'Montserrat-Bold';
      font-weight: normal;
      src: url('/fonts/montserrat-bold.ttf');
      font-display: swap;
    }

    @font-face {
      font-family: 'Roboto-Bold';
      font-weight: normal;
      src: url('/fonts/roboto-bold.woff2');
      font-display: swap;
    }

    @font-face {
      font-family: 'Roboto';
      font-weight: normal;
      src: url('/fonts/roboto-regular.woff2');
      font-display: swap;
    }

    @font-face {
      font-family: 'Roboto-Medium';
      font-weight: normal;
      src: url('/fonts/roboto-medium.woff2');
      font-display: swap;
    }

    @font-face {
      font-family: 'Roboto-Condensed-Bold';
      font-weight: normal;
      src: url('/fonts/roboto-condensed-bold.woff2');
      font-display: swap;
    }

    :root {
      --color-primary: #9d2235;
      --color-white: #ffffff;
      --color-font: #191919;
      --color-text: #535353;
      --color-black: #101010;
      --color-bg-base: #003865;
      --color-green: #0f8c22;
      --font-poppins: 'Arial', sans-serif;
      --font-poppins-bold: 'Poppins-Bold', sans-serif;
      --font-poppins-medium: 'Poppins-Medium', sans-serif;
      --font-poppins-semi-bold: 'Poppins-SemiBold', sans-serif;
      --font-montserrat: 'Arial', sans-serif;
      --font-montserrat-bold: 'Montserrat-Bold', sans-serif;
      --font-roboto: 'Arial', sans-serif;
      --font-roboto-bold: 'Roboto-Bold', sans-serif;
      --font-roboto-medium: 'Roboto-Medium', sans-serif;
      --font-roboto-condensed-bold: 'Roboto-Condensed-Bold', sans-serif;
    }

    body {
      color: var(--color-font);
      font-family: var(--font-poppins);
      font-size: 14px;
      font-weight: 400;
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    div {
      color: var(--color-font);
    }

    ul,
    ol {
      padding: 0;
      margin: 0;

      li {
        list-style-type: none;
      }
    }

    dl,
    dd {
      margin: 0;
    }

    p {
      margin-bottom: 0;
      color: var(--color-text);
    }

    a {
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }

    img {
      max-width: 100%;
    }

    svg {
      display: inline-block;
      vertical-align: middle;
    }

    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
      appearance: none;
    }

    input[type='number'] {
      appearance: textfield;
    }

    body {
      .main {
        .${emotionTheme.namespace} {
          &-form-item-explain-error {
            padding-left: 3px;
          }

          &-checkbox {
            & + span {
              padding-top: 2px;
            }

            &-wrapper {
              span {
                font-weight: 400;
                font-size: 15px;
                line-height: 21px;
                letter-spacing: 0.01em;
              }

              &:hover {
                .${emotionTheme.namespace}-checkbox-inner {
                  border-color: #d9d9d9;
                  background-color: var(--color-white);
                }
              }
            }

            &-inner {
              width: 26px;
              height: 26px;
              border-radius: 3px;
            }

            &-checked {
              .${emotionTheme.namespace} {
                &-checkbox-inner {
                  background-color: var(--color-primary) !important;
                  border-color: var(--color-primary) !important;

                  &::after {
                    transform: rotate(45deg) scale(1.3) translate(-50%, -50%);
                    top: 50%;
                    inset-inline-start: 8px;
                  }
                }
              }
            }
          }

          &-radio {
            align-self: initial;
            top: 3px;

            &-group {
              display: block;
              font-size: inherit;
            }

            &-wrapper {
              display: block;

              span {
                font-weight: 400;
                font-size: 15px;
                line-height: 21px;
                letter-spacing: 0.01em;
                color: var(--color-font);
              }

              &:hover {
                .${emotionTheme.namespace}-radio-inner {
                  border-color: #d9d9d9;
                }
              }
            }

            &-inner {
              width: 18px;
              height: 18px;
              background-color: var(--color-white) !important;

              &::after {
                width: 10px;
                height: 10px;
                margin-block-start: -5px;
                margin-inline-start: -5px;
                background: var(--color-primary);
              }
            }

            &-checked {
              .${emotionTheme.namespace}-radio-inner {
                border: 2px solid var(--color-primary) !important;

                &::after {
                  transform: scale(1) !important;
                }
              }
            }
          }

          &-space-gap-col-small {
            width: 100%;
            row-gap: 16px;
          }

          &-carousel {
            .slick-slider {
              .slick-dots {
                bottom: 24px;

                li {
                  width: 8px;
                  height: 8px;

                  button {
                    height: 8px;
                    border-radius: 50%;

                    &::after {
                      display: none;
                    }
                  }

                  &.slick-active {
                    width: 24px;

                    button {
                      background-color: var(--color-primary);
                      border-radius: 25px;
                    }
                  }
                }
              }
            }

            .slick-arrow {
              opacity: 1 !important;

              .arrow {
                width: 40px;
                height: 40px;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 50%;
                background-color: var(--color-font);

                svg {
                  font-size: 16px;
                }
              }

              &::after {
                display: none !important;
              }
            }

            .slick-prev {
              left: -8px !important;
            }

            .slick-next {
              right: 12px !important;
            }
          }

          &-spin {
            .${emotionTheme.namespace} {
              &-spin-dot-holder {
                .${emotionTheme.namespace} {
                  &-spin-dot-spin {
                    animation-name: none;
                  }

                  &-spin-dot-item {
                    background: var(--color-primary);
                  }
                }
              }
            }
          }

          &-select-outlined:not(.${emotionTheme.namespace}-select-customize-input) {
            width: 217px;
            height: 56px;

            .${emotionTheme.namespace} {
              &-select-selector {
                padding: 16px 24px;
                border-radius: 28px;

                .${emotionTheme.namespace} {
                  &-select-selection-search-input {
                    height: 54px;
                  }
                }
              }
            }
          }

          &-select {
            .${emotionTheme.namespace} {
              &-select-arrow {
                right: 20px;
                font-size: 9px;
              }
            }
          }
        }

        .mobile-category-panel__affix {
          .${emotionTheme.namespace}-affix {
            transition: all 0.3s;
            z-index: 9 !important;
          }
        }

        .global__flex-space-between {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
      }
    }

    .${emotionTheme.namespace} {
      &-modal-root {
        .modal-common {
          .${emotionTheme.namespace} {
            &-modal-content {
              padding: 0;
              border-radius: unset;
              box-shadow: 0px 0px 15px 8px #00000014;
            }

            &-modal-header {
              margin-bottom: 0;
              padding: 22px 0;
              text-align: center;
              background-color: #f2f2f2;

              .${emotionTheme.namespace} {
                &-modal-title {
                  line-height: 37px;
                  color: var(--color-font);
                  font-family: var(--font-poppins-medium);
                  font-size: 28px;
                  font-weight: 500;
                }
              }
            }

            &-modal-body {
              padding: 140px;
            }

            &-modal-close {
              top: 22px;
              right: 28px;
            }

            &-modal-close-icon {
              font-size: 24px;
            }
          }
        }

        .modal-video {
          .${emotionTheme.namespace} {
            &-modal-body {
              padding: 0 !important;
            }

            &-modal-content {
              background-color: #0f0f11 !important;
            }

            &-modal-close-icon {
              svg {
                fill: #fff;
                font-size: 20px;
                margin-top: -10px;
                margin-left: 15px;
              }
            }
          }
        }
      }

      &-affix {
        z-index: 99 !important;
      }
    }

    .mb-navigation {
      .${emotionTheme.namespace} {
        &-drawer-content-wrapper {
          box-shadow: unset;
        }

        &-drawer-body {
          padding: 0 !important;
        }
      }
    }
  `

  return <Global styles={globalStyle} />
}

export default GlobalStyled
