import { useCallback, useEffect, useState, memo } from 'react'

import { useAwaitQuery } from '@ranger-theme/apollo'
import { GET_PRODUCTS_CUSTOM_ATTRIBUTES } from '@/apis/queries/getProductsCustomAttributes'
import { GET_CUSTOM_ATTRIBUTE_METADATA } from '@/apis/queries/getCustomAttributeMetadata'

import CollapseBox from '../CollapseBox'

const SpecificationsTable = ({ sku }: any) => {
  const specificationsQuery = useAwaitQuery(GET_PRODUCTS_CUSTOM_ATTRIBUTES)
  const attributeMetadataQuery = useAwaitQuery(GET_CUSTOM_ATTRIBUTE_METADATA)

  const [specificationsList, setSpecificationsList] = useState<any[]>([])

  const fetchSpecifications = useCallback(async () => {
    try {
      const { data } = await specificationsQuery({
        variables: {
          filter: {
            sku: { eq: sku }
          }
        }
      })
      const items = data?.products?.items ?? []
      const productItem = items?.find((it) => it?.sku === sku)
      const attributes = productItem?.custom_attributesV2?.items ?? []
      if (attributes?.length > 0) {
        const attributesQuery = attributes.map((it) => {
          return {
            attribute_code: it?.code,
            entity_type: 'catalog_product'
          }
        })
        const { data: attrData } = await attributeMetadataQuery({
          variables: {
            attributes: attributesQuery
          }
        })
        const labels = attrData?.customAttributeMetadataV2?.items ?? []
        const list = attributes.map((item) => {
          const label = labels.find((it) => it?.code === item?.code)?.label ?? ''
          return {
            code: item?.code,
            label,
            value: item?.value || item?.selected_options?.[0]?.label || ''
          }
        })
        setSpecificationsList(list)
      }
    } catch (err) {
      console.error('Fetch error:', err)
    }
  }, [specificationsQuery, attributeMetadataQuery, sku])

  useEffect(() => {
    fetchSpecifications()
  }, [fetchSpecifications])

  return specificationsList.length > 0 ? (
    <CollapseBox
      className="pdp-specifications"
      title="Specifications"
      contentChildren={
        <table>
          <tbody>
            {specificationsList.map((item) => (
              <tr key={item.code}>
                <td>{item.label}</td>
                <td>{item.value}</td>
              </tr>
            ))}
          </tbody>
        </table>
      }
    />
  ) : null
}

export default memo(SpecificationsTable)
