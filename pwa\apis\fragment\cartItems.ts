import { gql } from '@apollo/client'
import type { DocumentNode } from '@apollo/client'

import { price_range } from './priceRange'
import { configurableCart } from './configurableCart'

export const cartItems: DocumentNode = gql`
  fragment cartItems on Cart {
    items {
      id
      product {
        name
        sku
        url_key
        only_x_left_in_stock
        thumbnail {
          label
          url
        }
        price_range {
          ...price_range
          __typename
        }
        stock_status
      }
      quantity
      prices {
        row_total {
          value
        }
        price {
          value
        }
      }
      ...configurableCart
      __typename
    }
  }
  ${price_range}
  ${configurableCart}
`
