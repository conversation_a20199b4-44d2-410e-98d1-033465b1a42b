<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <tab id="silksoftwarecorp" translate="label" sortOrder="300">
            <label>Silk Commerce</label>
        </tab>
        <section id="edi" translate="label" type="text" sortOrder="999" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>EDI</label>
            <tab>silksoftwarecorp</tab>
            <resource>Silksoftwarecorp_EDI::config</resource>
            <group id="general" translate="label" type="text" sortOrder="1" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>General</label>
                <field id="active" translate="label" type="select" sortOrder="1" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Enabled EDI API</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="debug" translate="label" type="select" sortOrder="55" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Debug</label>
                    <source_model>Magento\Config\Model\Config\Source\Enabledisable</source_model>
                </field>
            </group>
            <group id="api" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>API</label>
                <depends>
                    <field id="edi/general/active">1</field>
                </depends>
                <field id="environment" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Environment</label>
                    <source_model>Silksoftwarecorp\RealTimeB2BPricing\Model\Config\Source\Environment</source_model>
                </field>
                <field id="url" translate="label" type="text" sortOrder="15" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>URL</label>
                    <validate>required-entry</validate>
                    <depends>
                        <field id="environment">production</field>
                    </depends>
                </field>
                <field id="username" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Username</label>
                    <validate>required-entry</validate>
                    <depends>
                        <field id="environment">production</field>
                    </depends>
                </field>
                <field id="password" translate="label" type="obscure" sortOrder="25" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Password</label>
                    <validate>required-entry</validate>
                    <backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
                    <depends>
                        <field id="environment">production</field>
                    </depends>
                </field>
                <field id="sandbox_url" translate="label" type="text" sortOrder="35" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Sandbox URL</label>
                    <validate>required-entry</validate>
                    <depends>
                        <field id="environment">sandbox</field>
                    </depends>
                </field>
                <field id="sandbox_username" translate="label" type="text" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Sandbox Username</label>
                    <validate>required-entry</validate>
                    <depends>
                        <field id="environment">sandbox</field>
                    </depends>
                </field>
                <field id="sandbox_password" translate="label" type="obscure" sortOrder="45" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Sandbox Password</label>
                    <validate>required-entry</validate>
                    <backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
                    <depends>
                        <field id="environment">sandbox</field>
                    </depends>
                </field>
                <!--<field id="enable_ssl_certificate_verify" translate="label" type="select" sortOrder="55" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Enable SSL Certificate Verification</label>
                    <validate>required-entry</validate>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Enable SSL Certificate Verification when requesting the Real-Time B2B Pricing api.</comment>
                </field>-->
                <field id="timeout" translate="label" type="text" sortOrder="55" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Timeout</label>
                    <validate>required-entry validate-number validate-greater-than-zero</validate>
                    <comment>The timeout for request Real-Time B2B Pricing API. The unit is seconds. Default: 10s.</comment>
                </field>
            </group>
            <group id="frequently_ordered_products" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Frequently Ordered Products</label>
                <field id="enabled" translate="label" type="select" sortOrder="1" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Enabled</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="request_type" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>API Request Type</label>
                    <validate>required-entry</validate>
                </field>
                <field id="frequency_days" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Frequency Days</label>
                    <validate>required-entry validate-number validate-greater-than-zero</validate>
                    <comment>Frequency Days, default: 180.</comment>
                </field>
            </group>
            <group id="order" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Order</label>
                <field id="enabled" translate="label" type="select" sortOrder="1" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Enabled</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="frequency_days" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Frequency Days</label>
                    <validate>required-entry validate-number validate-greater-than-zero</validate>
                    <comment>Frequency Days, default: 365.</comment>
                </field>
            </group>
        </section>
    </system>
</config>
