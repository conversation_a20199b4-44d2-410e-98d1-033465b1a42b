import { gql } from '@apollo/client'
import type { DocumentNode } from '@apollo/client'

import { cartItems } from '../fragment/cartItems'
import { cart_prices } from '../fragment/cartPrices'

export const GET_CHECKOUT_ENTITY: DocumentNode = gql`
  query getCheckoutEntity($cartId: String!) {
    cart(cart_id: $cartId) {
      quantity: total_quantity
      email
      ...cartItems
      prices {
        ...cart_prices
        __typename
      }
      coupons: applied_coupons {
        code
      }
      shipping_addresses {
        city
        company
        firstname
        lastname
        postcode
        street
        telephone
        country {
          code
          label
        }
        region {
          region_id
          label
        }
        selected_shipping_method {
          amount {
            value
          }
          carrier_code
          carrier_title
          method_code
          method_title
        }
      }
      __typename
    }
  }
  ${cartItems}
  ${cart_prices}
`
