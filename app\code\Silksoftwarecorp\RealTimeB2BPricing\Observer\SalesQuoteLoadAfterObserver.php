<?php

namespace Silksoftwarecorp\RealTimeB2BPricing\Observer;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Quote\Model\Quote;
use Silksoftwarecorp\RealTimeB2BPricing\Model\Quote\UpdateExpiredPriceTrigger;

class SalesQuoteLoadAfterObserver implements ObserverInterface
{
    /**
     * @var UpdateExpiredPriceTrigger
     */
    protected $updateExpiredPriceTrigger;

    /**
     * @param UpdateExpiredPriceTrigger $updateExpiredPriceTrigger
     */
    public function __construct(
        UpdateExpiredPriceTrigger $updateExpiredPriceTrigger
    ) {
        $this->updateExpiredPriceTrigger = $updateExpiredPriceTrigger;
    }

    public function execute(Observer $observer): void
    {
        $quote = $observer->getEvent()->getQuote();
        if ($quote instanceof Quote) {
            $this->updateExpiredPriceTrigger->execute($quote);
        }
    }
}
