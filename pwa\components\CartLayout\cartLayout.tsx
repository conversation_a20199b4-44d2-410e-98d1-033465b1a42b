import { LineContainer } from '@ranger-theme/ui'
import { But<PERSON> } from 'antd'
import { isNull } from 'lodash-es'

import { MediaLayout } from '@/ui'
import { GET_CROSSSELL } from '@/apis/queries/getCrosssell'
import { useCartLayout } from '@/hooks/CartLayout'
import CmsBlock from '@/components/CmsBlock'
import Breadcrumb from '@/components/Breadcrumb'
import CouponCode from '@/components/CouponCode'
import RelatedProducts from '@/components/RelatedProducts'
import CartSummary from '@/components/CartSummary'
import CommonLoading from '@/components/Common/CommonLoading'

import CartList from './CartList'
import CheckoutAction from './CheckoutAction'
import {
  StyledTitle,
  StyledCartLayout,
  StyledCartSummary,
  StyledEmpty,
  StyledGetSupport
} from './styled'

const CartLayout = () => {
  const {
    cartDetail,
    cartQty,
    crumbs,
    skus,
    handleToCheckout,
    crossSellVisible,
    modalRef,
    contextHolder,
    handleClearCart,
    actionDisabled,
    loading,
    cartList
  } = useCartLayout()

  return (
    <>
      <Breadcrumb items={crumbs} />
      <LineContainer>
        <StyledTitle>Cart</StyledTitle>
        {!isNull(cartDetail) && (
          <>
            {cartQty > 0 ? (
              <CommonLoading spinning={loading}>
                <StyledCartLayout>
                  <MediaLayout type="mobile">
                    <StyledGetSupport>
                      <CmsBlock identifiers={['get_support']} />
                    </StyledGetSupport>
                  </MediaLayout>
                  <div>
                    <CartList cartList={cartList} />
                    <div className="empty-cart">
                      <Button disabled={actionDisabled} onClick={handleClearCart}>
                        EMPTY CART
                      </Button>
                    </div>
                    {!actionDisabled && (
                      <div style={{ marginTop: '40px' }}>
                        <CouponCode />
                      </div>
                    )}
                  </div>
                  <div>
                    <StyledCartSummary>
                      <h3>Order Summary</h3>
                      <CartSummary isCartPage />
                      <CheckoutAction
                        actionDisabled={actionDisabled}
                        modalRef={modalRef}
                        handleToCheckout={handleToCheckout}
                      />
                    </StyledCartSummary>
                    <MediaLayout>
                      <StyledGetSupport>
                        <CmsBlock identifiers={['get_support']} />
                      </StyledGetSupport>
                    </MediaLayout>
                  </div>
                </StyledCartLayout>
              </CommonLoading>
            ) : (
              <StyledEmpty>
                <p>You have no items in your shopping cart</p>
              </StyledEmpty>
            )}
          </>
        )}
        {contextHolder}
      </LineContainer>
      {crossSellVisible && (
        <RelatedProducts
          searchVariables={{
            filter: {
              sku: {
                in: skus
              }
            }
          }}
          documentNode={GET_CROSSSELL}
        />
      )}
    </>
  )
}

export default CartLayout
