<?php
/** @var \Magento\Customer\Block\Adminhtml\Edit\Tab\View\PersonalInfo $block */
?>
<div class="admin__scope-old">
    <div class="fieldset-wrapper customer-set-password">
        <div class="fieldset-wrapper-title">
            <span class="title"><?= $block->escapeHtml(__('Set customer password')) ?></span>
        </div>
        <div class="fieldset-wrapper-content">
            <form method="POST" action="<?= $block->getUrl('customerpassword/index/setpassword') ?>">
                <?= $block->getBlockHtml('formkey') ?>
                <input type="hidden" name="id" value="<?= $block->getCustomerId() ?>"/>
                <input type="password" name="password" id="newPassword" class="admin__control-text"/>
                <span onclick="togglePasswordVisibility('newPassword')" class="action-default toggle-password" title="<?= $block->escapeHtml(__('Toggle password visibility')) ?>"></span>
                <button type="submit" class="primary"><?= $block->escapeHtml(__('Set password')) ?></button>
            </form>
        </div>
    </div>
</div>
<script>
    function togglePasswordVisibility(id) {
        var el = document.getElementById(id);
        if (el.type === "password") {
            el.type = "text";
        } else {
            el.type = "password";
        }
    }
</script>
