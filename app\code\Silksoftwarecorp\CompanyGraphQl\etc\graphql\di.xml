<?xml version="1.0"?>
<!--
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Silksoftwarecorp\CompanyGraphQl\Model\Resolver\Company\AllUsers">
        <arguments>
            <argument name="allowedResources" xsi:type="array">
                <item name="company_users_view" xsi:type="string">Magento_Company::users_view</item>
            </argument>
        </arguments>
    </type>
    <type name="Silksoftwarecorp\CompanyGraphQl\Model\Company\Users">
        <arguments>
            <argument name="companyUserStatus" xsi:type="array">
                <item name="active" xsi:type="array">
                    <item name="value" xsi:type="string">1</item>
                    <item name="label" xsi:type="string" translate="true">ACTIVE</item>
                </item>
                <item name="inactive" xsi:type="array">
                    <item name="value" xsi:type="string">0</item>
                    <item name="label" xsi:type="string" translate="true">INACTIVE</item>
                </item>
            </argument>
        </arguments>
    </type>
</config> 