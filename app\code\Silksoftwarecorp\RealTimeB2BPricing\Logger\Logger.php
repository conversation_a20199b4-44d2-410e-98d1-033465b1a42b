<?php

namespace Silksoftwarecorp\RealTimeB2BPricing\Logger;

use DateTimeZone;
use Magento\Framework\Logger\Monolog;
//use Magento\Payment\Model\Method\Logger;
use Psr\Log\LoggerInterface as PsrLoggerInterface;
use Silksoftwarecorp\RealTimeB2BPricing\Helper\Data;
use Silksoftwarecorp\RealTimeB2BPricing\Logger\Debug as DebugLogger;

class Logger implements LoggerInterface
{
    /**
     * @var PsrLoggerInterface
     */
    private $logger;

    /**
     * @var Data
     */
    protected $helper;

    /**
     * @param PsrLoggerInterface $logger
     * @param Data $helper
     */
    public function __construct(
        PsrLoggerInterface $logger,
        Data $helper
    ) {
        $this->logger = $logger;
        $this->helper = $helper;
    }

    public function critical(\Stringable|string $message): void
    {
        $this->logger->critical($message);
    }

    public function error(\Stringable|string $message): void
    {
        $this->logger->critical($message);
    }

    public function info(\Stringable|string $message): void
    {
        $this->logger->info($message);
    }

    public function debug(\Stringable|string $message): void
    {
        if ($this->helper->isDebugged()) {
            $this->logger->debug($message);
        }
    }
}
