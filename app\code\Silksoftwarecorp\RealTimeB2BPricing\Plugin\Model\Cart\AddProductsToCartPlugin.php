<?php

namespace Silksoftwarecorp\RealTimeB2BPricing\Plugin\Model\Cart;

use Magento\Quote\Model\Cart\AddProductsToCart as Subject;
use Magento\Quote\Model\Quote;
use Silksoftwarecorp\RealTimeB2BPricing\Model\Quote\AddProductsToCartOperation;

/**
 * Class AddProductsToCartPlugin
 */
class AddProductsToCartPlugin
{
    /**
     * @var AddProductsToCartOperation
     */
    protected $operation;

    /**
     * @param AddProductsToCartOperation $operation
     */
    public function __construct(AddProductsToCartOperation $operation)
    {
        $this->operation = $operation;
    }

    public function beforeAddItemsToCart(
        Subject $subject,
        Quote $cart,
        array $cartItems
    ) {
        $this->operation->preloadB2BPrices($cart, $cartItems);

        return null;
    }
}
