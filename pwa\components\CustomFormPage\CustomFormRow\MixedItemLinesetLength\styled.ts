import styled from '@emotion/styled'

export const StyledMixedItemLinesetLength = styled.div`
  .mixed-item-box {
    display: flex;
    align-items: center;
    min-height: 44px;

    & > div {
      &:not(:first-of-type) {
        margin-left: 10px;
      }
    }
  }

  .mixed-item-flex {
    display: flex;
    align-items: center;

    span {
      padding-right: 5px;
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    .mixed-item-box {
      flex-direction: column;
      align-items: flex-start;

      & > div {
        &:not(:first-of-type) {
          margin-left: 0;
          margin-top: 5px;
        }
      }
    }

    .mixed-item-flex {
      width: 100%;
    }
  }
`
