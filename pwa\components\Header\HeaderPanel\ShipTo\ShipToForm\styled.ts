import styled from '@emotion/styled'

export const StyledAddressModal = styled.div`
  padding-top: 20px;

  .${({ theme }) => theme.namespace}-form-item {
    margin-bottom: 16px;

    &.default-billing-item {
      margin-bottom: 0;
    }

    div {
      font-weight: 400;
    }
  }
`

export const StyledFormItem = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-column-gap: 16px;
  justify-content: space-between;
  align-items: center;
`
