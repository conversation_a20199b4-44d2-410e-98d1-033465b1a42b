<?php

namespace Silksoftwarecorp\UnifiedArPayment\Helper;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;
use Psr\Log\LoggerInterface;

class Logger
{
    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * @param LoggerInterface $logger
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(
        LoggerInterface $logger,
        ScopeConfigInterface $scopeConfig
    ) {
        $this->logger = $logger;
        $this->scopeConfig = $scopeConfig;
    }

    /**
     * Check if logging is enabled
     *
     * @return bool
     */
    public function isLoggingEnabled()
    {
        return (bool)$this->scopeConfig->getValue(
            'payment/unifiedarpayment/enable_logs',
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Log API request
     *
     * @param string $endpoint
     * @param string $xml
     * @param string $operation
     * @return void
     */
    public function logRequest($endpoint, $xml, $operation = 'API Request')
    {
        if (!$this->isLoggingEnabled()) {
            return;
        }

        try {
            $this->logger->info(
                sprintf('[UnifiedArPayment] %s - Endpoint: %s', $operation, $endpoint),
                [
                    'endpoint' => $endpoint,
                    'request_xml' => $xml,
                    'operation' => $operation
                ]
            );
        } catch (\Exception $e) {
            // Silently fail if logging fails
        }
    }

    /**
     * Log API response
     *
     * @param string $response
     * @param int $httpCode
     * @param string $operation
     * @return void
     */
    public function logResponse($response, $httpCode, $operation = 'API Response')
    {
        if (!$this->isLoggingEnabled()) {
            return;
        }

        try {
            $this->logger->info(
                sprintf('[UnifiedArPayment] %s - HTTP Code: %d', $operation, $httpCode),
                [
                    'response' => $response,
                    'http_code' => $httpCode,
                    'operation' => $operation
                ]
            );
        } catch (\Exception $e) {
            // Silently fail if logging fails
        }
    }

    /**
     * Log error
     *
     * @param string $message
     * @param array $context
     * @param string $operation
     * @return void
     */
    public function logError($message, $context = [], $operation = 'API Error')
    {
        if (!$this->isLoggingEnabled()) {
            return;
        }

        try {
            $this->logger->error(
                sprintf('[UnifiedArPayment] %s - %s', $operation, $message),
                array_merge($context, ['operation' => $operation])
            );
        } catch (\Exception $e) {
            // Silently fail if logging fails
        }
    }
} 