import styled from '@emotion/styled'

export const StyledOrderHelp = styled.div`
  min-height: 97px;

  .order-help-block {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 97px;
    padding: 24px 42px;
    width: 100%;
    max-width: 478px;
    border-radius: 20px 0 20px 0;
    background: #968c8333;

    h3 {
      position: relative;
      padding-left: 21px;
      font-weight: 700;
      font-size: 17px;
      line-height: 21px;
      letter-spacing: 0.03em;

      svg {
        position: absolute;
        top: 3px;
        left: 0;
      }

      a,
      span {
        color: var(--color-font) !important;
      }
    }

    div {
      font-weight: 400;
      font-size: 15px;
      line-height: 25px;
      letter-spacing: 0.02em;
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    .order-help-block {
      padding: 25px 42px;
      box-sizing: border-box;

      h3 {
        display: flex;
        flex-direction: column;
        text-align: center;
      }

      div {
        text-align: center;
      }
    }
  }
`
