import styled from '@emotion/styled'

export const StyledSearch = styled.div`
  position: relative;
  transition: all 0.3s ease-in-out;
  z-index: 11;
  flex: 1;
  padding: 0 12px;

  .${({ theme }) => theme.namespace} {
    &-form {
      max-width: 576px;
      width: 100%;
      margin: 0 auto;
    }

    &-form-item {
      margin-bottom: 0;
    }

    &-select {
      height: 44px;
    }

    &-input {
      font-weight: 400;
      font-size: 16px;
      line-height: 24px;
      letter-spacing: 0.02em;
      color: var(--color-font);
      border-radius: 4px;

      &::placeholder {
        font-weight: 400;
        font-size: 16px;
        line-height: 24px;
        letter-spacing: 0.02em;
        color: var(--color-font);
      }
    }

    &-input-affix-wrapper {
      padding: 9px 16px;
      border-radius: 4px !important;
    }

    &-input-search {
      .${({ theme }) => theme.namespace} {
        &-input-group-addon {
          display: none;
        }
      }
    }
    &-select-item {
      padding-top: 10px !important;
      padding-bottom: 10px !important;
      &:not(:last-of-type) {
        border-bottom: 1px solid #eee;
      }
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    top: -56px;
    height: 0;
    overflow: hidden;
    padding: 0;

    &.search-visible {
      top: 0;
      height: 56px;
      overflow: initial;
    }

    .${({ theme }) => theme.namespace} {
      &-form {
        max-width: initial;
        width: 100%;
        padding: 6px 16px;
        background: #f5f5f5;
      }

      &-input-affix-wrapper {
        border: none;
      }
    }
  }
`

export const StyledLoading = styled.div`
  display: flex;
  height: 100px;
  justify-content: center;
  align-items: center;
`
