import styled from '@emotion/styled'

export const StyledMixedItemPropertyLocator = styled.div`
  .mixed-item-box {
    display: flex;
    align-items: center;
    min-height: 44px;

    & > div {
      &:not(:first-of-type) {
        margin-left: 10px;
      }
    }
  }

  .mixed-item-flex {
    display: flex;
    align-items: center;

    span {
      padding-right: 5px;
    }

    .mixed-item-date-picker {
      position: relative;
      padding-left: 30px;
      width: 120px;
      height: 44px;
      border: 1px solid #d9d9d9;
      border-radius: 3px;

      .${({ theme }) => theme.namespace}-picker-clear {
        padding-right: 0;
        margin-right: -14px;
      }

      &::after {
        content: '';
        display: block;
        position: absolute;
        top: 8px;
        left: 4px;
        z-index: 0;
        width: 24px;
        height: 24px;
        background-image: url('/images/date-picker-icon.png');
        background-position: center;
        background-repeat: no-repeat;
        background-size: cover;
      }
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    .mixed-item-box {
      flex-direction: column;
      align-items: flex-start;

      & > div {
        &:not(:first-of-type) {
          margin-left: 0;
          margin-top: 5px;
        }
      }
    }

    .mixed-item-flex {
      width: 100%;

      .mixed-item-date-picker {
        flex: 1;
      }
    }
  }
`
