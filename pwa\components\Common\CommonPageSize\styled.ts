import styled from '@emotion/styled'

export const StyledCommonPageSize = styled.div`
  display: inline-grid;
  grid-template-columns: auto 54px auto;
  grid-column-gap: 10px;
  align-items: center;

  p {
    font-weight: 400;
    font-size: 15px;
    line-height: 21px;
    letter-spacing: 0;
    color: var(--color-black);
    margin-top: -2px;
  }

  .${({ theme }) => theme.namespace}-select {
    width: 54px !important;
    height: 33px !important;

    &-selector {
      padding: 0 !important;
      border-radius: 3px !important;
      border: 1px solid #d9d9d9 !important;
      background: #f5f5f5 !important;
    }

    &-selection-item {
      padding-right: 16px !important;
      text-align: center;
    }

    &-arrow {
      right: 10px !important;
    }
  }
`
