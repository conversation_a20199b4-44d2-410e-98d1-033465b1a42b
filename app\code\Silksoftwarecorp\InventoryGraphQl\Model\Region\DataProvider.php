<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */

declare(strict_types=1);

namespace Silksoftwarecorp\InventoryGraphQl\Model\Region;

use Magento\Directory\Model\ResourceModel\Region\CollectionFactory;
use Magento\Directory\Model\Region;

/**
 * Region data provider for caching region data
 */
class DataProvider
{
    /**
     * @var CollectionFactory
     */
    private $regionCollectionFactory;

    /**
     * @var array
     */
    private $regionData = [];

    /**
     * @var array
     */
    private $regionCodeData = [];

    /**
     * @var bool
     */
    private $isLoaded = false;

    /**
     * @param CollectionFactory $regionCollectionFactory
     */
    public function __construct(
        CollectionFactory $regionCollectionFactory
    ) {
        $this->regionCollectionFactory = $regionCollectionFactory;
    }

    /**
     * Load all region data
     */
    private function loadRegionData(): void
    {
        if ($this->isLoaded) {
            return;
        }

        try {
            $collection = $this->regionCollectionFactory->create();

            foreach ($collection as $region) {
                $countryId = $region->getCountryId();
                $regionId = $region->getRegionId();
                $regionCode = $region->getCode();

                if (!isset($this->regionData[$countryId])) {
                    $this->regionData[$countryId] = [];
                }
                $this->regionData[$countryId][$regionId] = $region;

                if (!isset($this->regionCodeData[$countryId])) {
                    $this->regionCodeData[$countryId] = [];
                }
                $this->regionCodeData[$countryId][$regionCode] = $region;
            }

            $this->isLoaded = true;
        } catch (\Exception $e) {
            $this->isLoaded = false;
        }
    }

    /**
     * Get region by ID
     *
     * @param string $countryId
     * @param string $regionId
     * @return Region|null
     */
    public function getRegionById(string $countryId, string $regionId): ?Region
    {
        $this->loadRegionData();

        return $this->regionData[$countryId][$regionId] ?? null;
    }

    /**
     * Get region by code
     *
     * @param string $countryId
     * @param string $regionCode
     * @return Region|null
     */
    public function getRegionByCode(string $countryId, string $regionCode): ?Region
    {
        $this->loadRegionData();

        return $this->regionCodeData[$countryId][$regionCode] ?? null;
    }

    /**
     * Get all regions for a country
     *
     * @param string $countryId
     * @return array
     */
    public function getRegionsByCountry(string $countryId): array
    {
        $this->loadRegionData();

        return $this->regionData[$countryId] ?? [];
    }

    /**
     * Clear cached data
     */
    public function clearCache(): void
    {
        $this->regionData = [];
        $this->regionCodeData = [];
        $this->isLoaded = false;
    }
}
