<?php

declare(strict_types=1);

namespace Silksoftwarecorp\ShipToGraphQl\Model\Resolver\Product;

use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Catalog\Model\Product;
use Magento\Catalog\Helper\Output as OutputHelper;
use Magento\InventoryApi\Api\GetSourceItemsBySkuInterface;

class ProductInventorySource implements ResolverInterface
{
    private GetSourceItemsBySkuInterface $getSourceItemsBySku;

    public function __construct(
        GetSourceItemsBySkuInterface $getSourceItemsBySku
    ) {
        $this->getSourceItemsBySku = $getSourceItemsBySku;
    }

    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
        if (!array_key_exists('model', $value) || !$value['model'] instanceof ProductInterface) {
            throw new LocalizedException(__('"model" value should be specified'));
        }

        /* @var $product Product */
        $product = $value['model'];
        $sku = $product->getSku();
        $sourceItems = $this->getSourceItemsBySku->execute($sku);

        $inventoryData = [];
        foreach ($sourceItems as $item) {
            $inventoryData[] = [
                'source_code' => $item->getSourceCode(),
                'quantity' => $item->getQuantity(),
                'status' => $item->getStatus()
            ];
        }

        return $inventoryData;
    }
}
