<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Store:etc/config.xsd">
    <default>
        <payment>
            <unifiedarpayment>
                <active>1</active>
                <model>Silksoftwarecorp\UnifiedArPayment\Model\Payment\UnifiedAr</model>
                <title>Credit Card</title>
                <return_url_title>Return to Store</return_url_title>
                <welcome_message>Welcome to Unified A/R Payment</welcome_message>
                <company_name>Dunder <PERSON>lin</company_name>
                <payment_account_reference_number>100012</payment_account_reference_number>
                <return_url>https://mcstaging.blevinsinc.com/unifiedarpaymentreturn</return_url>
                <ui_iframe_page_endpoint>https://eem-ui.gounified-nonprod.com</ui_iframe_page_endpoint>
                <enable_logs>1</enable_logs>
                <environment>sandbox</environment>
                <sandbox_create_transaction_setup_endpoint>https://eem-api.gounified-nonprod.com</sandbox_create_transaction_setup_endpoint>
                <sandbox_payment_account_query_endpoint>https://eem-api.gounified-nonprod.com</sandbox_payment_account_query_endpoint>
                <sandbox_surcharge_query_endpoint>https://eem-api.gounified-nonprod.com</sandbox_surcharge_query_endpoint>
                <sandbox_account_id>dmiff</sandbox_account_id>
                <sandbox_account_token>abc</sandbox_account_token>
                <sandbox_acceptor_id>dmiff-1</sandbox_acceptor_id>
                <sandbox_application_id>509</sandbox_application_id>
                <sandbox_application_name>Epicor Prophet 21</sandbox_application_name>
                <sandbox_application_version>21.1.4649</sandbox_application_version>
                <production_create_transaction_setup_endpoint>https://eem-api.gounified.com</production_create_transaction_setup_endpoint>
                <production_payment_account_query_endpoint>https://eem-api.gounified.com</production_payment_account_query_endpoint>
                <production_surcharge_query_endpoint>https://eem-api.gounified.com</production_surcharge_query_endpoint>
                <production_account_id></production_account_id>
                <production_account_token></production_account_token>
                <production_acceptor_id></production_acceptor_id>
                <production_application_id>509</production_application_id>
                <production_application_name>Epicor Prophet 21</production_application_name>
                <production_application_version>21.1.4649</production_application_version>
<!--                <can_authorize>1</can_authorize>-->
<!--                <can_capture>1</can_capture>-->
<!--                <can_void>1</can_void>-->
                <payment_action>authorize</payment_action>
                <order_status>processing</order_status>
                <group>offline</group>
            </unifiedarpayment>
        </payment>
    </default>
</config>
