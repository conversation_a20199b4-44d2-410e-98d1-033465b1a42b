<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Store:etc/config.xsd">
    <default>
        <unified_ar>
            <general>
                <active>1</active>
                <debug>0</debug>
            </general>
            <api>
                <environment>sandbox</environment>
                <sandbox_url>https://coreapi-dev-eus.gounified-nonprod.com/api/v2</sandbox_url>
                <timeout>10</timeout>
                <api_key backend_model="Magento\Config\Model\Config\Backend\Encrypted"/>
                <sandbox_api_key backend_model="Magento\Config\Model\Config\Backend\Encrypted"/>
            </api>
            <invoice>
                <pay_now_key>blevinsinc</pay_now_key>
                <pay_now_link>https://paynow-dev-eus.gounified-nonprod.com/qpay/blevinsinc?sc=</pay_now_link>
                <complete_url>https://mcstaging.blevinsinc.com/completepaynow</complete_url>
            </invoice>
        </unified_ar>
    </default>
</config>
