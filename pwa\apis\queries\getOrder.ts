import { gql } from '@apollo/client'

export const GET_ORDER = gql`
  query getOrder($filter: CustomerOrdersFilterInput) {
    customer {
      orders(filter: $filter) {
        items {
          shipping_method
          shipping_address {
            city
            country_code
            company
            firstname
            lastname
            postcode
            region
            region_id
            street
            telephone
          }
          items {
            product_sku
          }
        }
      }
    }
  }
`
