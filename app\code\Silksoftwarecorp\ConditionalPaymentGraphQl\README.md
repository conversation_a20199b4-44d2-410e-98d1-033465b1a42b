# Conditional Payment Methods GraphQL Module

## Overview

This module provides conditional payment method availability based on selected shipping methods for Magento 2 GraphQL API.

## Features

- **Always Available Payment Method**: `unifiedarpayment` (Credit Card) is always available regardless of shipping method
- **Conditional Payment Methods**:
  - `cashondelivery` (Cash on Delivery) - Available only when shipping method is "Blevins Delivery" (carrier_code=flatrate)
  - `checkmo` (Check / Money Order) - Available only when shipping method is "In Store Pickup" (carrier_code=instore)

## GraphQL Behavior

When calling the `available_payment_methods` query on a cart, the response will dynamically include or exclude payment methods based on the currently selected shipping method:

### Expected Results:
- **Shipping = flatrate** → `unifiedarpayment`, `cashondelivery`
- **Shipping = instore** → `unifiedarpayment`, `checkmo`  
- **Shipping = anything else** → only `unifiedarpayment`

## GraphQL Query Example

```graphql
query getPaymentMethods($cartId: String!) {
  cart(cart_id: $cartId) {
    available_payment_methods {
      code
      title
    }
  }
}
```

## Implementation Details

### Plugin Architecture
- Extends `Magento\QuoteGraphQl\Model\Resolver\AvailablePaymentMethods` using an `afterResolve` plugin
- Filters payment methods based on the selected shipping method from the cart

### Payment Method Models
- Custom payment method classes that implement availability logic
- Each conditional payment method checks the shipping method in its `isAvailable()` method

### Configuration
- Payment methods are configured in `etc/config.xml` with default settings
- Payment method definitions are in `etc/payment.xml`

## Installation

1. Copy the module to `app/code/Silksoftwarecorp/ConditionalPaymentGraphQl/`
2. Run the following commands:
   ```bash
   php bin/magento setup:upgrade
   php bin/magento setup:di:compile
   php bin/magento cache:clean
   ```

## Configuration

The payment methods can be configured in the Magento Admin Panel:
- **Stores → Configuration → Sales → Payment Methods**
- Configure titles, instructions, and other settings for each payment method

## Debugging

The module includes extensive logging to help with debugging:
- Logs are written to the standard Magento log files
- Log messages include shipping method detection and payment method filtering decisions

## Files Structure

```
app/code/Silksoftwarecorp/ConditionalPaymentGraphQl/
├── registration.php
├── composer.json
├── etc/
│   ├── module.xml
│   ├── config.xml
│   ├── payment.xml
│   └── graphql/
│       └── di.xml
├── Model/
│   └── Payment/
│       ├── CashOnDelivery.php
│       └── CheckMoneyOrder.php
├── Plugin/
│   └── Model/
│       └── Resolver/
│           └── AvailablePaymentMethodsPlugin.php
└── README.md
```

## Dependencies

- Magento 2.4+
- PHP 7.4+ or 8.1+
- Magento_Quote
- Magento_QuoteGraphQl  
- Magento_Payment
