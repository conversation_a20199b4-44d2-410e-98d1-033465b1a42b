import { gql } from '@apollo/client'
import type { DocumentNode } from '@apollo/client'

export const GET_CUSTOMER_INFO: DocumentNode = gql`
  query getCustomerInfo {
    customer {
      default_billing
      default_shipping
      email
      firstname
      is_subscribed
      lastname
      customer_type
      customer_status
      role {
        name
        permissions {
          text
          children {
            id
            sort_order
            text
            children {
              children {
                id
                sort_order
                text
              }
              id
              sort_order
              text
            }
          }
        }
      }
    }
  }
`
