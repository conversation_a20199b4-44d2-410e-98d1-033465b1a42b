import { FormattedMessage } from 'react-intl'
import { useDispatch, useSelector } from 'react-redux'
import { useCallback, useState } from 'react'
import { notification } from 'antd'

import { useAwaitQuery } from '@ranger-theme/apollo'
import { useMutation } from '@apollo/client'
import CommonTable from '@/components/Common/CommonTable'
import CommonCheckbox from '@/components/Common/CommonCheckbox'
import CommonLoading from '@/components/Common/CommonLoading'
import { useCountryTransform } from '@/hooks/CountryTransform'
import CommonLink from '@/components/Common/CommonLink'
import { actions as userActions } from '@/store/user'
import { defaultCompanyIdSelector, defaultShipToSelector } from '@/store/user/slice'
import { B2B_GET_SHIP_TO_LIST_ALL } from '@/apis/queries/getShipToListAll'
import { SET_DEFAULT_SHIP_TO } from '@/apis/mutations/setDefaultShipTo'

const ShipToAddress = () => {
  const dispatch = useDispatch()
  const { getRegionNameByCode } = useCountryTransform()
  const getShipToListAllQuery = useAwaitQuery(B2B_GET_SHIP_TO_LIST_ALL)
  const [setDefaultShipToMutation] = useMutation(SET_DEFAULT_SHIP_TO)

  const shipToLoading = useSelector((state: Store) => state.user.shipToLoading)
  const shipToAddresses = useSelector((state: Store) => state.user.shipToAddresses)
  const defaultCompanyId = useSelector(defaultCompanyIdSelector)
  const defaultShipTo = useSelector(defaultShipToSelector)

  const [loading, setLoading] = useState(false)

  const handeSetDefaultShipTo = useCallback(
    async (entity_id) => {
      try {
        setLoading(true)
        const { data } = await setDefaultShipToMutation({
          variables: {
            input: { entity_id }
          }
        })
        if (!data?.setDefaultShipTo?.success) {
          notification.warning({
            message: data?.setDefaultShipTo?.message || 'Set default ship to failed.'
          })
          return
        }
        const { data: shipTpRes } = await getShipToListAllQuery({
          fetchPolicy: 'no-cache',
          variables: {
            company_id: defaultCompanyId
          }
        })
        const items = shipTpRes?.getShipToListAll?.items ?? []
        dispatch(userActions.setShipToAddresses(items))
      } catch (e) {
        console.error(e)
      } finally {
        setLoading(false)
      }
    },
    [defaultCompanyId, dispatch, getShipToListAllQuery, setDefaultShipToMutation]
  )

  const columns = [
    {
      title: 'Default',
      dataIndex: 'entity_id',
      key: 'entity_id',
      render: (param, record) => (
        <CommonCheckbox
          checked={record?.entity_id === defaultShipTo?.entity_id}
          onChange={(isChecked) => {
            if (isChecked && record?.entity_id) {
              handeSetDefaultShipTo(record.entity_id)
            }
          }}
        />
      )
    },
    {
      title: 'Company',
      dataIndex: 'ship_to_name',
      key: 'ship_to_name'
    },
    {
      title: 'Street Address',
      dataIndex: 'entity_id',
      key: 'entity_id',
      render: (param, record) => (
        <p>
          {`${record?.street ?? ''} ${record?.street_line2 || ''}  ${record?.street_line3 || ''}`}
        </p>
      )
    },
    {
      title: <FormattedMessage id="global.city" />,
      dataIndex: 'city',
      key: 'city'
    },
    {
      title: 'State',
      dataIndex: 'region_code',
      key: 'region_code',
      render: (param, record) => {
        return getRegionNameByCode(record?.country_id, record?.region_code)
      }
    },
    {
      title: 'Zip',
      dataIndex: 'postcode',
      key: 'postcode'
    },
    {
      title: 'Phone',
      dataIndex: 'telephone',
      key: 'telephone'
    },
    {
      title: 'Action',
      dataIndex: 'entity_id',
      key: 'entity_id',
      align: 'center',
      width: 130,
      mobileContentFull: true,
      render: (param, record) => {
        let href = `/change-address-request?`
        if (record?.ship_to_name) {
          href += `&ship_to_name=${record.ship_to_name}`
        }
        if (record?.street) {
          href += `&street=${record.street}`
        }
        if (record?.street_line2) {
          href += `&street_line2=${record.street_line2}`
        }
        if (record?.city) {
          href += `&city=${record.city}`
        }
        if (record?.region_code && record?.country_id) {
          href += `&state=${getRegionNameByCode(record?.country_id, record?.region_code)}`
        }
        if (record?.postcode) {
          href += `&postcode=${record.postcode}`
        }
        if (record?.telephone) {
          href += `&telephone=${record.telephone}`
        }
        if (record?.email) {
          href += `&email=${record.email}`
        }
        return (
          <CommonLink href={href} title="Change Address Request">
            Edit
          </CommonLink>
        )
      }
    }
  ]

  return (
    <div>
      {shipToAddresses.length === 0 ? (
        <FormattedMessage id="account.noAdditional" />
      ) : (
        <CommonLoading spinning={shipToLoading || loading}>
          <CommonTable
            rowKey={(record) => record.entity_id}
            columns={columns}
            dataSource={shipToAddresses}
          />
        </CommonLoading>
      )}
    </div>
  )
}

export default ShipToAddress
