type Query {
    storeBranchSources: [BranchSource] @resolver(class: "Silksoftwarecorp\\InventoryGraphQl\\Model\\Resolver\\StoreBranchSources") @doc(description: "Get store branch sources")
    getBranchSource(code: String) : BranchSource @resolver(class: "Silksoftwarecorp\\InventoryGraphQl\\Model\\Resolver\\GetBranchSource")
}

type BranchSource {
    code: String @doc(description: "Branch source code")
    name: String @doc(description: "Branch name")
    active: <PERSON><PERSON><PERSON> @doc(description: "Branch active status")
    description: String @doc(description: "Branch description")
    address: <PERSON><PERSON>ddress @doc(description: "Branch address information")
    lat: String @doc(description: "Latitude")
    lng: String @doc(description: "Longitude")
    contact_name: String @doc(description: "Contact person name")
    email: String @doc(description: "Contact email address")
    phone: String @doc(description: "Contact phone number")
    fax: String @doc(description: "Contact fax number")
    install_request_form: <PERSON><PERSON><PERSON> @doc(description: "Install Request form (Fast Fax)")
    install_app: <PERSON><PERSON><PERSON> @doc(description: "Install App")
    text_number: String @doc(description: "Text Number")
    schedule_string: String @doc(description: "Branch operating schedules JSON")
    schedules: BranchSchedules @doc(description: "Branch operating schedules")
    holiday_closures: String @doc(description: "Holiday Closures")
    general_manager: BranchManagerProfile @resolver(class: "Silksoftwarecorp\\InventoryGraphQl\\Model\\Resolver\\Source\\GeneralManager")
    territory_managers: [BranchManagerProfile] @resolver(class: "Silksoftwarecorp\\InventoryGraphQl\\Model\\Resolver\\Source\\TerritoryManagers")
}

type BranchAddress {
    country_code: String @doc(description: "Country code")
    region: BranchRegion @doc(description: "Region/State")
    city: String @doc(description: "City name")
    street: [String] @doc(description: "Street address lines")
    postcode: String @doc(description: "Postal/ZIP code")
}

type BranchRegion {
    region_id: String @doc(description: "Region ID")
    region_code: String @doc(description: "Region code")
    region: String @doc(description: "Region name")
}

type BranchSchedules {
    office: BranchSchedule @doc(description: "Office operating schedule")
    store: BranchSchedule @doc(description: "Store operating schedule")
}

type BranchSchedule {
    enabled: Boolean @doc(description: "Is enabled?")
    monday: BranchScheduleInfo @doc(description: "Monday schedule")
    tuesday: BranchScheduleInfo @doc(description: "Tuesday schedule")
    wednesday: BranchScheduleInfo @doc(description: "Wednesday schedule")
    thursday: BranchScheduleInfo @doc(description: "Thursday schedule")
    friday: BranchScheduleInfo @doc(description: "Friday schedule")
    saturday: BranchScheduleInfo @doc(description: "Saturday schedule")
    sunday: BranchScheduleInfo @doc(description: "Sunday schedule")
}

type BranchScheduleInfo {
    status: Boolean @doc(description: "Open/closed status")
    from: String @doc(description: "Opening time")
    break_form: String @doc(description: "Break start time")
    break_to: String @doc(description: "Break end time")
    to: String @doc(description: "Closing time")
}

type BranchManagerProfile {
    name: String
    image: String
    phone: String
    region: String
}
