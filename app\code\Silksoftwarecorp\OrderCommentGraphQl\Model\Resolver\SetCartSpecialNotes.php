<?php
namespace Silksoftwarecorp\OrderCommentGraphQl\Model\Resolver;

use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\QuoteGraphQl\Model\Cart\GetCartForUser;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;

class SetCartSpecialNotes implements ResolverInterface
{
    private $cartRepository;
    private $cartExtensionFactory;
        /**
     * @var GetCartForUser
     */
    private $getCartForUser;

    public function __construct(
        CartRepositoryInterface $cartRepository,
        \Magento\Quote\Api\Data\CartExtensionFactory $cartExtensionFactory,
        GetCartForUser $getCartForUser
    ) {
        $this->cartRepository = $cartRepository;
        $this->cartExtensionFactory = $cartExtensionFactory;
        $this->getCartForUser = $getCartForUser;
    }

    public function resolve($field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
        if (empty($args['cart_id'])) {
            throw new GraphQlInputException(__('Required parameter "cart_id" is missing'));
        }
        if (!isset($args['special_notes'])) {
            throw new GraphQlInputException(__('special_notes are required'));
        }
        $maskedCartId = $args['cart_id'];
        $specialNotes = $args['special_notes'];

        $currentUserId = $context->getUserId();
        $storeId = (int)$context->getExtensionAttributes()->getStore()->getId();
        $quote = $this->getCartForUser->execute($maskedCartId, $currentUserId, $storeId);

        //$quote = $this->cartRepository->getActive($cartId);
        $extensionAttributes = $quote->getExtensionAttributes();
        if ($extensionAttributes === null) {
            $extensionAttributes = $this->cartExtensionFactory->create();
        }
        $extensionAttributes->setSpecialNotes($specialNotes);
        $quote->setExtensionAttributes($extensionAttributes);
        $this->cartRepository->save($quote);

        return [
            'cart_id' => $maskedCartId,
            'special_notes' => $specialNotes,
            'success' => true
        ];
    }
} 