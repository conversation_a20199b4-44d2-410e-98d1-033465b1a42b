import { memo } from 'react'
import { Input } from 'antd'

import CommonTable from '@/components/Common/CommonTable'
import CommonLoading from '@/components/Common/CommonLoading'
import CommonLink from '@/components/Common/CommonLink'
import CommonTableSort from '@/components/Common/CommonTableSort'
import TableHeaderSearchItem from '@/components/Common/TableHeaderSearchItem'
import { useBackorderedProducts } from '@/hooks/BackorderedProducts'

import TableHeader from '../TableHeader'
import { StyledBackorderedProducts } from './styled'

const BackorderedProducts = ({ isQuickView = false }) => {
  const {
    loading,
    tableData,
    page,
    handlePageChange,
    handlePageSizeChange,
    sort,
    onSortChange,
    onFilterInputChange
  } = useBackorderedProducts(isQuickView)

  const columns = [
    {
      title: (
        <TableHeaderSearchItem>
          <p
            aria-hidden
            onClick={() => {
              onSortChange('sku')
            }}>
            Item ID
            <CommonTableSort sortOrder={sort.code === 'sku' ? sort.direction : ''} />
          </p>
          <div className="item-search">
            <Input
              allowClear
              onChange={(e) => {
                onFilterInputChange(e, 'sku')
              }}
            />
          </div>
        </TableHeaderSearchItem>
      ),
      dataIndex: 'sku',
      className: 'primary-text',
      width: 246,
      render: (param) => (
        <CommonLink href={`/${param}`} title={param}>
          <span dangerouslySetInnerHTML={{ __html: param || '' }} />
        </CommonLink>
      )
    },
    {
      title: (
        <TableHeaderSearchItem>
          <p
            aria-hidden
            onClick={() => {
              onSortChange('product_name')
            }}>
            Product Name
            <CommonTableSort sortOrder={sort.code === 'product_name' ? sort.direction : ''} />
          </p>
          <div className="item-search">
            <Input
              allowClear
              onChange={(e) => {
                onFilterInputChange(e, 'product_name')
              }}
            />
          </div>
        </TableHeaderSearchItem>
      ),
      dataIndex: 'product_name',
      className: 'primary-text',
      render: (param) => (
        <CommonLink href={`/${param}`} title={param}>
          <span dangerouslySetInnerHTML={{ __html: param || '' }} />
        </CommonLink>
      )
    },
    {
      title: (
        <TableHeaderSearchItem>
          <p
            aria-hidden
            onClick={() => {
              onSortChange('order_number')
            }}>
            Order Number
            <CommonTableSort sortOrder={sort.code === 'order_number' ? sort.direction : ''} />
          </p>
          <div className="item-search" />
        </TableHeaderSearchItem>
      ),
      dataIndex: 'order_number',
      className: 'primary-text',
      width: 202,
      render: (param) => (
        <CommonLink href={`/account/orders/${param}`} title={param}>
          <span dangerouslySetInnerHTML={{ __html: param || '' }} />
        </CommonLink>
      )
    },
    {
      title: (
        <TableHeaderSearchItem>
          <p
            aria-hidden
            onClick={() => {
              onSortChange('date_ordered')
            }}>
            Date Ordered
            <CommonTableSort sortOrder={sort.code === 'date_ordered' ? sort.direction : ''} />
          </p>
          <div className="item-search" />
        </TableHeaderSearchItem>
      ),
      dataIndex: 'date_ordered',
      width: 190
    },
    {
      title: (
        <TableHeaderSearchItem>
          <p
            aria-hidden
            onClick={() => {
              onSortChange('quantity')
            }}>
            Quantity
            <CommonTableSort sortOrder={sort.code === 'quantity' ? sort.direction : ''} />
          </p>
          <div className="item-search" />
        </TableHeaderSearchItem>
      ),
      dataIndex: 'quantity',
      align: 'center',
      width: 100
    }
  ]

  return (
    <CommonLoading spinning={loading}>
      {!loading && (
        <StyledBackorderedProducts className={isQuickView ? 'action-inactive' : ''}>
          {isQuickView && (
            <TableHeader
              title="Recent Backordered Products"
              viewAllUrl="/account/backordered-products"
            />
          )}
          <CommonTable
            rowKey={(params) => params.sku}
            columns={columns}
            dataSource={tableData.items}
            total={isQuickView ? 0 : tableData.total}
            totalCountVisible
            current={page.currentPage}
            pageSize={page.pageSize}
            onPageChange={handlePageChange}
            onPageSizeChange={handlePageSizeChange}
          />
        </StyledBackorderedProducts>
      )}
    </CommonLoading>
  )
}

export default memo(BackorderedProducts)
