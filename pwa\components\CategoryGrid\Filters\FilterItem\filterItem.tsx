import { memo, useMemo, useCallback } from 'react'
import { Collapse } from 'antd'
import { clsx } from 'clsx'

import { useAppMediaQuery } from '@/packages/hooks'

import { StyledFilterItem } from './styled'

const FilterItem = ({ filterState = new Set(), items, filterApi, setIsApplying }: any) => {
  const { toggleItem, selectItem } = filterApi
  const { attribute_code: group, options, label: filterItemTitle } = items
  const checkedItems = options.filter((option: any) => {
    return filterState && filterState.has(option)
  })
  const { isMobile } = useAppMediaQuery()

  const hasOptions = useMemo(() => {
    return options.length > 0
  }, [options])

  const filterOptionSelect = useCallback(
    (item) => {
      const curItem = options.find((option) => option.value === item.value)
      if (curItem) {
        const callback = group === 'price' ? selectItem : toggleItem
        // callback({ group, item: { label: curItem?.label, value: curItem?.value } })
        callback({ group, item })
        if (!isMobile) {
          // Update router
          setIsApplying(true)
        }
      }
    },
    [options, group, selectItem, toggleItem, isMobile, setIsApplying]
  )

  const expandIcon = useCallback(
    (panelProps) => (
      <svg width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false">
        <use xlinkHref={panelProps.isActive ? '#icon-collapse-hide-dark' : '#icon-collapse-dark'} />
      </svg>
    ),
    []
  )

  return hasOptions ? (
    <StyledFilterItem className="filter-item">
      <Collapse
        ghost
        // defaultActiveKey={['filter-item']}
        expandIconPosition="end"
        expandIcon={expandIcon}
        items={[
          {
            key: 'filter-item',
            label: <div className="filter-item-header">{filterItemTitle}</div>,
            children: (
              <div className="filter-item-options">
                {options.map((item: any) => {
                  const isActive = checkedItems.some((val) => val.value === item.value)
                  return (
                    <div
                      aria-hidden="true"
                      key={item.value}
                      className={clsx('filter-item-option', {
                        'option-active': isActive
                      })}
                      onClick={() => {
                        filterOptionSelect(item)
                      }}>
                      <div
                        className={clsx('option-checkbox', {
                          'option-checkbox-active': isActive
                        })}>
                        <svg
                          width="12px"
                          height="9px"
                          fill="currentColor"
                          aria-hidden="true"
                          focusable="false">
                          <use xlinkHref="#icon-checkbox-check" />
                        </svg>
                      </div>
                      <div>
                        <span dangerouslySetInnerHTML={{ __html: item.label }} />
                        <span className="item-count">
                          (<span dangerouslySetInnerHTML={{ __html: item.count }} />)
                        </span>
                      </div>
                    </div>
                  )
                })}
              </div>
            )
          }
        ]}
      />
    </StyledFilterItem>
  ) : null
}

export default memo(FilterItem)
