<?php

namespace Silksoftwarecorp\AmastyCustomForm\Plugin\Customform\Model;

use Amasty\Customform\Api\Data\AnswerInterface;
use Amasty\Customform\Model\Submit;
use Magento\Framework\App\RequestInterface;

class SubmitPlugin
{
    /**
     * @var RequestInterface
     */
    private $request;

    /**
     * @param RequestInterface $request
     */
    public function __construct(RequestInterface $request)
    {
        $this->request = $request;
    }

    public function beforeProcess(
        Submit $subject,
        array $params,
        ?AnswerInterface $answer = null
    ) {
        $locationId = $params['X-Location-Id'] ?? null;
        if ($locationId) {
            $this->request->setParam('X-Location-Id', $locationId);
        }

        return null;
    }
}
