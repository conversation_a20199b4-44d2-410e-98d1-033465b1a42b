<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Silksoftwarecorp\ShipToGraphQl\Model\Resolver;

use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlAuthorizationException;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\CustomerGraphQl\Model\Customer\GetCustomer;
use Silksoftwarecorp\CompanyB2b\Api\ErpShipToRepositoryInterface;
use Silksoftwarecorp\CompanyB2b\Model\ErpCompanyShipToFactory;

/**
 * Set default ship-to resolver
 */
class SetDefaultShipTo implements ResolverInterface
{
    protected $shipToRepository;
    protected $shipToFactory;
    protected $companyShipToModel;
    protected $getCustomer;

    public function __construct(
        ErpShipToRepositoryInterface $shipToRepository,
        ErpCompanyShipToFactory $shipToFactory,
        \Silksoftwarecorp\ShipToGraphQl\Model\ResourceModel\CompanyShipTo $companyShipTo,
        GetCustomer $getCustomer
    ) {
        $this->shipToRepository = $shipToRepository;
        $this->shipToFactory = $shipToFactory;
        $this->companyShipToModel = $companyShipTo;
        $this->getCustomer = $getCustomer;
    }

    /**
     * @inheritdoc
     */
    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        /** @phpstan-ignore-next-line */
        if (false === $context->getExtensionAttributes()->getIsCustomer()) {
            throw new GraphQlAuthorizationException(__('The current customer isn\'t authorized.'));
        }
        
        $this->getCustomer->execute($context);

        $entityId = $args['input']['entity_id'];
        
        if (!$entityId || $entityId <= 0) {
            throw new GraphQlInputException(__('Invalid entity ID.'));
        }

        try {
            // Get the ship-to record
            $shipTo = $this->shipToRepository->get($entityId);
            
            if (!$shipTo) {
                throw new GraphQlInputException(__('Ship-to record not found.'));
            }

            $erpCustNum = $shipTo->getErpCustNum();
            
            // First, set all other ship-to records for this erp_cust_num to not default
            $this->companyShipToModel->setAllNonDefaultByErpCustNum($erpCustNum, $entityId);
            
            // Then set this record as default
            $shipTo->setIsDefault(true);
            $this->shipToRepository->save($shipTo);

            return [
                'success' => true,
                'message' => __('Ship-to address has been set as default successfully.')
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => __('Error setting default ship-to: %1', $e->getMessage())
            ];
        }
    }
}
