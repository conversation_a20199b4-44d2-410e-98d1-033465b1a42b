<?php
/**
 * Copyright © Silksoftwarecorp. All rights reserved.
 */

namespace Silksoftwarecorp\Inventory\Observer;

use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Event\Observer;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\InventoryApi\Api\Data\SourceInterface;
use Silksoftwarecorp\Inventory\Api\BranchManagerProfileRepositoryInterface;
use Silksoftwarecorp\Inventory\Model\BranchManagerProfileFactory;

/**
 * Class SaveManagerProfile
 */
class SaveManagerProfile implements ObserverInterface
{
    const TERRITORY_MANAGER_COUNT = 3;

    /**
     * @var BranchManagerProfileRepositoryInterface
     */
    private $managerProfileRepository;

    /**
     * @var BranchManagerProfileFactory
     */
    private $managerProfileFactory;

    /**
     * SaveManagerProfile constructor.
     *
     * @param BranchManagerProfileRepositoryInterface $managerProfileRepository
     * @param BranchManagerProfileFactory $managerProfileFactory
     */
    public function __construct(
        BranchManagerProfileRepositoryInterface $managerProfileRepository,
        BranchManagerProfileFactory $managerProfileFactory
    ) {
        $this->managerProfileRepository = $managerProfileRepository;
        $this->managerProfileFactory = $managerProfileFactory;
    }

        /**
     * Execute observer
     *
     * @param Observer $observer
     * @return void
     * @throws CouldNotSaveException
     */
    public function execute(Observer $observer): void
    {
        $event = $observer->getEvent();
        $request = $event->getRequest();

        /**@var SourceInterface $source*/
        $source = $event->getSource();

        $postData = $request->getPostValue();

        // Get source_code
        $sourceCode = $source->getSourceCode();
        if (!$sourceCode) {
            return;
        }

        // Handle Manager Profile data
        if (isset($postData['manager_profile'])) {
            $managerProfile = $postData['manager_profile'];

            // Handle General Manager
            if (isset($managerProfile['general'])) {
                $general = $managerProfile['general'];
                $this->processManagerProfile($general, $source, true);
            }

            // Handle Territory Managers (territory_manager1, territory_manager2, territory_manager3)
            for ($i = 1; $i <= self::TERRITORY_MANAGER_COUNT; $i++) {
                $territoryKey = 'territory_manager' . $i;
                if (isset($managerProfile[$territoryKey])) {
                    $territoryManager = $managerProfile[$territoryKey];
                    $this->processManagerProfile($territoryManager, $source);
                }
            }
        }
    }

    /**
     * Process manager profile data (create, update, or delete)
     *
     * @param array $profileData
     * @param SourceInterface $source
     * @param bool $isGeneral
     * @return void
     * @throws CouldNotSaveException
     */
    private function processManagerProfile(
        array $profileData,
        SourceInterface $source,
        $isGeneral = false
    ): void {
        if ($this->hasProfileData($profileData)) {
            $profile = $this->managerProfileFactory->create();
            $profileId = $profileData['id'] ?? null;
            if ($profileId) {
                try {
                    $profile = $this->managerProfileRepository->getById((int)$profileId);
                } catch (\Exception $e) { }
            }

            $profile->setSourceCode($source->getSourceCode())
                ->setIsGeneral($isGeneral)
                ->setImage($this->extractImageName($profileData['image'] ?? ''))
                ->setName($profileData['name'] ?? '')
                ->setPhone($profileData['phone'] ?? '')
                ->setRegion($profileData['region'] ?? '');

            $this->managerProfileRepository->save($profile);
        } elseif (!empty($profileData['id'])) {
            // No data but has ID, delete existing record
            try {
                $profile = $this->managerProfileRepository->getById($profileData['id']);
                $this->managerProfileRepository->delete($profile);
            } catch (\Exception $e) {
                // Record doesn't exist, ignore
            }
        }
    }

    private function hasProfileData($profileData): bool
    {
        $keys = ['name', 'phone', 'region'];
        foreach ($keys as $key) {
            if (!empty($profileData[$key])) {
                return true;
            }
        }

        return false;
    }

    /**
     * Extract image name from UI component data
     *
     * @param mixed $imageData
     * @return string
     */
    private function extractImageName($imageData)
    {
        if (is_array($imageData) && isset($imageData[0]['name'])) {
            return $imageData[0]['name'];
        } elseif (is_string($imageData)) {
            return $imageData;
        }
        return '';
    }
}
