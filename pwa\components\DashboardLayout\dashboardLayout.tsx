import CommonAccountPageLayout from '@/components/Common/CommonAccountPageLayout'
import AccountOrderList from '@/components/AccountOrderList'

import DashboardNav from './DashboardNav'
import InformationGroup from './InformationGroup'
import FrequentlyOrderedProducts from './FrequentlyOrderedProducts'
import BackorderedProducts from './BackorderedProducts'
import { StyledDashboardTableWrapper } from './styled'

const DashboardLayout = () => {
  return (
    <CommonAccountPageLayout singleBreadcrumb title="Dashboard">
      <DashboardNav />
      <InformationGroup />
      <AccountOrderList isRecent />
      <StyledDashboardTableWrapper>
        <FrequentlyOrderedProducts isQuickView />
      </StyledDashboardTableWrapper>
      <StyledDashboardTableWrapper>
        <BackorderedProducts isQuickView />
      </StyledDashboardTableWrapper>
    </CommonAccountPageLayout>
  )
}

export default DashboardLayout
