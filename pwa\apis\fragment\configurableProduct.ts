import { gql } from '@apollo/client'
import type { DocumentNode } from '@apollo/client'

import { price_range } from '../fragment/priceRange'

export const configurableProduct: DocumentNode = gql`
  fragment configurableProduct on ConfigurableProduct {
    id
    sku
    name
    url_key
    meta_title
    meta_keyword
    meta_description
    main_image: image {
      label
      url
    }
    price_range {
      ...price_range
      __typename
    }
    short_description {
      html
    }
    stock_status
    categories {
      name
      url_path
    }
  }
  ${price_range}
`
