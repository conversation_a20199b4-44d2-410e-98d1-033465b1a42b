import styled from '@emotion/styled'

export const StyledNav = styled.nav`
  position: relative;
  min-height: 54px;
  border-top: 1px solid #d9d9d9;
  display: flex;
  justify-content: center;
  z-index: 10;

  .nav-container {
    display: inline-grid;
    grid-auto-flow: column;
    justify-content: center;
    align-items: stretch;
  }
`

export const StyledMenuItem = styled.div`
  margin-bottom: -1px;

  & > a,
  .simplae-menu-item {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    padding: 16px;
    font-weight: 700;
    font-size: 15px;
    line-height: 21px;
    color: ${(props: any) => (props.titleColor ? props.titleColor : `var(--color-font)`)};
    height: 100%;

    &:hover {
      color: var(--color-primary);
      text-decoration: none;
    }
  }

  &.has-submenus:hover {
    .submenu,
    .nav-mask {
      display: block;
    }
  }

  &.is-all-products {
    & > a,
    .simplae-menu-item {
      position: relative;

      &::after {
        content: '';
        position: absolute;
        top: 50%;
        right: 0;
        transform: translateY(-50%);
        display: block;
        width: 1px;
        height: 24px;
        background: #c0bcbc;
      }
    }
  }

  .nav-mask {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 29;
    width: 100%;
    height: calc(100vh - 200px);
    background: #191919;
    opacity: 0.5;
  }

  .submenu {
    display: none;
    position: absolute;
    top: 100%;
    z-index: 30;
    left: calc((100% - 1180px) / 2);
    width: 277px;
    height: calc(100vh - 300px);
    background-color: var(--color-white);
    border-top: 1px solid #d9d9d9;

    .submenu-item {
      .submenu-item-title {
        min-height: 41px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 15px;
        font-weight: 700;
        font-size: 15px;
        line-height: 23px;
        color: var(--color-font);
        transition: initial;

        &.is-new {
          color: var(--color-primary);
        }

        .menu-arrow-light {
          display: none;
        }
      }

      &:hover {
        .submenu-item-title {
          color: var(--color-primary);
        }
      }

      &.has-child-list:hover {
        background: var(--color-primary);

        .submenu-item-title {
          & > span {
            color: var(--color-white) !important;
          }

          .menu-arrow {
            display: none;
          }

          .menu-arrow-light {
            display: block;
          }
        }

        .submenu-children {
          display: block;
        }
      }
    }
  }

  .submenu-children {
    display: none;
    position: absolute;
    top: -1px;
    left: 100%;
    z-index: 30;
    padding: 16px;
    width: 277px;
    height: calc(100vh - 300px);
    background: #fff;
    border-top: 1px solid #d9d9d9;
    border-left: 1px solid #d9d9d9;

    .submenu-children-item {
      & > a {
        font-weight: 400;
        font-size: 15px;
        line-height: 35px;
        color: var(--color-font);

        &:hover {
          color: var(--color-primary);
        }
      }
    }
  }
`
