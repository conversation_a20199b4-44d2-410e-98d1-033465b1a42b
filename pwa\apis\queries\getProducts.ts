import { gql } from '@apollo/client'
import type { DocumentNode } from '@apollo/client'

import { price_range } from '../fragment/priceRange'

export const GET_PRODUCTS: DocumentNode = gql`
  query getProducts(
    $search: String
    $filter: ProductAttributeFilterInput
    $pageSize: Int
    $currentPage: Int
    $sort: ProductAttributeSortInput
  ) {
    products(
      search: $search
      pageSize: $pageSize
      currentPage: $currentPage
      filter: $filter
      sort: $sort
    ) {
      items {
        id
        name
        sku
        url_key
        only_x_left_in_stock
        image {
          label
          url
        }
        short_description {
          html
        }
        price_range {
          ...price_range
          __typename
        }
        stock_status
        __typename
      }
      total_count
    }
  }
  ${price_range}
`
