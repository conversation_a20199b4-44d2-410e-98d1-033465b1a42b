import CommonAccountPageLayout from '@/components/Common/CommonAccountPageLayout'
import CompanyUsersPage from '@/components/Account/CompanyUsersLayout/CompanyUsersPage'
import { useAppMediaQuery } from '@/packages/hooks'

const CompanyUsersLayout = () => {
  const { isMobile } = useAppMediaQuery()

  return (
    <CommonAccountPageLayout title="Company Users" titleVisible={!isMobile}>
      <CompanyUsersPage />
    </CommonAccountPageLayout>
  )
}

export default CompanyUsersLayout
