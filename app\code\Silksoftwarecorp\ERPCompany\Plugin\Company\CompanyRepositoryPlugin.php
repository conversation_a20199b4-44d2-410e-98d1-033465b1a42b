<?php

namespace Silksoftwarecorp\ERPCompany\Plugin\Company;

use Magento\Company\Api\CompanyRepositoryInterface;
use Magento\Company\Api\Data\CompanyExtensionFactory;
use Magento\Company\Api\Data\CompanyInterface;
use Silksoftwarecorp\ERPCompany\Model\Company\ERPRepository;

class CompanyRepositoryPlugin
{
    /**
     * @var ERPRepository
     */
    private $erpRepository;

    /**
     * @var CompanyExtensionFactory
     */
    private $companyExtensionFactory;
    /**
     * @param ERPRepository $erpRepository
     * @param CompanyExtensionFactory $companyExtensionFactory
     */
    public function __construct(
        ERPRepository $erpRepository,
        CompanyExtensionFactory $companyExtensionFactory
    ) {
        $this->erpRepository = $erpRepository;
        $this->companyExtensionFactory = $companyExtensionFactory;
    }

    /**
     * After get company.
     *
     * @param CompanyRepositoryInterface $subject
     * @param CompanyInterface $company
     * @return CompanyInterface
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function afterGet(
        CompanyRepositoryInterface $subject,
        CompanyInterface $company
    ) {
        $this->getErpCompanyDataForCompany($company);
        return $company;
    }

    /**
     * After save company.
     *
     * @param CompanyRepositoryInterface $subject
     * @param CompanyInterface $company
     * @return CompanyInterface
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function afterSave(
        CompanyRepositoryInterface $subject,
        CompanyInterface $company
    ) {
        $this->getErpCompanyDataForCompany($company);
        return $company;
    }

    /**
     * Get available and applicable payment methods for the company
     *
     * @param CompanyInterface $company
     */
    private function getErpCompanyDataForCompany(CompanyInterface $company): void
    {
        $erpCompany = $this->erpRepository->get((int)$company->getId());

        if ($erpCompany?->getId()) {
            $companyExtension = $company->getExtensionAttributes();
            if ($companyExtension === null) {
                $companyExtension = $this->companyExtensionFactory->create();
            }

            $companyExtension->setErpCustomerId($erpCompany->getErpCustomerId());
            $companyExtension->setErpSalesRepId($erpCompany->getErpSalesRepId());
            $companyExtension->setErpSalesRepName($erpCompany->getErpSalesRepName());
            $companyExtension->setErpCreditStatus($erpCompany->getErpCreditStatus());
            $company->setExtensionAttributes($companyExtension);
        }
    }
}
