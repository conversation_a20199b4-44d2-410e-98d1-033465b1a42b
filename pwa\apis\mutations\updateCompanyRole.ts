import { gql } from '@apollo/client'

export const B2B_UPDATE_COMPANY_ROLE = gql`
  mutation updateCompanyRole($input: CompanyRoleUpdateInput!) {
    companyRole: updateCompanyRole(input: $input) {
      role {
        id
        name
        permissions {
          key: id
          title: text
          children {
            key: id
            title: text
            children {
              key: id
              title: text
              children {
                key: id
                title: text
              }
            }
          }
        }
        users_count
      }
    }
  }
`
