import { memo } from 'react'
import { Input, Select, DatePicker } from 'antd'
import dayjs from 'dayjs'
import { clsx } from 'clsx'

import { MediaLayout } from '@/ui'
import { useAccountOrderList } from '@/hooks/AccountOrderList'
import CommonTable from '@/components/Common/CommonTable'
import BasePrice from '@/components/BasePrice'
import CommonLink from '@/components/Common/CommonLink'
import CommonTableSort from '@/components/Common/CommonTableSort'
import CommonLoading from '@/components/Common/CommonLoading'
import TableHeaderSearchItem from '@/components/Common/TableHeaderSearchItem'
import TableHeader from '@/components/DashboardLayout/TableHeader'
import { orderStatusOptions } from '@/utils/constant'

import { StyledAccountOrderList, StyledOrderStatus } from './styled'

// const OrderModal = dynamic(() => import('./OrderModal'), {
//   ssr: false
// })

const AccountOrderList = ({ isRecent }: any) => {
  const {
    isLoading,
    orders,
    onToDateChange,
    onFromDateChange,
    suffix,
    handlePageChange,
    handlePageSizeChange,
    page,
    isDataReady,
    onFilterInputChange,
    onSortChange,
    sort,
    dateValues,
    onStatusChange
  } = useAccountOrderList(isRecent)

  const columns = [
    {
      title: (
        <TableHeaderSearchItem>
          <p
            aria-hidden
            onClick={() => {
              onSortChange('order_number')
            }}>
            Order Number
            <CommonTableSort sortOrder={sort.code === 'order_number' ? sort.direction : ''} />
          </p>
          <div className="item-search">
            <Input
              allowClear
              onChange={(e) => {
                onFilterInputChange(e, 'order_number')
              }}
            />
          </div>
        </TableHeaderSearchItem>
      ),
      dataIndex: 'order_number',
      width: 185,
      render: (param) => {
        return (
          <CommonLink underline href={`/account/orders/${param}${suffix}`}>
            {param}
          </CommonLink>
        )
      }
    },
    {
      title: (
        <TableHeaderSearchItem>
          <p
            aria-hidden
            onClick={() => {
              onSortChange('order_date')
            }}>
            Date
            <CommonTableSort sortOrder={sort.code === 'order_date' ? sort.direction : ''} />
          </p>
          <div className="item-search">
            <DatePicker
              className="date-from"
              placeholder="From"
              suffixIcon={null}
              format="MM/DD/YYYY"
              showNow={false}
              value={dateValues.from}
              onChange={onFromDateChange}
            />
            <DatePicker
              allowClear
              placeholder="To"
              suffixIcon={null}
              format="MM/DD/YYYY"
              showNow={false}
              value={dateValues.to}
              onChange={onToDateChange}
            />
          </div>
        </TableHeaderSearchItem>
      ),
      dataIndex: 'order_date',
      width: 185,
      render: (param) => {
        return <div>{dayjs(param).format('MM/DD/YYYY')}</div>
      }
    },
    {
      title: (
        <TableHeaderSearchItem>
          <p
            aria-hidden
            onClick={() => {
              onSortChange('po_number')
            }}>
            PO Number
            <CommonTableSort sortOrder={sort.code === 'po_number' ? sort.direction : ''} />
          </p>
          <div className="item-search">
            <Input
              allowClear
              onChange={(e) => {
                onFilterInputChange(e, 'po_number')
              }}
            />
          </div>
        </TableHeaderSearchItem>
      ),
      dataIndex: 'po_number',
      width: 185
    },
    {
      title: (
        <TableHeaderSearchItem>
          <p
            aria-hidden
            onClick={() => {
              onSortChange('ship_to')
            }}>
            <MediaLayout>Ship To Name & ID</MediaLayout>
            <MediaLayout type="mobile">Ship To</MediaLayout>
            <CommonTableSort sortOrder={sort.code === 'ship_to' ? sort.direction : ''} />
          </p>
          <div className="item-search">
            <Input
              allowClear
              onChange={(e) => {
                onFilterInputChange(e, 'ship_to')
              }}
            />
          </div>
        </TableHeaderSearchItem>
      ),
      dataIndex: 'order_number',
      render: (param, record) => {
        return <span>{record?.ship_to ?? ''}</span>
      }
    },
    {
      title: (
        <TableHeaderSearchItem>
          <p
            aria-hidden
            onClick={() => {
              onSortChange('total_amount')
            }}>
            Total
            <CommonTableSort sortOrder={sort.code === 'total_amount' ? sort.direction : ''} />
          </p>
          <div className="item-search">
            <Input
              allowClear
              onChange={(e) => {
                onFilterInputChange(e, 'total_amount')
              }}
            />
          </div>
        </TableHeaderSearchItem>
      ),
      dataIndex: 'total_amount',
      width: 185,
      render: (param) => {
        return param ? <BasePrice value={param} /> : ''
      }
    },
    {
      title: (
        <TableHeaderSearchItem>
          <p>Status</p>
          <div className="item-search">
            <Select
              placeholder="Select"
              onChange={onStatusChange}
              allowClear
              suffixIcon={
                <span>
                  <svg
                    width="1em"
                    height="1em"
                    fill="currentColor"
                    aria-hidden="true"
                    focusable="false">
                    <use xlinkHref="#icon-select-suffix" />
                  </svg>
                </span>
              }>
              {orderStatusOptions.map(({ label, value }) => (
                <Select.Option key={value} value={value}>
                  {label}
                </Select.Option>
              ))}
            </Select>
          </div>
        </TableHeaderSearchItem>
      ),
      dataIndex: 'status',
      width: 185,
      render: (param) => {
        const statusItem = orderStatusOptions.find((item) => item.value === param)
        return (
          <StyledOrderStatus>
            <span style={{ backgroundColor: statusItem?.bg ?? '', color: statusItem?.color ?? '' }}>
              {statusItem?.label ?? ''}
            </span>
          </StyledOrderStatus>
        )
      }
    }
  ]

  return (
    <>
      <CommonLoading spinning={isLoading}>
        {isDataReady && (
          <StyledAccountOrderList className={clsx({ 'action-inactive': isRecent })}>
            {isRecent && <TableHeader title="Recent Orders" viewAllUrl="/account/orders" />}
            <CommonTable
              rowKey={(params, index) => `${params.order_number}_${index}`}
              columns={columns}
              dataSource={orders.items}
              total={isRecent ? 0 : orders.total} // isRecent: hide pagination
              totalCountVisible
              current={page.currentPage}
              pageSize={page.pageSize}
              totalTextLabel="Orders"
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
            />
          </StyledAccountOrderList>
        )}
      </CommonLoading>
      {/*{orderModal.open && (
        <OrderModal
          orderModal={orderModal}
          open={orderModal.open}
          onCancel={handleCancelOrderModal}
        />
      )}*/}
    </>
  )
}

export default memo(AccountOrderList)
