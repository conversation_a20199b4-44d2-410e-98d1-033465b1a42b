<?php

namespace Silksoftwarecorp\UnifiedAR\Model\Invoice;

use Magento\Framework\Exception\LocalizedException;
use Silksoftwarecorp\UnifiedAR\Model\Data\AbstractMapper;

/**
 * Enhanced Invoice Data Mapper
 *
 * Provides flexible and configurable invoice data mapping
 */
class EnhancedMapper extends AbstractMapper
{
    /**
     * Get default mapping configuration
     *
     * @return array
     */
    protected function getDefaultMappingConfig(): array
    {
        return [
            'invoice_number' => [
                'source' => 'invoiceNumber',
                'transformer' => 'string',
                'default' => null
            ],
            'order_number' => [
                'source' => 'orderNumber',
                'transformer' => 'string',
                'default' => null
            ],
            'order_date' => [
                'source' => 'orderDate',
                'transformer' => 'date',
                'default' => null
            ],
            'total_amount' => [
                'source' => 'totalAmount',
                'transformer' => 'float',
                'default' => 0.0
            ],
            'po_number' => [
                'source' => 'customerPoNumber',
                'transformer' => 'string',
                'default' => null
            ],
            'paid_in_full' => [
                'source' => 'paidInFull',
                'transformer' => 'bool',
                'default' => false
            ],
            'pdf_url' => [
                'source' => 'pdfUrl',
                'transformer' => 'url',
                'default' => null
            ],
            'html_url' => [
                'source' => 'htmlUrl',
                'transformer' => 'url',
                'default' => null
            ],
            'due_date' => [
                'source' => 'dueDate',
                'transformer' => 'date',
                'default' => null
            ],
            'invoice_date' => [
                'source' => 'invoiceDate',
                'transformer' => 'date',
                'default' => null
            ],
            'balance_due' => [
                'source' => 'balanceDue',
                'transformer' => 'float',
                'default' => 0.0
            ],
            'currency_code' => [
                'source' => 'currencyCode',
                'transformer' => 'string',
                'default' => 'USD'
            ],
            'status' => [
                'source' => 'status',
                'transformer' => 'invoice_status',
                'default' => 'unknown'
            ],
            'customer_number' => [
                'source' => 'customerNumber',
                'transformer' => 'string',
                'default' => null
            ],
            'terms' => [
                'source' => 'paymentTerms',
                'transformer' => 'string',
                'default' => null
            ]
        ];
    }

    /**
     * Get default validation rules
     *
     * @return array
     */
    protected function getDefaultValidationRules(): array
    {
        return [
            'invoiceNumber' => [
                'required' => true,
                'type' => 'string'
            ],
            'totalAmount' => [
                'required' => false,
                'type' => 'float'
            ],
            'orderNumber' => [
                'required' => false,
                'type' => 'string'
            ]
        ];
    }

    /**
     * Get default transformers
     *
     * @return array
     */
    protected function getDefaultTransformers(): array
    {
        $transformers = parent::getDefaultTransformers();

        // Add invoice-specific transformers
        $transformers['invoice_status'] = function ($value) {
            $statusMap = [
                'paid' => 'paid',
                'unpaid' => 'unpaid',
                'overdue' => 'overdue',
                'partial' => 'partially_paid',
                'disputed' => 'disputed',
                'cancelled' => 'cancelled'
            ];

            $normalizedValue = strtolower(trim($value ?? ''));
            return $statusMap[$normalizedValue] ?? 'unknown';
        };

        $transformers['amount_with_currency'] = function ($value, $data, $options) {
            $amount = (float)($value ?? 0);
            $currency = $data['currencyCode'] ?? $options['default_currency'] ?? 'USD';

            return [
                'amount' => $amount,
                'currency' => $currency,
                'formatted' => sprintf('%.2f %s', $amount, $currency)
            ];
        };

        $transformers['payment_status'] = function ($value, $data) {
            $totalAmount = (float)($data['totalAmount'] ?? 0);
            $balanceDue = (float)($data['balanceDue'] ?? 0);
            $paidInFull = (bool)($data['paidInFull'] ?? false);

            if ($paidInFull || $balanceDue <= 0) {
                return 'paid';
            } elseif ($balanceDue < $totalAmount) {
                return 'partially_paid';
            } else {
                $dueDate = $data['dueDate'] ?? null;
                if ($dueDate && strtotime($dueDate) < time()) {
                    return 'overdue';
                }
                return 'unpaid';
            }
        };

        return $transformers;
    }

    /**
     * Post-process mapped data
     *
     * @param array $result Mapped data
     * @param array $originalData Original source data
     * @param array $options Mapping options
     * @return array
     */
    protected function postProcess(array $result, array $originalData, array $options = []): array
    {
        // Calculate payment status if not explicitly set
        if (!isset($result['payment_status'])) {
            $result['payment_status'] = $this->transformers['payment_status'](null, $originalData);
        }

        // Add computed fields
        if (isset($options['include_computed']) && $options['include_computed']) {
            $result = $this->addComputedFields($result, $originalData, $options);
        }

        // Format amounts if requested
        if (isset($options['format_amounts']) && $options['format_amounts']) {
            $result = $this->formatAmounts($result, $options);
        }

        return $result;
    }

    /**
     * Add computed fields
     *
     * @param array $result Mapped data
     * @param array $originalData Original source data
     * @param array $options Mapping options
     * @return array
     */
    protected function addComputedFields(array $result, array $originalData, array $options = []): array
    {
        // Days overdue
        if ($result['due_date'] && $result['payment_status'] === 'overdue') {
            $dueDate = strtotime($result['due_date']);
            $today = time();
            $result['days_overdue'] = max(0, ceil(($today - $dueDate) / 86400));
        } else {
            $result['days_overdue'] = 0;
        }

        // Age in days
        if ($result['invoice_date']) {
            $invoiceDate = strtotime($result['invoice_date']);
            $today = time();
            $result['age_days'] = max(0, ceil(($today - $invoiceDate) / 86400));
        } else {
            $result['age_days'] = 0;
        }

        // Payment percentage
        if ($result['total_amount'] > 0) {
            $paidAmount = $result['total_amount'] - $result['balance_due'];
            $result['payment_percentage'] = round(($paidAmount / $result['total_amount']) * 100, 2);
        } else {
            $result['payment_percentage'] = 0;
        }

        return $result;
    }

    /**
     * Format amounts with currency
     *
     * @param array $result Mapped data
     * @param array $options Formatting options
     * @return array
     */
    protected function formatAmounts(array $result, array $options = []): array
    {
        $currency = $result['currency_code'] ?? $options['default_currency'] ?? 'USD';
        $locale = $options['locale'] ?? 'en_US';

        $amountFields = ['total_amount', 'balance_due'];

        foreach ($amountFields as $field) {
            if (isset($result[$field])) {
                $result[$field . '_formatted'] = $this->formatCurrency($result[$field], $currency, $locale);
            }
        }

        return $result;
    }

    /**
     * Format currency amount
     *
     * @param float $amount
     * @param string $currency
     * @param string $locale
     * @return string
     */
    protected function formatCurrency(float $amount, string $currency, string $locale): string
    {
        // Simple formatting - in real implementation, use NumberFormatter
        $symbols = [
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'JPY' => '¥'
        ];

        $symbol = $symbols[$currency] ?? $currency . ' ';
        return $symbol . number_format($amount, 2);
    }

    /**
     * Legacy execute method for backward compatibility
     *
     * @param array $invoice
     * @return array
     * @throws LocalizedException
     */
    public function execute(array $invoice): array
    {
        return $this->map($invoice);
    }
}
