<?php

namespace Silksoftwarecorp\UnifiedArPaymentGraphQl\Model\Resolver;

use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;

class OrderSurchargeAmount implements ResolverInterface
{
    /**
     * @inheritdoc
     */
    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
        if (!isset($value['model'])) {
            return 0.0;
        }

        $order = $value['model'];
        $surchargeAmount = $order->getData('unified_ar_surcharge_amount');
        
        return $surchargeAmount ? (float)$surchargeAmount : 0.0;
    }
} 