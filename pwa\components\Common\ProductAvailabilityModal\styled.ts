import styled from '@emotion/styled'

export const StyledRemovalModal = styled.div`
  margin: 0 auto;
  padding: 35px;
  max-width: 577px;
  text-align: center;

  svg {
    margin-bottom: 10px;
  }

  div {
    font-weight: 400;
    font-size: 15px;
    line-height: 21px;
    letter-spacing: 0.01em;

    b {
      display: block;
      font-weight: 700;
    }
  }

  .removal-action {
    display: flex;
    justify-content: center;
    margin-top: 24px;

    button {
      width: 147px;
      height: 49px;
      border-radius: 3px;

      span {
        font-weight: 700;
        font-size: 16px;
        line-height: 21px;
        letter-spacing: 0.03em;
        border-radius: 3px;
      }

      &.cancel-btn {
        margin-left: 15px;
        border: 1px solid #666565 !important;

        span {
          color: #666565;
        }
      }
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    padding: 35px 0;
  }
`
