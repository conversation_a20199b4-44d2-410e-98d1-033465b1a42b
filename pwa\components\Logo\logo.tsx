import Image from 'next/image'
import { useMemo, memo } from 'react'
import { useSelector } from 'react-redux'

import { useAppMediaQuery } from '@/packages/hooks'

import { StyledLogo } from './styled'

const Logo = () => {
  const { isMobile } = useAppMediaQuery()
  const storeConfig = useSelector((state: Store) => state.app.storeConfig)
  const src = `${storeConfig.media_url}logo/${storeConfig.logo_src}`
  const alt = storeConfig?.logo_alt ?? ''

  const [width, height] = useMemo(() => {
    let widthValue = 164
    let heightValue = 32
    if (!isMobile) {
      widthValue = storeConfig?.logo_width ?? 264
      heightValue = storeConfig?.logo_height ?? 54
    }
    return [widthValue, heightValue]
  }, [isMobile, storeConfig])

  return (
    <StyledLogo href="/">
      <Image src={src} alt={alt} width={width} height={height} />
    </StyledLogo>
  )
}

export default memo(Logo)
