import styled from '@emotion/styled'

export const StyledAddressTable = styled.div`
  height: 500px;
  padding: 24px 40px;
  overflow-y: auto;

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    padding: 16px;
  }
`

export const StyledAddressItem = styled.div`
  display: flex;
  margin-bottom: 16px;
  padding-bottom: 16px;
  justify-content: space-between;
  align-items: center;

  &:not(:last-child) {
    border-bottom: 1px solid #d9d9d9;
  }

  .address {
    font-weight: 400;
    font-size: 15px;
    line-height: 23px;
    letter-spacing: 0.01em;

    .address-company {
      font-weight: 700;
    }

    .address-telephone {
      color: var(--color-font) !important;
    }
  }

  .selected {
    font-weight: 700;
    font-size: 15px;
    line-height: 21px;
    letter-spacing: 0.01em;
  }

  .${({ theme }) => theme.namespace} {
    &-btn {
      padding: 0;
      font-weight: 700;
      font-size: 15px;
      line-height: 21px;
      letter-spacing: 0.01em;
      color: var(--color-primary) !important;
      span {
        white-space: nowrap;
      }
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    display: grid;
    grid-template-columns: 1fr auto;
    grid-column-gap: 16px;
    align-items: center;
  }
`
export const StyledRemovalModal = styled.div`
  margin: 0 auto;
  padding: 35px;
  max-width: 577px;
  text-align: center;

  svg {
    margin-bottom: 10px;
  }

  div {
    font-weight: 400;
    font-size: 15px;
    line-height: 21px;
    letter-spacing: 0.01em;

    b {
      display: block;
      font-weight: 700;
    }
  }

  .removal-action {
    display: flex;
    justify-content: center;
    margin-top: 24px;

    button {
      width: 147px;
      height: 49px;
      border-radius: 3px;

      span {
        font-weight: 700;
        font-size: 16px;
        line-height: 21px;
        letter-spacing: 0.03em;
        border-radius: 3px;
      }

      &.cancel-btn {
        margin-left: 15px;
        border: 1px solid #666565 !important;

        span {
          color: #666565;
        }
      }
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    padding: 35px 0;
  }
`
