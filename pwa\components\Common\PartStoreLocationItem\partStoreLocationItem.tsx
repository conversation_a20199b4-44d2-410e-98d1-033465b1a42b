import Link from 'next/link'
import { memo } from 'react'
import { clsx } from 'clsx'

import { StyledPartStoreLocationItem } from './styled'

const PartStoreLocationItem = ({ location, directionsVisible = false }: any) => {
  const phone = location?.phone ?? ''
  const email = location?.email ?? ''
  const isBuyOnline =
    location?.attributes?.find((atr) => atr?.attribute_code === 'buy_online')?.value === '1'
  const distance = location?.distance?.toFixed(2) ?? 0

  return (
    <StyledPartStoreLocationItem className="location-item">
      {isBuyOnline && (
        <Link
          className="buy-online-icon"
          href={location?.website || '/parts-store-finder'}
          target="_blank">
          <svg width="32px" height="33px" fill="currentColor" aria-hidden="true" focusable="false">
            <use xlinkHref="#icon-buy-online" />
          </svg>
        </Link>
      )}
      <div className={clsx('item-title', { 'is-buy-online': isBuyOnline })}>
        {location?.name ?? ''}
        {distance && <span>({distance}mi)</span>}
      </div>
      <div>
        <span>Address:</span>
        {location?.address ?? ''}
      </div>
      <div>
        <span>City:</span>
        {location?.city ?? ''}
      </div>
      <div>
        <span>State:</span>
        {location?.state ?? ''}
      </div>
      <div>
        <span>Zip Code:</span>
        {location?.zip ?? ''}
      </div>
      <div>
        <span>Phone:</span>
        <Link href={`tel:${phone}`}>{phone}</Link>
      </div>
      <div>
        <span>Email:</span>
        <Link href={`mailto:${email}`}>{email}</Link>
      </div>
      {directionsVisible && (
        <Link
          className="directions-btn"
          href={`https://www.google.com/maps?q=${location?.lat},${location?.lng}&z=15`}
          title={location?.name ?? ''}
          target="_blank">
          Directions
        </Link>
      )}
    </StyledPartStoreLocationItem>
  )
}

export default memo(PartStoreLocationItem)
