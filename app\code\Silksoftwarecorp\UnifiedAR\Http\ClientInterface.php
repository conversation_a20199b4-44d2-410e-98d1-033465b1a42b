<?php

namespace Silksoftwarecorp\UnifiedAR\Http;

interface ClientInterface
{
    function send($method, $url, $options): Response;

    public function get(string $url, array $queryParams = []): Response;

    public function post(string $url, array $params = []): Response;

    function patch($url, $params = []): Response;

    function put($url, $params = []): Response;

    function delete($url, $params = []): Response;

    public function withBaseUri(string $baseUri): static;

    public function withHeaders(array $headers): static;

    public function timeout(int $seconds = 10): static;

    public function withRetry(): static;

    public function withLog(): static;
}
