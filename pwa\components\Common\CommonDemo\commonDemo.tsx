import { memo, useState } from 'react'

import CommonButton from '@/components/Common/CommonButton'
import CommonLink from '@/components/Common/CommonLink'
import CommonPagination from '@/components/Common/CommonPagination'
import CommonPageSize from '@/components/Common/CommonPageSize'
import CommonCheckbox from '@/components/Common/CommonCheckbox'

const CommonDemo = () => {
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [checked, setChecked] = useState(false)

  return (
    <div>
      <CommonCheckbox
        checked={checked}
        onChange={(isChecked) => {
          setChecked(isChecked)
        }}
      />
      <div>pageSize: {pageSize}</div>
      <CommonPageSize
        pageSize={pageSize}
        onChange={(val) => {
          setPageSize(val)
        }}
      />
      <CommonPagination
        current={currentPage}
        total={112}
        onChange={(page, size) => {
          console.info('size: ', size)
          setCurrentPage(page)
        }}
      />
      <br />
      <br />
      <br />
      <div>
        <CommonButton>Save</CommonButton>
        <CommonButton>ADD TO CART</CommonButton>
        <CommonButton dark height={44} ph={12}>
          ADD TO CART
        </CommonButton>
        <CommonButton type="text" uppercase={false} underline>
          Add To Cart
        </CommonButton>
        <CommonButton type="link">ADD TO CART</CommonButton>
      </div>
      <hr />
      <div>
        <CommonButton disabled>Save</CommonButton>
        <CommonButton disabled>ADD TO CART</CommonButton>
        <CommonButton disabled dark height={44} ph={12}>
          ADD TO CART
        </CommonButton>
        <CommonButton disabled type="text" uppercase={false} underline>
          Add To Cart
        </CommonButton>
        <CommonButton disabled type="link">
          ADD TO CART
        </CommonButton>
      </div>
      <div>
        <CommonLink href="/" fontSize={24}>
          HOME
        </CommonLink>
        <CommonLink href="/" dark>
          Home
        </CommonLink>
        <CommonLink href="/" underline>
          HOME
        </CommonLink>
      </div>
      <div>
        <CommonLink href="/" type="button">
          Button Link
        </CommonLink>
      </div>
    </div>
  )
}

export default memo(CommonDemo)
