type Query {
    getProductOnlineInfo(location_id: String!, sku: [String!]!): ProductOnlineInfoOutput @doc(description: "Get product online information including inventory and ordering status") @resolver(class: "\\Silksoftwarecorp\\CatalogGraphQl\\Model\\Resolver\\GetProductOnlineInfo")
}

type ProductOnlineInfoOutput @doc(description: "Product online information output") {
    items: [ProductOnlineInfoItem!]! @doc(description: "List of product online information items")
}

type ProductOnlineInfoItem @doc(description: "Product online information item") {
    sku: String! @doc(description: "Product SKU")
    qty: Float! @doc(description: "Product quantity")
    allow_to_order: Boolean! @doc(description: "Whether the product can be ordered")
    allow_to_backorder: Boolean! @doc(description: "Whether the product can be backordered")
}

interface ProductInterface {
    pdp_ble_specifications: ComplexTextValue @doc(description: "specifications about the product. The value can include simple HTML tags.") @resolver(class: "\\Silksoftwarecorp\\CatalogGraphQl\\Model\\Resolver\\Product\\AttributeSpecifications")
    pdp_ble_downloads: ComplexTextValue @doc(description: "downloads about the product. The value can include simple HTML tags.") @resolver(class: "\\Silksoftwarecorp\\CatalogGraphQl\\Model\\Resolver\\Product\\AttributeDownloads")
    pdp_ble_warranty_information: ComplexTextValue @doc(description: "warranty_information about the product. The value can include simple HTML tags.") @resolver(class: "\\Silksoftwarecorp\\CatalogGraphQl\\Model\\Resolver\\Product\\AttributeWarrantyInformation")
    pdp_ble_product_video: ComplexTextValue @doc(description: "product_video content about the product. The value can include simple HTML tags.") @resolver(class: "\\Silksoftwarecorp\\CatalogGraphQl\\Model\\Resolver\\Product\\AttributeProductVideo")
    how_to_measure: String @doc(description: "How to measure field inherited from category") @resolver(class: "\\Silksoftwarecorp\\CatalogGraphQl\\Model\\Resolver\\Product\\HowToMeasure")
}

interface CategoryInterface {
    how_to_measure: String @doc(description: "How to Measure category attribute")
}
