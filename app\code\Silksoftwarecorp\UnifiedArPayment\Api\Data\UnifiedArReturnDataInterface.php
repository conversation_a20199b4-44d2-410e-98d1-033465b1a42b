<?php
namespace Silksoftwarecorp\UnifiedArPayment\Api\Data;

interface UnifiedArReturnDataInterface
{
    /**
     * Get transaction ID
     *
     * @return string|null
     */
    public function getElementTransactionId();

    /**
     * Set transaction ID
     *
     * @param string $transactionId
     * @return $this
     */
    public function setElementTransactionId($transactionId);

    /**
     * Get AVS response code
     *
     * @return string|null
     */
    public function getAvsResponseCode();

    /**
     * Set AVS response code
     *
     * @param string $avsResponseCode
     * @return $this
     */
    public function setAvsResponseCode($avsResponseCode);

    /**
     * Get card number (masked)
     *
     * @return string|null
     */
    public function getCardNumber();

    /**
     * Set card number
     *
     * @param string $cardNumber
     * @return $this
     */
    public function setCardNumber($cardNumber);

    /**
     * Get authorization code
     *
     * @return string|null
     */
    public function getAuthorizationCode();

    /**
     * Set authorization code
     *
     * @param string $authorizationCode
     * @return $this
     */
    public function setAuthorizationCode($authorizationCode);

    /**
     * Get card type
     *
     * @return string|null
     */
    public function getCardType();

    /**
     * Set card type
     *
     * @param string $cardType
     * @return $this
     */
    public function setCardType($cardType);

    /**
     * Get payment account ID
     *
     * @return string|null
     */
    public function getElementPaymentAccountId();

    /**
     * Set payment account ID
     *
     * @param string $paymentAccountId
     * @return $this
     */
    public function setElementPaymentAccountId($paymentAccountId);

    /**
     * Get expiration month
     *
     * @return string|null
     */
    public function getExpirationMonth();

    /**
     * Set expiration month
     *
     * @param string $expirationMonth
     * @return $this
     */
    public function setExpirationMonth($expirationMonth);

    /**
     * Get expiration year
     *
     * @return string|null
     */
    public function getExpirationYear();

    /**
     * Set expiration year
     *
     * @param string $expirationYear
     * @return $this
     */
    public function setExpirationYear($expirationYear);

    /**
     * Get first name
     *
     * @return string|null
     */
    public function getFirstName();

    /**
     * Set first name
     *
     * @param string $firstName
     * @return $this
     */
    public function setFirstName($firstName);

    /**
     * Get last name
     *
     * @return string|null
     */
    public function getLastName();

    /**
     * Set last name
     *
     * @param string $lastName
     * @return $this
     */
    public function setLastName($lastName);

    /**
     * Get company name
     *
     * @return string|null
     */
    public function getCompanyName();

    /**
     * Set company name
     *
     * @param string $companyName
     * @return $this
     */
    public function setCompanyName($companyName);

    /**
     * Get address line 1
     *
     * @return string|null
     */
    public function getAddress1();

    /**
     * Set address line 1
     *
     * @param string $address1
     * @return $this
     */
    public function setAddress1($address1);

    /**
     * Get address line 2
     *
     * @return string|null
     */
    public function getAddress2();

    /**
     * Set address line 2
     *
     * @param string $address2
     * @return $this
     */
    public function setAddress2($address2);

    /**
     * Get city
     *
     * @return string|null
     */
    public function getCity();

    /**
     * Set city
     *
     * @param string $city
     * @return $this
     */
    public function setCity($city);

    /**
     * Get state
     *
     * @return string|null
     */
    public function getState();

    /**
     * Set state
     *
     * @param string $state
     * @return $this
     */
    public function setState($state);

    /**
     * Get zip code
     *
     * @return string|null
     */
    public function getZip();

    /**
     * Set zip code
     *
     * @param string $zip
     * @return $this
     */
    public function setZip($zip);

    /**
     * Get country
     *
     * @return string|null
     */
    public function getCountry();

    /**
     * Set country
     *
     * @param string $country
     * @return $this
     */
    public function setCountry($country);

    /**
     * Get charge amount
     *
     * @return float|null
     */
    public function getChargeAmount();

    /**
     * Set charge amount
     *
     * @param float $chargeAmount
     * @return $this
     */
    public function setChargeAmount($chargeAmount);

    /**
     * Get Transaction Id
     *
     * @return string|null
     */
    public function getTransactionId();

    /**
     * Set Transaction ID
     *
     * @param string $transactionId
     * @return $this
     */
    public function setTransactionId($transactionId);

    /**
     * Get Approval Number
     *
     * @return string|null
     */
    public function getApprovalNumber();

    /**
     * Set Approval Number
     *
     * @param string $approvalNumber
     * @return $this
     */
    public function setApprovalNumber($approvalNumber);

    /**
     * Get Reference Number
     *
     * @return string|null
     */
    public function getReferenceNumber();

    /**
     * Set Reference Number
     *
     * @param string $referenceNumber
     * @return $this
     */
    public function setReferenceNumber($referenceNumber);

    /**
     * Get Acquirer Data
     *
     * @return string|null
     */
    public function getAcquirerData();

    /**
     * Set Acquirer Data
     *
     * @param string $acquirerData
     * @return $this
     */
    public function setAcquirerData($acquirerData);

    /**
     * Get Processor Name
     *
     * @return string|null
     */
    public function getProcessorName();

    /**
     * Set Processor Name
     *
     * @param string $processorName
     * @return $this
     */
    public function setProcessorName($processorName);

    /**
     * Get Transaction Status
     *
     * @return string|null
     */
    public function getTransactionStatus();

    /**
     * Set Transaction Status
     *
     * @param string $transactionStatus
     * @return $this
     */
    public function setTransactionStatus($transactionStatus);

    /**
     * Get Transaction Status Code
     *
     * @return string|null
     */
    public function getTransactionStatusCode();

    /**
     * Set Transaction Status Code
     *
     * @param string $transactionStatusCode
     * @return $this
     */
    public function setTransactionStatusCode($transactionStatusCode);

    /**
     * Get Approved Amount
     *
     * @return float|null
     */
    public function getApprovedAmount();

    /**
     * Set Approved Amount
     *
     * @param float $approvedAmount
     * @return $this
     */
    public function setApprovedAmount($approvedAmount);

    /**
     * Get Balance Amount
     *
     * @return float|null
     */
    public function getBalanceAmount();

    /**
     * Set Balance Amount
     *
     * @param float $balanceAmount
     * @return $this
     */
    public function setBalanceAmount($balanceAmount);

    /**
     * Get Surcharge Amount
     *
     * @return float|null
     */
    public function getSurchargeAmount();

    /**
     * Set Surcharge Amount
     *
     * @param float $surchargeAmount
     * @return $this
     */
    public function setSurchargeAmount($surchargeAmount);

    /**
     * Get Total Transaction Amount
     *
     * @return float|null
     */
    public function getTotalTransactionAmount();

    /**
     * Set Total Transaction Amount
     *
     * @param float $totalTransactionAmount
     * @return $this
     */
    public function setTotalTransactionAmount($totalTransactionAmount);

    /**
     * Get Base Amount
     *
     * @return float|null
     */
    public function getBaseAmount();

    /**
     * Set Base Amount
     *
     * @param float $baseAmount
     * @return $this
     */
    public function setBaseAmount($baseAmount);

    /**
     * Get Captured Amount
     *
     * @return float|null
     */
    public function getCapturedAmount();

    /**
     * Set Captured Amount
     *
     * @param float $capturedAmount
     * @return $this
     */
    public function setCapturedAmount($capturedAmount);

    /**
     * Get Refunded Amount
     *
     * @return float|null
     */
    public function getRefundedAmount();

    /**
     * Set Refunded Amount
     *
     * @param float $refundedAmount
     * @return $this
     */
    public function setRefundedAmount($refundedAmount);

    /**
     * Get CVVResponseCode
     *
     * @return string|null
     */
    public function getCVVResponseCode();

    /**
     * Set CVVResponseCode
     *
     * @param string $code
     * @return $this
     */
    public function setCVVResponseCode($code);


}
