<?php
namespace Silksoftwarecorp\OrderCommentGraphQl\Plugin;

use Magento\Quote\Api\Data\CartExtensionFactory;
use Magento\Quote\Api\CartRepositoryInterface;

class QuoteRepositoryPlugin
{
    private $extensionFactory;

    public function __construct(CartExtensionFactory $extensionFactory)
    {
        $this->extensionFactory = $extensionFactory;
    }

    public function afterGet(CartRepositoryInterface $subject, $quote)
    {
        $extensionAttributes = $quote->getExtensionAttributes();
        if ($extensionAttributes === null) {
            $extensionAttributes = $this->extensionFactory->create();
        }
        $extensionAttributes->setSpecialNotes($quote->getData('special_notes'));
        $quote->setExtensionAttributes($extensionAttributes);
        return $quote;
    }

    public function afterGetActive(CartRepositoryInterface $subject, $quote)
    {
        return $this->afterGet($subject, $quote);
    }

    public function beforeSave(CartRepositoryInterface $subject, $quote)
    {
        $extensionAttributes = $quote->getExtensionAttributes();
        if ($extensionAttributes && $extensionAttributes->getSpecialNotes() !== null) {
            $quote->setData('special_notes', $extensionAttributes->getSpecialNotes());
        }
        return [$quote];
    }
} 