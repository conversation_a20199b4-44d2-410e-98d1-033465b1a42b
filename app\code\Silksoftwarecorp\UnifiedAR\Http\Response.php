<?php

namespace Silksoftwarecorp\UnifiedAR\Http;

use Illuminate\Support\Traits\Macroable;
use Magento\Framework\Serialize\Serializer\Json;
use Psr\Http\Message\ResponseInterface;

class Response
{
    protected ResponseInterface $response;

    protected $transferStats;

    protected $cookies;

    /**
     * @var Json
     */
    protected $serializer;

    use Macroable {
        __call as macroCall;
    }

    public function __construct(
        ResponseInterface $response,
        Json $serializer = null,
    ) {
        $this->response = $response;
        $this->serializer = $serializer ?: new Json();
    }

    public function setCookies(array $cookies): static
    {
        $this->cookies = $cookies;

        return $this;
    }

    public function setTransferStats($transferStats): static
    {
        $this->transferStats = $transferStats;

        return $this;
    }

    public function body(): string
    {
        $body = $this->response->getBody();
        $body->rewind(); // Ensure we read from the beginning
        return $body->getContents();
    }

    public function json(): array
    {
        $body = $this->response->getBody();
        $body->rewind(); // Ensure we read from the beginning
        return $this->serializer->unserialize($body->getContents());
    }

    public function header($header)
    {
        return $this->response->getHeaderLine($header);
    }

    public function headers(): array
    {
        return collect($this->response->getHeaders())->mapWithKeys(function ($v, $k) {
            return [$k => $v[0]];
        })->all();
    }

    public function status(): int
    {
        return (int)$this->response->getStatusCode();
    }

    public function effectiveUri()
    {
        return $this->transferStats?->getEffectiveUri();
    }

    public function isSuccess(): bool
    {
        return $this->status() >= 200 && $this->status() < 300;
    }

    public function isOk(): bool
    {
        return $this->isSuccess();
    }

    public function isRedirect(): bool
    {
        return $this->status() >= 300 && $this->status() < 400;
    }

    public function isClientError(): bool
    {
        return $this->status() >= 400 && $this->status() < 500;
    }

    public function isServerError(): bool
    {
        return $this->status() >= 500;
    }

    public function cookies()
    {
        return $this->cookies;
    }

    public function __toString()
    {
        return $this->body();
    }

    public function __call($method, $args)
    {
        if (static::hasMacro($method)) {
            return $this->macroCall($method, $args);
        }

        return $this->response->{$method}(...$args);
    }
}
