import { useEffect, useState, useCallback, useRef, memo } from 'react'
import { Map, InfoWindow } from 'google-maps-react'

import { MarkerClusterer } from '@googlemaps/markerclusterer'
import PartStoreLocationItem from '@/components/Common/PartStoreLocationItem'
import { useAppMediaQuery } from '@/packages/hooks'

import { StyledGoogleMapMarkerPage } from './styled'

const GoogleMapMarkerPage = ({ google, markers = [], googleMapRef, center }) => {
  const [showingInfoWindow, setShowingInfoWindow] = useState(false)
  const [activeMarker, setActiveMarker] = useState<any>(null)
  const [selectedPlace, setSelectedPlace] = useState<any>({})
  const mapRef = useRef<any>(null)
  const markerInstancesRef = useRef<google.maps.Marker[]>([])
  const clustererRef = useRef<MarkerClusterer | null>(null)
  const { isMobile } = useAppMediaQuery()

  const onMarkerClick = (marker, markerData) => {
    setSelectedPlace(markerData)
    setActiveMarker(marker)
    setShowingInfoWindow(true)
  }

  const renderMarkersAndCluster = useCallback(
    (map) => {
      if (!map || !google) {
        return
      }

      // Clear
      markerInstancesRef.current.forEach((marker) => marker.setMap(null))
      markerInstancesRef.current = []
      if (clustererRef.current) {
        clustererRef.current.clearMarkers()
        clustererRef.current = null
      }

      if (markers.length === 0) {
        return
      }

      const newMarkerInstances = markers.map((marker) => {
        const markerObj = new google.maps.Marker({
          position: {
            lat: marker?.lat ? Number(marker.lat) : 0,
            lng: marker?.lng ? Number(marker.lng) : 0
          },
          map,
          title: marker?.name ?? ''
        })

        markerObj?.addListener('click', () => {
          onMarkerClick(markerObj, marker)
        })

        return markerObj
      })

      markerInstancesRef.current = newMarkerInstances

      // Create locations group
      if (!clustererRef.current) {
        clustererRef.current = new MarkerClusterer({
          map,
          markers: newMarkerInstances,
          renderer: {
            render: ({ count, position }) => {
              return new google.maps.Marker({
                position,
                label: {
                  text: String(count),
                  color: 'white',
                  fontSize: '12px'
                },
                icon: {
                  url: `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(`
          <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40">
            <style>
              .cluster-circle {
                fill: #9D2235;
                stroke: white;
                stroke-width: 2;
              }
              .cluster-text {
                font: bold 14px Arial, sans-serif;
                fill: white;
                text-anchor: middle;
                dominant-baseline: central;
                text-shadow: 0 0 3px rgba(0,0,0,0.5);
              }
            </style>
            <circle class="cluster-circle" cx="20" cy="20" r="18"/>
            <text class="cluster-text" x="20" y="20">${count}</text>
          </svg>
        `)}`
                }
              })
            }
          }
        })
      }

      // Reset map center and zoom
      // mapRef.current.setZoom(4)
      // mapRef.current.setCenter(center)
    },
    [
      google,
      markers
      // center
    ]
  )

  const onMapReady = (mapProps: any, map: any) => {
    mapRef.current = map
    renderMarkersAndCluster(map)
  }

  const onInfoWindowClose = () => {
    setShowingInfoWindow(false)
    setActiveMarker(null)
  }

  useEffect(() => {
    if (mapRef.current) {
      renderMarkersAndCluster(mapRef.current)
    }
  }, [markers, renderMarkersAndCluster])

  return (
    <StyledGoogleMapMarkerPage>
      <Map
        google={google}
        zoom={4}
        style={{
          height: isMobile ? '402px' : '725px'
        }}
        initialCenter={center}
        ref={googleMapRef}
        onReady={onMapReady}>
        <InfoWindow marker={activeMarker} visible={showingInfoWindow} onClose={onInfoWindowClose}>
          <div className="location-popup">
            <PartStoreLocationItem location={selectedPlace} directionsVisible />
          </div>
        </InfoWindow>
      </Map>
    </StyledGoogleMapMarkerPage>
  )
}

export default memo(GoogleMapMarkerPage)
