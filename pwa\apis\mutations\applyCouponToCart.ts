import { gql } from '@apollo/client'
import type { DocumentNode } from '@apollo/client'

import { cartItems } from '../fragment/cartItems'
import { cart_prices } from '../fragment/cartPrices'

export const POST_APPLY_COUPON: DocumentNode = gql`
  mutation applyCouponToCart($cartId: String!, $promo: String!) {
    applyCoupon: applyCouponToCart(input: { cart_id: $cartId, coupon_code: $promo }) {
      cart {
        prices {
          ...cart_prices
          __typename
        }
        quantity: total_quantity
        applied_coupons {
          code
        }
        ...cartItems
        __typename
      }
    }
  }
  ${cartItems}
  ${cart_prices}
`
