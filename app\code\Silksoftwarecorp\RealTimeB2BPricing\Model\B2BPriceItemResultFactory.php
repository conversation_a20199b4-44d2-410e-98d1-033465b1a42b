<?php

namespace Silksoftwarecorp\RealTimeB2BPricing\Model;

use Magento\Framework\ObjectManagerInterface;
use Silksoftwarecorp\RealTimeB2BPricing\Api\B2BPriceItemResultInterface;

class B2BPriceItemResultFactory
{
    /**
     * @var ObjectManagerInterface
     */
    private $objectManager;

    /**
     * @param ObjectManagerInterface $objectManager
     */
    public function __construct(ObjectManagerInterface $objectManager)
    {
        $this->objectManager = $objectManager;
    }

    /**
     * Instantiate SearchResult
     *
     * @param array $data
     * @return B2BPriceItemResult
     */
    public function create(
        array $data = []
    ): B2BPriceItemResultInterface {
        return $this->objectManager->create(
            B2BPriceItemResult::class
        );
    }
}
