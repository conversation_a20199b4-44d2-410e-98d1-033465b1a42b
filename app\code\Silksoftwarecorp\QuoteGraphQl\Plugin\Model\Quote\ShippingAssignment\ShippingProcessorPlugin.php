<?php

namespace Silksoftwarecorp\QuoteGraphQl\Plugin\Model\Quote\ShippingAssignment;

use Magento\Quote\Api\Data\CartInterface;
use Magento\Quote\Api\Data\ShippingInterface;
use Magento\Quote\Model\Quote\ShippingAssignment\ShippingProcessor;

class ShippingProcessorPlugin
{
    public function beforeSave(
        ShippingProcessor $subject,
        ShippingInterface $shipping,
        CartInterface $quote
    ) {
        $shipping->getAddress()?->setShippingMethod($shipping->getMethod());

        return [$shipping, $quote];
    }
}
