import { gql } from '@apollo/client'

export const GET_B2B_ORDER_DETAILS = gql`
  query b2bOrderDetails($order_number: String!) {
    b2bOrderDetails(order_number: $order_number) {
      order_date
      order_number
      po_number
      status
      carrier
      payment_method
      sales_tax
      total_amount
      tracking_number
      freight
      subtotal
      billing_address {
        id
        name
        city
        country
        state
        email
        phone
        address
        zip_code
      }
      shipping_address {
        id
        name
        city
        country
        state
        email
        phone
        address
        zip_code
      }
      ship_from {
        name
        city
        country
        state
        address
        zip_code
      }
      items {
        item_id
        product_name
        product_sku
        quantity_ordered
        status
        row_total
        tracking_number
        description
        product_sale_price {
          value
          currency
        }
        product {
          url_key
        }
      }
      shipping_method {
        carrier_code
        carrier_title
        method_code
        method_title
      }
    }
  }
`
