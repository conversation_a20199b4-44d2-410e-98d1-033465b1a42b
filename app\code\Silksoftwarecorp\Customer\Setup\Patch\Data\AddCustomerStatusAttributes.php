<?php

namespace Silksoftwarecorp\Customer\Setup\Patch\Data;

use Magento\Customer\Model\Customer;
use Magento\Eav\Model\Config;
use Magento\Eav\Model\Entity\Attribute\Source\Table;
use Magento\Eav\Setup\EavSetupFactory;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;

class AddCustomerStatusAttributes implements DataPatchInterface
{
    /**
     * @var ModuleDataSetupInterface
     */
    private $moduleDataSetup;

    /**
     * @var EavSetupFactory
     */
    private $eavSetupFactory;

    /**
     * @var Config
     */
    private $eavConfig;

    /**
     * @param ModuleDataSetupInterface $moduleDataSetup
     * @param EavSetupFactory $eavSetupFactory
     * @param Config $eavConfig
     */
    public function __construct(
        ModuleDataSetupInterface $moduleDataSetup,
        EavSetupFactory $eavSetupFactory,
        \Magento\Eav\Model\Config $eavConfig
    ) {
        $this->moduleDataSetup = $moduleDataSetup;
        $this->eavSetupFactory = $eavSetupFactory;
        $this->eavConfig = $eavConfig;
    }

    /**
     * @inheritDoc
     */
    public function apply()
    {
        $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);
        $eavSetup->addAttribute(
            Customer::ENTITY,
            'customer_status',
            [
                'label' => 'Customer Status',
                'type' => 'int',
                'input' => 'select',
                'source' => Table::class,
                'required' => false,
                'default' => '',
                'system' => false,
                'position' => 100,
                'visible' => true,
                'note' => '',
                'is_user_defined' => true,
                'option' => [
                    'values' => [
                        1 => 'NO SALE',
                        2 => 'OFC'
                    ]
                ]
            ]
        );

        $this->eavConfig->getAttribute(Customer::ENTITY, 'customer_status')
            ->setData('is_user_defined', 1)
            ->setData('is_required', false)
            ->setData('default_value', '')
            ->setData('used_in_forms', [
                'adminhtml_customer'
            ])->save();
    }

    /**
     * @inheritDoc
     */
    public static function getDependencies()
    {
        return [];
    }

    /**
     * @inheritDoc
     */
    public function getAliases()
    {
        return [];
    }
}
