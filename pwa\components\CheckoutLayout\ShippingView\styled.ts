import styled from '@emotion/styled'

export const StyledShippingView = styled.div`
  .title {
    padding-bottom: 15px;
    margin-bottom: 22px;
    font-weight: 700;
    font-size: 24px;
    line-height: 30px;
    letter-spacing: 0;
    border-bottom: 1px solid #d9d9d9;
  }

  .email {
    border-bottom: 1px solid #d9d9d9;
  }

  .contact {
    margin-top: 20px;
    padding-top: 20px;
    padding-bottom: 24px;

    p {
      line-height: 24px;
      font-weight: 400;
    }

    a {
      color: var(--color-primary);
      font-weight: 700;
    }
  }

  .message {
    line-height: 24px;
    margin-bottom: 20px;
    font-size: 15px;
    font-weight: 400;

    span {
      color: var(--color-primary);
      font-weight: 700;
      cursor: pointer;
    }
  }

  .info {
    margin-top: 4px;
    padding: 24px;
    background-color: #f2f2f2;

    p {
      line-height: 24px;
      font-size: 15px;
      font-weight: 400;
    }

    .shipping {
      line-height: 27px;
      font-size: 20px;
      font-weight: 500;
    }
  }

  .shipping-next {
    margin-top: 24px;
    width: 109px;
    height: 49px;
    border-radius: 3px;
    font-weight: 700;
    font-size: 16px;
    line-height: 21px;
    letter-spacing: 0.03em;
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    .shipping-next {
      width: 100%;
    }
  }
`

export const StyledMethodList = styled.div`
  margin-bottom: 56px;

  &#fulfillment-method-list {
    .${({ theme }) => theme.namespace}-space {
      row-gap: 12px;
    }
    .${({ theme }) => theme.namespace}-radio {
      &-wrapper {
        display: inline-flex;

        h3,
        p {
          font-weight: 400;
          font-size: 15px;
          line-height: 25px;
          letter-spacing: 0.01em;
        }
        h3 {
          margin-bottom: 0;
          font-weight: 700;
        }
      }
    }
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    margin-bottom: 40px;
  }
`
