import styled from '@emotion/styled'

export const StyledAddressContainer = styled.div`
  h3 {
    margin-bottom: 16px;
    font-weight: 700;
    font-size: 24px;
    line-height: 30px;
    letter-spacing: 0;
  }
`

export const StyledActiveAddress = styled.div`
  display: flex;
  padding: 22px 38px 22px 22px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  justify-content: space-between;
  align-items: center;

  .address {
    font-weight: 400;
    font-size: 15px;
    line-height: 23px;
    letter-spacing: 0.01em;

    .address-company {
      font-weight: 700;
    }

    .address-telephone {
      color: var(--color-font) !important;
    }
  }

  .change-btn {
    font-weight: 700;
    font-size: 15px;
    line-height: 21px;
    letter-spacing: 0.01em;
    text-decoration: underline;
    text-decoration-style: solid;
    text-decoration-thickness: Auto;
    color: var(--color-primary) !important;
  }

  @media (max-width: ${({ theme }) => theme.breakPoint.sm}px) {
    padding: 22px;

    .change-btn {
      padding: 0;
      margin-left: 16px;
    }
  }
`

export const StyledNoAddress = styled.div`
  margin: 2rem 0;
`
