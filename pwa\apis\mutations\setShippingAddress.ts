import { gql, DocumentNode } from '@apollo/client'

import { shippingAddress } from '../fragment/shippingAddress'

export const POST_SHIPPING_ADDRESS: DocumentNode = gql`
  mutation setShippingAddress($cartId: String!, $addressList: [ShippingAddressInput]!) {
    shippingAddress: setShippingAddressesOnCart(
      input: { cart_id: $cartId, shipping_addresses: $addressList }
    ) {
      cart {
        shipping_addresses {
          ...shippingAddress
        }
      }
    }
  }
  ${shippingAddress}
`
