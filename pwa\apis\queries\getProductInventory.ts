import { gql } from '@apollo/client'

export const GET_PRODUCT_INVENTORY = gql`
  query getProductInventory(
    $search: String
    $filter: ProductAttributeFilterInput
    $pageSize: Int
    $currentPage: Int
    $sort: ProductAttributeSortInput
  ) {
    products(
      search: $search
      pageSize: $pageSize
      currentPage: $currentPage
      filter: $filter
      sort: $sort
    ) {
      items {
        name
        sku
        product_inventory_source {
          source_code
          quantity
        }
      }
    }
  }
`
